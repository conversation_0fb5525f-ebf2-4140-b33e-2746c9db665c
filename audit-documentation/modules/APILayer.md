# API Layer Module Documentation

## Module Identity

- **Module Name**: API Layer
- **Type**: Core Infrastructure Module
- **Repository**: ryvyl-cards-main
- **Framework**: Next.js 14 API Routes
- **Version**: 1.0.0
- **Location**: `src/app/api/`, `server.js`

## Purpose and Functionality

### Business Purpose
The API Layer provides a comprehensive backend interface for the Ryvyl Cards platform, handling authentication, data operations, file processing, and business logic through RESTful endpoints and custom middleware.

### Core Features
- **RESTful API Endpoints**: Standardized HTTP API interface
- **Authentication Middleware**: JWT-based request authentication
- **Data CRUD Operations**: Complete data management capabilities
- **File Processing APIs**: Upload and processing endpoints
- **Activity Logging**: Comprehensive request/response logging
- **Error Handling**: Centralized error management

### Target Users
- **Frontend Applications**: Web and mobile clients
- **External Partners**: B2B API integrations
- **Internal Services**: Microservice communication
- **Administrative Tools**: Management interfaces

## Technical Architecture

### Pattern
**Layered API Architecture** with middleware, controllers, and data access layers

### Key Components

#### 1. API Routes Structure
```
src/app/api/
├── activity/           # Activity logging endpoints
├── get-records/        # Data retrieval
├── save-records/       # Data persistence
├── save-records-enhanced/  # Advanced data processing
├── validate-polish-post/   # Validation services
├── check-dhl-duplicates/   # Duplicate detection
└── get-country-delivery-methods/  # Delivery options
```

#### 2. Express Server (`server.js`)
```javascript
const server = express()
server.use('/api/users', userRoutes)
server.use('/api/roles', roleRoutes)
server.use('/api/company', companyRoutes)
```

#### 3. Middleware Stack
```typescript
// Authentication middleware
export async function middleware(request: NextRequest) {
    // JWT validation and user context
}

// CORS configuration
server.use(cors({
    origin: 'http://localhost:3000'  // ⚠️ Hardcoded origin
}))
```

### Data Flow

```mermaid
sequenceDiagram
    participant Client
    participant Middleware
    participant APIRoute
    participant Database
    participant Logger
    
    Client->>Middleware: HTTP Request
    Middleware->>Middleware: Validate JWT
    Middleware->>APIRoute: Authorized Request
    APIRoute->>Database: Data Operation
    Database-->>APIRoute: Result
    APIRoute->>Logger: Log Activity
    APIRoute-->>Client: HTTP Response
```

### Performance Characteristics
- **Response Time**: 100-500ms for typical operations
- **Throughput**: Limited by database connection pool
- **Concurrency**: Node.js event loop handling
- **Caching**: ❌ No caching implemented

## Dependencies and Integrations

### Libraries
```json
{
  "express": "^5.1.0",          // HTTP server framework
  "cors": "^2.8.5",             // Cross-origin resource sharing
  "body-parser": "^1.20.3",     // Request body parsing
  "cookie-parser": "^1.4.7",    // Cookie handling
  "express-validator": "^7.2.1", // Input validation
  "express-session": "^1.18.1"  // Session management
}
```

### Internal Dependencies
- **Database Layer**: MongoDB connection and operations
- **Authentication**: JWT verification and user context
- **Activity Logger**: Request/response tracking
- **Validation Engine**: Input validation and sanitization

### External Integrations
- **MongoDB Atlas**: Primary database
- **Email Services**: Notification delivery
- **Payment Gateways**: Transaction processing
- **External APIs**: DHL, Polish Post integrations

## Development Info

### Build Process
- **TypeScript Compilation**: API route compilation
- **Express Server**: Separate server process
- **Environment Configuration**: Multi-environment support

### Testing
- **Unit Tests**: ❌ Missing for API endpoints
- **Integration Tests**: ❌ Missing for database operations
- **API Tests**: ❌ Missing endpoint testing

### Linting
- **ESLint**: Basic configuration
- **API Standards**: ❌ No API linting rules

### Setup Requirements
```bash
# Environment variables
DATABASE_URL=mongodb://localhost:27017/ryvyl
JWT_SECRET=your-secret-key
PORT=3000
NODE_ENV=development
```

## Deployment & Operations

### CI/CD
- **Build**: Next.js API compilation
- **Testing**: ❌ No automated API testing
- **Deployment**: Vercel/Node.js hosting

### Configuration
```typescript
// API configuration
const API_CONFIG = {
  PORT: process.env.PORT || 3000,
  CORS_ORIGIN: process.env.CORS_ORIGIN || 'http://localhost:3000',
  RATE_LIMIT: 100, // requests per minute
  TIMEOUT: 30000   // 30 seconds
}
```

### Scaling Considerations
- **Horizontal Scaling**: Stateless API design
- **Load Balancing**: Multiple instance support
- **Database Connections**: Connection pooling required

## Monitoring & Health

### Logging
- **Request Logging**: All API requests logged
- **Error Logging**: Detailed error tracking
- **Performance Logging**: Response time monitoring
- **Activity Logging**: User action tracking

### Metrics
- **Request Rate**: Requests per second
- **Error Rate**: Failed request percentage
- **Response Time**: Average response latency
- **Database Performance**: Query execution times

### Alerts
- **High Error Rate**: ❌ Not implemented
- **Slow Responses**: ❌ Not implemented
- **Database Issues**: ❌ Not implemented

## Database Usage

### Collections Accessed
- **users**: User management operations
- **companies**: Company data operations
- **cards**: Card management
- **activities**: Activity logging
- **PocztaPost**: Postal data
- **DHLDeliveryMethods**: Shipping data

### Connection Management
```typescript
// MongoDB connection
let client: MongoClient
let db: Db

export async function connectToDatabase(): Promise<Db> {
  if (db) return db
  
  client = new MongoClient(MONGODB_URI)
  await client.connect()
  db = client.db(DB_NAME)
  return db
}
```

## Security Considerations

### 🔴 CRITICAL VULNERABILITIES

#### 1. Hardcoded CORS Origin
- **Location**: `server.js:38`
- **Issue**: CORS origin hardcoded to localhost
- **Risk**: Production security bypass
- **CVSS**: 7.5 (High)
- **Fix**: Use environment variables for CORS configuration

#### 2. Missing Rate Limiting
- **Issue**: No rate limiting on API endpoints
- **Risk**: DoS attacks, resource exhaustion
- **CVSS**: 7.0 (High)
- **Fix**: Implement rate limiting middleware

#### 3. Insufficient Input Validation
- **Issue**: Inconsistent input validation across endpoints
- **Risk**: Injection attacks, data corruption
- **CVSS**: 8.0 (High)
- **Fix**: Implement comprehensive validation

### 🟡 MEDIUM RISK ISSUES

#### 4. Error Information Disclosure
- **Issue**: Detailed error messages in production
- **Risk**: Information leakage
- **Fix**: Sanitize error responses

#### 5. Missing Security Headers
- **Issue**: No security headers implemented
- **Risk**: Various client-side attacks
- **Fix**: Add security headers middleware

### Security Fixes Required

```typescript
// 1. CORS configuration
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
  optionsSuccessStatus: 200
}))

// 2. Rate limiting
import rateLimit from 'express-rate-limit'
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
})
app.use('/api/', limiter)

// 3. Security headers
import helmet from 'helmet'
app.use(helmet())

// 4. Input validation
import { body, validationResult } from 'express-validator'
const validateInput = [
  body('email').isEmail(),
  body('password').isLength({ min: 8 }),
  (req, res, next) => {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() })
    }
    next()
  }
]
```

## Operational Procedures

### Start/Stop
```bash
# Start API server
npm run dev          # Development
npm run start        # Production

# Stop server
kill -TERM $PID      # Graceful shutdown
```

### Troubleshooting

#### Common Issues
1. **Database Connection Errors**: Check MongoDB connectivity
2. **CORS Issues**: Verify origin configuration
3. **Authentication Failures**: Check JWT secret and token validity

#### Debug Commands
```bash
# Check API health
curl http://localhost:3000/api/health

# Monitor API logs
tail -f /var/log/api.log

# Test database connection
node -e "require('./src/lib/mongodb').connectToDatabase().then(() => console.log('Connected'))"
```

### Maintenance
- **Log Rotation**: Implement log rotation for large log files
- **Database Cleanup**: Regular cleanup of old activity logs
- **Performance Monitoring**: Track API performance metrics

## APIs & Integration Points

### Core Endpoints

#### Authentication
- `POST /api/auth/login` - User authentication
- `POST /api/auth/logout` - User logout
- `GET /api/users/me` - Current user info

#### Data Management
- `GET /api/get-records` - Retrieve data records
- `POST /api/save-records` - Save data records
- `POST /api/save-records-enhanced` - Advanced data processing

#### Validation Services
- `POST /api/validate-polish-post` - Validate postal data
- `POST /api/check-dhl-duplicates` - Check for duplicates

#### Activity Tracking
- `POST /api/activity` - Log user activity
- `GET /api/activity` - Retrieve activity logs

### API Standards
```typescript
// Standard response format
interface APIResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
  timestamp: string
}

// Error response format
interface APIError {
  error: string
  code?: string
  details?: any
  timestamp: string
}
```

### Integration Patterns
- **RESTful Design**: Standard HTTP methods and status codes
- **JSON Communication**: Request/response in JSON format
- **Error Handling**: Consistent error response structure
- **Authentication**: JWT bearer token authentication

## Development Context for AI Agents

### Patterns Used
- **Express.js Middleware**: Request processing pipeline
- **Next.js API Routes**: File-based routing
- **Repository Pattern**: Data access abstraction
- **Middleware Chain**: Authentication and validation

### Extension Points
- **Custom Middleware**: Add new request processing logic
- **API Versioning**: Support multiple API versions
- **Plugin System**: Modular functionality extensions
- **Webhook Support**: Event-driven integrations

### Impact of Changes
- **Route Changes**: Affects client applications
- **Middleware Changes**: Impacts all requests
- **Schema Changes**: Requires API versioning

## Ownership & Contact

### Responsible Team
- **Backend Development**: API implementation and maintenance
- **DevOps**: Deployment and infrastructure
- **Security**: API security and compliance

### Subject Matter Experts
- **API Design**: Senior Backend Developer
- **Security**: Security Engineer
- **Performance**: Performance Engineer

### Documentation Links
- **API Documentation**: ❌ Not available
- **Integration Guide**: ❌ Not available
- **Security Guidelines**: ❌ Not available

## Security Score: 3.5/10 (High Risk)

The API layer has significant security vulnerabilities including missing rate limiting, insufficient input validation, and hardcoded configuration values that require immediate attention to secure the application.
