# Data Processing Module Documentation

## Module Identity

- **Module Name**: Data Processing System
- **Type**: Core Data Module
- **Repository**: ryvyl-cards-main
- **Framework**: Next.js 14 with MongoDB
- **Version**: 1.0.0
- **Location**: `src/lib/database.ts`, `src/lib/enhanced-database.ts`, `src/app/api/save-records*`

## Purpose and Functionality

### Business Purpose
The Data Processing module handles large-scale Excel/CSV file uploads, data validation, duplicate detection, and batch processing for delivery methods (Polish Post and DHL) and other business data within the Ryvyl Cards platform.

### Core Features
- **File Upload Processing**: Excel/CSV file parsing and validation
- **Batch Data Processing**: Large dataset handling with progress tracking
- **Duplicate Detection**: Advanced duplicate identification and handling
- **Data Validation**: Comprehensive validation rules and error reporting
- **Database Operations**: Optimized MongoDB operations for large datasets
- **Progress Tracking**: Real-time upload progress monitoring

### Target Users
- **Data Administrators**: Bulk data upload and management
- **Operations Team**: Delivery method configuration
- **System Administrators**: Data maintenance and cleanup

## Technical Architecture

### Pattern
**ETL Pipeline Architecture** with validation, transformation, and loading phases

### Key Components

#### 1. Database Layer (`src/lib/database.ts`)
```typescript
export async function saveRecordsToMongoDB(
    records: Array<{
      fileName: string
      sheetName: string
      headers: string[]
      rowData: Record<string, any>
      rowIndex: number
      totalRows: number
    }>,
    onProgress: (saved: number, total: number) => void,
): Promise<string[]>
```

#### 2. Enhanced Processing (`src/lib/enhanced-database.ts`)
```typescript
export interface EnhancedUploadOptions {
  duplicateHandling: 'skip' | 'replace' | 'merge'
  validationLevel: 'strict' | 'moderate' | 'lenient'
  batchSize: number
  enableComparison: boolean
}
```

#### 3. Validation Engine (`src/lib/dhl-validation.ts`)
```typescript
export interface ValidationRule {
  field: string
  rule: (value: any, record: Record<string, any>) => boolean
  errorMessage: string
}
```

### Data Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant Validator
    participant Database
    participant ProgressTracker
    
    User->>Frontend: Upload File
    Frontend->>API: POST /api/save-records-enhanced
    API->>Validator: Validate Data
    Validator-->>API: Validation Results
    API->>Database: Batch Insert (if valid)
    Database-->>ProgressTracker: Update Progress
    ProgressTracker-->>Frontend: Progress Update
    API-->>Frontend: Final Results
    Frontend-->>User: Display Summary
```

### Performance Characteristics
- **Batch Size**: 1000 records per batch for optimal performance
- **Memory Usage**: Streaming processing to minimize memory footprint
- **Progress Tracking**: Real-time progress updates every 50ms
- **Concurrent Processing**: Single-threaded to prevent database overload

## Dependencies and Integrations

### Libraries
```json
{
  "mongodb": "^6.16.0",         // Database operations
  "xlsx": "^0.18.5",            // ⚠️ VULNERABLE - Excel file processing
  "mongoose": "^8.14.2"         // ODM for MongoDB
}
```

### Internal Dependencies
- **File Upload Components**: Frontend file handling
- **Progress Indicators**: UI progress tracking
- **Error Reporting**: Validation error display
- **Activity Logger**: Data operation tracking

### External Integrations
- **MongoDB Atlas**: Primary data storage
- **File Storage**: Temporary file processing
- **Validation Services**: External data validation APIs

## Development Info

### Build Process
- **TypeScript Compilation**: Strict type checking
- **Database Schema**: MongoDB collections with indexes
- **Validation Rules**: Configurable validation engine

### Testing
- **Unit Tests**: ❌ Missing for validation logic
- **Integration Tests**: ❌ Missing for database operations
- **Performance Tests**: ❌ Missing for large datasets

### Linting
- **ESLint**: Basic configuration
- **Data Validation**: Runtime validation with Zod

### Setup Requirements
```bash
# Environment variables
MONGODB_URI=mongodb+srv://user:<EMAIL>/db
UPLOAD_MAX_SIZE=50MB
BATCH_SIZE=1000
```

## Deployment & Operations

### CI/CD
- **Build**: Next.js API route compilation
- **Database Migration**: Manual schema updates
- **Performance Testing**: ❌ Not implemented

### Configuration
```typescript
// Processing configuration
const PROCESSING_CONFIG = {
  BATCH_SIZE: 1000,
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  SUPPORTED_FORMATS: ['.xlsx', '.csv'],
  VALIDATION_TIMEOUT: 30000
}
```

### Scaling Considerations
- **Memory Management**: Streaming for large files
- **Database Connections**: Connection pooling
- **Concurrent Uploads**: Queue-based processing

## Monitoring & Health

### Logging
- **Upload Events**: File processing start/end
- **Validation Errors**: Detailed error logging
- **Performance Metrics**: Processing time tracking
- **Database Operations**: Query performance logging

### Metrics
- **Upload Success Rate**: Percentage of successful uploads
- **Processing Time**: Average time per record
- **Error Rate**: Validation failure percentage
- **Database Performance**: Query execution times

### Alerts
- **Upload Failures**: ❌ Not implemented
- **Performance Degradation**: ❌ Not implemented
- **Database Errors**: ❌ Not implemented

## Database Usage

### Collections Used
- **PocztaPost**: Polish postal delivery methods
- **DHLDeliveryMethods**: DHL shipping options
- **FileMetadata**: Upload metadata and statistics
- **ValidationErrors**: Error tracking and reporting

### Schema
```typescript
interface PocztaPostRecord {
  _id?: string
  [key: string]: any          // Dynamic fields from upload
  _fileName: string
  _sheetName: string
  _rowIndex: number
  _totalRows: number
  _uploadedAt: Date
  _createdAt: Date
  _updatedAt: Date
}

interface FileMetadata {
  fileName: string
  sheetName: string
  totalRows: number
  totalColumns: number
  headers: string[]
  uploadedAt: Date
}
```

### Indexes
```javascript
// Performance indexes
db.PocztaPost.createIndex({ "_fileName": 1, "_uploadedAt": -1 })
db.DHLDeliveryMethods.createIndex({ "Country": 1 })
db.FileMetadata.createIndex({ "uploadedAt": -1 })
```

## Security Considerations

### 🔴 CRITICAL VULNERABILITIES

#### 1. XLSX Package Vulnerability
- **Package**: xlsx@0.18.5
- **Issues**: Prototype pollution (CVSS 7.8), ReDoS (CVSS 7.5)
- **Risk**: Code execution, DoS attacks
- **Fix**: Upgrade to xlsx@0.20.2+

#### 2. File Upload Security
- **Issue**: No file type validation beyond extension
- **Risk**: Malicious file upload
- **CVSS**: 7.0 (High)
- **Fix**: Implement proper MIME type validation

#### 3. Injection Vulnerabilities
- **Issue**: Dynamic field insertion without sanitization
- **Risk**: NoSQL injection attacks
- **CVSS**: 8.0 (High)
- **Fix**: Implement input sanitization

### 🟡 MEDIUM RISK ISSUES

#### 4. Resource Exhaustion
- **Issue**: No limits on file size or processing time
- **Risk**: DoS through large file uploads
- **Fix**: Implement proper resource limits

#### 5. Data Validation Bypass
- **Issue**: Validation can be disabled or bypassed
- **Risk**: Invalid data in database
- **Fix**: Enforce mandatory validation

### Security Fixes Required

```typescript
// 1. File validation
const validateFile = (file: File) => {
  const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
  const maxSize = 50 * 1024 * 1024 // 50MB
  
  if (!allowedTypes.includes(file.type)) {
    throw new Error('Invalid file type')
  }
  if (file.size > maxSize) {
    throw new Error('File too large')
  }
}

// 2. Input sanitization
const sanitizeInput = (data: any) => {
  if (typeof data === 'string') {
    return data.replace(/[<>\"']/g, '') // Basic XSS prevention
  }
  return data
}

// 3. Resource limits
const RESOURCE_LIMITS = {
  MAX_RECORDS: 100000,
  MAX_PROCESSING_TIME: 300000, // 5 minutes
  MAX_MEMORY_USAGE: 512 * 1024 * 1024 // 512MB
}
```

## Operational Procedures

### Start/Stop
- **Start**: Automatic with API server
- **Stop**: Graceful shutdown with cleanup

### Troubleshooting

#### Common Issues
1. **Upload Timeouts**: Check file size and processing limits
2. **Validation Failures**: Review validation rules and data format
3. **Database Errors**: Check connection and disk space

#### Debug Commands
```bash
# Check upload status
db.FileMetadata.find().sort({ uploadedAt: -1 }).limit(10)

# Monitor processing performance
db.PocztaPost.aggregate([
  { $group: { _id: "$_fileName", count: { $sum: 1 } } }
])

# Check validation errors
grep "validation error" /var/log/application.log
```

### Maintenance
- **Data Cleanup**: Remove old temporary files
- **Index Optimization**: Rebuild database indexes
- **Performance Monitoring**: Track processing metrics

## APIs & Integration Points

### Data Processing Endpoints
- `POST /api/save-records` - Basic record upload
- `POST /api/save-records-enhanced` - Advanced upload with options
- `GET /api/get-records` - Retrieve processed data
- `POST /api/validate-polish-post` - Validate postal data
- `POST /api/check-dhl-duplicates` - Check for duplicates

### Integration Contracts
```typescript
interface UploadRequest {
  records: Array<{
    fileName: string
    sheetName: string
    headers: string[]
    rowData: Record<string, any>
    rowIndex: number
    totalRows: number
  }>
  options: EnhancedUploadOptions
  isFirstBatch: boolean
}

interface UploadResponse {
  success: boolean
  savedRecords: number
  duplicatesHandled: number
  validationErrors: ValidationError[]
  metadataId: string
}
```

### Consumers
- **Admin Dashboard**: Data upload interface
- **Reporting System**: Processed data consumption
- **External APIs**: Data export functionality

## Development Context for AI Agents

### Patterns Used
- **ETL Pipeline**: Extract, Transform, Load pattern
- **Batch Processing**: Chunked data processing
- **Progress Tracking**: Real-time progress updates
- **Validation Engine**: Rule-based data validation

### Extension Points
- **Custom Validators**: Add new validation rules
- **Data Transformers**: Custom data transformation logic
- **Export Formats**: Additional output formats
- **Integration APIs**: External data source connectors

### Impact of Changes
- **Schema Changes**: Requires data migration
- **Validation Changes**: Affects data quality
- **Performance Changes**: Impacts user experience

## Ownership & Contact

### Responsible Team
- **Data Engineering**: Data processing logic
- **Backend Development**: API implementation
- **Operations**: Data quality and monitoring

### Subject Matter Experts
- **Data Processing**: Senior Data Engineer
- **Database Optimization**: Database Administrator
- **Validation Logic**: Business Analyst

### Documentation Links
- **Data Processing Guide**: ❌ Not available
- **Validation Rules**: ❌ Not documented
- **Performance Tuning**: ❌ Not available

## Security Score: 3.2/10 (High Risk)

The data processing module has critical vulnerabilities in file handling and data validation that could lead to security breaches and system compromise. Immediate attention required for XLSX package upgrade and input validation.
