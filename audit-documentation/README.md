# 🚨 Ryvyl Cards Security Audit - Critical Issues Summary

## 📊 Overall Security Score: **2.8/10 (CRITICAL RISK)**

This document summarizes the **most critical and high-priority security issues** found in the Ryvyl Cards codebase that require **immediate attention**.

---

## 🔴 CRITICAL VULNERABILITIES (Fix within 24 hours)

### 1. 🚨 Hardcoded Database Credentials (CVSS 9.8)

**📍 Location**: `src/lib/mongodb.ts:3`

**❌ Current Code**:
```typescript
const MONGODB_URI = "mongodb+srv://hostingstratified20:<EMAIL>/ryvyl"
```

**✅ Fix**:
```typescript
const MONGODB_URI = process.env.MONGODB_URI || ""
if (!MONGODB_URI) {
  throw new Error("MONGODB_URI environment variable is required")
}
```

**🔧 Action Required**:
1. Create `.env.local` file with: `MONGODB_URI=your-secure-connection-string`
2. Remove hardcoded credentials from source code
3. Update deployment environment variables

---

### 2. 🚨 Vulnerable Next.js Version (CVSS 9.1)

**📍 Location**: `package.json:70`

**❌ Current Version**: `next@14.2.13`
**✅ Required Version**: `next@14.2.32` or later

**🔧 Fix Command**:
```bash
npm update next@14.2.32
```

**🛡️ Vulnerabilities Fixed**:
- Authorization bypass in middleware
- DoS with Server Actions
- Cache poisoning attacks
- Information exposure in dev server

---

### 3. 🚨 Vulnerable XLSX Package (CVSS 7.8)

**📍 Location**: `package.json:91`

**❌ Current Version**: `xlsx@0.18.5`
**✅ Required Version**: `xlsx@0.20.2` or later

**🔧 Fix Command**:
```bash
npm update xlsx@0.20.2
```

**🛡️ Vulnerabilities Fixed**:
- Prototype pollution attacks
- Regular Expression DoS (ReDoS)

---

### 4. 🚨 Client-Side JWT Validation (CVSS 9.1)

**📍 Location**: `src/middleware.ts:6-28`

**❌ Current Code**:
```typescript
const decodeJWT = (token: string) => {
    try {
        const parts = token.split(".")
        const payload = JSON.parse(atob(parts[1]))  // ⚠️ CLIENT-SIDE ONLY!
        return { payload, isExpired: Date.now() >= payload.exp * 1000 }
    } catch (error) {
        return null
    }
}
```

**✅ Fix**:
```typescript
import { jwtVerify } from 'jose'

const verifyJWT = async (token: string) => {
    try {
        const secret = new TextEncoder().encode(process.env.JWT_SECRET)
        const { payload } = await jwtVerify(token, secret)
        return payload
    } catch (error) {
        console.error("JWT verification failed:", error)
        return null
    }
}
```

---

### 5. 🚨 Insecure Token Storage (CVSS 8.5)

**📍 Location**: `src/utils/axiosInstance.js:12`

**❌ Current Code**:
```javascript
const token = localStorage.getItem("authToken")  // ⚠️ XSS VULNERABLE!
```

**📍 Also in**: `src/components/auto-logout-provider.tsx:37`

**✅ Fix**:
```typescript
// Use httpOnly cookies instead
const setSecureToken = (token: string) => {
    document.cookie = `token=${token}; HttpOnly; Secure; SameSite=Strict; Max-Age=3600`
}

// Server-side token retrieval
const getTokenFromCookies = (request: NextRequest) => {
    return request.cookies.get('token')?.value
}
```

---

## 🟡 HIGH PRIORITY ISSUES (Fix within 1 week)

### 6. 🔒 Hardcoded Company ID

**📍 Location**: `src/app/corporate/cards/page.tsx:21`

**❌ Current Code**:
```typescript
const DEFAULT_COMPANY_ID = "6785126247b8a6a67fbf7cad"  // ⚠️ HARDCODED!
```

**✅ Fix**:
```typescript
const getCompanyId = async (userId: string) => {
    const user = await User.findById(userId)
    if (!user?.companyId) {
        throw new Error('User not associated with company')
    }
    return user.companyId
}
```

---

### 7. 🔒 Missing Input Validation

**📍 Location**: `src/app/api/save-records/route.ts:5-12`

**❌ Current Code**:
```typescript
export async function POST(request: NextRequest) {
    const body = await request.json()  // ⚠️ NO VALIDATION!
    const { records, metadata } = body
    // Direct use without validation
}
```

**✅ Fix**:
```typescript
import { z } from 'zod'

const RecordSchema = z.object({
    fileName: z.string().min(1).max(255),
    sheetName: z.string().min(1).max(100),
    headers: z.array(z.string()),
    rowData: z.record(z.any())
})

export async function POST(request: NextRequest) {
    try {
        const body = await request.json()
        const validatedData = RecordSchema.parse(body)
        // Use validated data
    } catch (error) {
        return NextResponse.json({ error: "Invalid input" }, { status: 400 })
    }
}
```

---

### 8. 🔒 Hardcoded CORS Origin

**📍 Location**: `server.js:38`

**❌ Current Code**:
```javascript
server.use(cors({
    origin: 'http://localhost:3000'  // ⚠️ HARDCODED!
}));
```

**✅ Fix**:
```javascript
server.use(cors({
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
    credentials: true,
    optionsSuccessStatus: 200
}));
```

---

### 9. 🔒 Missing Rate Limiting

**📍 Location**: All API routes lack rate limiting

**✅ Fix**:
```typescript
import rateLimit from 'express-rate-limit'

const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP'
})

server.use('/api/', limiter)
```

---

### 10. 🔒 Information Disclosure in Errors

**📍 Location**: Multiple API routes

**❌ Current Code**:
```typescript
} catch (error) {
    console.error("Error:", error)
    return NextResponse.json({ error: `Failed: ${error}` }, { status: 500 })  // ⚠️ EXPOSES DETAILS!
}
```

**✅ Fix**:
```typescript
} catch (error) {
    console.error("Error:", error)
    const message = process.env.NODE_ENV === 'production' 
        ? 'Internal server error' 
        : error.message
    return NextResponse.json({ error: message }, { status: 500 })
}
```

---

### 11. 🔒 Missing Security Headers

**📍 Location**: No security headers implemented

**✅ Fix**:
```typescript
import helmet from 'helmet'

server.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"]
        }
    },
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
    }
}))
```

---

## 🛠️ Immediate Action Plan

### Step 1: Environment Setup (30 minutes)
```bash
# 1. Create environment file
cp .env.example .env.local

# 2. Add required variables
echo "MONGODB_URI=your-secure-connection-string" >> .env.local
echo "JWT_SECRET=your-secure-jwt-secret" >> .env.local
echo "ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com" >> .env.local
```

### Step 2: Package Updates (15 minutes)
```bash
# Update critical packages
npm update next@14.2.32
npm update xlsx@0.20.2

# Install security packages
npm install helmet express-rate-limit
```

### Step 3: Code Fixes (2-4 hours)
1. **Fix hardcoded credentials** in `src/lib/mongodb.ts`
2. **Implement server-side JWT verification** in `src/middleware.ts`
3. **Replace localStorage with httpOnly cookies** in auth components
4. **Add input validation** to all API routes
5. **Remove hardcoded values** throughout codebase

### Step 4: Security Hardening (1 day)
1. **Add rate limiting** to all API endpoints
2. **Implement security headers** with Helmet
3. **Add comprehensive error handling**
4. **Implement CSRF protection**

---

## 📋 Verification Checklist

- [ ] ✅ Removed hardcoded database credentials
- [ ] ✅ Updated Next.js to secure version
- [ ] ✅ Updated XLSX package to secure version  
- [ ] ✅ Implemented server-side JWT verification
- [ ] ✅ Replaced localStorage with httpOnly cookies
- [ ] ✅ Added input validation to API routes
- [ ] ✅ Removed hardcoded configuration values
- [ ] ✅ Implemented rate limiting
- [ ] ✅ Added security headers
- [ ] ✅ Fixed error information disclosure

---

## 🔍 Testing Your Fixes

### Security Test Commands
```bash
# 1. Test JWT verification
curl -H "Authorization: Bearer invalid-token" http://localhost:3000/api/protected

# 2. Test rate limiting
for i in {1..110}; do curl http://localhost:3000/api/test; done

# 3. Test input validation
curl -X POST -H "Content-Type: application/json" \
  -d '{"malicious": "<script>alert(1)</script>"}' \
  http://localhost:3000/api/save-records
```

### Security Scan
```bash
# Run npm audit
npm audit

# Check for hardcoded secrets
grep -r "mongodb+srv://" src/
grep -r "password.*=" src/
grep -r "secret.*=" src/
```

---

## 📞 Need Help?

If you encounter issues while implementing these fixes:

1. **Check the detailed documentation** in the `modules/` folder
2. **Review the specific file locations** mentioned above
3. **Test each fix incrementally** to avoid breaking changes
4. **Use environment variables** for all sensitive configuration

**Remember**: These are critical security issues that expose your application to serious attacks. Prioritize fixing them immediately!

---

## 🔍 Additional Critical Code Locations

### 12. 🔒 Unsafe Database Queries

**📍 Location**: `src/lib/database.ts:158-183`

**❌ Current Code**:
```typescript
export async function getRecordsFromMongoDB(
    fileName?: string,
    limit = 100,
    skip = 0,
    searchFilter?: any,  // ⚠️ NO VALIDATION!
): Promise<PocztaPostRecord[]> {
    // Direct use of searchFilter without sanitization
    if (searchFilter && Object.keys(searchFilter).length > 0) {
        filter = searchFilter  // ⚠️ INJECTION RISK!
    }
}
```

**✅ Fix**:
```typescript
import { z } from 'zod'

const SearchFilterSchema = z.object({
    field: z.string().regex(/^[a-zA-Z_][a-zA-Z0-9_]*$/), // Only valid field names
    value: z.string().max(100),
    operator: z.enum(['eq', 'ne', 'gt', 'lt', 'contains'])
})

export async function getRecordsFromMongoDB(
    fileName?: string,
    limit = 100,
    skip = 0,
    searchFilter?: any,
): Promise<PocztaPostRecord[]> {
    let filter: any = {}

    if (searchFilter) {
        try {
            const validatedFilter = SearchFilterSchema.parse(searchFilter)
            // Build safe query based on validated input
            filter[validatedFilter.field] = buildSafeQuery(validatedFilter)
        } catch (error) {
            throw new Error('Invalid search filter')
        }
    }
}
```

---

### 13. 🔒 Weak Authentication Check

**📍 Location**: `src/components/with-auth.tsx:13-26`

**❌ Current Code**:
```typescript
const checkAuth = async () => {
    try {
        const response = await axiosInstance.get('/users/me', { withCredentials: true });
        if (!response.data) {  // ⚠️ WEAK CHECK!
            router.push('/login');
        }
    } catch (error) {
        router.push('/login');
    }
};
```

**✅ Fix**:
```typescript
const checkAuth = async () => {
    try {
        const response = await axiosInstance.get('/users/me', { withCredentials: true });

        // Proper validation
        if (!response.data ||
            !response.data.id ||
            !response.data.email ||
            response.data.status !== 'active') {
            throw new Error('Invalid user session');
        }

        // Verify token expiration
        const tokenExp = response.data.tokenExpiration;
        if (tokenExp && new Date(tokenExp) < new Date()) {
            throw new Error('Token expired');
        }

    } catch (error) {
        // Clear any stored auth data
        localStorage.removeItem('authToken');
        sessionStorage.clear();
        router.push('/login');
    }
};
```

---

### 14. 🔒 Unprotected API Routes

**📍 Location**: `src/app/api/get-records/route.ts:5`

**❌ Current Code**:
```typescript
export async function GET(request: NextRequest) {
    // ⚠️ NO AUTHENTICATION CHECK!
    try {
        const { searchParams } = new URL(request.url)
        // Direct data access without auth verification
    }
}
```

**✅ Fix**:
```typescript
import { verifyToken } from '@/utils/auth-utils'

export async function GET(request: NextRequest) {
    // Verify authentication first
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    if (!token) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const payload = await verifyToken(token)
    if (!payload) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    try {
        const { searchParams } = new URL(request.url)
        // Now proceed with authenticated request
    } catch (error) {
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    }
}
```

---

### 15. 🔒 File Upload Vulnerabilities

**📍 Location**: `src/app/api/save-records-enhanced/route.ts:196-214`

**❌ Current Code**:
```typescript
export async function POST(request: NextRequest) {
    try {
        const body = await request.json()
        const { records, options, isFirstBatch } = body  // ⚠️ NO FILE VALIDATION!

        if (!records || !Array.isArray(records)) {
            return NextResponse.json({ error: "Invalid records data" }, { status: 400 })
        }
        // Direct processing without security checks
    }
}
```

**✅ Fix**:
```typescript
import { z } from 'zod'

const FileUploadSchema = z.object({
    records: z.array(z.object({
        fileName: z.string().regex(/^[a-zA-Z0-9._-]+\.(xlsx|csv)$/), // Only safe filenames
        sheetName: z.string().max(100),
        headers: z.array(z.string().max(50)).max(100), // Limit headers
        rowData: z.record(z.string().max(1000)), // Limit field size
        rowIndex: z.number().min(0).max(1000000),
        totalRows: z.number().min(1).max(1000000)
    })).max(10000), // Limit number of records
    options: z.object({
        duplicateHandling: z.enum(['skip', 'replace', 'merge']),
        validationLevel: z.enum(['strict', 'moderate', 'lenient']),
        batchSize: z.number().min(1).max(1000)
    }),
    isFirstBatch: z.boolean()
})

export async function POST(request: NextRequest) {
    // Check authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '')
    const payload = await verifyToken(token)
    if (!payload) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
        const body = await request.json()

        // Validate file size (check content-length)
        const contentLength = request.headers.get('content-length')
        if (contentLength && parseInt(contentLength) > 50 * 1024 * 1024) { // 50MB limit
            return NextResponse.json({ error: "File too large" }, { status: 413 })
        }

        // Validate input structure
        const validatedData = FileUploadSchema.parse(body)

        // Additional security checks
        for (const record of validatedData.records) {
            // Check for malicious content
            const recordStr = JSON.stringify(record.rowData)
            if (recordStr.includes('<script>') || recordStr.includes('javascript:')) {
                return NextResponse.json({ error: "Malicious content detected" }, { status: 400 })
            }
        }

        // Proceed with validated data
    } catch (error) {
        if (error instanceof z.ZodError) {
            return NextResponse.json({
                error: "Invalid input format",
                details: error.errors
            }, { status: 400 })
        }
        return NextResponse.json({ error: "Internal server error" }, { status: 500 })
    }
}
```

---

## 🚨 Emergency Security Patches

### Quick Fix Script
Create a file `security-patches.sh` and run it immediately:

```bash
#!/bin/bash
echo "🚨 Applying emergency security patches..."

# 1. Backup current code
git add . && git commit -m "Backup before security patches"

# 2. Update vulnerable packages
npm update next@14.2.32
npm update xlsx@0.20.2

# 3. Install security dependencies
npm install helmet express-rate-limit zod

# 4. Create environment template
cat > .env.example << EOF
# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/database

# Authentication
JWT_SECRET=your-super-secure-jwt-secret-here

# CORS
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# Security
NODE_ENV=production
EOF

echo "✅ Emergency patches applied. Please review and test!"
```

### Environment Variables Setup
```bash
# Create secure environment file
cp .env.example .env.local

# Generate secure JWT secret
node -e "console.log('JWT_SECRET=' + require('crypto').randomBytes(64).toString('hex'))" >> .env.local

# Add your MongoDB URI (replace with your actual credentials)
echo "MONGODB_URI=your-secure-mongodb-connection-string" >> .env.local
```

---

## 🔧 Code Quality Issues to Fix

### Missing Error Boundaries
**📍 Location**: All React components lack error boundaries

**✅ Add Error Boundary**:
```typescript
// src/components/ErrorBoundary.tsx
import React from 'react'

interface ErrorBoundaryState {
    hasError: boolean
    error?: Error
}

class ErrorBoundary extends React.Component<
    React.PropsWithChildren<{}>,
    ErrorBoundaryState
> {
    constructor(props: React.PropsWithChildren<{}>) {
        super(props)
        this.state = { hasError: false }
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        return { hasError: true, error }
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        console.error('Error caught by boundary:', error, errorInfo)
        // Log to monitoring service
    }

    render() {
        if (this.state.hasError) {
            return (
                <div className="error-boundary">
                    <h2>Something went wrong.</h2>
                    <details style={{ whiteSpace: 'pre-wrap' }}>
                        {this.state.error && this.state.error.toString()}
                    </details>
                </div>
            )
        }

        return this.props.children
    }
}

export default ErrorBoundary
```

### Missing TypeScript Strict Mode
**📍 Location**: `tsconfig.json`

**✅ Enable Strict Mode**:
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true
  }
}
```

---

## 📈 Monitoring and Alerting Setup

### Add Security Monitoring
```typescript
// src/lib/security-monitor.ts
export const logSecurityEvent = (event: {
    type: 'auth_failure' | 'injection_attempt' | 'rate_limit_exceeded'
    ip: string
    userAgent: string
    details: any
}) => {
    console.error('SECURITY EVENT:', {
        timestamp: new Date().toISOString(),
        ...event
    })

    // Send to monitoring service
    // Example: Sentry, DataDog, etc.
}

// Usage in API routes
if (suspiciousActivity) {
    logSecurityEvent({
        type: 'injection_attempt',
        ip: request.ip,
        userAgent: request.headers.get('user-agent') || '',
        details: { payload: request.body }
    })
}
```

---

## ⚡ Performance Critical Issues

### Database Connection Leaks
**📍 Location**: `src/lib/mongodb.ts:9-24`

**❌ Current Code**:
```typescript
export async function connectToDatabase(): Promise<Db> {
  if (db) {
    return db  // ⚠️ NO CONNECTION HEALTH CHECK!
  }
  // Creates new connection every time if db is null
}
```

**✅ Fix**:
```typescript
export async function connectToDatabase(): Promise<Db> {
  if (db) {
    try {
      // Health check existing connection
      await db.admin().ping()
      return db
    } catch (error) {
      console.warn('Database connection unhealthy, reconnecting...')
      db = null
      client = null
    }
  }

  try {
    client = new MongoClient(MONGODB_URI, {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    })
    await client.connect()
    db = client.db(DB_NAME)

    // Add connection event listeners
    client.on('error', (error) => {
      console.error('MongoDB connection error:', error)
    })

    return db
  } catch (error) {
    console.error("Failed to connect to MongoDB:", error)
    throw error
  }
}
```

**Remember**: These are critical security issues that expose your application to serious attacks. Prioritize fixing them immediately!
