/** @type {import('next').NextConfig} */
const nextConfig = {
    productionBrowserSourceMaps: true, // Enable source maps in production
    webpack: (config, {dev, isServer}) => {
        // Always generate source maps
        if (!dev) {
            config.devtool = 'source-map'
        }
        return config
    },
    images: {
        remotePatterns: [
            {
                protocol: 'http',
                hostname: 'localhost',
                port: '3001', // specify the port where your backend server is running
                pathname: '/uploads/**', // path where the images are located
            },
        ],
    },

    reactStrictMode: false,

    eslint: {
        ignoreDuringBuilds: true,
    },
};

export default nextConfig;
