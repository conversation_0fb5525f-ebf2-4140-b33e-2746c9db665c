"use client"

import { useEffect, useRef, useCallback } from "react"

interface UseInactivityTimerProps {
    timeout: number // timeout in milliseconds
    onTimeout: () => void
    events?: string[]
}

export function useInactivityTimer({
                                       timeout,
                                       onTimeout,
                                       events = ["mousedown", "mousemove", "keypress", "scroll", "touchstart", "click"],
                                   }: UseInactivityTimerProps) {
    const timeoutRef = useRef<NodeJS.Timeout | null>(null)

    const resetTimer = useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
        }

        timeoutRef.current = setTimeout(() => {
            onTimeout()
        }, timeout)
    }, [timeout, onTimeout])

    const clearTimer = useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
            timeoutRef.current = null
        }
    }, [])

    useEffect(() => {
        // Set initial timer
        resetTimer()

        // Add event listeners
        events.forEach((event) => {
            document.addEventListener(event, resetTimer, true)
        })

        // Cleanup function
        return () => {
            clearTimer()
            events.forEach((event) => {
                document.removeEventListener(event, resetTimer, true)
            })
        }
    }, [resetTimer, clearTimer, events])

    return { resetTimer, clearTimer }
}
