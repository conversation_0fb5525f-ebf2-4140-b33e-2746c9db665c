"use client"

import { useCallback, useRef, useState, useEffect } from "react"
import { useActivityLogger } from "@/components/activity-logger"

export function useActivityTracking() {
    const lastLogRef = useRef<string>("")
    const [activityLogger, setActivityLogger] = useState<ReturnType<typeof useActivityLogger> | null>(null)

    useEffect(() => {
        try {
            setActivityLogger(useActivityLogger())
        } catch (error) {
            console.warn("useActivityTracking: ActivityLogger context not available")
            setActivityLogger(null)
        }
    }, [])

    // Return no-op functions if context is not available
    if (!activityLogger) {
        return {
            trackActivity: () => {},
            trackAuth: () => {},
            trackTransaction: () => {},
            trackCard: () => {},
            trackProfile: () => {},
            trackSecurity: () => {},
        }
    }

    const { addLog } = activityLogger

    const trackActivity = useCallback(
        (
            action: string,
            description: string,
            options?: {
                category?: "auth" | "transaction" | "card" | "profile" | "security" | "system"
                severity?: "info" | "warning" | "success" | "error"
                metadata?: Record<string, any>
            },
        ) => {
            // Prevent duplicate logs
            const logKey = `${action}-${description}-${Date.now()}`
            if (lastLogRef.current === logKey) {
                return
            }
            lastLogRef.current = logKey

            try {
                addLog({
                    action,
                    description,
                    category: options?.category || "system",
                    severity: options?.severity || "info",
                    metadata: options?.metadata,
                })
            } catch (error) {
                console.error("Error adding activity log:", error)
            }
        },
        [addLog],
    )

    // Predefined tracking functions for common actions
    const trackAuth = useCallback(
        (action: "LOGIN" | "LOGOUT" | "FAILED_LOGIN", metadata?: Record<string, any>) => {
            const descriptions = {
                LOGIN: "User successfully logged in",
                LOGOUT: "User logged out",
                FAILED_LOGIN: "Failed login attempt",
            }

            trackActivity(action, descriptions[action], {
                category: "auth",
                severity: action === "FAILED_LOGIN" ? "warning" : "success",
                metadata,
            })
        },
        [trackActivity],
    )

    const trackTransaction = useCallback(
        (action: "VIEW_BALANCE" | "TRANSACTION_CREATED" | "TRANSACTION_FAILED", metadata?: Record<string, any>) => {
            const descriptions = {
                VIEW_BALANCE: "Account balance viewed",
                TRANSACTION_CREATED: "New transaction created",
                TRANSACTION_FAILED: "Transaction failed",
            }

            trackActivity(action, descriptions[action], {
                category: "transaction",
                severity: action === "TRANSACTION_FAILED" ? "error" : "info",
                metadata,
            })
        },
        [trackActivity],
    )

    const trackCard = useCallback(
        (action: "CARD_CREATED" | "CARD_BLOCKED" | "CARD_ACTIVATED" | "CARD_VIEWED", metadata?: Record<string, any>) => {
            const descriptions = {
                CARD_CREATED: "New card created",
                CARD_BLOCKED: "Card blocked",
                CARD_ACTIVATED: "Card activated",
                CARD_VIEWED: "Card details viewed",
            }

            trackActivity(action, descriptions[action], {
                category: "card",
                severity: action === "CARD_BLOCKED" ? "warning" : "success",
                metadata,
            })
        },
        [trackActivity],
    )

    const trackProfile = useCallback(
        (action: "PROFILE_VIEWED" | "PROFILE_UPDATED" | "PASSWORD_CHANGED", metadata?: Record<string, any>) => {
            const descriptions = {
                PROFILE_VIEWED: "Profile information viewed",
                PROFILE_UPDATED: "Profile information updated",
                PASSWORD_CHANGED: "Password changed",
            }

            trackActivity(action, descriptions[action], {
                category: "profile",
                severity: "info",
                metadata,
            })
        },
        [trackActivity],
    )

    const trackSecurity = useCallback(
        (action: "SUSPICIOUS_ACTIVITY" | "SECURITY_ALERT" | "TWO_FA_ENABLED", metadata?: Record<string, any>) => {
            const descriptions = {
                SUSPICIOUS_ACTIVITY: "Suspicious activity detected",
                SECURITY_ALERT: "Security alert triggered",
                TWO_FA_ENABLED: "Two-factor authentication enabled",
            }

            trackActivity(action, descriptions[action], {
                category: "security",
                severity: action === "TWO_FA_ENABLED" ? "success" : "warning",
                metadata,
            })
        },
        [trackActivity],
    )

    return {
        trackActivity,
        trackAuth,
        trackTransaction,
        trackCard,
        trackProfile,
        trackSecurity,
    }
}
