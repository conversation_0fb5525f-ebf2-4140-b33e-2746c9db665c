import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export async function middleware(request: NextRequest) {
    // Enhanced JWT decoding function
    const decodeJWT = (token: string) => {
        try {
            const parts = token.split(".")
            if (parts.length !== 3) {
                return null
            }

            const header = JSON.parse(atob(parts[0]))
            const payload = JSON.parse(atob(parts[1]))

            return {
                header,
                payload,
                raw: token,
                isExpired: payload.exp ? Date.now() >= payload.exp * 1000 : false,
                expiresAt: payload.exp ? new Date(payload.exp * 1000).toISOString() : null,
                issuedAt: payload.iat ? new Date(payload.iat * 1000).toISOString() : null,
            }
        } catch (error) {
            console.error("JWT decode error:", error)
            return null
        }
    }

    // Function to determine if we should log this activity - ONLY PAGE VISITS
    const shouldLogActivity = (pathname: string, method: string, headers: Headers, userAgent: string) => {
        // ONLY log GET requests that are actual page visits
        if (method !== "GET") {
            return false
        }

        // Exclude ALL static assets and files with extensions
        const hasFileExtension = /\.[a-zA-Z0-9]+$/.test(pathname)
        if (hasFileExtension) {
            return false
        }

        // Exclude system/background paths
        const excludedPaths = [
            "/.well-known/",
            "/favicon.ico",
            "/robots.txt",
            "/sitemap.xml",
            "/_next/",
            "/api/", // Exclude ALL API requests
            "/sw.js",
            "/manifest.json",
            "/apple-touch-icon",
            "/browserconfig.xml",
            "/images/",
            "/assets/",
            "/static/",
            "/public/",
        ]

        if (excludedPaths.some((path) => pathname.includes(path))) {
            return false
        }

        // Exclude browser extension requests
        if (
            pathname.includes("chrome-extension://") ||
            pathname.includes("moz-extension://") ||
            pathname.includes("safari-extension://")
        ) {
            return false
        }

        // Exclude bot/crawler requests
        if (/bot|crawler|spider|scraper|facebookexternalhit|twitterbot|linkedinbot/i.test(userAgent)) {
            return false
        }

        // Only allow requests that are actual page navigations
        const secFetchDest = headers.get("sec-fetch-dest") || ""
        const secFetchMode = headers.get("sec-fetch-mode") || ""
        const accept = headers.get("accept") || ""

        // Must be a document request (page navigation)
        const isPageNavigation = secFetchDest === "document" || secFetchMode === "navigate" || accept.includes("text/html")

        // Exclude empty requests or non-page requests
        if (!isPageNavigation || accept === "" || secFetchDest === "empty") {
            return false
        }

        return true
    }

    // Get user information
    const getUserInfo = () => {
        const authHeader = request.headers.get("authorization")
        const bearerToken = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : null

        const cookieToken =
            request.cookies.get("token")?.value ||
            request.cookies.get("jwt")?.value ||
            request.cookies.get("access_token")?.value ||
            request.cookies.get("auth-token")?.value

        const jwtToken = bearerToken || cookieToken
        const decodedJWT = jwtToken ? decodeJWT(jwtToken) : null
        const jwtPayload = decodedJWT?.payload

        const userId = jwtPayload?.sub || jwtPayload?.userId || jwtPayload?.id || jwtPayload?.user_id

        const userEmail = jwtPayload?.email || jwtPayload?.user_email

        const userName = jwtPayload?.name || jwtPayload?.username || jwtPayload?.user_name || jwtPayload?.displayName

        return {
            hasJWT: !!jwtToken,
            hasBearerToken: !!bearerToken,
            hasSessionToken: false,
            jwt: decodedJWT
                ? {
                    header: decodedJWT.header,
                    payload: decodedJWT.payload,
                    raw: decodedJWT.raw,
                    isExpired: decodedJWT.isExpired,
                    expiresAt: decodedJWT.expiresAt,
                    issuedAt: decodedJWT.issuedAt,
                    algorithm: decodedJWT.header?.alg,
                    tokenType: decodedJWT.header?.typ,
                }
                : null,
            userId,
            userEmail,
            userName,
            userProperties: jwtPayload
                ? {
                    customClaims: Object.keys(jwtPayload)
                        .filter((key) => !["sub", "iat", "exp", "iss", "aud", "email", "name", "role"].includes(key))
                        .reduce((obj, key) => ({ ...obj, [key]: jwtPayload[key] }), {}),
                }
                : null,
            tokenSource: bearerToken ? "authorization_header" : cookieToken ? "cookie" : null,
        }
    }

    // Check if we should log this activity
    const userAgent = request.headers.get("user-agent") || ""
    if (!shouldLogActivity(request.nextUrl.pathname, request.method, request.headers, userAgent)) {
        return NextResponse.next()
    }

    // Get user info
    const userInfo = getUserInfo()

    // Only log activities for authenticated users
    if (!userInfo.userId || !userInfo.hasJWT) {
        return NextResponse.next()
    }

    const activityData = {
        // Request information
        url: request.url,
        pathname: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString(),
        searchParams: Object.fromEntries(request.nextUrl.searchParams),

        // Client information
        userAgent: userAgent,
        referer: request.headers.get("referer") || "",
        ip:
            request.ip ||
            request.headers.get("x-forwarded-for") ||
            request.headers.get("x-real-ip") ||
            request.headers.get("cf-connecting-ip") ||
            request.headers.get("x-vercel-forwarded-for") ||
            "unknown",

        // Browser/Client details
        headers: {
            accept: request.headers.get("accept") || "",
            "accept-language": request.headers.get("accept-language") || "",
            "sec-fetch-dest": request.headers.get("sec-fetch-dest") || "",
            "sec-fetch-mode": request.headers.get("sec-fetch-mode") || "",
            "sec-fetch-site": request.headers.get("sec-fetch-site") || "",
            "sec-ch-ua-platform": request.headers.get("sec-ch-ua-platform") || "",
        },

        // User information
        user: {
            ...userInfo,
            isAuthenticated: !!(userInfo.userId && userInfo.hasJWT),
            authenticationMethod: userInfo.hasJWT ? "jwt" : "none",
            sessionFingerprint: null,
        },

        // Geographic information
        geo: {
            country: request.headers.get("cf-ipcountry") || request.headers.get("x-vercel-ip-country") || null,
            region: request.headers.get("cf-region") || request.headers.get("x-vercel-ip-country-region") || null,
            city: request.headers.get("cf-ipcity") || request.headers.get("x-vercel-ip-city") || null,
            timezone: request.headers.get("cf-timezone") || request.headers.get("x-vercel-ip-timezone") || null,
        },

        // Device information
        device: {
            isMobile: /Mobile|Android|iPhone|iPad/.test(userAgent),
            isTablet: /iPad|Android(?!.*Mobile)/.test(userAgent),
            isDesktop: !/Mobile|Android|iPhone|iPad/.test(userAgent),
            isBot: false, // We already filtered out bots
            platform: request.headers.get("sec-ch-ua-platform")?.replace(/"/g, "") || null,
            isMobileUA: request.headers.get("sec-ch-ua-mobile") === "?1",
        },

        // Request context
        context: {
            isApiRequest: false, // We only log page visits now
            isStaticAsset: false, // We filtered these out
            hasQueryParams: request.nextUrl.searchParams.size > 0,
            isPageNavigation: true, // All logged requests are page navigations
            isPageVisit: true, // New flag to clearly identify page visits
        },
    }

    // Send activity data to API endpoint (non-blocking)
    try {
        fetch(new URL("/api/activity", request.url), {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(activityData),
        }).catch((error) => {
            console.error("Failed to log activity:", error)
        })
    } catch (error) {
        console.error("Middleware activity logging error:", error)
    }

    return NextResponse.next()
}

// Configure which routes the middleware should run on
export const config = {
    matcher: [
        /*
         * Match all request paths except static files
         */
        "/((?!_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)",
    ],
}
