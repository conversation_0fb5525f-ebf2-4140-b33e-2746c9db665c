// Define regions
export const regions = ["Europe", "North America/Africa", "South/Central America/Asia", "Australia/Oceania"]

// European countries
export const europeanCountries = [
    "ISLE OF MAN",
    "ITALY",
    "JERSEY",
    "LATVIA",
    "LIECHTENSTEIN",
    "LITH<PERSON>AN<PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>BOURG",
    "MALTA",
    "M<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>NDS",
    "NORTHERN IRELAND",
    "NORTH MACEDONIA",
    "NORWAY",
    "POLAND",
    "PORTUGAL",
    "ROMANIA",
    "RUSSIA",
    "SAN MARINO",
    "SERBIA",
    "SLOVAKIA",
    "SLOVENIA",
    "<PERSON>AIN",
    "<PERSON><PERSON><PERSON><PERSON> AND JAN MAYEN",
    "<PERSON><PERSON><PERSON><PERSON>",
    "SWITZERLAND",
    "UKRAINE",
    "UNITED KINGDOM",
    "VATICAN CITY",
]

// North American and African countries
export const northAmericaAfricaCountries = [
    "ALGERIA",
    "ANGOLA",
    "BENIN",
    "B<PERSON><PERSON>DA",
    "BOTSWA<PERSON>",
    "BURKINA FASO",
    "<PERSON><PERSON><PERSON><PERSON>",
    "CABO VERDE",
    "CAMEROON",
    "CANADA",
    "CENTRAL AFRICAN REPUBLIC",
    "CHAD",
    "COMOROS",
    "DEMOCRATIC REPUBLIC OF THE CONGO",
    "REPUBLIC OF THE CONGO",
    "CÔTE D'IVOIRE",
    "DJIBOUTI",
    "EGYPT",
    "EQUATORIAL GUINEA",
    "ERITREA",
    "ESWATINI",
    "ETHIOPIA",
    "GABON",
    "GAMBIA",
    "GHANA",
    "GREENLAND",
    "GUINEA",
    "GUINEA-BISSAU",
    "KENYA",
    "LESOTHO",
    "LIBERIA",
    "LIBYA",
    "MADAGASCAR",
    "MALAWI",
    "MALI",
    "MAURITANIA",
    "MAURITIUS",
    "MAYOTTE",
    "MEXICO",
    "MOROCCO",
    "MOZAMBIQUE",
    "NAMIBIA",
    "NIGER",
    "NIGERIA",
    "RÉUNION",
    "RWANDA",
    "SAINT HELENA, ASCENSION AND TRISTAN DA CUNHA",
    "SAINT PIERRE AND MIQUELON",
    "SAO TOME AND PRINCIPE",
    "SENEGAL",
    "SEYCHELLES",
    "SIERRA LEONE",
    "SOMALIA",
    "SOUTH AFRICA",
    "SOUTH SUDAN",
    "SUDAN",
    "TANZANIA",
    "TOGO",
    "TUNISIA",
    "UGANDA",
    "UNITED STATES MINOR OUTLYING ISLANDS",
    "UNITED STATES OF AMERICA",
    "WESTERN SAHARA",
    "ZAMBIA",
    "ZIMBABWE",
]

// South/Central American and Asian countries
export const southCentralAmericaAsiaCountries = [
    "AFGHANISTAN",
    "ANGUILLA",
    "ANTIGUA AND BARBUDA",
    "ARGENTINA",
    "ARMENIA",
    "ARUBA",
    "AZERBAIJAN",
    "BAHAMAS",
    "BAHRAIN",
    "BANGLADESH",
    "BARBADOS",
    "BELIZE",
    "BHUTAN",
    "BOLIVIA",
    "BONAIRE, SINT EUSTATIUS AND SABA",
    "BRAZIL",
    "BRITISH INDIAN OCEAN TERRITORY",
    "BRITISH VIRGIN ISLANDS",
    "BRUNEI",
    "CAMBODIA",
    "CAYMAN ISLANDS",
    "CHILE",
    "CHINA",
    "COLOMBIA",
    "COSTA RICA",
    "CUBA",
    "CURAÇAO",
    "DOMINICA",
    "DOMINICAN REPUBLIC",
    "ECUADOR",
    "EL SALVADOR",
    "FALKLAND ISLANDS",
    "FRENCH GUIANA",
    "GEORGIA",
    "GRENADA",
    "GUADELOUPE",
    "GUATEMALA",
    "GUYANA",
    "HAITI",
    "HONDURAS",
    "HONG KONG",
    "INDIA",
    "INDONESIA",
    "IRAN",
    "IRAQ",
    "ISRAEL",
    "JAMAICA",
    "JAPAN",
    "JORDAN",
    "KAZAKHSTAN",
    "NORTH KOREA",
    "SOUTH KOREA",
    "KUWAIT",
    "KYRGYZSTAN",
    "LAOS",
    "LEBANON",
    "MACAO",
    "MALAYSIA",
    "MALDIVES",
    "MARTINIQUE",
    "MONGOLIA",
    "MONTSERRAT",
    "MYANMAR",
    "NEPAL",
    "NICARAGUA",
    "OMAN",
    "PAKISTAN",
    "PALESTINE",
    "PANAMA",
    "PARAGUAY",
    "PERU",
    "PHILIPPINES",
    "PUERTO RICO",
    "QATAR",
    "SAINT BARTHÉLEMY",
    "SAINT KITTS AND NEVIS",
    "SAINT LUCIA",
    "SAINT MARTIN (FRENCH PART)",
    "SAINT VINCENT AND THE GRENADINES",
    "SAUDI ARABIA",
    "SINGAPORE",
    "SINT MAARTEN (DUTCH PART)",
    "SRI LANKA",
    "SURINAME",
    "SYRIA",
    "TAIWAN",
    "TAJIKISTAN",
    "THAILAND",
    "TIMOR OESTE",
    "TRINIDAD AND TOBAGO",
    "TURKEY",
    "TURKMENISTAN",
    "TURKS AND CAICOS ISLANDS",
    "UNITED ARAB EMIRATES",
    "URUGUAY",
    "UZBEKISTAN",
    "VENEZUELA",
    "VIETNAM",
    "BRITISH VIRGIN ISLANDS",
    "UNITED STATES VIRGIN ISLANDS",
    "YEMEN",
]

// Australia/Oceania countries
export const australiaOceaniaCountries = [
    "AMERICAN SAMOA",
    "AUSTRALIA",
    "BOUVET ISLAND",
    "CHRISTMAS ISLAND",
    "COCOS (KEELING) ISLANDS",
    "COOK ISLANDS",
    "FIJI",
    "FRENCH POLYNESIA",
    "FRENCH SOUTHERN TERRITORIES",
    "GUAM",
    "HEARD ISLAND AND McDONALD ISLANDS",
    "KIRIBATI",
    "MARSHALL ISLANDS",
    "FEDERATED STATES OF MICRONESIA",
    "NAURU",
    "NEW CALEDONIA",
    "NEW ZEALAND",
    "NIUE ISLAND",
    "NORFOLK ISLAND",
    "NORTHERN MARIANA ISLANDS",
    "PALAU",
    "PAPUA NEW GUINEA",
    "PITCAIRN",
    "SAMOA",
    "SOLOMON ISLANDS",
    "SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS",
    "TOKELAU",
    "TONGA",
    "TUVALU",
    "VANUATU",
    "WALLIS AND FUTUNA",
]

// Create a mapping function to assign countries to regions
export function getCountryRegionMap(countries: { name: string; code: string }[]) {
    const countryRegionMap: Record<string, string> = {}

    countries.forEach((country) => {
        // European countries
        if (
            country.name.match(
                /ALBANIA|ANDORRA|AUSTRIA|BELARUS|BELGIUM|BOSNIA AND HERZEGOVINA|BULGARIA|CROATIA|CYPRUS|CZECH REPUBLIC|DENMARK|ESTONIA|FINLAND|FRANCE|GERMANY|GREECE|HUNGARY|ICELAND|IRELAND|ISLE OF MAN|ITALY|JERSEY|LATVIA|LIECHTENSTEIN|LITHUANIA|LUXEMBOURG|MALTA|MOLDOVA|MONACO|MONTENEGRO|NETHERLANDS|NORTHERN IRELAND|NORTH MACEDONIA|NORWAY|POLAND|PORTUGAL|ROMANIA|RUSSIA|SAN MARINO|SERBIA|SLOVAKIA|SLOVENIA|SPAIN|SVALBARD AND JAN MAYEN|SWEDEN|SWITZERLAND|UKRAINE|UNITED KINGDOM|VATICAN CITY/i,
            )
        ) {
            countryRegionMap[country.name] = "Europe"
        } else if (
            country.name.match(
                /ALGERIA|ANGOLA|BENIN|BERMUDA|BOTSWANA|BURKINA FASO|BURUNDI|CABO VERDE|CAMEROON|CANADA|CENTRAL AFRICAN REPUBLIC|CHAD|COMOROS|DEMOCRATIC REPUBLIC OF THE CONGO|REPUBLIC OF THE CONGO|CÔTE D'IVOIRE|DJIBOUTI|EGYPT|EQUATORIAL GUINEA|ERITREA|ESWATINI|ETHIOPIA|GABON|GAMBIA|GHANA|GREENLAND|GUINEA|GUINEA-BISSAU|KENYA|LESOTHO|LIBERIA|LIBYA|MADAGASCAR|MALAWI|MALI|MAURITANIA|MAURITIUS|MAYOTTE|MEXICO|MOROCCO|MOZAMBIQUE|NAMIBIA|NIGER|NIGERIA|RÉUNION|RWANDA|SAINT HELENA, ASCENSION AND TRISTAN DA CUNHA|SAINT PIERRE AND MIQUELON|SAO TOME AND PRINCIPE|SENEGAL|SEYCHELLES|SIERRA LEONE|SOMALIA|SOUTH AFRICA|SOUTH SUDAN|SUDAN|TANZANIA|TOGO|TUNISIA|UGANDA|UNITED STATES MINOR OUTLYING ISLANDS|UNITED STATES OF AMERICA|WESTERN SAHARA|ZAMBIA|ZIMBABWE/i,
            )
        ) {
            countryRegionMap[country.name] = "North America/Africa"
        } else if (
            country.name.match(
                /AFGHANISTAN|ANGUILLA|ANTIGUA AND BARBUDA|ARGENTINA|ARMENIA|ARUBA|AZERBAIJAN|BAHAMAS|BAHRAIN|BANGLADESH|BARBADOS|BELIZE|BHUTAN|BOLIVIA|BONAIRE, SINT EUSTATIUS AND SABA|BRAZIL|BRITISH INDIAN OCEAN TERRITORY|BRITISH VIRGIN ISLANDS|BRUNEI|CAMBODIA|CAYMAN ISLANDS|CHILE|CHINA|COLOMBIA|COSTA RICA|CUBA|CURAÇAO|DOMINICA|DOMINICAN REPUBLIC|ECUADOR|EL SALVADOR|FALKLAND ISLANDS|FRENCH GUIANA|GEORGIA|GRENADA|GUADELOUPE|GUATEMALA|GUYANA|HAITI|HONDURAS|HONG KONG|INDIA|INDONESIA|IRAN|IRAQ|ISRAEL|JAMAICA|JAPAN|JORDAN|KAZAKHSTAN|NORTH KOREA|SOUTH KOREA|KUWAIT|KYRGYZSTAN|LAOS|LEBANON|MACAO|MALAYSIA|MALDIVES|MARTINIQUE|MONGOLIA|MONTSERRAT|MYANMAR|NEPAL|NICARAGUA|OMAN|PAKISTAN|PALESTINE|PANAMA|PARAGUAY|PERU|PHILIPPINES|PUERTO RICO|QATAR|SAINT BARTHÉLEMY|SAINT KITTS AND NEVIS|SAINT LUCIA|SAINT MARTIN $$FRENCH PART$$|SAINT VINCENT AND THE GRENADINES|SAUDI ARABIA|SINGAPORE|SINT MAARTEN $$DUTCH PART$$|SRI LANKA|SURINAME|SYRIA|TAIWAN|TAJIKISTAN|THAILAND|TIMOR OESTE|TRINIDAD AND TOBAGO|TURKEY|TURKMENISTAN|TURKS AND CAICOS ISLANDS|UNITED ARAB EMIRATES|URUGUAY|UZBEKISTAN|VENEZUELA|VIETNAM|BRITISH VIRGIN ISLANDS|UNITED STATES VIRGIN ISLANDS|YEMEN/i,
            )
        ) {
            countryRegionMap[country.name] = "South/Central America/Asia"
        } else if (
            country.name.match(
                /AMERICAN SAMOA|AUSTRALIA|BOUVET ISLAND|CHRISTMAS ISLAND|COCOS $$KEELING$$ ISLANDS|COOK ISLANDS|FIJI|FRENCH POLYNESIA|FRENCH SOUTHERN TERRITORIES|GUAM|HEARD ISLAND AND McDONALD ISLANDS|KIRIBATI|MARSHALL ISLANDS|FEDERATED STATES OF MICRONESIA|NAURU|NEW CALEDONIA|NEW ZEALAND|NIUE ISLAND|NORFOLK ISLAND|NORTHERN MARIANA ISLANDS|PALAU|PAPUA NEW GUINEA|PITCAIRN|SAMOA|SOLOMON ISLANDS|SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS|TOKELAU|TONGA|TUVALU|VANUATU|WALLIS AND FUTUNA/i,
            )
        ) {
            countryRegionMap[country.name] = "Australia/Oceania"
        } else {
            countryRegionMap[country.name] = "Australia/Oceania" // Default to Australia/Oceania for any unmatched countries
        }
    })

    return countryRegionMap
}

// Function to ensure all countries are available in the countries array
export function ensureCountriesExist(
    countriesList: string[],
    existingCountries: { name: string; code: string }[],
): { name: string; code: string }[] {
    const updatedCountries = [...existingCountries]

    countriesList.forEach((countryName) => {
        if (!existingCountries.some((c) => c.name.toUpperCase() === countryName)) {
            updatedCountries.push({ name: countryName, code: countryName.substring(0, 2) })
        }
    })

    return updatedCountries
}
