/*
	Follows ISO 4217, https://www.iso.org/iso-4217-currency-codes.html
	See https://www.currency-iso.org/dam/downloads/lists/list_one.xml
	Data last updated 2024-06-25
*/



export const currencies =  [
    {
        "code": "AED",
        "number": "784",
        "digits": 2,
        "currency": "UAE Dirham",
        "countries": [
            "United Arab Emirates (The)"
        ]
    },
    {
        "code": "AFN",
        "number": "971",
        "digits": 2,
        "currency": "Afghani",
        "countries": [
            "Afghanistan"
        ]
    },
    {
        "code": "ALL",
        "number": "008",
        "digits": 2,
        "currency": "Lek",
        "countries": [
            "Albania"
        ]
    },
    {
        "code": "AMD",
        "number": "051",
        "digits": 2,
        "currency": "Armenian Dram",
        "countries": [
            "Armenia"
        ]
    },
    {
        "code": "ANG",
        "number": "532",
        "digits": 2,
        "currency": "Netherlands Antillean Guilder",
        "countries": [
            "Curaçao",
            "<PERSON><PERSON> (Dutch Part)"
        ]
    },
    {
        "code": "AOA",
        "number": "973",
        "digits": 2,
        "currency": "Kwanza",
        "countries": [
            "Angola"
        ]
    },
    {
        "code": "ARS",
        "number": "032",
        "digits": 2,
        "currency": "Argentine Peso",
        "countries": [
            "Argentina"
        ]
    },
    {
        "code": "AUD",
        "number": "036",
        "digits": 2,
        "currency": "Australian Dollar",
        "countries": [
            "Australia",
            "Christmas Island",
            "Cocos (Keeling) Islands (The)",
            "Heard Island and Mcdonald Islands",
            "Kiribati",
            "Nauru",
            "Norfolk Island",
            "Tuvalu"
        ]
    },
    {
        "code": "AWG",
        "number": "533",
        "digits": 2,
        "currency": "Aruban Florin",
        "countries": [
            "Aruba"
        ]
    },
    {
        "code": "AZN",
        "number": "944",
        "digits": 2,
        "currency": "Azerbaijan Manat",
        "countries": [
            "Azerbaijan"
        ]
    },
    {
        "code": "BAM",
        "number": "977",
        "digits": 2,
        "currency": "Convertible Mark",
        "countries": [
            "Bosnia and Herzegovina"
        ]
    },
    {
        "code": "BBD",
        "number": "052",
        "digits": 2,
        "currency": "Barbados Dollar",
        "countries": [
            "Barbados"
        ]
    },
    {
        "code": "BDT",
        "number": "050",
        "digits": 2,
        "currency": "Taka",
        "countries": [
            "Bangladesh"
        ]
    },
    {
        "code": "BGN",
        "number": "975",
        "digits": 2,
        "currency": "Bulgarian Lev",
        "countries": [
            "Bulgaria"
        ]
    },
    {
        "code": "BHD",
        "number": "048",
        "digits": 3,
        "currency": "Bahraini Dinar",
        "countries": [
            "Bahrain"
        ]
    },
    {
        "code": "BIF",
        "number": "108",
        "digits": 0,
        "currency": "Burundi Franc",
        "countries": [
            "Burundi"
        ]
    },
    {
        "code": "BMD",
        "number": "060",
        "digits": 2,
        "currency": "Bermudian Dollar",
        "countries": [
            "Bermuda"
        ]
    },
    {
        "code": "BND",
        "number": "096",
        "digits": 2,
        "currency": "Brunei Dollar",
        "countries": [
            "Brunei Darussalam"
        ]
    },
    {
        "code": "BOB",
        "number": "068",
        "digits": 2,
        "currency": "Boliviano",
        "countries": [
            "Bolivia (Plurinational State Of)"
        ]
    },
    {
        "code": "BOV",
        "number": "984",
        "digits": 2,
        "currency": "Mvdol",
        "countries": [
            "Bolivia (Plurinational State Of)"
        ]
    },
    {
        "code": "BRL",
        "number": "986",
        "digits": 2,
        "currency": "Brazilian Real",
        "countries": [
            "Brazil"
        ]
    },
    {
        "code": "BSD",
        "number": "044",
        "digits": 2,
        "currency": "Bahamian Dollar",
        "countries": [
            "Bahamas (The)"
        ]
    },
    {
        "code": "BTN",
        "number": "064",
        "digits": 2,
        "currency": "Ngultrum",
        "countries": [
            "Bhutan"
        ]
    },
    {
        "code": "BWP",
        "number": "072",
        "digits": 2,
        "currency": "Pula",
        "countries": [
            "Botswana"
        ]
    },
    {
        "code": "BYN",
        "number": "933",
        "digits": 2,
        "currency": "Belarusian Ruble",
        "countries": [
            "Belarus"
        ]
    },
    {
        "code": "BZD",
        "number": "084",
        "digits": 2,
        "currency": "Belize Dollar",
        "countries": [
            "Belize"
        ]
    },
    {
        "code": "CAD",
        "number": "124",
        "digits": 2,
        "currency": "Canadian Dollar",
        "countries": [
            "Canada"
        ]
    },
    {
        "code": "CDF",
        "number": "976",
        "digits": 2,
        "currency": "Congolese Franc",
        "countries": [
            "Congo (The Democratic Republic of The)"
        ]
    },
    {
        "code": "CHE",
        "number": "947",
        "digits": 2,
        "currency": "WIR Euro",
        "countries": [
            "Switzerland"
        ]
    },
    {
        "code": "CHF",
        "number": "756",
        "digits": 2,
        "currency": "Swiss Franc",
        "countries": [
            "Liechtenstein",
            "Switzerland"
        ]
    },
    {
        "code": "CHW",
        "number": "948",
        "digits": 2,
        "currency": "WIR Franc",
        "countries": [
            "Switzerland"
        ]
    },
    {
        "code": "CLF",
        "number": "990",
        "digits": 4,
        "currency": "Unidad de Fomento",
        "countries": [
            "Chile"
        ]
    },
    {
        "code": "CLP",
        "number": "152",
        "digits": 0,
        "currency": "Chilean Peso",
        "countries": [
            "Chile"
        ]
    },
    {
        "code": "CNY",
        "number": "156",
        "digits": 2,
        "currency": "Yuan Renminbi",
        "countries": [
            "China"
        ]
    },
    {
        "code": "COP",
        "number": "170",
        "digits": 2,
        "currency": "Colombian Peso",
        "countries": [
            "Colombia"
        ]
    },
    {
        "code": "COU",
        "number": "970",
        "digits": 2,
        "currency": "Unidad de Valor Real",
        "countries": [
            "Colombia"
        ]
    },
    {
        "code": "CRC",
        "number": "188",
        "digits": 2,
        "currency": "Costa Rican Colon",
        "countries": [
            "Costa Rica"
        ]
    },
    {
        "code": "CUC",
        "number": "931",
        "digits": 2,
        "currency": "Peso Convertible",
        "countries": [
            "Cuba"
        ]
    },
    {
        "code": "CUP",
        "number": "192",
        "digits": 2,
        "currency": "Cuban Peso",
        "countries": [
            "Cuba"
        ]
    },
    {
        "code": "CVE",
        "number": "132",
        "digits": 2,
        "currency": "Cabo Verde Escudo",
        "countries": [
            "Cabo Verde"
        ]
    },
    {
        "code": "CZK",
        "number": "203",
        "digits": 2,
        "currency": "Czech Koruna",
        "countries": [
            "Czechia"
        ]
    },
    {
        "code": "DJF",
        "number": "262",
        "digits": 0,
        "currency": "Djibouti Franc",
        "countries": [
            "Djibouti"
        ]
    },
    {
        "code": "DKK",
        "number": "208",
        "digits": 2,
        "currency": "Danish Krone",
        "countries": [
            "Denmark",
            "Faroe Islands (The)",
            "Greenland"
        ]
    },
    {
        "code": "DOP",
        "number": "214",
        "digits": 2,
        "currency": "Dominican Peso",
        "countries": [
            "Dominican Republic (The)"
        ]
    },
    {
        "code": "DZD",
        "number": "012",
        "digits": 2,
        "currency": "Algerian Dinar",
        "countries": [
            "Algeria"
        ]
    },
    {
        "code": "EGP",
        "number": "818",
        "digits": 2,
        "currency": "Egyptian Pound",
        "countries": [
            "Egypt"
        ]
    },
    {
        "code": "ERN",
        "number": "232",
        "digits": 2,
        "currency": "Nakfa",
        "countries": [
            "Eritrea"
        ]
    },
    {
        "code": "ETB",
        "number": "230",
        "digits": 2,
        "currency": "Ethiopian Birr",
        "countries": [
            "Ethiopia"
        ]
    },
    {
        "code": "EUR",
        "number": "978",
        "digits": 2,
        "currency": "Euro",
        "countries": [
            "Åland Islands",
            "Andorra",
            "Austria",
            "Belgium",
            "Croatia",
            "Cyprus",
            "Estonia",
            "European Union",
            "Finland",
            "France",
            "French Guiana",
            "French Southern Territories (The)",
            "Germany",
            "Greece",
            "Guadeloupe",
            "Holy See (The)",
            "Ireland",
            "Italy",
            "Latvia",
            "Lithuania",
            "Luxembourg",
            "Malta",
            "Martinique",
            "Mayotte",
            "Monaco",
            "Montenegro",
            "Netherlands (The)",
            "Portugal",
            "Réunion",
            "Saint Barthélemy",
            "Saint Martin (French Part)",
            "Saint Pierre and Miquelon",
            "San Marino",
            "Slovakia",
            "Slovenia",
            "Spain"
        ]
    },
    {
        "code": "FJD",
        "number": "242",
        "digits": 2,
        "currency": "Fiji Dollar",
        "countries": [
            "Fiji"
        ]
    },
    {
        "code": "FKP",
        "number": "238",
        "digits": 2,
        "currency": "Falkland Islands Pound",
        "countries": [
            "Falkland Islands (The) [Malvinas]"
        ]
    },
    {
        "code": "GBP",
        "number": "826",
        "digits": 2,
        "currency": "Pound Sterling",
        "countries": [
            "Guernsey",
            "Isle of Man",
            "Jersey",
            "United Kingdom of Great Britain and Northern Ireland (The)"
        ]
    },
    {
        "code": "GEL",
        "number": "981",
        "digits": 2,
        "currency": "Lari",
        "countries": [
            "Georgia"
        ]
    },
    {
        "code": "GHS",
        "number": "936",
        "digits": 2,
        "currency": "Ghana Cedi",
        "countries": [
            "Ghana"
        ]
    },
    {
        "code": "GIP",
        "number": "292",
        "digits": 2,
        "currency": "Gibraltar Pound",
        "countries": [
            "Gibraltar"
        ]
    },
    {
        "code": "GMD",
        "number": "270",
        "digits": 2,
        "currency": "Dalasi",
        "countries": [
            "Gambia (The)"
        ]
    },
    {
        "code": "GNF",
        "number": "324",
        "digits": 0,
        "currency": "Guinean Franc",
        "countries": [
            "Guinea"
        ]
    },
    {
        "code": "GTQ",
        "number": "320",
        "digits": 2,
        "currency": "Quetzal",
        "countries": [
            "Guatemala"
        ]
    },
    {
        "code": "GYD",
        "number": "328",
        "digits": 2,
        "currency": "Guyana Dollar",
        "countries": [
            "Guyana"
        ]
    },
    {
        "code": "HKD",
        "number": "344",
        "digits": 2,
        "currency": "Hong Kong Dollar",
        "countries": [
            "Hong Kong"
        ]
    },
    {
        "code": "HNL",
        "number": "340",
        "digits": 2,
        "currency": "Lempira",
        "countries": [
            "Honduras"
        ]
    },
    {
        "code": "HTG",
        "number": "332",
        "digits": 2,
        "currency": "Gourde",
        "countries": [
            "Haiti"
        ]
    },
    {
        "code": "HUF",
        "number": "348",
        "digits": 2,
        "currency": "Forint",
        "countries": [
            "Hungary"
        ]
    },
    {
        "code": "IDR",
        "number": "360",
        "digits": 2,
        "currency": "Rupiah",
        "countries": [
            "Indonesia"
        ]
    },
    {
        "code": "ILS",
        "number": "376",
        "digits": 2,
        "currency": "New Israeli Sheqel",
        "countries": [
            "Israel"
        ]
    },
    {
        "code": "INR",
        "number": "356",
        "digits": 2,
        "currency": "Indian Rupee",
        "countries": [
            "Bhutan",
            "India"
        ]
    },
    {
        "code": "IQD",
        "number": "368",
        "digits": 3,
        "currency": "Iraqi Dinar",
        "countries": [
            "Iraq"
        ]
    },
    {
        "code": "IRR",
        "number": "364",
        "digits": 2,
        "currency": "Iranian Rial",
        "countries": [
            "Iran (Islamic Republic Of)"
        ]
    },
    {
        "code": "ISK",
        "number": "352",
        "digits": 0,
        "currency": "Iceland Krona",
        "countries": [
            "Iceland"
        ]
    },
    {
        "code": "JMD",
        "number": "388",
        "digits": 2,
        "currency": "Jamaican Dollar",
        "countries": [
            "Jamaica"
        ]
    },
    {
        "code": "JOD",
        "number": "400",
        "digits": 3,
        "currency": "Jordanian Dinar",
        "countries": [
            "Jordan"
        ]
    },
    {
        "code": "JPY",
        "number": "392",
        "digits": 0,
        "currency": "Yen",
        "countries": [
            "Japan"
        ]
    },
    {
        "code": "KES",
        "number": "404",
        "digits": 2,
        "currency": "Kenyan Shilling",
        "countries": [
            "Kenya"
        ]
    },
    {
        "code": "KGS",
        "number": "417",
        "digits": 2,
        "currency": "Som",
        "countries": [
            "Kyrgyzstan"
        ]
    },
    {
        "code": "KHR",
        "number": "116",
        "digits": 2,
        "currency": "Riel",
        "countries": [
            "Cambodia"
        ]
    },
    {
        "code": "KMF",
        "number": "174",
        "digits": 0,
        "currency": "Comorian Franc ",
        "countries": [
            "Comoros (The)"
        ]
    },
    {
        "code": "KPW",
        "number": "408",
        "digits": 2,
        "currency": "North Korean Won",
        "countries": [
            "Korea (The Democratic People’s Republic Of)"
        ]
    },
    {
        "code": "KRW",
        "number": "410",
        "digits": 0,
        "currency": "Won",
        "countries": [
            "Korea (The Republic Of)"
        ]
    },
    {
        "code": "KWD",
        "number": "414",
        "digits": 3,
        "currency": "Kuwaiti Dinar",
        "countries": [
            "Kuwait"
        ]
    },
    {
        "code": "KYD",
        "number": "136",
        "digits": 2,
        "currency": "Cayman Islands Dollar",
        "countries": [
            "Cayman Islands (The)"
        ]
    },
    {
        "code": "KZT",
        "number": "398",
        "digits": 2,
        "currency": "Tenge",
        "countries": [
            "Kazakhstan"
        ]
    },
    {
        "code": "LAK",
        "number": "418",
        "digits": 2,
        "currency": "Lao Kip",
        "countries": [
            "Lao People’s Democratic Republic (The)"
        ]
    },
    {
        "code": "LBP",
        "number": "422",
        "digits": 2,
        "currency": "Lebanese Pound",
        "countries": [
            "Lebanon"
        ]
    },
    {
        "code": "LKR",
        "number": "144",
        "digits": 2,
        "currency": "Sri Lanka Rupee",
        "countries": [
            "Sri Lanka"
        ]
    },
    {
        "code": "LRD",
        "number": "430",
        "digits": 2,
        "currency": "Liberian Dollar",
        "countries": [
            "Liberia"
        ]
    },
    {
        "code": "LSL",
        "number": "426",
        "digits": 2,
        "currency": "Loti",
        "countries": [
            "Lesotho"
        ]
    },
    {
        "code": "LYD",
        "number": "434",
        "digits": 3,
        "currency": "Libyan Dinar",
        "countries": [
            "Libya"
        ]
    },
    {
        "code": "MAD",
        "number": "504",
        "digits": 2,
        "currency": "Moroccan Dirham",
        "countries": [
            "Morocco",
            "Western Sahara"
        ]
    },
    {
        "code": "MDL",
        "number": "498",
        "digits": 2,
        "currency": "Moldovan Leu",
        "countries": [
            "Moldova (The Republic Of)"
        ]
    },
    {
        "code": "MGA",
        "number": "969",
        "digits": 2,
        "currency": "Malagasy Ariary",
        "countries": [
            "Madagascar"
        ]
    },
    {
        "code": "MKD",
        "number": "807",
        "digits": 2,
        "currency": "Denar",
        "countries": [
            "North Macedonia"
        ]
    },
    {
        "code": "MMK",
        "number": "104",
        "digits": 2,
        "currency": "Kyat",
        "countries": [
            "Myanmar"
        ]
    },
    {
        "code": "MNT",
        "number": "496",
        "digits": 2,
        "currency": "Tugrik",
        "countries": [
            "Mongolia"
        ]
    },
    {
        "code": "MOP",
        "number": "446",
        "digits": 2,
        "currency": "Pataca",
        "countries": [
            "Macao"
        ]
    },
    {
        "code": "MRU",
        "number": "929",
        "digits": 2,
        "currency": "Ouguiya",
        "countries": [
            "Mauritania"
        ]
    },
    {
        "code": "MUR",
        "number": "480",
        "digits": 2,
        "currency": "Mauritius Rupee",
        "countries": [
            "Mauritius"
        ]
    },
    {
        "code": "MVR",
        "number": "462",
        "digits": 2,
        "currency": "Rufiyaa",
        "countries": [
            "Maldives"
        ]
    },
    {
        "code": "MWK",
        "number": "454",
        "digits": 2,
        "currency": "Malawi Kwacha",
        "countries": [
            "Malawi"
        ]
    },
    {
        "code": "MXN",
        "number": "484",
        "digits": 2,
        "currency": "Mexican Peso",
        "countries": [
            "Mexico"
        ]
    },
    {
        "code": "MXV",
        "number": "979",
        "digits": 2,
        "currency": "Mexican Unidad de Inversion (UDI)",
        "countries": [
            "Mexico"
        ]
    },
    {
        "code": "MYR",
        "number": "458",
        "digits": 2,
        "currency": "Malaysian Ringgit",
        "countries": [
            "Malaysia"
        ]
    },
    {
        "code": "MZN",
        "number": "943",
        "digits": 2,
        "currency": "Mozambique Metical",
        "countries": [
            "Mozambique"
        ]
    },
    {
        "code": "NAD",
        "number": "516",
        "digits": 2,
        "currency": "Namibia Dollar",
        "countries": [
            "Namibia"
        ]
    },
    {
        "code": "NGN",
        "number": "566",
        "digits": 2,
        "currency": "Naira",
        "countries": [
            "Nigeria"
        ]
    },
    {
        "code": "NIO",
        "number": "558",
        "digits": 2,
        "currency": "Cordoba Oro",
        "countries": [
            "Nicaragua"
        ]
    },
    {
        "code": "NOK",
        "number": "578",
        "digits": 2,
        "currency": "Norwegian Krone",
        "countries": [
            "Bouvet Island",
            "Norway",
            "Svalbard and Jan Mayen"
        ]
    },
    {
        "code": "NPR",
        "number": "524",
        "digits": 2,
        "currency": "Nepalese Rupee",
        "countries": [
            "Nepal"
        ]
    },
    {
        "code": "NZD",
        "number": "554",
        "digits": 2,
        "currency": "New Zealand Dollar",
        "countries": [
            "Cook Islands (The)",
            "New Zealand",
            "Niue",
            "Pitcairn",
            "Tokelau"
        ]
    },
    {
        "code": "OMR",
        "number": "512",
        "digits": 3,
        "currency": "Rial Omani",
        "countries": [
            "Oman"
        ]
    },
    {
        "code": "PAB",
        "number": "590",
        "digits": 2,
        "currency": "Balboa",
        "countries": [
            "Panama"
        ]
    },
    {
        "code": "PEN",
        "number": "604",
        "digits": 2,
        "currency": "Sol",
        "countries": [
            "Peru"
        ]
    },
    {
        "code": "PGK",
        "number": "598",
        "digits": 2,
        "currency": "Kina",
        "countries": [
            "Papua New Guinea"
        ]
    },
    {
        "code": "PHP",
        "number": "608",
        "digits": 2,
        "currency": "Philippine Peso",
        "countries": [
            "Philippines (The)"
        ]
    },
    {
        "code": "PKR",
        "number": "586",
        "digits": 2,
        "currency": "Pakistan Rupee",
        "countries": [
            "Pakistan"
        ]
    },
    {
        "code": "PLN",
        "number": "985",
        "digits": 2,
        "currency": "Zloty",
        "countries": [
            "Poland"
        ]
    },
    {
        "code": "PYG",
        "number": "600",
        "digits": 0,
        "currency": "Guarani",
        "countries": [
            "Paraguay"
        ]
    },
    {
        "code": "QAR",
        "number": "634",
        "digits": 2,
        "currency": "Qatari Rial",
        "countries": [
            "Qatar"
        ]
    },
    {
        "code": "RON",
        "number": "946",
        "digits": 2,
        "currency": "Romanian Leu",
        "countries": [
            "Romania"
        ]
    },
    {
        "code": "RSD",
        "number": "941",
        "digits": 2,
        "currency": "Serbian Dinar",
        "countries": [
            "Serbia"
        ]
    },
    {
        "code": "RUB",
        "number": "643",
        "digits": 2,
        "currency": "Russian Ruble",
        "countries": [
            "Russian Federation (The)"
        ]
    },
    {
        "code": "RWF",
        "number": "646",
        "digits": 0,
        "currency": "Rwanda Franc",
        "countries": [
            "Rwanda"
        ]
    },
    {
        "code": "SAR",
        "number": "682",
        "digits": 2,
        "currency": "Saudi Riyal",
        "countries": [
            "Saudi Arabia"
        ]
    },
    {
        "code": "SBD",
        "number": "090",
        "digits": 2,
        "currency": "Solomon Islands Dollar",
        "countries": [
            "Solomon Islands"
        ]
    },
    {
        "code": "SCR",
        "number": "690",
        "digits": 2,
        "currency": "Seychelles Rupee",
        "countries": [
            "Seychelles"
        ]
    },
    {
        "code": "SDG",
        "number": "938",
        "digits": 2,
        "currency": "Sudanese Pound",
        "countries": [
            "Sudan (The)"
        ]
    },
    {
        "code": "SEK",
        "number": "752",
        "digits": 2,
        "currency": "Swedish Krona",
        "countries": [
            "Sweden"
        ]
    },
    {
        "code": "SGD",
        "number": "702",
        "digits": 2,
        "currency": "Singapore Dollar",
        "countries": [
            "Singapore"
        ]
    },
    {
        "code": "SHP",
        "number": "654",
        "digits": 2,
        "currency": "Saint Helena Pound",
        "countries": [
            "Saint Helena, Ascension and Tristan Da Cunha"
        ]
    },
    {
        "code": "SLE",
        "number": "925",
        "digits": 2,
        "currency": "Leone",
        "countries": [
            "Sierra Leone"
        ]
    },
    {
        "code": "SOS",
        "number": "706",
        "digits": 2,
        "currency": "Somali Shilling",
        "countries": [
            "Somalia"
        ]
    },
    {
        "code": "SRD",
        "number": "968",
        "digits": 2,
        "currency": "Surinam Dollar",
        "countries": [
            "Suriname"
        ]
    },
    {
        "code": "SSP",
        "number": "728",
        "digits": 2,
        "currency": "South Sudanese Pound",
        "countries": [
            "South Sudan"
        ]
    },
    {
        "code": "STN",
        "number": "930",
        "digits": 2,
        "currency": "Dobra",
        "countries": [
            "Sao Tome and Principe"
        ]
    },
    {
        "code": "SVC",
        "number": "222",
        "digits": 2,
        "currency": "El Salvador Colon",
        "countries": [
            "El Salvador"
        ]
    },
    {
        "code": "SYP",
        "number": "760",
        "digits": 2,
        "currency": "Syrian Pound",
        "countries": [
            "Syrian Arab Republic"
        ]
    },
    {
        "code": "SZL",
        "number": "748",
        "digits": 2,
        "currency": "Lilangeni",
        "countries": [
            "Eswatini"
        ]
    },
    {
        "code": "THB",
        "number": "764",
        "digits": 2,
        "currency": "Baht",
        "countries": [
            "Thailand"
        ]
    },
    {
        "code": "TJS",
        "number": "972",
        "digits": 2,
        "currency": "Somoni",
        "countries": [
            "Tajikistan"
        ]
    },
    {
        "code": "TMT",
        "number": "934",
        "digits": 2,
        "currency": "Turkmenistan New Manat",
        "countries": [
            "Turkmenistan"
        ]
    },
    {
        "code": "TND",
        "number": "788",
        "digits": 3,
        "currency": "Tunisian Dinar",
        "countries": [
            "Tunisia"
        ]
    },
    {
        "code": "TOP",
        "number": "776",
        "digits": 2,
        "currency": "Pa’anga",
        "countries": [
            "Tonga"
        ]
    },
    {
        "code": "TRY",
        "number": "949",
        "digits": 2,
        "currency": "Turkish Lira",
        "countries": [
            "Türki̇ye"
        ]
    },
    {
        "code": "TTD",
        "number": "780",
        "digits": 2,
        "currency": "Trinidad and Tobago Dollar",
        "countries": [
            "Trinidad and Tobago"
        ]
    },
    {
        "code": "TWD",
        "number": "901",
        "digits": 2,
        "currency": "New Taiwan Dollar",
        "countries": [
            "Taiwan (Province of China)"
        ]
    },
    {
        "code": "TZS",
        "number": "834",
        "digits": 2,
        "currency": "Tanzanian Shilling",
        "countries": [
            "Tanzania, United Republic Of"
        ]
    },
    {
        "code": "UAH",
        "number": "980",
        "digits": 2,
        "currency": "Hryvnia",
        "countries": [
            "Ukraine"
        ]
    },
    {
        "code": "UGX",
        "number": "800",
        "digits": 0,
        "currency": "Uganda Shilling",
        "countries": [
            "Uganda"
        ]
    },
    {
        "code": "USD",
        "number": "840",
        "digits": 2,
        "currency": "US Dollar",
        "countries": [
            "American Samoa",
            "Bonaire, Sint Eustatius and Saba",
            "British Indian Ocean Territory (The)",
            "Ecuador",
            "El Salvador",
            "Guam",
            "Haiti",
            "Marshall Islands (The)",
            "Micronesia (Federated States Of)",
            "Northern Mariana Islands (The)",
            "Palau",
            "Panama",
            "Puerto Rico",
            "Timor-Leste",
            "Turks and Caicos Islands (The)",
            "United States Minor Outlying Islands (The)",
            "United States of America (The)",
            "Virgin Islands (British)",
            "Virgin Islands (u.s.)"
        ]
    },
    {
        "code": "USN",
        "number": "997",
        "digits": 2,
        "currency": "US Dollar (Next day)",
        "countries": [
            "United States of America (The)"
        ]
    },
    {
        "code": "UYI",
        "number": "940",
        "digits": 0,
        "currency": "Uruguay Peso en Unidades Indexadas (UI)",
        "countries": [
            "Uruguay"
        ]
    },
    {
        "code": "UYU",
        "number": "858",
        "digits": 2,
        "currency": "Peso Uruguayo",
        "countries": [
            "Uruguay"
        ]
    },
    {
        "code": "UYW",
        "number": "927",
        "digits": 4,
        "currency": "Unidad Previsional",
        "countries": [
            "Uruguay"
        ]
    },
    {
        "code": "UZS",
        "number": "860",
        "digits": 2,
        "currency": "Uzbekistan Sum",
        "countries": [
            "Uzbekistan"
        ]
    },
    {
        "code": "VED",
        "number": "926",
        "digits": 2,
        "currency": "Bolívar Soberano",
        "countries": [
            "Venezuela (Bolivarian Republic Of)"
        ]
    },
    {
        "code": "VES",
        "number": "928",
        "digits": 2,
        "currency": "Bolívar Soberano",
        "countries": [
            "Venezuela (Bolivarian Republic Of)"
        ]
    },
    {
        "code": "VND",
        "number": "704",
        "digits": 0,
        "currency": "Dong",
        "countries": [
            "Viet Nam"
        ]
    },
    {
        "code": "VUV",
        "number": "548",
        "digits": 0,
        "currency": "Vatu",
        "countries": [
            "Vanuatu"
        ]
    },
    {
        "code": "WST",
        "number": "882",
        "digits": 2,
        "currency": "Tala",
        "countries": [
            "Samoa"
        ]
    },
    {
        "code": "XAF",
        "number": "950",
        "digits": 0,
        "currency": "CFA Franc BEAC",
        "countries": [
            "Cameroon",
            "Central African Republic (The)",
            "Chad",
            "Congo (The)",
            "Equatorial Guinea",
            "Gabon"
        ]
    },
    {
        "code": "XAG",
        "number": "961",
        "digits": 0,
        "currency": "Silver",
        "countries": [
            "Zz11_silver"
        ]
    },
    {
        "code": "XAU",
        "number": "959",
        "digits": 0,
        "currency": "Gold",
        "countries": [
            "Zz08_gold"
        ]
    },
    {
        "code": "XBA",
        "number": "955",
        "digits": 0,
        "currency": "Bond Markets Unit European Composite Unit (EURCO)",
        "countries": [
            "Zz01_bond Markets Unit European_eurco"
        ]
    },
    {
        "code": "XBB",
        "number": "956",
        "digits": 0,
        "currency": "Bond Markets Unit European Monetary Unit (E.M.U.-6)",
        "countries": [
            "Zz02_bond Markets Unit European_emu-6"
        ]
    },
    {
        "code": "XBC",
        "number": "957",
        "digits": 0,
        "currency": "Bond Markets Unit European Unit of Account 9 (E.U.A.-9)",
        "countries": [
            "Zz03_bond Markets Unit European_eua-9"
        ]
    },
    {
        "code": "XBD",
        "number": "958",
        "digits": 0,
        "currency": "Bond Markets Unit European Unit of Account 17 (E.U.A.-17)",
        "countries": [
            "Zz04_bond Markets Unit European_eua-17"
        ]
    },
    {
        "code": "XCD",
        "number": "951",
        "digits": 2,
        "currency": "East Caribbean Dollar",
        "countries": [
            "Anguilla",
            "Antigua and Barbuda",
            "Dominica",
            "Grenada",
            "Montserrat",
            "Saint Kitts and Nevis",
            "Saint Lucia",
            "Saint Vincent and the Grenadines"
        ]
    },
    {
        "code": "XDR",
        "number": "960",
        "digits": 0,
        "currency": "SDR (Special Drawing Right)",
        "countries": [
            "International Monetary Fund (Imf) "
        ]
    },
    {
        "code": "XOF",
        "number": "952",
        "digits": 0,
        "currency": "CFA Franc BCEAO",
        "countries": [
            "Benin",
            "Burkina Faso",
            "Côte D'ivoire",
            "Guinea-Bissau",
            "Mali",
            "Niger (The)",
            "Senegal",
            "Togo"
        ]
    },
    {
        "code": "XPD",
        "number": "964",
        "digits": 0,
        "currency": "Palladium",
        "countries": [
            "Zz09_palladium"
        ]
    },
    {
        "code": "XPF",
        "number": "953",
        "digits": 0,
        "currency": "CFP Franc",
        "countries": [
            "French Polynesia",
            "New Caledonia",
            "Wallis and Futuna"
        ]
    },
    {
        "code": "XPT",
        "number": "962",
        "digits": 0,
        "currency": "Platinum",
        "countries": [
            "Zz10_platinum"
        ]
    },
    {
        "code": "XSU",
        "number": "994",
        "digits": 0,
        "currency": "Sucre",
        "countries": [
            "Sistema Unitario De Compensacion Regional De Pagos \"Sucre\""
        ]
    },
    {
        "code": "XTS",
        "number": "963",
        "digits": 0,
        "currency": "Codes specifically reserved for testing purposes",
        "countries": [
            "Zz06_testing_code"
        ]
    },
    {
        "code": "XUA",
        "number": "965",
        "digits": 0,
        "currency": "ADB Unit of Account",
        "countries": [
            "Member Countries of the African Development Bank Group"
        ]
    },
    {
        "code": "XXX",
        "number": "999",
        "digits": 0,
        "currency": "The codes assigned for transactions where no currency is involved",
        "countries": [
            "Zz07_no_currency"
        ]
    },
    {
        "code": "YER",
        "number": "886",
        "digits": 2,
        "currency": "Yemeni Rial",
        "countries": [
            "Yemen"
        ]
    },
    {
        "code": "ZAR",
        "number": "710",
        "digits": 2,
        "currency": "Rand",
        "countries": [
            "Lesotho",
            "Namibia",
            "South Africa"
        ]
    },
    {
        "code": "ZMW",
        "number": "967",
        "digits": 2,
        "currency": "Zambian Kwacha",
        "countries": [
            "Zambia"
        ]
    },
    {
        "code": "ZWG",
        "number": "924",
        "digits": 2,
        "currency": "Zimbabwe Gold",
        "countries": [
            "Zimbabwe"
        ]
    }
];

export const country_currency =[
    {
        "currencyName": "Afghan Afghani",
        "currencySymbol": "؋",
        "countryNameCode": "AF",
        "currencyCode": "AFN",
        "countryFlag": "🇦🇫",
        "countryName": "Afghanistan",
        "dialCode": "+93",
        "iso3": "AFG",
        "numericCode": "004"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "AX",
        "currencyCode": "EUR",
        "countryFlag": "🇦🇽",
        "countryName": "Åland Islands",
        "dialCode": "+358",
        "iso3": "ALA",
        "numericCode": "248"
    },
    {
        "currencyName": "Albanian Lek",
        "currencySymbol": "L",
        "countryNameCode": "AL",
        "currencyCode": "ALL",
        "countryFlag": "🇦🇱",
        "countryName": "Albania",
        "dialCode": "+355",
        "iso3": "ALB",
        "numericCode": "008"
    },
    {
        "currencyName": "Algerian Dinar",
        "currencySymbol": "دج",
        "countryNameCode": "DZ",
        "currencyCode": "DZD",
        "countryFlag": "🇩🇿",
        "countryName": "Algeria",
        "dialCode": "+213",
        "iso3": "DZA",
        "numericCode": "012"
    },
    {
        "currencyName": "United States Dollar",
        "currencySymbol": "$",
        "countryNameCode": "AS",
        "currencyCode": "USD",
        "countryFlag": "🇦🇸",
        "countryName": "American Samoa",
        "dialCode": "+1684",
        "iso3": "ASM",
        "numericCode": "016"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "AD",
        "currencyCode": "EUR",
        "countryFlag": "🇦🇩",
        "countryName": "Andorra",
        "dialCode": "+376",
        "iso3": "AND",
        "numericCode": "020"
    },
    {
        "currencyName": "Angolan Kwanza",
        "currencySymbol": "Kz",
        "countryNameCode": "AO",
        "currencyCode": "AOA",
        "countryFlag": "🇦🇴",
        "countryName": "Angola",
        "dialCode": "+244",
        "iso3": "AGO",
        "numericCode": "024"
    },
    {
        "currencyName": "East Caribbean Dollar",
        "currencySymbol": "$",
        "countryNameCode": "AI",
        "currencyCode": "XCD",
        "countryFlag": "🇦🇮",
        "countryName": "Anguilla",
        "dialCode": "+1264",
        "iso3": "AIA",
        "numericCode": "660"
    },
    {
        "currencyName": "United States Dollar",
        "currencySymbol": "$",
        "countryNameCode": "AQ",
        "currencyCode": "USD",
        "countryFlag": "🇦🇶",
        "countryName": "Antarctica",
        "dialCode": "+1",
        "iso3": "ATA",
        "numericCode": "010",
        "note": "Antarctica does not have an official currency. Various currencies are used depending on the currency operating the station."
    },
    {
        "currencyName": "East Caribbean Dollar",
        "currencySymbol": "$",
        "countryNameCode": "AG",
        "currencyCode": "XCD",
        "countryFlag": "🇦🇬",
        "countryName": "Antigua and Barbuda",
        "dialCode": "+1268",
        "iso3": "ATG",
        "numericCode": "028"
    },
    {
        "currencyName": "Argentine Peso",
        "currencySymbol": "$",
        "countryNameCode": "AR",
        "currencyCode": "ARS",
        "countryFlag": "🇦🇷",
        "countryName": "Argentina",
        "dialCode": "+54",
        "iso3": "ARG",
        "numericCode": "032"
    },
    {
        "currencyName": "Armenian Dram",
        "currencySymbol": "֏",
        "countryNameCode": "AM",
        "currencyCode": "AMD",
        "countryFlag": "🇦🇲",
        "countryName": "Armenia",
        "dialCode": "+374",
        "iso3": "ARM",
        "numericCode": "051"
    },
    {
        "currencyName": "Aruban Florin",
        "currencySymbol": "ƒ",
        "countryNameCode": "AW",
        "currencyCode": "AWG",
        "countryFlag": "🇦🇼",
        "countryName": "Aruba",
        "dialCode": "+297",
        "iso3": "ABW",
        "numericCode": "533"
    },
    {
        "currencyName": "Australian Dollar",
        "currencySymbol": "A$",
        "countryNameCode": "AU",
        "currencyCode": "AUD",
        "countryFlag": "🇦🇺",
        "countryName": "Australia",
        "dialCode": "+61",
        "iso3": "AUS",
        "numericCode": "036"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "AT",
        "currencyCode": "EUR",
        "countryFlag": "🇦🇹",
        "countryName": "Austria",
        "dialCode": "+43",
        "iso3": "AUT",
        "numericCode": "040"
    },
    {
        "currencyName": "Azerbaijani Manat",
        "currencySymbol": "₼",
        "countryNameCode": "AZ",
        "currencyCode": "AZN",
        "countryFlag": "🇦🇿",
        "countryName": "Azerbaijan",
        "dialCode": "+994",
        "iso3": "AZE",
        "numericCode": "031"
    },
    {
        "currencyName": "Bahamian Dollar",
        "currencySymbol": "$",
        "countryNameCode": "BS",
        "currencyCode": "BSD",
        "countryFlag": "🇧🇸",
        "countryName": "Bahamas",
        "dialCode": "+1242",
        "iso3": "BHS",
        "numericCode": "044"
    },
    {
        "currencyName": "Bahraini Dinar",
        "currencySymbol": ".د.ب",
        "countryNameCode": "BH",
        "currencyCode": "BHD",
        "countryFlag": "🇧🇭",
        "countryName": "Bahrain",
        "dialCode": "+973",
        "iso3": "BHR",
        "numericCode": "048"
    },
    {
        "currencyName": "Bangladeshi Taka",
        "currencySymbol": "৳",
        "countryNameCode": "BD",
        "currencyCode": "BDT",
        "countryFlag": "🇧🇩",
        "countryName": "Bangladesh",
        "dialCode": "+880",
        "iso3": "BGD",
        "numericCode": "050"
    },
    {
        "currencyName": "Barbadian Dollar",
        "currencySymbol": "$",
        "countryNameCode": "BB",
        "currencyCode": "BBD",
        "countryFlag": "🇧🇧",
        "countryName": "Barbados",
        "dialCode": "+1246",
        "iso3": "BRB",
        "numericCode": "052"
    },
    {
        "currencyName": "Belarusian Ruble",
        "currencySymbol": "Br",
        "countryNameCode": "BY",
        "currencyCode": "BYN",
        "countryFlag": "🇧🇾",
        "countryName": "Belarus",
        "dialCode": "+375",
        "iso3": "BLR",
        "numericCode": "112"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "BE",
        "currencyCode": "EUR",
        "countryFlag": "🇧🇪",
        "countryName": "Belgium",
        "dialCode": "+32",
        "iso3": "BEL",
        "numericCode": "056"
    },
    {
        "currencyName": "Belize Dollar",
        "currencySymbol": "$",
        "countryNameCode": "BZ",
        "currencyCode": "BZD",
        "countryFlag": "🇧🇿",
        "countryName": "Belize",
        "dialCode": "+501",
        "iso3": "BLZ",
        "numericCode": "084"
    },
    {
        "currencyName": "West African CFA Franc",
        "currencySymbol": "CFA",
        "countryNameCode": "BJ",
        "currencyCode": "XOF",
        "countryFlag": "🇧🇯",
        "countryName": "Benin",
        "dialCode": "+229",
        "iso3": "BEN",
        "numericCode": "204"
    },
    {
        "currencyName": "Bermudian Dollar",
        "currencySymbol": "$",
        "countryNameCode": "BM",
        "currencyCode": "BMD",
        "countryFlag": "🇧🇲",
        "countryName": "Bermuda",
        "dialCode": "+1441",
        "iso3": "BMU",
        "numericCode": "060"
    },
    {
        "currencyName": "Bhutanese Ngultrum",
        "currencySymbol": "Nu.",
        "countryNameCode": "BT",
        "currencyCode": "BTN",
        "countryFlag": "🇧🇹",
        "countryName": "Bhutan",
        "dialCode": "+975",
        "iso3": "BTN",
        "numericCode": "064"
    },
    {
        "currencyName": "Bolivian Boliviano",
        "currencySymbol": "Bs.",
        "countryNameCode": "BO",
        "currencyCode": "BOB",
        "countryFlag": "🇧🇴",
        "countryName": "Bolivia",
        "dialCode": "+591",
        "iso3": "BOL",
        "numericCode": "068"
    },
    {
        "currencyName": "Bosnia-Herzegovina Convertible Mark",
        "currencySymbol": "KM",
        "countryNameCode": "BA",
        "currencyCode": "BAM",
        "countryFlag": "🇧🇦",
        "countryName": "Bosnia and Herzegovina",
        "dialCode": "+387",
        "iso3": "BIH",
        "numericCode": "070"
    },
    {
        "currencyName": "Botswana Pula",
        "currencySymbol": "P",
        "countryNameCode": "BW",
        "currencyCode": "BWP",
        "countryFlag": "🇧🇼",
        "countryName": "Botswana",
        "dialCode": "+267",
        "iso3": "BWA",
        "numericCode": "072"
    },
    {
        "currencyName": "Brazilian Real",
        "currencySymbol": "R$",
        "countryNameCode": "BR",
        "currencyCode": "BRL",
        "countryFlag": "🇧🇷",
        "countryName": "Brazil",
        "dialCode": "+55",
        "iso3": "BRA",
        "numericCode": "076"
    },
    {
        "currencyName": "Brunei Dollar",
        "currencySymbol": "$",
        "countryNameCode": "BN",
        "currencyCode": "BND",
        "countryFlag": "🇧🇳",
        "countryName": "Brunei",
        "dialCode": "+673",
        "iso3": "BRN",
        "numericCode": "096"
    },
    {
        "currencyName": "Bulgarian Lev",
        "currencySymbol": "лв",
        "countryNameCode": "BG",
        "currencyCode": "BGN",
        "countryFlag": "🇧🇬",
        "countryName": "Bulgaria",
        "dialCode": "+359",
        "iso3": "BGR",
        "numericCode": "100"
    },
    {
        "currencyName": "West African CFA Franc",
        "currencySymbol": "CFA",
        "countryNameCode": "BF",
        "currencyCode": "XOF",
        "countryFlag": "🇧🇫",
        "countryName": "Burkina Faso",
        "dialCode": "+226",
        "iso3": "BFA",
        "numericCode": "854"
    },
    {
        "currencyName": "Burundian Franc",
        "currencySymbol": "FBu",
        "countryNameCode": "BI",
        "currencyCode": "BIF",
        "countryFlag": "🇧🇮",
        "countryName": "Burundi",
        "dialCode": "+257",
        "iso3": "BDI",
        "numericCode": "108"
    },
    {
        "currencyName": "Cambodian Riel",
        "currencySymbol": "៛",
        "countryNameCode": "KH",
        "currencyCode": "KHR",
        "countryFlag": "🇰🇭",
        "countryName": "Cambodia",
        "dialCode": "+855",
        "iso3": "KHM",
        "numericCode": "116"
    },
    {
        "currencyName": "Central African CFA Franc",
        "currencySymbol": "FCFA",
        "countryNameCode": "CM",
        "currencyCode": "XAF",
        "countryFlag": "🇨🇲",
        "countryName": "Cameroon",
        "dialCode": "+237",
        "iso3": "CMR",
        "numericCode": "120"
    },
    {
        "currencyName": "Canadian Dollar",
        "currencySymbol": "CA$",
        "countryNameCode": "CA",
        "currencyCode": "CAD",
        "countryFlag": "🇨🇦",
        "countryName": "Canada",
        "dialCode": "+1",
        "iso3": "CAN",
        "numericCode": "124"
    },
    {
        "currencyName": "Cape Verdean Escudo",
        "currencySymbol": "Esc",
        "countryNameCode": "CV",
        "currencyCode": "CVE",
        "countryFlag": "🇨🇻",
        "countryName": "Cape Verde",
        "dialCode": "+238",
        "iso3": "CPV",
        "numericCode": "132"
    },
    {
        "currencyName": "Cayman Islands Dollar",
        "currencySymbol": "$",
        "countryNameCode": "KY",
        "currencyCode": "KYD",
        "countryFlag": "🇰🇾",
        "countryName": "Cayman Islands",
        "dialCode": "+345",
        "iso3": "CYM",
        "numericCode": "136"
    },
    {
        "currencyName": "Central African CFA Franc",
        "currencySymbol": "FCFA",
        "countryNameCode": "CF",
        "currencyCode": "XAF",
        "countryFlag": "🇨🇫",
        "countryName": "Central African Republic",
        "dialCode": "+236",
        "iso3": "CAF",
        "numericCode": "140"
    },
    {
        "currencyName": "Central African CFA Franc",
        "currencySymbol": "FCFA",
        "countryNameCode": "TD",
        "currencyCode": "XAF",
        "countryFlag": "🇹🇩",
        "countryName": "Chad",
        "dialCode": "+235",
        "iso3": "TCD",
        "numericCode": "148"
    },
    {
        "currencyName": "Chilean Peso",
        "currencySymbol": "$",
        "countryNameCode": "CL",
        "currencyCode": "CLP",
        "countryFlag": "🇨🇱",
        "countryName": "Chile",
        "dialCode": "+56",
        "iso3": "CHL",
        "numericCode": "152"
    },
    {
        "currencyName": "Chinese Yuan",
        "currencySymbol": "¥",
        "countryNameCode": "CN",
        "currencyCode": "CNY",
        "countryFlag": "🇨🇳",
        "countryName": "China",
        "dialCode": "+86",
        "iso3": "CHN",
        "numericCode": "156"
    },
    {
        "currencyName": "Australian Dollar",
        "currencySymbol": "A$",
        "countryNameCode": "CX",
        "currencyCode": "AUD",
        "countryFlag": "🇨🇽",
        "countryName": "Christmas Island",
        "dialCode": "+61",
        "iso3": "CXR",
        "numericCode": "162",
        "note": "Christmas Island is an external territory of Australia and uses the Australian Dollar (AUD) and Australia’s dial code."
    },
    {
        "currencyName": "Australian Dollar",
        "currencySymbol": "A$",
        "countryNameCode": "CC",
        "currencyCode": "AUD",
        "countryFlag": "🇨🇨",
        "countryName": "Cocos (Keeling) Islands",
        "dialCode": "+61",
        "iso3": "CCK",
        "numericCode": "166",
        "note": "The Cocos (Keeling) Islands are an external territory of Australia, and they use the Australian Dollar (AUD) and Australia’s dial code."
    },
    {
        "currencyName": "Colombian Peso",
        "currencySymbol": "$",
        "countryNameCode": "CO",
        "currencyCode": "COP",
        "countryFlag": "🇨🇴",
        "countryName": "Colombia",
        "dialCode": "+57",
        "iso3": "COL",
        "numericCode": "170"
    },
    {
        "currencyName": "Comorian Franc",
        "currencySymbol": "CF",
        "countryNameCode": "KM",
        "currencyCode": "KMF",
        "countryFlag": "🇰🇲",
        "countryName": "Comoros",
        "dialCode": "+269",
        "iso3": "COM",
        "numericCode": "174"
    },
    {
        "currencyName": "Central African CFA Franc",
        "currencySymbol": "FCFA",
        "countryNameCode": "CG",
        "currencyCode": "XAF",
        "countryFlag": "🇨🇬",
        "countryName": "Republic of the Congo",
        "dialCode": "+242",
        "iso3": "COG",
        "numericCode": "178"
    },
    {
        "currencyName": "Congolese Franc",
        "currencySymbol": "FC",
        "countryNameCode": "CD",
        "currencyCode": "CDF",
        "countryFlag": "🇨🇩",
        "countryName": "Congo, The Democratic Republic of the Congo",
        "dialCode": "+243",
        "iso3": "COD",
        "numericCode": "180"
    },
    {
        "currencyName": "New Zealand Dollar",
        "currencySymbol": "NZ$",
        "countryNameCode": "CK",
        "currencyCode": "NZD",
        "countryFlag": "🇨🇰",
        "countryName": "Cook Islands",
        "dialCode": "+682",
        "iso3": "COK",
        "numericCode": "184",
        "note": "The Cook Islands use the New Zealand Dollar (NZD) as their official currency, although they also issue their own Cook Islands Dollars, which are not widely accepted outside the islands."
    },
    {
        "currencyName": "Costa Rican Colón",
        "currencySymbol": "₡",
        "countryNameCode": "CR",
        "currencyCode": "CRC",
        "countryFlag": "🇨🇷",
        "countryName": "Costa Rica",
        "dialCode": "+506",
        "iso3": "CRI",
        "numericCode": "188"
    },
    {
        "currencyName": "West African CFA Franc",
        "currencySymbol": "CFA",
        "countryNameCode": "CI",
        "currencyCode": "XOF",
        "countryFlag": "🇨🇮",
        "countryName": "Côte d'Ivoire",
        "dialCode": "+225",
        "iso3": "CIV",
        "numericCode": "384"
    },
    {
        "currencyName": "Croatian Kuna",
        "currencySymbol": "kn",
        "countryNameCode": "HR",
        "currencyCode": "HRK",
        "countryFlag": "🇭🇷",
        "countryName": "Croatia",
        "dialCode": "+385",
        "iso3": "HRV",
        "numericCode": "191"
    },
    {
        "currencyName": "Cuban Peso",
        "currencySymbol": "$",
        "countryNameCode": "CU",
        "currencyCode": "CUP",
        "countryFlag": "🇨🇺",
        "countryName": "Cuba",
        "dialCode": "+53",
        "iso3": "CUB",
        "numericCode": "192"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "CY",
        "currencyCode": "EUR",
        "countryFlag": "🇨🇾",
        "countryName": "Cyprus",
        "dialCode": "+357",
        "iso3": "CYP",
        "numericCode": "196"
    },
    {
        "currencyName": "Czech Koruna",
        "currencySymbol": "Kč",
        "countryNameCode": "CZ",
        "currencyCode": "CZK",
        "countryFlag": "🇨🇿",
        "countryName": "Czech Republic",
        "dialCode": "+420",
        "iso3": "CZE",
        "numericCode": "203"
    },
    {
        "currencyName": "Danish Krone",
        "currencySymbol": "kr",
        "countryNameCode": "DK",
        "currencyCode": "DKK",
        "countryFlag": "🇩🇰",
        "countryName": "Denmark",
        "dialCode": "+45",
        "iso3": "DNK",
        "numericCode": "208"
    },
    {
        "currencyName": "Djiboutian Franc",
        "currencySymbol": "Fdj",
        "countryNameCode": "DJ",
        "currencyCode": "DJF",
        "countryFlag": "🇩🇯",
        "countryName": "Djibouti",
        "dialCode": "+253",
        "iso3": "DJI",
        "numericCode": "262"
    },
    {
        "currencyName": "Dominican Peso",
        "currencySymbol": "RD$",
        "countryNameCode": "DO",
        "currencyCode": "DOP",
        "countryFlag": "🇩🇴",
        "countryName": "Dominican Republic",
        "dialCode": "+1849",
        "iso3": "DOM",
        "numericCode": "214"
    },
    {
        "currencyName": "East Caribbean Dollar",
        "currencySymbol": "$",
        "countryNameCode": "DM",
        "currencyCode": "XCD",
        "countryFlag": "🇩🇲",
        "countryName": "Dominica",
        "dialCode": "+1767",
        "iso3": "DMA",
        "numericCode": "212"
    },
    {
        "currencyName": "United States Dollar",
        "currencySymbol": "$",
        "countryNameCode": "EC",
        "currencyCode": "USD",
        "countryFlag": "🇪🇨",
        "countryName": "Ecuador",
        "dialCode": "+593",
        "iso3": "ECU",
        "numericCode": "218"
    },
    {
        "currencyName": "Egyptian Pound",
        "currencySymbol": "£",
        "countryNameCode": "EG",
        "currencyCode": "EGP",
        "countryFlag": "🇪🇬",
        "countryName": "Egypt",
        "dialCode": "+20",
        "iso3": "EGY",
        "numericCode": "818"
    },
    {
        "currencyName": "United States Dollar",
        "currencySymbol": "$",
        "countryNameCode": "SV",
        "currencyCode": "USD",
        "countryFlag": "🇸🇻",
        "countryName": "El Salvador",
        "dialCode": "+503",
        "iso3": "SLV",
        "numericCode": "222",
        "note": "El Salvador also recognizes Bitcoin as legal tender alongside the United States Dollar."
    },
    {
        "currencyName": "Central African CFA Franc",
        "currencySymbol": "FCFA",
        "countryNameCode": "GQ",
        "currencyCode": "XAF",
        "countryFlag": "🇬🇶",
        "countryName": "Equatorial Guinea",
        "dialCode": "+240",
        "iso3": "GNQ",
        "numericCode": "226"
    },
    {
        "currencyName": "Eritrean Nakfa",
        "currencySymbol": "Nkf",
        "countryNameCode": "ER",
        "currencyCode": "ERN",
        "countryFlag": "🇪🇷",
        "countryName": "Eritrea",
        "dialCode": "+291",
        "iso3": "ERI",
        "numericCode": "232"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "EE",
        "currencyCode": "EUR",
        "countryFlag": "🇪🇪",
        "countryName": "Estonia",
        "dialCode": "+372",
        "iso3": "EST",
        "numericCode": "233"
    },
    {
        "currencyName": "Ethiopian Birr",
        "currencySymbol": "Br",
        "countryNameCode": "ET",
        "currencyCode": "ETB",
        "countryFlag": "🇪🇹",
        "countryName": "Ethiopia",
        "dialCode": "+251",
        "iso3": "ETH",
        "numericCode": "231"
    },
    {
        "currencyName": "Falkland Islands Pound",
        "currencySymbol": "£",
        "countryNameCode": "FK",
        "currencyCode": "FKP",
        "countryFlag": "🇫🇰",
        "countryName": "Falkland Islands (Malvinas)",
        "dialCode": "+500",
        "iso3": "FLK",
        "numericCode": "238",
        "note": "The Falkland Islands Pound (FKP) is pegged at par with the British Pound (GBP), and the British Pound is also widely accepted."
    },
    {
        "currencyName": "Danish Krone",
        "currencySymbol": "kr",
        "countryNameCode": "FO",
        "currencyCode": "DKK",
        "countryFlag": "🇫🇴",
        "countryName": "Faroe Islands",
        "dialCode": "+298",
        "iso3": "FRO",
        "numericCode": "234",
        "note": "The Faroe Islands use the Danish Krone (DKK) as their official currency, but they also issue their own Faroese banknotes, which are pegged to the Danish Krone."
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "FI",
        "currencyCode": "EUR",
        "countryFlag": "🇫🇮",
        "countryName": "Finland",
        "dialCode": "+358",
        "iso3": "FIN",
        "numericCode": "246"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "FR",
        "currencyCode": "EUR",
        "countryFlag": "🇫🇷",
        "countryName": "France",
        "dialCode": "+33",
        "iso3": "FRA",
        "numericCode": "250"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "GF",
        "currencyCode": "EUR",
        "countryFlag": "🇬🇫",
        "countryName": "French Guiana",
        "dialCode": "+594",
        "iso3": "GUF",
        "numericCode": "254"
    },
    {
        "currencyName": "CFP Franc",
        "currencySymbol": "₣",
        "countryNameCode": "PF",
        "currencyCode": "XPF",
        "countryFlag": "🇵🇫",
        "countryName": "French Polynesia",
        "dialCode": "+689",
        "iso3": "PYF",
        "numericCode": "258"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "TF",
        "currencyCode": "EUR",
        "countryFlag": "🇹🇫",
        "countryName": "French Southern Territories",
        "dialCode": "+262",
        "iso3": "ATF",
        "numericCode": "260",
        "note": "The French Southern Territories use the Euro (EUR) as their official currency. The islands are uninhabited except for research stations."
    },
    {
        "currencyName": "Central African CFA Franc",
        "currencySymbol": "FCFA",
        "countryNameCode": "GA",
        "currencyCode": "XAF",
        "countryFlag": "🇬🇦",
        "countryName": "Gabon",
        "dialCode": "+241",
        "iso3": "GAB",
        "numericCode": "266"
    },
    {
        "currencyName": "Gambian Dalasi",
        "currencySymbol": "D",
        "countryNameCode": "GM",
        "currencyCode": "GMD",
        "countryFlag": "🇬🇲",
        "countryName": "Gambia",
        "dialCode": "+220",
        "iso3": "GMB",
        "numericCode": "270"
    },
    {
        "currencyName": "Georgian Lari",
        "currencySymbol": "₾",
        "countryNameCode": "GE",
        "currencyCode": "GEL",
        "countryFlag": "🇬🇪",
        "countryName": "Georgia",
        "dialCode": "+995",
        "iso3": "GEO",
        "numericCode": "268"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "DE",
        "currencyCode": "EUR",
        "countryFlag": "🇩🇪",
        "countryName": "Germany",
        "dialCode": "+49",
        "iso3": "DEU",
        "numericCode": "276"
    },
    {
        "currencyName": "Ghanaian Cedi",
        "currencySymbol": "GH₵",
        "countryNameCode": "GH",
        "currencyCode": "GHS",
        "countryFlag": "🇬🇭",
        "countryName": "Ghana",
        "dialCode": "+233",
        "iso3": "GHA",
        "numericCode": "288"
    },
    {
        "currencyName": "Gibraltar Pound",
        "currencySymbol": "£",
        "countryNameCode": "GI",
        "currencyCode": "GIP",
        "countryFlag": "🇬🇮",
        "countryName": "Gibraltar",
        "dialCode": "+350",
        "iso3": "GIB",
        "numericCode": "292"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "GR",
        "currencyCode": "EUR",
        "countryFlag": "🇬🇷",
        "countryName": "Greece",
        "dialCode": "+30",
        "iso3": "GRC",
        "numericCode": "300"
    },
    {
        "currencyName": "Danish Krone",
        "currencySymbol": "kr",
        "countryNameCode": "GL",
        "currencyCode": "DKK",
        "countryFlag": "🇬🇱",
        "countryName": "Greenland",
        "dialCode": "+299",
        "iso3": "GRL",
        "numericCode": "304",
        "note": "Greenland is an autonomous territory of Denmark and uses the Danish Krone (DKK) as its official currency."
    },
    {
        "currencyName": "East Caribbean Dollar",
        "currencySymbol": "$",
        "countryNameCode": "GD",
        "currencyCode": "XCD",
        "countryFlag": "🇬🇩",
        "countryName": "Grenada",
        "dialCode": "+1473",
        "iso3": "GRD",
        "numericCode": "308"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "GP",
        "currencyCode": "EUR",
        "countryFlag": "🇬🇵",
        "countryName": "Guadeloupe",
        "dialCode": "+590",
        "iso3": "GLP",
        "numericCode": "312"
    },
    {
        "currencyName": "United States Dollar",
        "currencySymbol": "$",
        "countryNameCode": "GU",
        "currencyCode": "USD",
        "countryFlag": "🇬🇺",
        "countryName": "Guam",
        "dialCode": "+1671",
        "iso3": "GUM",
        "numericCode": "316"
    },
    {
        "currencyName": "Guatemalan Quetzal",
        "currencySymbol": "Q",
        "countryNameCode": "GT",
        "currencyCode": "GTQ",
        "countryFlag": "🇬🇹",
        "countryName": "Guatemala",
        "dialCode": "+502",
        "iso3": "GTM",
        "numericCode": "320"
    },
    {
        "currencyName": "Guinean Franc",
        "currencySymbol": "FG",
        "countryNameCode": "GN",
        "currencyCode": "GNF",
        "countryFlag": "🇬🇳",
        "countryName": "Guinea",
        "dialCode": "+224",
        "iso3": "GIN",
        "numericCode": "324"
    },
    {
        "currencyName": "West African CFA Franc",
        "currencySymbol": "CFA",
        "countryNameCode": "GW",
        "currencyCode": "XOF",
        "countryFlag": "🇬🇼",
        "countryName": "Guinea-Bissau",
        "dialCode": "+245",
        "iso3": "GNB",
        "numericCode": "624"
    },
    {
        "currencyName": "Guyanese Dollar",
        "currencySymbol": "$",
        "countryNameCode": "GY",
        "currencyCode": "GYD",
        "countryFlag": "🇬🇾",
        "countryName": "Guyana",
        "dialCode": "+592",
        "iso3": "GUY",
        "numericCode": "328"
    },
    {
        "currencyName": "Haitian Gourde",
        "currencySymbol": "G",
        "countryNameCode": "HT",
        "currencyCode": "HTG",
        "countryFlag": "🇭🇹",
        "countryName": "Haiti",
        "dialCode": "+509",
        "iso3": "HTI",
        "numericCode": "332"
    },
    {
        "currencyName": "Australian Dollar",
        "currencySymbol": "A$",
        "countryNameCode": "HM",
        "currencyCode": "AUD",
        "countryFlag": "🇭🇲",
        "countryName": "Heard Island and McDonald Islands",
        "dialCode": "+672",
        "iso3": "HMD",
        "numericCode": "334",
        "note": "Heard Island and McDonald Islands are uninhabited Australian external territories and use the Australian Dollar (AUD)."
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "VA",
        "currencyCode": "EUR",
        "countryFlag": "🇻🇦",
        "countryName": "Holy See (Vatican City State)",
        "dialCode": "+379",
        "iso3": "VAT",
        "numericCode": "336"
    },
    {
        "currencyName": "Honduran Lempira",
        "currencySymbol": "L",
        "countryNameCode": "HN",
        "currencyCode": "HNL",
        "countryFlag": "🇭🇳",
        "countryName": "Honduras",
        "dialCode": "+504",
        "iso3": "HND",
        "numericCode": "340"
    },
    {
        "currencyName": "Hong Kong Dollar",
        "currencySymbol": "HK$",
        "countryNameCode": "HK",
        "currencyCode": "HKD",
        "countryFlag": "🇭🇰",
        "countryName": "Hong Kong",
        "dialCode": "+852",
        "iso3": "HKG",
        "numericCode": "344"
    },
    {
        "currencyName": "Hungarian Forint",
        "currencySymbol": "Ft",
        "countryNameCode": "HU",
        "currencyCode": "HUF",
        "countryFlag": "🇭🇺",
        "countryName": "Hungary",
        "dialCode": "+36",
        "iso3": "HUN",
        "numericCode": "348"
    },
    {
        "currencyName": "Icelandic Króna",
        "currencySymbol": "kr",
        "countryNameCode": "IS",
        "currencyCode": "ISK",
        "countryFlag": "🇮🇸",
        "countryName": "Iceland",
        "dialCode": "+354",
        "iso3": "ISL",
        "numericCode": "352"
    },
    {
        "currencyName": "Indian Rupee",
        "currencySymbol": "₹",
        "countryNameCode": "IN",
        "currencyCode": "INR",
        "countryFlag": "🇮🇳",
        "countryName": "India",
        "dialCode": "+91",
        "iso3": "IND",
        "numericCode": "356"
    },
    {
        "currencyName": "Indonesian Rupiah",
        "currencySymbol": "Rp",
        "countryNameCode": "ID",
        "currencyCode": "IDR",
        "countryFlag": "🇮🇩",
        "countryName": "Indonesia",
        "dialCode": "+62",
        "iso3": "IDN",
        "numericCode": "360"
    },
    {
        "currencyName": "Iranian Rial",
        "currencySymbol": "﷼",
        "countryNameCode": "IR",
        "currencyCode": "IRR",
        "countryFlag": "🇮🇷",
        "countryName": "Iran",
        "dialCode": "+98",
        "iso3": "IRN",
        "numericCode": "364"
    },
    {
        "currencyName": "Iraqi Dinar",
        "currencySymbol": "ع.د",
        "countryNameCode": "IQ",
        "currencyCode": "IQD",
        "countryFlag": "🇮🇶",
        "countryName": "Iraq",
        "dialCode": "+964",
        "iso3": "IRQ",
        "numericCode": "368"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "IE",
        "currencyCode": "EUR",
        "countryFlag": "🇮🇪",
        "countryName": "Ireland",
        "dialCode": "+353",
        "iso3": "IRL",
        "numericCode": "372"
    },
    {
        "currencyName": "British Pound",
        "currencySymbol": "£",
        "countryNameCode": "IM",
        "currencyCode": "GBP",
        "countryFlag": "🇮🇲",
        "countryName": "Isle of Man",
        "dialCode": "+44",
        "iso3": "IMN",
        "numericCode": "833"
    },
    {
        "currencyName": "Israeli New Shekel",
        "currencySymbol": "₪",
        "countryNameCode": "IL",
        "currencyCode": "ILS",
        "countryFlag": "🇮🇱",
        "countryName": "Israel",
        "dialCode": "+972",
        "iso3": "ISR",
        "numericCode": "376"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "IT",
        "currencyCode": "EUR",
        "countryFlag": "🇮🇹",
        "countryName": "Italy",
        "dialCode": "+39",
        "iso3": "ITA",
        "numericCode": "380"
    },
    {
        "currencyName": "Jamaican Dollar",
        "currencySymbol": "J$",
        "countryNameCode": "JM",
        "currencyCode": "JMD",
        "countryFlag": "🇯🇲",
        "countryName": "Jamaica",
        "dialCode": "+1876",
        "iso3": "JAM",
        "numericCode": "388"
    },
    {
        "currencyName": "Japanese Yen",
        "currencySymbol": "¥",
        "countryNameCode": "JP",
        "currencyCode": "JPY",
        "countryFlag": "🇯🇵",
        "countryName": "Japan",
        "dialCode": "+81",
        "iso3": "JPN",
        "numericCode": "392"
    },
    {
        "currencyName": "British Pound",
        "currencySymbol": "£",
        "countryNameCode": "JE",
        "currencyCode": "GBP",
        "countryFlag": "🇯🇪",
        "countryName": "Jersey",
        "dialCode": "+44",
        "iso3": "JEY",
        "numericCode": "832"
    },
    {
        "currencyName": "Jordanian Dinar",
        "currencySymbol": "د.ا",
        "countryNameCode": "JO",
        "currencyCode": "JOD",
        "countryFlag": "🇯🇴",
        "countryName": "Jordan",
        "dialCode": "+962",
        "iso3": "JOR",
        "numericCode": "400"
    },
    {
        "currencyName": "Kazakhstani Tenge",
        "currencySymbol": "₸",
        "countryNameCode": "KZ",
        "currencyCode": "KZT",
        "countryFlag": "🇰🇿",
        "countryName": "Kazakhstan",
        "dialCode": "+7",
        "iso3": "KAZ",
        "numericCode": "398"
    },
    {
        "currencyName": "Kenyan Shilling",
        "currencySymbol": "KSh",
        "countryNameCode": "KE",
        "currencyCode": "KES",
        "countryFlag": "🇰🇪",
        "countryName": "Kenya",
        "dialCode": "+254",
        "iso3": "KEN",
        "numericCode": "404"
    },
    {
        "currencyName": "Australian Dollar",
        "currencySymbol": "A$",
        "countryNameCode": "KI",
        "currencyCode": "AUD",
        "countryFlag": "🇰🇮",
        "countryName": "Kiribati",
        "dialCode": "+686",
        "iso3": "KIR",
        "numericCode": "296"
    },
    {
        "currencyName": "North Korean Won",
        "currencySymbol": "₩",
        "countryNameCode": "KP",
        "currencyCode": "KPW",
        "countryFlag": "🇰🇵",
        "countryName": "Korea, Democratic People's Republic of (North Korea)",
        "dialCode": "+850",
        "iso3": "PRK",
        "numericCode": "408"
    },
    {
        "currencyName": "South Korean Won",
        "currencySymbol": "₩",
        "countryNameCode": "KR",
        "currencyCode": "KRW",
        "countryFlag": "🇰🇷",
        "countryName": "Korea, Republic of (South Korea)",
        "dialCode": "+82",
        "iso3": "KOR",
        "numericCode": "410"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "XK",
        "currencyCode": "EUR",
        "countryFlag": "🇽🇰",
        "countryName": "Kosovo",
        "dialCode": "+383",
        "iso3": "XKX",
        "numericCode": "0",
        "note": "Kosovo unilaterally adopted the Euro (EUR) as its official currency, even though it is not an official member of the Eurozone."
    },
    {
        "currencyName": "Kuwaiti Dinar",
        "currencySymbol": "د.ك",
        "countryNameCode": "KW",
        "currencyCode": "KWD",
        "countryFlag": "🇰🇼",
        "countryName": "Kuwait",
        "dialCode": "+965",
        "iso3": "KWT",
        "numericCode": "414"
    },
    {
        "currencyName": "Kyrgyzstani Som",
        "currencySymbol": "с",
        "countryNameCode": "KG",
        "currencyCode": "KGS",
        "countryFlag": "🇰🇬",
        "countryName": "Kyrgyzstan",
        "dialCode": "+996",
        "iso3": "KGZ",
        "numericCode": "417"
    },
    {
        "currencyName": "Lao Kip",
        "currencySymbol": "₭",
        "countryNameCode": "LA",
        "currencyCode": "LAK",
        "countryFlag": "🇱🇦",
        "countryName": "Laos",
        "dialCode": "+856",
        "iso3": "LAO",
        "numericCode": "418"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "LV",
        "currencyCode": "EUR",
        "countryFlag": "🇱🇻",
        "countryName": "Latvia",
        "dialCode": "+371",
        "iso3": "LVA",
        "numericCode": "428"
    },
    {
        "currencyName": "Lebanese Pound",
        "currencySymbol": "ل.ل",
        "countryNameCode": "LB",
        "currencyCode": "LBP",
        "countryFlag": "🇱🇧",
        "countryName": "Lebanon",
        "dialCode": "+961",
        "iso3": "LBN",
        "numericCode": "422"
    },
    {
        "currencyName": "Lesotho Loti",
        "currencySymbol": "L",
        "countryNameCode": "LS",
        "currencyCode": "LSL",
        "countryFlag": "🇱🇸",
        "countryName": "Lesotho",
        "dialCode": "+266",
        "iso3": "LSO",
        "numericCode": "426"
    },
    {
        "currencyName": "Liberian Dollar",
        "currencySymbol": "$",
        "countryNameCode": "LR",
        "currencyCode": "LRD",
        "countryFlag": "🇱🇷",
        "countryName": "Liberia",
        "dialCode": "+231",
        "iso3": "LBR",
        "numericCode": "430"
    },
    {
        "currencyName": "Libyan Dinar",
        "currencySymbol": "ل.د",
        "countryNameCode": "LY",
        "currencyCode": "LYD",
        "countryFlag": "🇱🇾",
        "countryName": "Libya",
        "dialCode": "+218",
        "iso3": "LBY",
        "numericCode": "434"
    },
    {
        "currencyName": "Swiss Franc",
        "currencySymbol": "CHF",
        "countryNameCode": "LI",
        "currencyCode": "CHF",
        "countryFlag": "🇱🇮",
        "countryName": "Liechtenstein",
        "dialCode": "+423",
        "iso3": "LIE",
        "numericCode": "438",
        "note": "Liechtenstein uses the Swiss Franc (CHF) as its official currency."
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "LT",
        "currencyCode": "EUR",
        "countryFlag": "🇱🇹",
        "countryName": "Lithuania",
        "dialCode": "+370",
        "iso3": "LTU",
        "numericCode": "440"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "LU",
        "currencyCode": "EUR",
        "countryFlag": "🇱🇺",
        "countryName": "Luxembourg",
        "dialCode": "+352",
        "iso3": "LUX",
        "numericCode": "442"
    },
    {
        "currencyName": "Macanese Pataca",
        "currencySymbol": "MOP$",
        "countryNameCode": "MO",
        "currencyCode": "MOP",
        "countryFlag": "🇲🇴",
        "countryName": "Macao",
        "dialCode": "+853",
        "iso3": "MAC",
        "numericCode": "446"
    },
    {
        "currencyName": "Macedonian Denar",
        "currencySymbol": "ден",
        "countryNameCode": "MK",
        "currencyCode": "MKD",
        "countryFlag": "🇲🇰",
        "countryName": "North Macedonia",
        "dialCode": "+389",
        "iso3": "MKD",
        "numericCode": "807"
    },
    {
        "currencyName": "Malagasy Ariary",
        "currencySymbol": "Ar",
        "countryNameCode": "MG",
        "currencyCode": "MGA",
        "countryFlag": "🇲🇬",
        "countryName": "Madagascar",
        "dialCode": "+261",
        "iso3": "MDG",
        "numericCode": "450"
    },
    {
        "currencyName": "Malawian Kwacha",
        "currencySymbol": "MK",
        "countryNameCode": "MW",
        "currencyCode": "MWK",
        "countryFlag": "🇲🇼",
        "countryName": "Malawi",
        "dialCode": "+265",
        "iso3": "MWI",
        "numericCode": "454"
    },
    {
        "currencyName": "Malaysian Ringgit",
        "currencySymbol": "RM",
        "countryNameCode": "MY",
        "currencyCode": "MYR",
        "countryFlag": "🇲🇾",
        "countryName": "Malaysia",
        "dialCode": "+60",
        "iso3": "MYS",
        "numericCode": "458"
    },
    {
        "currencyName": "Maldivian Rufiyaa",
        "currencySymbol": "Rf",
        "countryNameCode": "MV",
        "currencyCode": "MVR",
        "countryFlag": "🇲🇻",
        "countryName": "Maldives",
        "dialCode": "+960",
        "iso3": "MDV",
        "numericCode": "462"
    },
    {
        "currencyName": "West African CFA Franc",
        "currencySymbol": "CFA",
        "countryNameCode": "ML",
        "currencyCode": "XOF",
        "countryFlag": "🇲🇱",
        "countryName": "Mali",
        "dialCode": "+223",
        "iso3": "MLI",
        "numericCode": "466"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "MT",
        "currencyCode": "EUR",
        "countryFlag": "🇲🇹",
        "countryName": "Malta",
        "dialCode": "+356",
        "iso3": "MLT",
        "numericCode": "470"
    },

    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "MQ",
        "currencyCode": "EUR",
        "countryFlag": "🇲🇶",
        "countryName": "Martinique",
        "dialCode": "+596",
        "iso3": "MTQ",
        "numericCode": "474"
    },
    {
        "currencyName": "Mauritanian Ouguiya",
        "currencySymbol": "UM",
        "countryNameCode": "MR",
        "currencyCode": "MRU",
        "countryFlag": "🇲🇷",
        "countryName": "Mauritania",
        "dialCode": "+222",
        "iso3": "MRT",
        "numericCode": "478"
    },
    {
        "currencyName": "Mauritian Rupee",
        "currencySymbol": "₨",
        "countryNameCode": "MU",
        "currencyCode": "MUR",
        "countryFlag": "🇲🇺",
        "countryName": "Mauritius",
        "dialCode": "+230",
        "iso3": "MUS",
        "numericCode": "480"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "YT",
        "currencyCode": "EUR",
        "countryFlag": "🇾🇹",
        "countryName": "Mayotte",
        "dialCode": "+262",
        "iso3": "MYT",
        "numericCode": "175"
    },
    {
        "currencyName": "Mexican Peso",
        "currencySymbol": "MX$",
        "countryNameCode": "MX",
        "currencyCode": "MXN",
        "countryFlag": "🇲🇽",
        "countryName": "Mexico",
        "dialCode": "+52",
        "iso3": "MEX",
        "numericCode": "484"
    },
    {
        "currencyName": "United States Dollar",
        "currencySymbol": "$",
        "countryNameCode": "FM",
        "currencyCode": "USD",
        "countryFlag": "🇫🇲",
        "countryName": "Micronesia, Federated States of",
        "dialCode": "+691",
        "iso3": "FSM",
        "numericCode": "583"
    },
    {
        "currencyName": "Moldovan Leu",
        "currencySymbol": "L",
        "countryNameCode": "MD",
        "currencyCode": "MDL",
        "countryFlag": "🇲🇩",
        "countryName": "Moldova",
        "dialCode": "+373",
        "iso3": "MDA",
        "numericCode": "498"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "MC",
        "currencyCode": "EUR",
        "countryFlag": "🇲🇨",
        "countryName": "Monaco",
        "dialCode": "+377",
        "iso3": "MCO",
        "numericCode": "492"
    },
    {
        "currencyName": "Mongolian Tögrög",
        "currencySymbol": "₮",
        "countryNameCode": "MN",
        "currencyCode": "MNT",
        "countryFlag": "🇲🇳",
        "countryName": "Mongolia",
        "dialCode": "+976",
        "iso3": "MNG",
        "numericCode": "496"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "ME",
        "currencyCode": "EUR",
        "countryFlag": "🇲🇪",
        "countryName": "Montenegro",
        "dialCode": "+382",
        "iso3": "MNE",
        "numericCode": "499",
        "note": "Montenegro uses the Euro (EUR) as its official currency, although it is not a member of the Eurozone."
    },
    {
        "currencyName": "East Caribbean Dollar",
        "currencySymbol": "$",
        "countryNameCode": "MS",
        "currencyCode": "XCD",
        "countryFlag": "🇲🇸",
        "countryName": "Montserrat",
        "dialCode": "+1664",
        "iso3": "MSR",
        "numericCode": "500"
    },
    {
        "currencyName": "Moroccan Dirham",
        "currencySymbol": "MAD",
        "countryNameCode": "MA",
        "currencyCode": "MAD",
        "countryFlag": "🇲🇦",
        "countryName": "Morocco",
        "dialCode": "+212",
        "iso3": "MAR",
        "numericCode": "504"
    },
    {
        "currencyName": "Mozambican Metical",
        "currencySymbol": "MT",
        "countryNameCode": "MZ",
        "currencyCode": "MZN",
        "countryFlag": "🇲🇿",
        "countryName": "Mozambique",
        "dialCode": "+258",
        "iso3": "MOZ",
        "numericCode": "508"
    },
    {
        "currencyName": "Myanma Kyat",
        "currencySymbol": "K",
        "countryNameCode": "MM",
        "currencyCode": "MMK",
        "countryFlag": "🇲🇲",
        "countryName": "Myanmar",
        "dialCode": "+95",
        "iso3": "MMR",
        "numericCode": "104"
    },
    {
        "currencyName": "Namibian Dollar",
        "currencySymbol": "$",
        "countryNameCode": "NA",
        "currencyCode": "NAD",
        "countryFlag": "🇳🇦",
        "countryName": "Namibia",
        "dialCode": "+264",
        "iso3": "NAM",
        "numericCode": "516"
    },
    {
        "currencyName": "Australian Dollar",
        "currencySymbol": "A$",
        "countryNameCode": "NR",
        "currencyCode": "AUD",
        "countryFlag": "🇳🇷",
        "countryName": "Nauru",
        "dialCode": "+674",
        "iso3": "NRU",
        "numericCode": "520"
    },
    {
        "currencyName": "Nepalese Rupee",
        "currencySymbol": "₨",
        "countryNameCode": "NP",
        "currencyCode": "NPR",
        "countryFlag": "🇳🇵",
        "countryName": "Nepal",
        "dialCode": "+977",
        "iso3": "NPL",
        "numericCode": "524"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "NL",
        "currencyCode": "EUR",
        "countryFlag": "🇳🇱",
        "countryName": "Netherlands",
        "dialCode": "+31",
        "iso3": "NLD",
        "numericCode": "528"
    },
    {
        "currencyName": "CFP Franc",
        "currencySymbol": "₣",
        "countryNameCode": "NC",
        "currencyCode": "XPF",
        "countryFlag": "🇳🇨",
        "countryName": "New Caledonia",
        "dialCode": "+687",
        "iso3": "NCL",
        "numericCode": "540"
    },
    {
        "currencyName": "New Zealand Dollar",
        "currencySymbol": "NZ$",
        "countryNameCode": "NZ",
        "currencyCode": "NZD",
        "countryFlag": "🇳🇿",
        "countryName": "New Zealand",
        "dialCode": "+64",
        "iso3": "NZL",
        "numericCode": "554"
    },
    {
        "currencyName": "Nicaraguan Córdoba",
        "currencySymbol": "C$",
        "countryNameCode": "NI",
        "currencyCode": "NIO",
        "countryFlag": "🇳🇮",
        "countryName": "Nicaragua",
        "dialCode": "+505",
        "iso3": "NIC",
        "numericCode": "558"
    },
    {
        "currencyName": "West African CFA Franc",
        "currencySymbol": "CFA",
        "countryNameCode": "NE",
        "currencyCode": "XOF",
        "countryFlag": "🇳🇪",
        "countryName": "Niger",
        "dialCode": "+227",
        "iso3": "NER",
        "numericCode": "562"
    },
    {
        "currencyName": "Nigerian Naira",
        "currencySymbol": "₦",
        "countryNameCode": "NG",
        "currencyCode": "NGN",
        "countryFlag": "🇳🇬",
        "countryName": "Nigeria",
        "dialCode": "+234",
        "iso3": "NGA",
        "numericCode": "566"
    },
    {
        "currencyName": "New Zealand Dollar",
        "currencySymbol": "NZ$",
        "countryNameCode": "NU",
        "currencyCode": "NZD",
        "countryFlag": "🇳🇺",
        "countryName": "Niue",
        "dialCode": "+683",
        "iso3": "NIU",
        "numericCode": "570",
        "note": "Niue uses the New Zealand Dollar (NZD) as its official currency."
    },
    {
        "currencyName": "Australian Dollar",
        "currencySymbol": "A$",
        "countryNameCode": "NF",
        "currencyCode": "AUD",
        "countryFlag": "🇳🇫",
        "countryName": "Norfolk Island",
        "dialCode": "+672",
        "iso3": "NFK",
        "numericCode": "574"
    },
    {
        "currencyName": "United States Dollar",
        "currencySymbol": "$",
        "countryNameCode": "MP",
        "currencyCode": "USD",
        "countryFlag": "🇲🇵",
        "countryName": "Northern Mariana Islands",
        "dialCode": "+1670",
        "iso3": "MNP",
        "numericCode": "580"
    },
    {
        "currencyName": "North Korean Won",
        "currencySymbol": "₩",
        "countryNameCode": "KP",
        "currencyCode": "KPW",
        "countryFlag": "🇰🇵",
        "countryName": "North Korea (Democratic People's Republic of Korea)",
        "dialCode": "+850",
        "iso3": "PRK",
        "numericCode": "408"
    },
    {
        "currencyName": "Norwegian Krone",
        "currencySymbol": "kr",
        "countryNameCode": "NO",
        "currencyCode": "NOK",
        "countryFlag": "🇳🇴",
        "countryName": "Norway",
        "dialCode": "+47",
        "iso3": "NOR",
        "numericCode": "578"
    },
    {
        "currencyName": "Omani Rial",
        "currencySymbol": "﷼",
        "countryNameCode": "OM",
        "currencyCode": "OMR",
        "countryFlag": "🇴🇲",
        "countryName": "Oman",
        "dialCode": "+968",
        "iso3": "OMN",
        "numericCode": "512"
    },
    {
        "currencyName": "Pakistani Rupee",
        "currencySymbol": "₨",
        "countryNameCode": "PK",
        "currencyCode": "PKR",
        "countryFlag": "🇵🇰",
        "countryName": "Pakistan",
        "dialCode": "+92",
        "iso3": "PAK",
        "numericCode": "586"
    },
    {
        "currencyName": "United States Dollar",
        "currencySymbol": "$",
        "countryNameCode": "PW",
        "currencyCode": "USD",
        "countryFlag": "🇵🇼",
        "countryName": "Palau",
        "dialCode": "+680",
        "iso3": "PLW",
        "numericCode": "585"
    },
    {
        "currencyName": "Israeli New Shekel",
        "currencySymbol": "₪",
        "countryNameCode": "PS",
        "currencyCode": "ILS",
        "countryFlag": "🇵🇸",
        "countryName": "Palestinian Territory, Occupied",
        "dialCode": "+970",
        "iso3": "PSE",
        "numericCode": "275",
        "note": "The Israeli New Shekel (ILS) is widely used in the Palestinian territories, though the Jordanian Dinar (JOD) and United States Dollar (USD) are also commonly accepted."
    },
    {
        "currencyName": "Panamanian Balboa",
        "currencySymbol": "B/.",
        "countryNameCode": "PA",
        "currencyCode": "PAB",
        "countryFlag": "🇵🇦",
        "countryName": "Panama",
        "dialCode": "+507",
        "iso3": "PAN",
        "numericCode": "591"
    },
    {
        "currencyName": "Papua New Guinean Kina",
        "currencySymbol": "K",
        "countryNameCode": "PG",
        "currencyCode": "PGK",
        "countryFlag": "🇵🇬",
        "countryName": "Papua New Guinea",
        "dialCode": "+675",
        "iso3": "PNG",
        "numericCode": "598"
    },
    {
        "currencyName": "Paraguayan Guarani",
        "currencySymbol": "₲",
        "countryNameCode": "PY",
        "currencyCode": "PYG",
        "countryFlag": "🇵🇾",
        "countryName": "Paraguay",
        "dialCode": "+595",
        "iso3": "PRY",
        "numericCode": "600"
    },
    {
        "currencyName": "Peruvian Sol",
        "currencySymbol": "S/",
        "countryNameCode": "PE",
        "currencyCode": "PEN",
        "countryFlag": "🇵🇪",
        "countryName": "Peru",
        "dialCode": "+51",
        "iso3": "PER",
        "numericCode": "604"
    },
    {
        "currencyName": "Philippine Peso",
        "currencySymbol": "₱",
        "countryNameCode": "PH",
        "currencyCode": "PHP",
        "countryFlag": "🇵🇭",
        "countryName": "Philippines",
        "dialCode": "+63",
        "iso3": "PHL",
        "numericCode": "608"
    },
    {
        "currencyName": "Polish Złoty",
        "currencySymbol": "zł",
        "countryNameCode": "PL",
        "currencyCode": "PLN",
        "countryFlag": "🇵🇱",
        "countryName": "Poland",
        "dialCode": "+48",
        "iso3": "POL",
        "numericCode": "616"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "PT",
        "currencyCode": "EUR",
        "countryFlag": "🇵🇹",
        "countryName": "Portugal",
        "dialCode": "+351",
        "iso3": "PRT",
        "numericCode": "620"
    },
    {
        "currencyName": "United States Dollar",
        "currencySymbol": "$",
        "countryNameCode": "PR",
        "currencyCode": "USD",
        "countryFlag": "🇵🇷",
        "countryName": "Puerto Rico",
        "dialCode": "+1787",
        "iso3": "PRI",
        "numericCode": "630"
    },
    {
        "currencyName": "Qatari Riyal",
        "currencySymbol": "﷼",
        "countryNameCode": "QA",
        "currencyCode": "QAR",
        "countryFlag": "🇶🇦",
        "countryName": "Qatar",
        "dialCode": "+974",
        "iso3": "QAT",
        "numericCode": "634"
    },
    {
        "currencyName": "Romanian Leu",
        "currencySymbol": "lei",
        "countryNameCode": "RO",
        "currencyCode": "RON",
        "countryFlag": "🇷🇴",
        "countryName": "Romania",
        "dialCode": "+40",
        "iso3": "ROU",
        "numericCode": "642"
    },
    {
        "currencyName": "Russian Ruble",
        "currencySymbol": "₽",
        "countryNameCode": "RU",
        "currencyCode": "RUB",
        "countryFlag": "🇷🇺",
        "countryName": "Russia",
        "dialCode": "+7",
        "iso3": "RUS",
        "numericCode": "643"
    },
    {
        "currencyName": "Rwandan Franc",
        "currencySymbol": "FRw",
        "countryNameCode": "RW",
        "currencyCode": "RWF",
        "countryFlag": "🇷🇼",
        "countryName": "Rwanda",
        "dialCode": "+250",
        "iso3": "RWA",
        "numericCode": "646"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "RE",
        "currencyCode": "EUR",
        "countryFlag": "🇷🇪",
        "countryName": "Réunion",
        "dialCode": "+262",
        "iso3": "REU",
        "numericCode": "638"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "BL",
        "currencyCode": "EUR",
        "countryFlag": "🇧🇱",
        "countryName": "Saint Barthélemy",
        "dialCode": "+590",
        "iso3": "BLM",
        "numericCode": "652"
    },
    {
        "currencyName": "Saint Helena Pound",
        "currencySymbol": "£",
        "countryNameCode": "SH",
        "currencyCode": "SHP",
        "countryFlag": "🇸🇭",
        "countryName": "Saint Helena, Ascension and Tristan da Cunha",
        "dialCode": "+290",
        "iso3": "SHN",
        "numericCode": "654",
        "note": "The Saint Helena Pound (SHP) is used, and it is pegged at par with the British Pound (GBP). The British Pound is also widely accepted."
    },
    {
        "currencyName": "East Caribbean Dollar",
        "currencySymbol": "$",
        "countryNameCode": "KN",
        "currencyCode": "XCD",
        "countryFlag": "🇰🇳",
        "countryName": "Saint Kitts and Nevis",
        "dialCode": "+1869",
        "iso3": "KNA",
        "numericCode": "659"
    },
    {
        "currencyName": "East Caribbean Dollar",
        "currencySymbol": "$",
        "countryNameCode": "LC",
        "currencyCode": "XCD",
        "countryFlag": "🇱🇨",
        "countryName": "Saint Lucia",
        "dialCode": "+1758",
        "iso3": "LCA",
        "numericCode": "662"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "MF",
        "currencyCode": "EUR",
        "countryFlag": "🇲🇫",
        "countryName": "Saint Martin",
        "dialCode": "+590",
        "iso3": "MAF",
        "numericCode": "663"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "PM",
        "currencyCode": "EUR",
        "countryFlag": "🇵🇲",
        "countryName": "Saint Pierre and Miquelon",
        "dialCode": "+508",
        "iso3": "SPM",
        "numericCode": "666"
    },
    {
        "currencyName": "East Caribbean Dollar",
        "currencySymbol": "$",
        "countryNameCode": "VC",
        "currencyCode": "XCD",
        "countryFlag": "🇻🇨",
        "countryName": "Saint Vincent and the Grenadines",
        "dialCode": "+1784",
        "iso3": "VCT",
        "numericCode": "670"
    },
    {
        "currencyName": "Samoan Tala",
        "currencySymbol": "WS$",
        "countryNameCode": "WS",
        "currencyCode": "WST",
        "countryFlag": "🇼🇸",
        "countryName": "Samoa",
        "dialCode": "+685",
        "iso3": "WSM",
        "numericCode": "882"
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "SM",
        "currencyCode": "EUR",
        "countryFlag": "🇸🇲",
        "countryName": "San Marino",
        "dialCode": "+378",
        "iso3": "SMR",
        "numericCode": "674"
    },
    {
        "currencyName": "São Tomé and Príncipe Dobra",
        "currencySymbol": "Db",
        "countryNameCode": "ST",
        "currencyCode": "STN",
        "countryFlag": "🇸🇹",
        "countryName": "São Tomé and Príncipe",
        "dialCode": "+239",
        "iso3": "STP",
        "numericCode": "678"
    },
    {
        "currencyName": "Saudi Riyal",
        "currencySymbol": "﷼",
        "countryNameCode": "SA",
        "currencyCode": "SAR",
        "countryFlag": "🇸🇦",
        "countryName": "Saudi Arabia",
        "dialCode": "+966",
        "iso3": "SAU",
        "numericCode": "682"
    },
    {
        "currencyName": "Senegalese Franc",
        "currencySymbol": "CFA",
        "countryNameCode": "SN",
        "currencyCode": "XOF",
        "countryFlag": "🇸🇳",
        "countryName": "Senegal",
        "dialCode": "+221",
        "iso3": "SEN",
        "numericCode": "686"
    },
    {
        "currencyName": "Serbian Dinar",
        "currencySymbol": "дин.",
        "countryNameCode": "RS",
        "currencyCode": "RSD",
        "countryFlag": "🇷🇸",
        "countryName": "Serbia",
        "dialCode": "+381",
        "iso3": "SRB",
        "numericCode": "688"
    },
    {
        "currencyName": "Seychellois Rupee",
        "currencySymbol": "₨",
        "countryNameCode": "SC",
        "currencyCode": "SCR",
        "countryFlag": "🇸🇨",
        "countryName": "Seychelles",
        "dialCode": "+248",
        "iso3": "SYC",
        "numericCode": "690"
    },
    {
        "currencyName": "Sierra Leonean Leone",
        "currencySymbol": "Le",
        "countryNameCode": "SL",
        "currencyCode": "SLL",
        "countryFlag": "🇸🇱",
        "countryName": "Sierra Leone",
        "dialCode": "+232",
        "iso3": "SLE",
        "numericCode": "694"
    },
    {
        "currencyName": "Singapore Dollar",
        "currencySymbol": "S$",
        "countryNameCode": "SG",
        "currencyCode": "SGD",
        "countryFlag": "🇸🇬",
        "countryName": "Singapore",
        "dialCode": "+65",
        "iso3": "SGP",
        "numericCode": "702"
    },
    {
        "currencyName": "Slovak Euro",
        "currencySymbol": "€",
        "countryNameCode": "SK",
        "currencyCode": "EUR",
        "countryFlag": "🇸🇰",
        "countryName": "Slovakia",
        "dialCode": "+421",
        "iso3": "SVK",
        "numericCode": "703"
    },
    {
        "currencyName": "Slovenian Euro",
        "currencySymbol": "€",
        "countryNameCode": "SI",
        "currencyCode": "EUR",
        "countryFlag": "🇸🇮",
        "countryName": "Slovenia",
        "dialCode": "+386",
        "iso3": "SVN",
        "numericCode": "705"
    },
    {
        "currencyName": "Solomon Islands Dollar",
        "currencySymbol": "SI$",
        "countryNameCode": "SB",
        "currencyCode": "SBD",
        "countryFlag": "🇸🇧",
        "countryName": "Solomon Islands",
        "dialCode": "+677",
        "iso3": "SLB",
        "numericCode": "090"
    },
    {
        "currencyName": "Somali Shilling",
        "currencySymbol": "Sh.So.",
        "countryNameCode": "SO",
        "currencyCode": "SOS",
        "countryFlag": "🇸🇴",
        "countryName": "Somalia",
        "dialCode": "+252",
        "iso3": "SOM",
        "numericCode": "706"
    },
    {
        "currencyName": "South African Rand",
        "currencySymbol": "R",
        "countryNameCode": "ZA",
        "currencyCode": "ZAR",
        "countryFlag": "🇿🇦",
        "countryName": "South Africa",
        "dialCode": "+27",
        "iso3": "ZAF",
        "numericCode": "710"
    },
    {
        "currencyName": "South Korean Won",
        "currencySymbol": "₩",
        "countryNameCode": "KR",
        "currencyCode": "KRW",
        "countryFlag": "🇰🇷",
        "countryName": "South Korea",
        "dialCode": "+82",
        "iso3": "KOR",
        "numericCode": "410"
    },
    {
        "currencyName": "South Sudanese Pound",
        "currencySymbol": "£",
        "countryNameCode": "SS",
        "currencyCode": "SSP",
        "countryFlag": "🇸🇸",
        "countryName": "South Sudan",
        "dialCode": "+211",
        "iso3": "SSD",
        "numericCode": "728"
    },
    {
        "currencyName": "Pound Sterling",
        "currencySymbol": "£",
        "countryNameCode": "GS",
        "currencyCode": "GBP",
        "countryFlag": "🇬🇸",
        "countryName": "South Georgia and the South Sandwich Islands",
        "dialCode": "+500",
        "iso3": "SGS",
        "numericCode": "239",
        "note": "As a British Overseas Territory, the islands use the Pound Sterling (GBP)."
    },
    {
        "currencyName": "Euro",
        "currencySymbol": "€",
        "countryNameCode": "ES",
        "currencyCode": "EUR",
        "countryFlag": "🇪🇸",
        "countryName": "Spain",
        "dialCode": "+34",
        "iso3": "ESP",
        "numericCode": "724"
    },
    {
        "currencyName": "Sri Lankan Rupee",
        "currencySymbol": "Rs",
        "countryNameCode": "LK",
        "currencyCode": "LKR",
        "countryFlag": "🇱🇰",
        "countryName": "Sri Lanka",
        "dialCode": "+94",
        "iso3": "LKA",
        "numericCode": "144"
    },
    {
        "currencyName": "Sudanese Pound",
        "currencySymbol": "£",
        "countryNameCode": "SD",
        "currencyCode": "SDG",
        "countryFlag": "🇸🇩",
        "countryName": "Sudan",
        "dialCode": "+249",
        "iso3": "SDN",
        "numericCode": "729"
    },
    {
        "currencyName": "Surinamese Dollar",
        "currencySymbol": "$",
        "countryNameCode": "SR",
        "currencyCode": "SRD",
        "countryFlag": "🇸🇷",
        "countryName": "Suriname",
        "dialCode": "+597",
        "iso3": "SUR",
        "numericCode": "740"
    },
    {
        "currencyName": "Norwegian Krone",
        "currencySymbol": "kr",
        "countryNameCode": "SJ",
        "currencyCode": "NOK",
        "countryFlag": "🇸🇯",
        "countryName": "Svalbard and Jan Mayen",
        "dialCode": "+47",
        "iso3": "SJM",
        "numericCode": "744",
        "note": "Svalbard and Jan Mayen are territories of Norway, and they use the Norwegian Krone (NOK)."
    },
    {
        "currencyName": "Swazi Lilangeni",
        "currencySymbol": "L",
        "countryNameCode": "SZ",
        "currencyCode": "SZL",
        "countryFlag": "🇸🇿",
        "countryName": "Eswatini",
        "dialCode": "+268",
        "iso3": "SWZ",
        "numericCode": "748"
    },
    {
        "currencyName": "Swedish Krona",
        "currencySymbol": "kr",
        "countryNameCode": "SE",
        "currencyCode": "SEK",
        "countryFlag": "🇸🇪",
        "countryName": "Sweden",
        "dialCode": "+46",
        "iso3": "SWE",
        "numericCode": "752"
    },
    {
        "currencyName": "Swiss Franc",
        "currencySymbol": "CHF",
        "countryNameCode": "CH",
        "currencyCode": "CHF",
        "countryFlag": "🇨🇭",
        "countryName": "Switzerland",
        "dialCode": "+41",
        "iso3": "CHE",
        "numericCode": "756"
    },
    {
        "currencyName": "Syrian Pound",
        "currencySymbol": "£S",
        "countryNameCode": "SY",
        "currencyCode": "SYP",
        "countryFlag": "🇸🇾",
        "countryName": "Syria",
        "dialCode": "+963",
        "iso3": "SYR",
        "numericCode": "760"
    },
    {
        "currencyName": "New Taiwan Dollar",
        "currencySymbol": "NT$",
        "countryNameCode": "TW",
        "currencyCode": "TWD",
        "countryFlag": "🇹🇼",
        "countryName": "Taiwan",
        "dialCode": "+886",
        "iso3": "TWN",
        "numericCode": "158"
    },
    {
        "currencyName": "Tajikistani Somoni",
        "currencySymbol": "ЅM",
        "countryNameCode": "TJ",
        "currencyCode": "TJS",
        "countryFlag": "🇹🇯",
        "countryName": "Tajikistan",
        "dialCode": "+992",
        "iso3": "TJK",
        "numericCode": "762"
    },
    {
        "currencyName": "Tanzanian Shilling",
        "currencySymbol": "Sh",
        "countryNameCode": "TZ",
        "currencyCode": "TZS",
        "countryFlag": "🇹🇿",
        "countryName": "Tanzania",
        "dialCode": "+255",
        "iso3": "TZA",
        "numericCode": "834"
    },
    {
        "currencyName": "Thai Baht",
        "currencySymbol": "฿",
        "countryNameCode": "TH",
        "currencyCode": "THB",
        "countryFlag": "🇹🇭",
        "countryName": "Thailand",
        "dialCode": "+66",
        "iso3": "THA",
        "numericCode": "764"
    },

    {
        "currencyName": "West African CFA Franc",
        "currencySymbol": "CFA",
        "countryNameCode": "TG",
        "currencyCode": "XOF",
        "countryFlag": "🇹🇬",
        "countryName": "Togo",
        "dialCode": "+228",
        "iso3": "TGO",
        "numericCode": "768"
    },
    {
        "currencyName": "New Zealand Dollar",
        "currencySymbol": "NZ$",
        "countryNameCode": "TK",
        "currencyCode": "NZD",
        "countryFlag": "🇹🇰",
        "countryName": "Tokelau",
        "dialCode": "+690",
        "iso3": "TKL",
        "numericCode": "772"
    },
    {
        "currencyName": "Tongan Paʻanga",
        "currencySymbol": "T$",
        "countryNameCode": "TO",
        "currencyCode": "TOP",
        "countryFlag": "🇹🇴",
        "countryName": "Tonga",
        "dialCode": "+676",
        "iso3": "TON",
        "numericCode": "776"
    },
    {
        "currencyName": "Trinidad and Tobago Dollar",
        "currencySymbol": "$",
        "countryNameCode": "TT",
        "currencyCode": "TTD",
        "countryFlag": "🇹🇹",
        "countryName": "Trinidad and Tobago",
        "dialCode": "+1868",
        "iso3": "TTO",
        "numericCode": "780"
    },
    {
        "currencyName": "Tunisian Dinar",
        "currencySymbol": "د.ت",
        "countryNameCode": "TN",
        "currencyCode": "TND",
        "countryFlag": "🇹🇳",
        "countryName": "Tunisia",
        "dialCode": "+216",
        "iso3": "TUN",
        "numericCode": "788"
    },
    {
        "currencyName": "Turkish Lira",
        "currencySymbol": "₺",
        "countryNameCode": "TR",
        "currencyCode": "TRY",
        "countryFlag": "🇹🇷",
        "countryName": "Turkey",
        "dialCode": "+90",
        "iso3": "TUR",
        "numericCode": "792"
    },
    {
        "currencyName": "Turkmenistani Manat",
        "currencySymbol": "m",
        "countryNameCode": "TM",
        "currencyCode": "TMT",
        "countryFlag": "🇹🇲",
        "countryName": "Turkmenistan",
        "dialCode": "+993",
        "iso3": "TKM",
        "numericCode": "795"
    },

    {
        "currencyName": "Australian Dollar",
        "currencySymbol": "A$",
        "countryNameCode": "TV",
        "currencyCode": "AUD",
        "countryFlag": "🇹🇻",
        "countryName": "Tuvalu",
        "dialCode": "+688",
        "iso3": "TUV",
        "numericCode": "798",
        "note": "Tuvalu uses the Australian Dollar (AUD) as its official currency, and Tuvaluan dollars are also issued as commemorative coins."
    },
    {
        "currencyName": "Ugandan Shilling",
        "currencySymbol": "Sh",
        "countryNameCode": "UG",
        "currencyCode": "UGX",
        "countryFlag": "🇺🇬",
        "countryName": "Uganda",
        "dialCode": "+256",
        "iso3": "UGA",
        "numericCode": "800"
    },
    {
        "currencyName": "Ukrainian Hryvnia",
        "currencySymbol": "₴",
        "countryNameCode": "UA",
        "currencyCode": "UAH",
        "countryFlag": "🇺🇦",
        "countryName": "Ukraine",
        "dialCode": "+380",
        "iso3": "UKR",
        "numericCode": "804"
    },
    {
        "currencyName": "United Arab Emirates Dirham",
        "currencySymbol": "د.إ",
        "countryNameCode": "AE",
        "currencyCode": "AED",
        "countryFlag": "🇦🇪",
        "countryName": "United Arab Emirates",
        "dialCode": "+971",
        "iso3": "ARE",
        "numericCode": "784"
    },
    {
        "currencyName": "British Pound",
        "currencySymbol": "£",
        "countryNameCode": "GB",
        "currencyCode": "GBP",
        "countryFlag": "🇬🇧",
        "countryName": "United Kingdom",
        "dialCode": "+44",
        "iso3": "GBR",
        "numericCode": "826"
    },
    {
        "currencyName": "United States Dollar",
        "currencySymbol": "$",
        "countryNameCode": "US",
        "currencyCode": "USD",
        "countryFlag": "🇺🇸",
        "countryName": "United States of America",
        "dialCode": "+1",
        "iso3": "USA",
        "numericCode": "840"
    },
    {
        "currencyName": "Uruguayan Peso",
        "currencySymbol": "$U",
        "countryNameCode": "UY",
        "currencyCode": "UYU",
        "countryFlag": "🇺🇾",
        "countryName": "Uruguay",
        "dialCode": "+598",
        "iso3": "URY",
        "numericCode": "858"
    },
    {
        "currencyName": "Uzbekistani Soʻm",
        "currencySymbol": "сўм",
        "countryNameCode": "UZ",
        "currencyCode": "UZS",
        "countryFlag": "🇺🇿",
        "countryName": "Uzbekistan",
        "dialCode": "+998",
        "iso3": "UZB",
        "numericCode": "860"
    },
    {
        "currencyName": "Vanuatu Vatu",
        "currencySymbol": "VT",
        "countryNameCode": "VU",
        "currencyCode": "VUV",
        "countryFlag": "🇻🇺",
        "countryName": "Vanuatu",
        "dialCode": "+678",
        "iso3": "VUT",
        "numericCode": "548"
    },
    {
        "currencyName": "Venezuelan Bolívar",
        "currencySymbol": "Bs.",
        "countryNameCode": "VE",
        "currencyCode": "VES",
        "countryFlag": "🇻🇪",
        "countryName": "Venezuela",
        "dialCode": "+58",
        "iso3": "VEN",
        "numericCode": "862"
    },
    {
        "currencyName": "Vietnamese Đồng",
        "currencySymbol": "₫",
        "countryNameCode": "VN",
        "currencyCode": "VND",
        "countryFlag": "🇻🇳",
        "countryName": "Vietnam",
        "dialCode": "+84",
        "iso3": "VNM",
        "numericCode": "704"
    },

    {
        "currencyName": "CFP Franc",
        "currencySymbol": "₣",
        "countryNameCode": "WF",
        "currencyCode": "XPF",
        "countryFlag": "🇼🇫",
        "countryName": "Wallis and Futuna",
        "dialCode": "+681",
        "iso3": "WLF",
        "numericCode": "876"
    },
    {
        "currencyName": "Yemeni Rial",
        "currencySymbol": "﷼",
        "countryNameCode": "YE",
        "currencyCode": "YER",
        "countryFlag": "🇾🇪",
        "countryName": "Yemen",
        "dialCode": "+967",
        "iso3": "YEM",
        "numericCode": "886"
    },
    {
        "currencyName": "Zambian Kwacha",
        "currencySymbol": "ZK",
        "countryNameCode": "ZM",
        "currencyCode": "ZMW",
        "countryFlag": "🇿🇲",
        "countryName": "Zambia",
        "dialCode": "+260",
        "iso3": "ZMB",
        "numericCode": "894"
    },
    {
        "currencyName": "Zimbabwean Dollar",
        "currencySymbol": "$",
        "countryNameCode": "ZW",
        "currencyCode": "ZWL",
        "countryFlag": "🇿🇼",
        "countryName": "Zimbabwe",
        "dialCode": "+263",
        "iso3": "ZWE",
        "numericCode": "716"
    }
]
export interface Country {
    code: string;
    name: string;
    flag: string;
}
export const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "EUR",
    }).format(price)
}
export const allCountries: Country[] = [
    { code: "+93", name: "Afghanistan", flag: "🇦🇫" },
    { code: "+355", name: "Albania", flag: "🇦🇱" },
    { code: "+213", name: "Algeria", flag: "🇩🇿" },
    { code: "+1684", name: "American Samoa", flag: "🇦🇸" },
    { code: "+376", name: "Andorra", flag: "🇦🇩" },
    { code: "+244", name: "Angola", flag: "🇦🇴" },
    { code: "+1264", name: "Anguilla", flag: "🇦🇮" },
    { code: "+672", name: "Antarctica", flag: "🇦🇶" },
    { code: "+1268", name: "Antigua and Barbuda", flag: "🇦🇬" },
    { code: "+54", name: "Argentina", flag: "🇦🇷" },
    { code: "+374", name: "Armenia", flag: "🇦🇲" },
    { code: "+297", name: "Aruba", flag: "🇦🇼" },
    { code: "+61", name: "Australia", flag: "🇦🇺" },
    { code: "+43", name: "Austria", flag: "🇦🇹" },
    { code: "+994", name: "Azerbaijan", flag: "🇦🇿" },
    { code: "+1242", name: "Bahamas", flag: "🇧🇸" },
    { code: "+973", name: "Bahrain", flag: "🇧🇭" },
    { code: "+880", name: "Bangladesh", flag: "🇧🇩" },
    { code: "+1246", name: "Barbados", flag: "🇧🇧" },
    { code: "+375", name: "Belarus", flag: "🇧🇾" },
    { code: "+32", name: "Belgium", flag: "🇧🇪" },
    { code: "+501", name: "Belize", flag: "🇧🇿" },
    { code: "+229", name: "Benin", flag: "🇧🇯" },
    { code: "+1441", name: "Bermuda", flag: "🇧🇲" },
    { code: "+975", name: "Bhutan", flag: "🇧🇹" },
    { code: "+591", name: "Bolivia", flag: "🇧🇴" },
    { code: "+387", name: "Bosnia and Herzegovina", flag: "🇧🇦" },
    { code: "+267", name: "Botswana", flag: "🇧🇼" },
    { code: "+55", name: "Brazil", flag: "🇧🇷" },
    { code: "+246", name: "British Indian Ocean Territory", flag: "🇮🇴" },
    { code: "+673", name: "Brunei", flag: "🇧🇳" },
    { code: "+359", name: "Bulgaria", flag: "🇧🇬" },
    { code: "+226", name: "Burkina Faso", flag: "🇧🇫" },
    { code: "+257", name: "Burundi", flag: "🇧🇮" },
    { code: "+855", name: "Cambodia", flag: "🇰🇭" },
    { code: "+237", name: "Cameroon", flag: "🇨🇲" },
    { code: "+1", name: "Canada", flag: "🇨🇦" },
    { code: "+238", name: "Cape Verde", flag: "🇨🇻" },
    { code: "+1345", name: "Cayman Islands", flag: "🇰🇾" },
    { code: "+236", name: "Central African Republic", flag: "🇨🇫" },
    { code: "+235", name: "Chad", flag: "🇹🇩" },
    { code: "+56", name: "Chile", flag: "🇨🇱" },
    { code: "+86", name: "China", flag: "🇨🇳" },
    { code: "+61", name: "Christmas Island", flag: "🇨🇽" },
    { code: "+61", name: "Cocos Islands", flag: "🇨🇨" },
    { code: "+57", name: "Colombia", flag: "🇨🇴" },
    { code: "+269", name: "Comoros", flag: "🇰🇲" },
    { code: "+242", name: "Congo", flag: "🇨🇬" },
    { code: "+243", name: "Congo Democratic Republic", flag: "🇨🇩" },
    { code: "+682", name: "Cook Islands", flag: "🇨🇰" },
    { code: "+506", name: "Costa Rica", flag: "🇨🇷" },
    { code: "+385", name: "Croatia", flag: "🇭🇷" },
    { code: "+53", name: "Cuba", flag: "🇨🇺" },
    { code: "+599", name: "Curacao", flag: "🇨🇼" },
    { code: "+357", name: "Cyprus", flag: "🇨🇾" },
    { code: "+420", name: "Czech Republic", flag: "🇨🇿" },
    { code: "+45", name: "Denmark", flag: "🇩🇰" },
    { code: "+253", name: "Djibouti", flag: "🇩🇯" },
    { code: "+1767", name: "Dominica", flag: "🇩🇲" },
    { code: "+1809", name: "Dominican Republic", flag: "🇩🇴" },
    { code: "+670", name: "East Timor", flag: "🇹🇱" },
    { code: "+593", name: "Ecuador", flag: "🇪🇨" },
    { code: "+20", name: "Egypt", flag: "🇪🇬" },
    { code: "+503", name: "El Salvador", flag: "🇸🇻" },
    { code: "+240", name: "Equatorial Guinea", flag: "🇬🇶" },
    { code: "+291", name: "Eritrea", flag: "🇪🇷" },
    { code: "+372", name: "Estonia", flag: "🇪🇪" },
    { code: "+251", name: "Ethiopia", flag: "🇪🇹" },
    { code: "+500", name: "Falkland Islands", flag: "🇫🇰" },
    { code: "+298", name: "Faroe Islands", flag: "🇫🇴" },
    { code: "+679", name: "Fiji", flag: "🇫🇯" },
    { code: "+358", name: "Finland", flag: "🇫🇮" },
    { code: "+33", name: "France", flag: "🇫🇷" },
    { code: "+594", name: "French Guiana", flag: "🇬🇫" },
    { code: "+689", name: "French Polynesia", flag: "🇵🇫" },
    { code: "+241", name: "Gabon", flag: "🇬🇦" },
    { code: "+220", name: "Gambia", flag: "🇬🇲" },
    { code: "+995", name: "Georgia", flag: "🇬🇪" },
    { code: "+49", name: "Germany", flag: "🇩🇪" },
    { code: "+233", name: "Ghana", flag: "🇬🇭" },
    { code: "+350", name: "Gibraltar", flag: "🇬🇮" },
    { code: "+30", name: "Greece", flag: "🇬🇷" },
    { code: "+299", name: "Greenland", flag: "🇬🇱" },
    { code: "+1473", name: "Grenada", flag: "🇬🇩" },
    { code: "+590", name: "Guadeloupe", flag: "🇬🇵" },
    { code: "+1671", name: "Guam", flag: "🇬🇺" },
    { code: "+502", name: "Guatemala", flag: "🇬🇹" },
    { code: "+44", name: "Guernsey", flag: "🇬🇬" },
    { code: "+224", name: "Guinea", flag: "🇬🇳" },
    { code: "+245", name: "Guinea-Bissau", flag: "🇬🇼" },
    { code: "+592", name: "Guyana", flag: "🇬🇾" },
    { code: "+509", name: "Haiti", flag: "🇭🇹" },
    { code: "+504", name: "Honduras", flag: "🇭🇳" },
    { code: "+852", name: "Hong Kong", flag: "🇭🇰" },
    { code: "+36", name: "Hungary", flag: "🇭🇺" },
    { code: "+354", name: "Iceland", flag: "🇮🇸" },
    { code: "+91", name: "India", flag: "🇮🇳" },
    { code: "+62", name: "Indonesia", flag: "🇮🇩" },
    { code: "+98", name: "Iran", flag: "🇮🇷" },
    { code: "+964", name: "Iraq", flag: "🇮🇶" },
    { code: "+353", name: "Ireland", flag: "🇮🇪" },
    { code: "+44", name: "Isle of Man", flag: "🇮🇲" },
    { code: "+972", name: "Israel", flag: "🇮🇱" },
    { code: "+39", name: "Italy", flag: "🇮🇹" },
    { code: "+225", name: "Ivory Coast", flag: "🇨🇮" },
    { code: "+1876", name: "Jamaica", flag: "🇯🇲" },
    { code: "+81", name: "Japan", flag: "🇯🇵" },
    { code: "+44", name: "Jersey", flag: "🇯🇪" },
    { code: "+962", name: "Jordan", flag: "🇯🇴" },
    { code: "+7", name: "Kazakhstan", flag: "🇰🇿" },
    { code: "+254", name: "Kenya", flag: "🇰🇪" },
    { code: "+686", name: "Kiribati", flag: "🇰🇮" },
    { code: "+965", name: "Kuwait", flag: "🇰🇼" },
    { code: "+996", name: "Kyrgyzstan", flag: "🇰🇬" },
    { code: "+856", name: "Laos", flag: "🇱🇦" },
    { code: "+371", name: "Latvia", flag: "🇱🇻" },
    { code: "+961", name: "Lebanon", flag: "🇱🇧" },
    { code: "+266", name: "Lesotho", flag: "🇱🇸" },
    { code: "+231", name: "Liberia", flag: "🇱🇷" },
    { code: "+218", name: "Libya", flag: "🇱🇾" },
    { code: "+423", name: "Liechtenstein", flag: "🇱🇮" },
    { code: "+370", name: "Lithuania", flag: "🇱🇹" },
    { code: "+352", name: "Luxembourg", flag: "🇱🇺" },
    { code: "+853", name: "Macau", flag: "🇲🇴" },
    { code: "+389", name: "Macedonia", flag: "🇲🇰" },
    { code: "+261", name: "Madagascar", flag: "🇲🇬" },
    { code: "+265", name: "Malawi", flag: "🇲🇼" },
    { code: "+60", name: "Malaysia", flag: "🇲🇾" },
    { code: "+960", name: "Maldives", flag: "🇲🇻" },
    { code: "+223", name: "Mali", flag: "🇲🇱" },
    { code: "+356", name: "Malta", flag: "🇲🇹" },
    { code: "+692", name: "Marshall Islands", flag: "🇲🇭" },
    { code: "+596", name: "Martinique", flag: "🇲🇶" },
    { code: "+222", name: "Mauritania", flag: "🇲🇷" },
    { code: "+230", name: "Mauritius", flag: "🇲🇺" },
    { code: "+262", name: "Mayotte", flag: "🇾🇹" },
    { code: "+52", name: "Mexico", flag: "🇲🇽" },
    { code: "+691", name: "Micronesia", flag: "🇫🇲" },
    { code: "+373", name: "Moldova", flag: "🇲🇩" },
    { code: "+377", name: "Monaco", flag: "🇲🇨" },
    { code: "+976", name: "Mongolia", flag: "🇲🇳" },
    { code: "+382", name: "Montenegro", flag: "🇲🇪" },
    { code: "+1664", name: "Montserrat", flag: "🇲🇸" },
    { code: "+212", name: "Morocco", flag: "🇲🇦" },
    { code: "+258", name: "Mozambique", flag: "🇲🇿" },
    { code: "+95", name: "Myanmar", flag: "🇲🇲" },
    { code: "+264", name: "Namibia", flag: "🇳🇦" },
    { code: "+674", name: "Nauru", flag: "🇳🇷" },
    { code: "+977", name: "Nepal", flag: "🇳🇵" },
    { code: "+31", name: "Netherlands", flag: "🇳🇱" },
    { code: "+687", name: "New Caledonia", flag: "🇳🇨" },
    { code: "+64", name: "New Zealand", flag: "🇳🇿" },
    { code: "+505", name: "Nicaragua", flag: "🇳🇮" },
    { code: "+227", name: "Niger", flag: "🇳🇪" },
    { code: "+234", name: "Nigeria", flag: "🇳🇬" },
    { code: "+683", name: "Niue", flag: "🇳🇺" },
    { code: "+850", name: "North Korea", flag: "🇰🇵" },
    { code: "+1670", name: "Northern Mariana Islands", flag: "🇲🇵" },
    { code: "+47", name: "Norway", flag: "🇳🇴" },
    { code: "+968", name: "Oman", flag: "🇴🇲" },
    { code: "+92", name: "Pakistan", flag: "🇵🇰" },
    { code: "+680", name: "Palau", flag: "🇵🇼" },
    { code: "+970", name: "Palestine", flag: "🇵🇸" },
    { code: "+507", name: "Panama", flag: "🇵🇦" },
    { code: "+675", name: "Papua New Guinea", flag: "🇵🇬" },
    { code: "+595", name: "Paraguay", flag: "🇵🇾" },
    { code: "+51", name: "Peru", flag: "🇵🇪" },
    { code: "+63", name: "Philippines", flag: "🇵🇭" },
    { code: "+48", name: "Poland", flag: "🇵🇱" },
    { code: "+351", name: "Portugal", flag: "🇵🇹" },
    { code: "+1787", name: "Puerto Rico", flag: "🇵🇷" },
    { code: "+974", name: "Qatar", flag: "🇶🇦" },
    { code: "+262", name: "Reunion", flag: "🇷🇪" },
    { code: "+40", name: "Romania", flag: "🇷🇴" },
    { code: "+7", name: "Russia", flag: "🇷🇺" },
    { code: "+250", name: "Rwanda", flag: "🇷🇼" },
    { code: "+590", name: "Saint Barthelemy", flag: "🇧🇱" },
    { code: "+290", name: "Saint Helena", flag: "🇸🇭" },
    { code: "+1869", name: "Saint Kitts and Nevis", flag: "🇰🇳" },
    { code: "+1758", name: "Saint Lucia", flag: "🇱🇨" },
    { code: "+590", name: "Saint Martin", flag: "🇲🇫" },
    { code: "+508", name: "Saint Pierre and Miquelon", flag: "🇵🇲" },
    { code: "+1784", name: "Saint Vincent and the Grenadines", flag: "🇻🇨" },
    { code: "+685", name: "Samoa", flag: "🇼🇸" },
    { code: "+378", name: "San Marino", flag: "🇸🇲" },
    { code: "+239", name: "Sao Tome and Principe", flag: "🇸🇹" },
    { code: "+966", name: "Saudi Arabia", flag: "🇸🇦" },
    { code: "+221", name: "Senegal", flag: "🇸🇳" },
    { code: "+381", name: "Serbia", flag: "🇷🇸" },
    { code: "+248", name: "Seychelles", flag: "🇸🇨" },
    { code: "+232", name: "Sierra Leone", flag: "🇸🇱" },
    { code: "+65", name: "Singapore", flag: "🇸🇬" },
    { code: "+1721", name: "Sint Maarten", flag: "🇸🇽" },
    { code: "+421", name: "Slovakia", flag: "🇸🇰" },
    { code: "+386", name: "Slovenia", flag: "🇸🇮" },
    { code: "+677", name: "Solomon Islands", flag: "🇸🇧" },
    { code: "+252", name: "Somalia", flag: "🇸🇴" },
    { code: "+27", name: "South Africa", flag: "🇿🇦" },
    { code: "+82", name: "South Korea", flag: "🇰🇷" },
    { code: "+211", name: "South Sudan", flag: "🇸🇸" },
    { code: "+34", name: "Spain", flag: "🇪🇸" },
    { code: "+94", name: "Sri Lanka", flag: "🇱🇰" },
    { code: "+249", name: "Sudan", flag: "🇸🇩" },
    { code: "+597", name: "Suriname", flag: "🇸🇷" },
    { code: "+47", name: "Svalbard and Jan Mayen", flag: "🇸🇯" },
    { code: "+268", name: "Swaziland", flag: "🇸🇿" },
    { code: "+46", name: "Sweden", flag: "🇸🇪" },
    { code: "+41", name: "Switzerland", flag: "🇨🇭" },
    { code: "+963", name: "Syria", flag: "🇸🇾" },
    { code: "+886", name: "Taiwan", flag: "🇹🇼" },
    { code: "+992", name: "Tajikistan", flag: "🇹🇯" },
    { code: "+255", name: "Tanzania", flag: "🇹🇿" },
    { code: "+66", name: "Thailand", flag: "🇹🇭" },
    { code: "+228", name: "Togo", flag: "🇹🇬" },
    { code: "+690", name: "Tokelau", flag: "🇹🇰" },
    { code: "+676", name: "Tonga", flag: "🇹🇴" },
    { code: "+1868", name: "Trinidad and Tobago", flag: "🇹🇹" },
    { code: "+216", name: "Tunisia", flag: "🇹🇳" },
    { code: "+90", name: "Turkey", flag: "🇹🇷" },
    { code: "+993", name: "Turkmenistan", flag: "🇹🇲" },
    { code: "+1649", name: "Turks and Caicos Islands", flag: "🇹🇨" },
    { code: "+688", name: "Tuvalu", flag: "🇹🇻" },
    { code: "+1340", name: "U.S. Virgin Islands", flag: "🇻🇮" },
    { code: "+256", name: "Uganda", flag: "🇺🇬" },
    { code: "+380", name: "Ukraine", flag: "🇺🇦" },
    { code: "+971", name: "United Arab Emirates", flag: "🇦🇪" },
    { code: "+44", name: "United Kingdom", flag: "🇬🇧" },
    { code: "+1", name: "United States", flag: "🇺🇸" },
    { code: "+598", name: "Uruguay", flag: "🇺🇾" },
    { code: "+998", name: "Uzbekistan", flag: "🇺🇿" },
    { code: "+678", name: "Vanuatu", flag: "🇻🇺" },
    { code: "+379", name: "Vatican", flag: "🇻🇦" },
    { code: "+58", name: "Venezuela", flag: "🇻🇪" },
    { code: "+84", name: "Vietnam", flag: "🇻🇳" },
    { code: "+681", name: "Wallis and Futuna", flag: "🇼🇫" },
    { code: "+212", name: "Western Sahara", flag: "🇪🇭" },
    { code: "+967", name: "Yemen", flag: "🇾🇪" },
    { code: "+260", name: "Zambia", flag: "🇿🇲" },
    { code: "+263", name: "Zimbabwe", flag: "🇿🇼" },
];
export const countries = [
    // Existing countries
    {code: "AUT", name: "Austria", iso2: "AT", isoNumeric: "040"},
    {code: "BEL", name: "Belgium", iso2: "BE", isoNumeric: "056"},
    {code: "BGR", name: "Bulgaria", iso2: "BG", isoNumeric: "100"},
    {code: "HRV", name: "Croatia", iso2: "HR", isoNumeric: "191"},
    {code: "CYP", name: "Cyprus", iso2: "CY", isoNumeric: "196"},
    {code: "CZE", name: "Czech Republic", iso2: "CZ", isoNumeric: "203"},
    {code: "DNK", name: "Denmark", iso2: "DK", isoNumeric: "208"},
    {code: "DEU", name: "Germany", iso2: "DE", isoNumeric: "276"},
    {code: "EST", name: "Estonia", iso2: "EE", isoNumeric: "233"},
    {code: "FIN", name: "Finland", iso2: "FI", isoNumeric: "246"},
    {code: "FRA", name: "France", iso2: "FR", isoNumeric: "250"},
    {code: "GRC", name: "Greece", iso2: "GR", isoNumeric: "300"},
    {code: "HUN", name: "Hungary", iso2: "HU", isoNumeric: "348"},
    {code: "ISL", name: "Iceland", iso2: "IS", isoNumeric: "352"},
    {code: "IRL", name: "Ireland", iso2: "IE", isoNumeric: "372"},
    {code: "ITA", name: "Italy", iso2: "IT", isoNumeric: "380"},
    {code: "LVA", name: "Latvia", iso2: "LV", isoNumeric: "428"},
    {code: "LIE", name: "Liechtenstein", iso2: "LI", isoNumeric: "438"},
    {code: "LTU", name: "Lithuania", iso2: "LT", isoNumeric: "440"},
    {code: "LUX", name: "Luxembourg", iso2: "LU", isoNumeric: "442"},
    {code: "MLT", name: "Malta", iso2: "MT", isoNumeric: "470"},
    {code: "NLD", name: "Netherlands", iso2: "NL", isoNumeric: "528"},
    {code: "NOR", name: "Norway", iso2: "NO", isoNumeric: "578"},
    {code: "POL", name: "Poland", iso2: "PL", isoNumeric: "616"},
    {code: "PRT", name: "Portugal", iso2: "PT", isoNumeric: "620"},
    {code: "ROU", name: "Romania", iso2: "RO", isoNumeric: "642"},
    {code: "SVN", name: "Slovenia", iso2: "SI", isoNumeric: "705"},
    {code: "SVK", name: "Slovakia", iso2: "SK", isoNumeric: "703"},
    {code: "ESP", name: "Spain", iso2: "ES", isoNumeric: "724"},
    {code: "SWE", name: "Sweden", iso2: "SE", isoNumeric: "752"},
    {code: "CHE", name: "Switzerland", iso2: "CH", isoNumeric: "756"},
    {code: "GUF", name: "French Guiana", iso2: "GF", isoNumeric: "254"},
    {code: "GLP", name: "Guadeloupe", iso2: "GP", isoNumeric: "312"},
    {code: "MTQ", name: "Martinique", iso2: "MQ", isoNumeric: "474"},
    {code: "MYT", name: "Mayotte", iso2: "YT", isoNumeric: "175"},
    {code: "REU", name: "Réunion", iso2: "RE", isoNumeric: "638"},
    {code: "MAF", name: "Saint Martin", iso2: "MF", isoNumeric: "663"},

    // Additional countries
    {code: "AFG", name: "Afghanistan", iso2: "AF", isoNumeric: "004"},
    {code: "ALB", name: "Albania", iso2: "AL", isoNumeric: "008"},
    {code: "DZA", name: "Algeria", iso2: "DZ", isoNumeric: "012"},
    {code: "ASM", name: "American Samoa", iso2: "AS", isoNumeric: "016"},
    {code: "AND", name: "Andorra", iso2: "AD", isoNumeric: "020"},
    {code: "AGO", name: "Angola", iso2: "AO", isoNumeric: "024"},
    {code: "AIA", name: "Anguilla", iso2: "AI", isoNumeric: "660"},
    {code: "ATA", name: "Antarctica", iso2: "AQ", isoNumeric: "010"},
    {code: "ATG", name: "Antigua and Barbuda", iso2: "AG", isoNumeric: "028"},
    {code: "ARG", name: "Argentina", iso2: "AR", isoNumeric: "032"},
    {code: "ARM", name: "Armenia", iso2: "AM", isoNumeric: "051"},
    {code: "ABW", name: "Aruba", iso2: "AW", isoNumeric: "533"},
    {code: "AUS", name: "Australia", iso2: "AU", isoNumeric: "036"},
    {code: "AZE", name: "Azerbaijan", iso2: "AZ", isoNumeric: "031"},
    {code: "BHS", name: "Bahamas", iso2: "BS", isoNumeric: "044"},
    {code: "BHR", name: "Bahrain", iso2: "BH", isoNumeric: "048"},
    {code: "BGD", name: "Bangladesh", iso2: "BD", isoNumeric: "050"},
    {code: "BRB", name: "Barbados", iso2: "BB", isoNumeric: "052"},
    {code: "BLR", name: "Belarus", iso2: "BY", isoNumeric: "112"},
    {code: "BLZ", name: "Belize", iso2: "BZ", isoNumeric: "084"},
    {code: "BEN", name: "Benin", iso2: "BJ", isoNumeric: "204"},
    {code: "BMU", name: "Bermuda", iso2: "BM", isoNumeric: "060"},
    {code: "BTN", name: "Bhutan", iso2: "BT", isoNumeric: "064"},
    {code: "BOL", name: "Bolivia", iso2: "BO", isoNumeric: "068"},
    {code: "BES", name: "Bonaire, Sint Eustatius and Saba", iso2: "BQ", isoNumeric: "535"},
    {code: "BIH", name: "Bosnia and Herzegovina", iso2: "BA", isoNumeric: "070"},
    {code: "BWA", name: "Botswana", iso2: "BW", isoNumeric: "072"},
    {code: "BVT", name: "Bouvet Island", iso2: "BV", isoNumeric: "074"},
    {code: "BRA", name: "Brazil", iso2: "BR", isoNumeric: "076"},
    {code: "IOT", name: "British Indian Ocean Territory", iso2: "IO", isoNumeric: "086"},
    {code: "BRN", name: "Brunei Darussalam", iso2: "BN", isoNumeric: "096"},
    {code: "BFA", name: "Burkina Faso", iso2: "BF", isoNumeric: "854"},
    {code: "BDI", name: "Burundi", iso2: "BI", isoNumeric: "108"},
    {code: "CPV", name: "Cabo Verde", iso2: "CV", isoNumeric: "132"},
    {code: "KHM", name: "Cambodia", iso2: "KH", isoNumeric: "116"},
    {code: "CMR", name: "Cameroon", iso2: "CM", isoNumeric: "120"},
    {code: "CAN", name: "Canada", iso2: "CA", isoNumeric: "124"},
    {code: "CYM", name: "Cayman Islands", iso2: "KY", isoNumeric: "136"},
    {code: "CAF", name: "Central African Republic", iso2: "CF", isoNumeric: "140"},
    {code: "TCD", name: "Chad", iso2: "TD", isoNumeric: "148"},
    {code: "CHL", name: "Chile", iso2: "CL", isoNumeric: "152"},
    {code: "CHN", name: "China", iso2: "CN", isoNumeric: "156"},
    {code: "CXR", name: "Christmas Island", iso2: "CX", isoNumeric: "162"},
    {code: "CCK", name: "Cocos (Keeling) Islands", iso2: "CC", isoNumeric: "166"},
    {code: "COL", name: "Colombia", iso2: "CO", isoNumeric: "170"},
    {code: "COM", name: "Comoros", iso2: "KM", isoNumeric: "174"},
    {code: "COG", name: "Congo", iso2: "CG", isoNumeric: "178"},
    {code: "COD", name: "Congo (Democratic Republic)", iso2: "CD", isoNumeric: "180"},
    {code: "COK", name: "Cook Islands", iso2: "CK", isoNumeric: "184"},
    {code: "CRI", name: "Costa Rica", iso2: "CR", isoNumeric: "188"},
    {code: "CIV", name: "Côte d'Ivoire", iso2: "CI", isoNumeric: "384"},
    {code: "CUB", name: "Cuba", iso2: "CU", isoNumeric: "192"},
    {code: "CUW", name: "Curaçao", iso2: "CW", isoNumeric: "531"},
    {code: "DJI", name: "Djibouti", iso2: "DJ", isoNumeric: "262"},
    {code: "DMA", name: "Dominica", iso2: "DM", isoNumeric: "212"},
    {code: "DOM", name: "Dominican Republic", iso2: "DO", isoNumeric: "214"},
    {code: "ECU", name: "Ecuador", iso2: "EC", isoNumeric: "218"},
    {code: "EGY", name: "Egypt", iso2: "EG", isoNumeric: "818"},
    {code: "SLV", name: "El Salvador", iso2: "SV", isoNumeric: "222"},
    {code: "GNQ", name: "Equatorial Guinea", iso2: "GQ", isoNumeric: "226"},
    {code: "ERI", name: "Eritrea", iso2: "ER", isoNumeric: "232"},
    {code: "ETH", name: "Ethiopia", iso2: "ET", isoNumeric: "231"},
    {code: "FLK", name: "Falkland Islands", iso2: "FK", isoNumeric: "238"},
    {code: "FRO", name: "Faroe Islands", iso2: "FO", isoNumeric: "234"},
    {code: "FJI", name: "Fiji", iso2: "FJ", isoNumeric: "242"},
    {code: "GAB", name: "Gabon", iso2: "GA", isoNumeric: "266"},
    {code: "GMB", name: "Gambia", iso2: "GM", isoNumeric: "270"},
    {code: "GEO", name: "Georgia", iso2: "GE", isoNumeric: "268"},
    {code: "GHA", name: "Ghana", iso2: "GH", isoNumeric: "288"},
    {code: "GIB", name: "Gibraltar", iso2: "GI", isoNumeric: "292"},
    {code: "GRL", name: "Greenland", iso2: "GL", isoNumeric: "304"},
    {code: "GRD", name: "Grenada", iso2: "GD", isoNumeric: "308"},
    {code: "GUM", name: "Guam", iso2: "GU", isoNumeric: "316"},
    {code: "GTM", name: "Guatemala", iso2: "GT", isoNumeric: "320"},
    {code: "GGY", name: "Guernsey", iso2: "GG", isoNumeric: "831"},
    {code: "GIN", name: "Guinea", iso2: "GN", isoNumeric: "324"},
    {code: "GNB", name: "Guinea-Bissau", iso2: "GW", isoNumeric: "624"},
    {code: "GUY", name: "Guyana", iso2: "GY", isoNumeric: "328"},
    {code: "HTI", name: "Haiti", iso2: "HT", isoNumeric: "332"},
    {code: "HMD", name: "Heard Island and McDonald Islands", iso2: "HM", isoNumeric: "334"},
    {code: "VAT", name: "Holy See", iso2: "VA", isoNumeric: "336"},
    {code: "HND", name: "Honduras", iso2: "HN", isoNumeric: "340"},
    {code: "HKG", name: "Hong Kong", iso2: "HK", isoNumeric: "344"},
    {code: "IND", name: "India", iso2: "IN", isoNumeric: "356"},
    {code: "IDN", name: "Indonesia", iso2: "ID", isoNumeric: "360"},
    {code: "IRN", name: "Iran", iso2: "IR", isoNumeric: "364"},
    {code: "IRQ", name: "Iraq", iso2: "IQ", isoNumeric: "368"},
    {code: "IMN", name: "Isle of Man", iso2: "IM", isoNumeric: "833"},
    {code: "ISR", name: "Israel", iso2: "IL", isoNumeric: "376"},
    {code: "JAM", name: "Jamaica", iso2: "JM", isoNumeric: "388"},
    {code: "JPN", name: "Japan", iso2: "JP", isoNumeric: "392"},
    {code: "JEY", name: "Jersey", iso2: "JE", isoNumeric: "832"},
    {code: "JOR", name: "Jordan", iso2: "JO", isoNumeric: "400"},
    {code: "KAZ", name: "Kazakhstan", iso2: "KZ", isoNumeric: "398"},
    {code: "KEN", name: "Kenya", iso2: "KE", isoNumeric: "404"},
    {code: "KIR", name: "Kiribati", iso2: "KI", isoNumeric: "296"},
    {code: "PRK", name: "Korea (Democratic People's Republic)", iso2: "KP", isoNumeric: "408"},
    {code: "KOR", name: "Korea (Republic)", iso2: "KR", isoNumeric: "410"},
    {code: "KWT", name: "Kuwait", iso2: "KW", isoNumeric: "414"},
    {code: "KGZ", name: "Kyrgyzstan", iso2: "KG", isoNumeric: "417"},
    {code: "LAO", name: "Lao People's Democratic Republic", iso2: "LA", isoNumeric: "418"},
    {code: "LBN", name: "Lebanon", iso2: "LB", isoNumeric: "422"},
    {code: "LSO", name: "Lesotho", iso2: "LS", isoNumeric: "426"},
    {code: "LBR", name: "Liberia", iso2: "LR", isoNumeric: "430"},
    {code: "LBY", name: "Libya", iso2: "LY", isoNumeric: "434"},
    {code: "MAC", name: "Macao", iso2: "MO", isoNumeric: "446"},
    {code: "MDG", name: "Madagascar", iso2: "MG", isoNumeric: "450"},
    {code: "MWI", name: "Malawi", iso2: "MW", isoNumeric: "454"},
    {code: "MYS", name: "Malaysia", iso2: "MY", isoNumeric: "458"},
    {code: "MDV", name: "Maldives", iso2: "MV", isoNumeric: "462"},
    {code: "MLI", name: "Mali", iso2: "ML", isoNumeric: "466"},
    {code: "MHL", name: "Marshall Islands", iso2: "MH", isoNumeric: "584"},
    {code: "MRT", name: "Mauritania", iso2: "MR", isoNumeric: "478"},
    {code: "MUS", name: "Mauritius", iso2: "MU", isoNumeric: "480"},
    {code: "MEX", name: "Mexico", iso2: "MX", isoNumeric: "484"},
    {code: "FSM", name: "Micronesia", iso2: "FM", isoNumeric: "583"},
    {code: "MDA", name: "Moldova", iso2: "MD", isoNumeric: "498"},
    {code: "MCO", name: "Monaco", iso2: "MC", isoNumeric: "492"},
    {code: "MNG", name: "Mongolia", iso2: "MN", isoNumeric: "496"},
    {code: "MNE", name: "Montenegro", iso2: "ME", isoNumeric: "499"},
    {code: "MSR", name: "Montserrat", iso2: "MS", isoNumeric: "500"},
    {code: "MAR", name: "Morocco", iso2: "MA", isoNumeric: "504"},
    {code: "MOZ", name: "Mozambique", iso2: "MZ", isoNumeric: "508"},
    {code: "MMR", name: "Myanmar", iso2: "MM", isoNumeric: "104"},
    {code: "NAM", name: "Namibia", iso2: "NA", isoNumeric: "516"},
    {code: "NRU", name: "Nauru", iso2: "NR", isoNumeric: "520"},
    {code: "NPL", name: "Nepal", iso2: "NP", isoNumeric: "524"},
    {code: "NCL", name: "New Caledonia", iso2: "NC", isoNumeric: "540"},
    {code: "NZL", name: "New Zealand", iso2: "NZ", isoNumeric: "554"},
    {code: "NIC", name: "Nicaragua", iso2: "NI", isoNumeric: "558"},
    {code: "NER", name: "Niger", iso2: "NE", isoNumeric: "562"},
    {code: "NGA", name: "Nigeria", iso2: "NG", isoNumeric: "566"},
    {code: "NIU", name: "Niue", iso2: "NU", isoNumeric: "570"},
    {code: "NFK", name: "Norfolk Island", iso2: "NF", isoNumeric: "574"},
    {code: "MKD", name: "North Macedonia", iso2: "MK", isoNumeric: "807"},
    {code: "MNP", name: "Northern Mariana Islands", iso2: "MP", isoNumeric: "580"},
    {code: "OMN", name: "Oman", iso2: "OM", isoNumeric: "512"},
    {code: "PAK", name: "Pakistan", iso2: "PK", isoNumeric: "586"},
    {code: "PLW", name: "Palau", iso2: "PW", isoNumeric: "585"},
    {code: "PSE", name: "Palestine, State of", iso2: "PS", isoNumeric: "275"},
    {code: "PAN", name: "Panama", iso2: "PA", isoNumeric: "591"},
    {code: "PNG", name: "Papua New Guinea", iso2: "PG", isoNumeric: "598"},
    {code: "PRY", name: "Paraguay", iso2: "PY", isoNumeric: "600"},
    {code: "PER", name: "Peru", iso2: "PE", isoNumeric: "604"},
    {code: "PHL", name: "Philippines", iso2: "PH", isoNumeric: "608"},
    {code: "PCN", name: "Pitcairn", iso2: "PN", isoNumeric: "612"},
    {code: "PRI", name: "Puerto Rico", iso2: "PR", isoNumeric: "630"},
    {code: "QAT", name: "Qatar", iso2: "QA", isoNumeric: "634"},
    {code: "RUS", name: "Russian Federation", iso2: "RU", isoNumeric: "643"},
    {code: "RWA", name: "Rwanda", iso2: "RW", isoNumeric: "646"},
    {code: "BLM", name: "Saint Barthélemy", iso2: "BL", isoNumeric: "652"},
    {code: "SHN", name: "Saint Helena, Ascension and Tristan da Cunha", iso2: "SH", isoNumeric: "654"},
    {code: "KNA", name: "Saint Kitts and Nevis", iso2: "KN", isoNumeric: "659"},
    {code: "LCA", name: "Saint Lucia", iso2: "LC", isoNumeric: "662"},
    {code: "SPM", name: "Saint Pierre and Miquelon", iso2: "PM", isoNumeric: "666"},
    {code: "VCT", name: "Saint Vincent and the Grenadines", iso2: "VC", isoNumeric: "670"},
    {code: "WSM", name: "Samoa", iso2: "WS", isoNumeric: "882"},
    {code: "SMR", name: "San Marino", iso2: "SM", isoNumeric: "674"},
    {code: "STP", name: "Sao Tome and Principe", iso2: "ST", isoNumeric: "678"},
    {code: "SAU", name: "Saudi Arabia", iso2: "SA", isoNumeric: "682"},
    {code: "SEN", name: "Senegal", iso2: "SN", isoNumeric: "686"},
    {code: "SRB", name: "Serbia", iso2: "RS", isoNumeric: "688"},
    {code: "SYC", name: "Seychelles", iso2: "SC", isoNumeric: "690"},
    {code: "SLE", name: "Sierra Leone", iso2: "SL", isoNumeric: "694"},
    {code: "SGP", name: "Singapore", iso2: "SG", isoNumeric: "702"},
    {code: "SXM", name: "Sint Maarten", iso2: "SX", isoNumeric: "534"},
    {code: "SLB", name: "Solomon Islands", iso2: "SB", isoNumeric: "090"},
    {code: "SOM", name: "Somalia", iso2: "SO", isoNumeric: "706"},
    {code: "ZAF", name: "South Africa", iso2: "ZA", isoNumeric: "710"},
    {code: "SGS", name: "South Georgia and the South Sandwich Islands", iso2: "GS", isoNumeric: "239"},
    {code: "SSD", name: "South Sudan", iso2: "SS", isoNumeric: "728"},
    {code: "LKA", name: "Sri Lanka", iso2: "LK", isoNumeric: "144"},
    {code: "SDN", name: "Sudan", iso2: "SD", isoNumeric: "729"},
    {code: "SUR", name: "Suriname", iso2: "SR", isoNumeric: "740"},
    {code: "SJM", name: "Svalbard and Jan Mayen", iso2: "SJ", isoNumeric: "744"},
    {code: "SWZ", name: "Eswatini", iso2: "SZ", isoNumeric: "748"},
    {code: "SYR", name: "Syrian Arab Republic", iso2: "SY", isoNumeric: "760"},
    {code: "TWN", name: "Taiwan", iso2: "TW", isoNumeric: "158"},
    {code: "TJK", name: "Tajikistan", iso2: "TJ", isoNumeric: "762"},
    {code: "TZA", name: "Tanzania", iso2: "TZ", isoNumeric: "834"},
    {code: "THA", name: "Thailand", iso2: "TH", isoNumeric: "764"},
    {code: "TLS", name: "Timor-Leste", iso2: "TL", isoNumeric: "626"},
    {code: "TGO", name: "Togo", iso2: "TG", isoNumeric: "768"},
    {code: "TKL", name: "Tokelau", iso2: "TK", isoNumeric: "772"},
    {code: "TON", name: "Tonga", iso2: "TO", isoNumeric: "776"},
    {code: "TTO", name: "Trinidad and Tobago", iso2: "TT", isoNumeric: "780"},
    {code: "TUN", name: "Tunisia", iso2: "TN", isoNumeric: "788"},
    {code: "TUR", name: "Turkey", iso2: "TR", isoNumeric: "792"},
    {code: "TKM", name: "Turkmenistan", iso2: "TM", isoNumeric: "795"},
    {code: "TCA", name: "Turks and Caicos Islands", iso2: "TC", isoNumeric: "796"},
    {code: "TUV", name: "Tuvalu", iso2: "TV", isoNumeric: "798"},
    {code: "UGA", name: "Uganda", iso2: "UG", isoNumeric: "800"},
    {code: "UKR", name: "Ukraine", iso2: "UA", isoNumeric: "804"},
    {code: "ARE", name: "United Arab Emirates", iso2: "AE", isoNumeric: "784"},
    {code: "GBR", name: "United Kingdom", iso2: "GB", isoNumeric: "826"},
    {code: "USA", name: "United States", iso2: "US", isoNumeric: "840"},
    {code: "UMI", name: "United States Minor Outlying Islands", iso2: "UM", isoNumeric: "581"},
    {code: "URY", name: "Uruguay", iso2: "UY", isoNumeric: "858"},
    {code: "UZB", name: "Uzbekistan", iso2: "UZ", isoNumeric: "860"},
    {code: "VUT", name: "Vanuatu", iso2: "VU", isoNumeric: "548"},
    {code: "VEN", name: "Venezuela", iso2: "VE", isoNumeric: "862"},
    {code: "VNM", name: "Vietnam", iso2: "VN", isoNumeric: "704"},
    {code: "VGB", name: "Virgin Islands (British)", iso2: "VG", isoNumeric: "092"},
    {code: "VIR", name: "Virgin Islands (U.S.)", iso2: "VI", isoNumeric: "850"},
    {code: "WLF", name: "Wallis and Futuna", iso2: "WF", isoNumeric: "876"},
    {code: "ESH", name: "Western Sahara", iso2: "EH", isoNumeric: "732"},
    {code: "YEM", name: "Yemen", iso2: "YE", isoNumeric: "887"},
    {code: "ZMB", name: "Zambia", iso2: "ZM", isoNumeric: "894"},
    {code: "ZWE", name: "Zimbabwe", iso2: "ZW", isoNumeric: "716"}
];

export function countryNameByCode(code: any){
    return countries.find(c => c.isoNumeric === code || c.code === code || c.iso2.toUpperCase() === code)?.name
    
}

export const currencyOptions = [
    {name: "Afghan Afghani", code: "AFA"},
    {name: "Albanian Lek", code: "ALL"},
    {name: "Algerian Dinar", code: "DZD"},
    {name: "Angolan Kwanza", code: "AOA"},
    {name: "Argentine Peso", code: "ARS"},
    {name: "Armenian Dram", code: "AMD"},
    {name: "Aruban Florin", code: "AWG"},
    {name: "Australian Dollar", code: "AUD"},
    {name: "Azerbaijani Manat", code: "AZN"},
    {name: "Bahamian Dollar", code: "BSD"},
    {name: "Bahraini Dinar", code: "BHD"},
    {name: "Bangladeshi Taka", code: "BDT"},
    {name: "Barbadian Dollar", code: "BBD"},
    {name: "Belarusian Ruble", code: "BYR"},
    {name: "Belgian Franc", code: "BEF"},
    {name: "Belize Dollar", code: "BZD"},
    {name: "Bermudan Dollar", code: "BMD"},
    {name: "Bhutanese Ngultrum", code: "BTN"},
    {name: "Bitcoin", code: "BTC"},
    {name: "Bolivian Boliviano", code: "BOB"},
    {name: "Bosnia-Herzegovina Convertible Mark", code: "BAM"},
    {name: "Botswanan Pula", code: "BWP"},
    {name: "Brazilian Real", code: "BRL"},
    {name: "British Pound Sterling", code: "GBP"},
    {name: "Brunei Dollar", code: "BND"},
    {name: "Bulgarian Lev", code: "BGN"},
    {name: "Burundian Franc", code: "BIF"},
    {name: "Cambodian Riel", code: "KHR"},
    {name: "Canadian Dollar", code: "CAD"},
    {name: "Cape Verdean Escudo", code: "CVE"},
    {name: "Cayman Islands Dollar", code: "KYD"},
    {name: "CFA Franc BCEAO", code: "XOF"},
    {name: "CFA Franc BEAC", code: "XAF"},
    {name: "CFP Franc", code: "XPF"},
    {name: "Chilean Peso", code: "CLP"},
    {name: "Chilean Unit of Account", code: "CLF"},
    {name: "Chinese Yuan", code: "CNY"},
    {name: "Colombian Peso", code: "COP"},
    {name: "Comorian Franc", code: "KMF"},
    {name: "Congolese Franc", code: "CDF"},
    {name: "Costa Rican Colón", code: "CRC"},
    {name: "Croatian Kuna", code: "HRK"},
    {name: "Cuban Convertible Peso", code: "CUC"},
    {name: "Czech Republic Koruna", code: "CZK"},
    {name: "Danish Krone", code: "DKK"},
    {name: "Djiboutian Franc", code: "DJF"},
    {name: "Dominican Peso", code: "DOP"},
    {name: "East Caribbean Dollar", code: "XCD"},
    {name: "Egyptian Pound", code: "EGP"},
    {name: "Eritrean Nakfa", code: "ERN"},
    {name: "Estonian Kroon", code: "EEK"},
    {name: "Ethiopian Birr", code: "ETB"},
    {name: "Euro", code: "EUR"},
    {name: "Falkland Islands Pound", code: "FKP"},
    {name: "Fijian Dollar", code: "FJD"},
    {name: "Gambian Dalasi", code: "GMD"},
    {name: "Georgian Lari", code: "GEL"},
    {name: "German Mark", code: "DEM"},
    {name: "Ghanaian Cedi", code: "GHS"},
    {name: "Gibraltar Pound", code: "GIP"},
    {name: "Greek Drachma", code: "GRD"},
    {name: "Guatemalan Quetzal", code: "GTQ"},
    {name: "Guinean Franc", code: "GNF"},
    {name: "Guyanaese Dollar", code: "GYD"},
    {name: "Haitian Gourde", code: "HTG"},
    {name: "Honduran Lempira", code: "HNL"},
    {name: "Hong Kong Dollar", code: "HKD"},
    {name: "Hungarian Forint", code: "HUF"},
    {name: "Icelandic Króna", code: "ISK"},
    {name: "Indian Rupee", code: "INR"},
    {name: "Indonesian Rupiah", code: "IDR"},
    {name: "Iranian Rial", code: "IRR"},
    {name: "Iraqi Dinar", code: "IQD"},
    {name: "Israeli New Sheqel", code: "ILS"},
    {name: "Italian Lira", code: "ITL"},
    {name: "Jamaican Dollar", code: "JMD"},
    {name: "Japanese Yen", code: "JPY"},
    {name: "Jordanian Dinar", code: "JOD"},
    {name: "Kazakhstani Tenge", code: "KZT"},
    {name: "Kenyan Shilling", code: "KES"},
    {name: "Kuwaiti Dinar", code: "KWD"},
    {name: "Kyrgystani Som", code: "KGS"},
    {name: "Laotian Kip", code: "LAK"},
    {name: "Latvian Lats", code: "LVL"},
    {name: "Lebanese Pound", code: "LBP"},
    {name: "Lesotho Loti", code: "LSL"},
    {name: "Liberian Dollar", code: "LRD"},
    {name: "Libyan Dinar", code: "LYD"},
    {name: "Litecoin", code: "LTC"},
    {name: "Lithuanian Litas", code: "LTL"},
    {name: "Macanese Pataca", code: "MOP"},
    {name: "Macedonian Denar", code: "MKD"},
    {name: "Malagasy Ariary", code: "MGA"},
    {name: "Malawian Kwacha", code: "MWK"},
    {name: "Malaysian Ringgit", code: "MYR"},
    {name: "Maldivian Rufiyaa", code: "MVR"},
    {name: "Mauritanian Ouguiya", code: "MRO"},
    {name: "Mauritian Rupee", code: "MUR"},
    {name: "Mexican Peso", code: "MXN"},
    {name: "Moldovan Leu", code: "MDL"},
    {name: "Mongolian Tugrik", code: "MNT"},
    {name: "Moroccan Dirham", code: "MAD"},
    {name: "Mozambican Metical", code: "MZM"},
    {name: "Myanmar Kyat", code: "MMK"},
    {name: "Namibian Dollar", code: "NAD"},
    {name: "Nepalese Rupee", code: "NPR"},
    {name: "Netherlands Antillean Guilder", code: "ANG"},
    {name: "New Taiwan Dollar", code: "TWD"},
    {name: "New Zealand Dollar", code: "NZD"},
    {name: "Nicaraguan Córdoba", code: "NIO"},
    {name: "Nigerian Naira", code: "NGN"},
    {name: "North Korean Won", code: "KPW"},
    {name: "Norwegian Krone", code: "NOK"},
    {name: "Omani Rial", code: "OMR"},
    {name: "Pakistani Rupee", code: "PKR"},
    {name: "Panamanian Balboa", code: "PAB"},
    {name: "Papua New Guinean Kina", code: "PGK"},
    {name: "Paraguayan Guarani", code: "PYG"},
    {name: "Peruvian Nuevo Sol", code: "PEN"},
    {name: "Philippine Peso", code: "PHP"},
    {name: "Polish Zloty", code: "PLN"},
    {name: "Qatari Rial", code: "QAR"},
    {name: "Romanian Leu", code: "RON"},
    {name: "Russian Ruble", code: "RUB"},
    {name: "Rwandan Franc", code: "RWF"},
    {name: "Salvadoran Colón", code: "SVC"},
    {name: "Samoan Tala", code: "WST"},
    {name: "São Tomé and Príncipe Dobra", code: "STD"},
    {name: "Saudi Riyal", code: "SAR"},
    {name: "Serbian Dinar", code: "RSD"},
    {name: "Seychellois Rupee", code: "SCR"},
    {name: "Sierra Leonean Leone", code: "SLL"},
    {name: "Singapore Dollar", code: "SGD"},
    {name: "Slovak Koruna", code: "SKK"},
    {name: "Solomon Islands Dollar", code: "SBD"},
    {name: "Somali Shilling", code: "SOS"},
    {name: "South African Rand", code: "ZAR"},
    {name: "South Korean Won", code: "KRW"},
    {name: "South Sudanese Pound", code: "SSP"},
    {name: "Special Drawing Rights", code: "XDR"},
    {name: "Sri Lankan Rupee", code: "LKR"},
    {name: "St. Helena Pound", code: "SHP"},
    {name: "Sudanese Pound", code: "SDG"},
    {name: "Surinamese Dollar", code: "SRD"},
    {name: "Swazi Lilangeni", code: "SZL"},
    {name: "Swedish Krona", code: "SEK"},
    {name: "Swiss Franc", code: "CHF"},
    {name: "Syrian Pound", code: "SYP"},
    {name: "Tajikistani Somoni", code: "TJS"},
    {name: "Tanzanian Shilling", code: "TZS"},
    {name: "Thai Baht", code: "THB"},
    {name: "Tongan Pa'anga", code: "TOP"},
    {name: "Trinidad & Tobago Dollar", code: "TTD"},
    {name: "Tunisian Dinar", code: "TND"},
    {name: "Turkish Lira", code: "TRY"},
    {name: "Turkmenistani Manat", code: "TMT"},
    {name: "Ugandan Shilling", code: "UGX"},
    {name: "Ukrainian Hryvnia", code: "UAH"},
    {name: "United Arab Emirates Dirham", code: "AED"},
    {name: "Uruguayan Peso", code: "UYU"},
    {name: "US Dollar", code: "USD"},
    {name: "Uzbekistan Som", code: "UZS"},
    {name: "Vanuatu Vatu", code: "VUV"},
    {name: "Venezuelan BolÃvar", code: "VEF"},
    {name: "Vietnamese Dong", code: "VND"},
    {name: "Yemeni Rial", code: "YER"},
    {name: "Zambian Kwacha", code: "ZMK"},
    {name: "Zimbabwean dollar", code: "ZWL"}
];



const restrictedWords = [
    // English
    'password', 'admin', '123456', 'qwerty', 'letmein', 'monkey', 'abc123', 'welcome',
    'fuck', 'shit', 'ass', 'asshole', 'bitch', 'bastard', 'cunt', 'dick', 'cock', 'pussy',
    'whore', 'slut', 'piss', 'damn', 'goddamn', 'motherfucker', 'wanker', 'bullshit',
    'fuckoff', 'twat', 'prick',

    // Bulgarian
    'парола', 'админ', 'мамка', 'глупак', 'простак', 'шибан', 'курва', 'путка', 'пичка',
    'гъз', 'дупе', 'копеле', 'шибаняк', 'педераст', 'лайно', 'гомнар', 'скапаняк',
    'кур', 'пишка', 'задник',

    // Serbian/Croatian/Bosnian
    'lozinka', 'sranje', 'govno', 'jebati', 'kurac', 'pička', 'dupe', 'šupak', 'sisa', 'kurčina',
    'pizda', 'jebač', 'jebeno', 'jebi', 'jebote', 'izdrkati', 'pizdarija', 'guzica', 'srati',

    // Czech
    'heslo', 'kurva', 'hovno', 'prdel', 'čurák', 'píča', 'kokot', 'prdět', 'posrat', 'zmrd',
    'zkurvysyn', 'do piče', 'pičus', 'curak', 'jebat', 'mrdka', 'čůrák', 'vyjebaný', 'nasrat',

    // Danish
    'kodeord', 'fanden', 'helvede', 'lort', 'pik', 'fisse', 'røv', 'røvhul', 'skid', 'knep',
    'fuck', 'pis', 'kraftedeme', 'satan', 'svin', 'kælling', 'kusse', 'patter', 'skide',

    // Dutch
    'wachtwoord', 'kut', 'lul', 'klootzak', 'eikel', 'kanker', 'tyfus', 'godverdomme',
    'klote', 'neuk', 'neuken', 'hoer', 'slet', 'trut', 'reet', 'kontgat', 'kakker', 'schijt', 'pik',

    // Estonian
    'parool', 'perse', 'sitt', 'munn', 'vittu', 'türa', 'pask', 'persevest', 'lirva', 'lits',
    'jobu', 'puts', 'sitapea', 'nuss', 'vastik', 'kuradi', 'persse', 'sitane', 'munni',

    // Finnish
    'salasana', 'perkele', 'vittu', 'paska', 'kyrpä', 'helvetti', 'saatana', 'jumalauta',
    'pillu', 'kusipää', 'mulkku', 'perse', 'paskainen', 'runkata', 'vittumainen',
    'paskapää', 'huora', 'lutka', 'persereikä',

    // French
    'motdepasse', 'merde', 'putain', 'connard', 'enculé', 'salope', 'con', 'foutre', 'bite',
    'cul', 'couilles', 'nique', 'niquer', 'branler', 'bordel', 'chier', 'baiser', 'pute', 'branleur',

    // German
    'passwort', 'scheiße', 'arschloch', 'wichser', 'fotze', 'hurensohn', 'schwanz', 'fick',
    'ficken', 'scheißkerl', 'kacke', 'muschi', 'schwuchtel', 'hure', 'nutte', 'pisser',
    'scheisse', 'verpiss', 'mistkerl',

    // Greek
    'κωδικός', 'μαλάκας', 'σκατά', 'πούστης', 'καριόλης', 'γαμώ', 'γαμιέσαι', 'κώλος',
    'βλάκας', 'μουνί', 'μουνόπανο', 'παλιομαλάκας', 'γαμημένος', 'καργιόλης', 'τσόλι',
    'αρχίδια', 'γαμιόλης', 'πουτάνα', 'κωλοτρυπίδα', 'παπάρας',

    // Hungarian
    'jelszó', 'szar', 'fasz', 'kurva', 'baszik', 'picsa', 'pina', 'segg', 'seggfej', 'baszd',
    'geci', 'köcsög', 'anyád', 'baszott', 'szaros', 'fasszopó', 'bazdmeg', 'ribanc', 'faszfej',

    // Icelandic
    'lykilorð', 'skítur', 'rassgat', 'fokk', 'tík', 'andskotinn', 'djöfull', 'helvíti', 'hóra',
    'píka', 'typpi', 'drullusokkur', 'fífl', 'mella', 'fáviti', 'hálfviti', 'skítseiði', 'skíthæll',

    // Italian
    'password', 'cazzo', 'merda', 'stronzo', 'vaffanculo', 'puttana', 'troia', 'fica', 'figa',
    'minchia', 'coglione', 'fottere', 'inculare', 'porca', 'pompino', 'mignotta', 'tette',
    'figlio di puttana', 'stronza',

    // Latvian
    'parole', 'sūds', 'dirsa', 'pimpis', 'mauka', 'pēdējs', 'pisties', 'pakaļa', 'sūkāt',
    'izpist', 'maukas dēls', 'pizdec', 'mēsls', 'stulbenis', 'mūlāps', 'pidars', 'kuce',
    'dirsā', 'sūdīgs',

    // Lithuanian
    'slaptažodis', 'šūdas', 'kalė', 'žaltys', 'pyzdiec', 'bybi', 'subinė', 'šiknius', 'kurva',
    'šūdinas', 'pisti', 'išpisti', 'nachui', 'pize', 'šikti', 'bybis', 'kalės vaikas', 'kekšė', 'nahui',

    // Maltese
    'password', 'għoxx', 'hara', 'żobb', 'liba', 'ħara', 'qaħba', 'nieklu', 'żobbok', 'ommok',
    'liva', 'żobbi', 'pajjiz', 'għoxx ommok', 'għoxxok', 'foxx', 'tikomli', 'kilba', 'tiżżobb',

    // Norwegian
    'passord', 'faen', 'helvete', 'dritt', 'kukk', 'fitte', 'ræva', 'rasshøl', 'føkk', 'satan',
    'hore', 'pikk', 'kuk', 'drittsekk', 'hestkuk', 'jævla', 'forpult', 'kødd', 'fittetryne',

    // Polish
    'hasło', 'kurwa', 'gówno', 'dupa', 'chuj', 'pierdolić', 'jebać', 'pizda', 'kutas',
    'skurwysyn', 'pojeb', 'szmata', 'cipa', 'dziwka', 'fiut', 'huj', 'spierdalaj', 'spermojad', 'zjeb',

    // Portuguese
    'senha', 'merda', 'caralho', 'puta', 'foder', 'porra', 'filho da puta', 'cu', 'bosta',
    'cagar', 'buceta', 'punheta', 'vadia', 'cacete', 'piça', 'cuzão', 'fodido', 'rapariga', 'pica',

    // Romanian
    'parolă', 'căcat', 'pulă', 'pizdă', 'fut', 'futu-te', 'sugaci', 'muie', 'curva', 'măta',
    'găoz', 'poponar', 'pula', 'sugi', 'sugi pula', 'morții', 'pizda', 'papagal', 'prost',

    // Slovak
    'heslo', 'hovno', 'kokot', 'kurva', 'jebať', 'piča', 'kokotina', 'chuj', 'riť', 'jebnutý',
    'čurák', 'hajzel', 'pojebať', 'mrdka', 'skurvysyn', 'prdel', 'šukať', 'sračka', 'zmrd',

    // Slovenian
    'geslo', 'drek', 'pizda', 'kurac', 'jebati', 'sranje', 'rit', 'kurec', 'fukati', 'pička',
    'prasec', 'fuk', 'jebi se', 'kurba', 'prasica', 'kurčev', 'zajebati',

    // Spanish
    'contraseña', 'mierda', 'joder', 'cojones', 'puta', 'follar', 'cabrón', 'coño',
    'gilipollas', 'hostia', 'jodete', 'mamón', 'marica', 'pendejo', 'pollas', 'puto',
    'zorra', 'imbécil', 'hijoputa',

    // Swedish
    'lösenord', 'skit', 'fan', 'helvete', 'fitta', 'kuk', 'röv', 'arsle', 'jävlar', 'satan',
    'hora', 'knulla', 'bajs', 'balle', 'tusan', 'rövhål', 'kuksugare', 'fan i helvete', 'surfitta'
];
