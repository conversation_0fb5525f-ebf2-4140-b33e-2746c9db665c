//@ts-nocheck
import { jwtVerify } from "jose"

export interface UserJwtPayload {
    id: string
    email: string
    dashboard?: string
    requires2FA?: boolean
    iat: number
    exp: number
}

/**
 * Verify JWT token and return the payload
 */
export async function verifyToken(token: string): Promise<UserJwtPayload | null> {
    if (!token) return null

    try {
        const secret = new TextEncoder().encode(process.env.JWT_SECRET)
        const { payload } = await jwtVerify(token, secret)
        return payload as UserJwtPayload
    } catch (error) {
        console.error("Token verification failed:", error)
        return null
    }
}

/**
 * Check if the user is authenticated and has completed 2FA if required
 */
export async function isAuthenticated(token: string): Promise<boolean> {
    const payload = await verifyToken(token)

    if (!payload) return false

    // If requires2FA is true, the user hasn't completed 2FA verification
    if (payload.requires2FA === true) return false

    return true
}

/**
 * Check if the user has access to a specific dashboard
 */
export async function hasAccess(token: string, requiredDashboard: string): Promise<boolean> {
    const payload = await verifyToken(token)

    if (!payload) return false
    if (payload.requires2FA === true) return false

    return payload.dashboard === requiredDashboard
}
