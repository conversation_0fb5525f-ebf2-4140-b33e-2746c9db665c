// utils/helpers.ts

// Formats a date string to 'DD MMM YYYY'
export const formatDate = (dateString: string): string => {
    const options: Intl.DateTimeFormatOptions = {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit"
    }
    return new Date(dateString).toLocaleString('en-GB', options)
}


export const formatDob = (dateString: string): string => {
    const options: Intl.DateTimeFormatOptions = {
        year: "numeric",
        month: "long",
        day: "numeric"
    }
    return new Date(dateString).toLocaleString('en-GB', options)
}


// Function to capitalize the first letter of a string
export const capitalizeFirstLetter = (text: string): string => {
    return text.charAt(0).toUpperCase() + text.slice(1)
}

// Function to check if an object is empty
export const isEmptyObject = (obj: object): boolean => {
    return Object.keys(obj).length === 0
}

// Function to format currency (e.g., to GBP)
export const formatCurrency = (amount: number, currency: string = 'GBP'): string => {
    return new Intl.NumberFormat('en-GB', { style: 'currency', currency }).format(amount)
}

// Add more functions here as needed
export function calculateRiskLevel(score: number): string {
    if (score < 0) {
        throw new Error("Score cannot be negative.");
    }

    if (score <= 199) {
        return "Low";
    } else if (score <= 399) {
        return "Medium";
    } else if (score <= 599) {
        return "High";
    } else if (score <= 899) {
        return "Very High";
    } else {
        return "DO NOT ONBOARD";
    }
}


export const generateSerialNumber = (lastSerial?: string): string => {
    // Prefix and year
    const prefix = "RYI-";
    const year = new Date().getFullYear().toString().slice(-2); // Get last 2 digits of the year

    // Extract the numeric part from the last serial number, if provided
    let counter = 1; // Default start value
    if (lastSerial) {
        const match = lastSerial.match(/RYI-\d{2}(\d+)/);
        if (match && match[1]) {
            counter = parseInt(match[1], 10) + 1; // Increment the counter
        }
    }

    // Pad the counter with leading zeros to ensure it is always 7 digits
    const counterString = counter.toString().padStart(7, "0");

    // Combine the parts
    return `${prefix}${year}${counterString}`;
};

