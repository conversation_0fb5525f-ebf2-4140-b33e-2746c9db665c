import Swal from 'sweetalert2';

type AlertType = 'success' | 'error' | 'warning' | 'info';

const showToast = (message: string, type: AlertType = 'info') => {
    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
    });

    Toast.fire({
        icon: type,
        title: message
    });
};

const showAlert = (title: string, message: string, type: AlertType = 'info') => {
    Swal.fire({
        title: title,
        text: message,
        icon: type,
        confirmButtonText: 'OK'
    });
};

const showConfirm = (title: string, message: string, confirmButtonText: string = 'Yes', cancelButtonText: string = 'No') => {
    return Swal.fire({
        title: title,
        text: message,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: confirmButtonText,
        cancelButtonText: cancelButtonText,
        confirmButtonColor: '#d33', // Blue color for confirm button
        cancelButtonColor: '#00a8a5', // Red color for cancel button


    });
};

export const alertHelper = {
    showToast,
    showAlert,
    showConfirm
};