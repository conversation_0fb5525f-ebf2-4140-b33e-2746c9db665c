//@ts-nocheck
import jsPDF from "jspdf"
import { formatDate, formatDob, calculateRiskLevel } from "./helpers"
import { country_currency, countryNameByCode } from "./data"

interface OnboardingData {
    _id: string
    clientID: string
    personalInfo: {
        firstName: string
        middleName?: string
        lastName: string
        email: string
        phone: string
        authPhoneNumber: string
        dateOfBirth: string
        mothersMaidenName?: string
    }
    address: {
        street: string
        buildingNumber: string
        apartmentNumber?: string
        city: string
        stateProvince: string
        zipCode: string
        country: string
    }
    idDocument?: {
        customerIdType: string
        number: string
        issueDate: string
        expiryDate: string
        idAuthority: string
        issuingCountry: string
    }
    taxInfo?: {
        country: string
        taxIdNumber: string
    }
    citizenship: string
    riskLevel: number
    riskStatus: string
    applicationStatus: string
    createdAt: string
    updatedAt: string
    company?: {
        company_name: string
    }
}

interface AccountData {
    accountNumber: string
    accountHolder?: string
    bankName?: string
    bankNumber?: string
    currency: number
}

interface CardData {
    embossName1: string
    cardMask: string
    expDate: string
    status: string
    createdAt: string
    cardType?: string
}

export const generateProfilePDF = (
    onboarding: OnboardingData,
    account: AccountData | null,
    cards: CardData[],
    accountBalance: number | null,
) => {
    const doc = new jsPDF()
    let yPosition = 20
    const pageWidth = doc.internal.pageSize.width
    const margin = 20
    const contentWidth = pageWidth - margin * 2
    let isAlternateRow = false

    // Helper function to add a new page if needed
    const checkPageBreak = (requiredSpace = 20) => {
        if (yPosition + requiredSpace > doc.internal.pageSize.height - 20) {
            doc.addPage()
            yPosition = 20
            isAlternateRow = false
        }
    }

    // Helper function to add section header
    const addSectionHeader = (title: string, color = "#01a9a5") => {
        checkPageBreak(30)
        doc.setFillColor(color)
        doc.rect(margin, yPosition, contentWidth, 8, "F")
        doc.setTextColor(255, 255, 255)
        doc.setFontSize(12)
        doc.setFont("helvetica", "bold")
        doc.text(title, margin + 5, yPosition + 6)
        yPosition += 15
        doc.setTextColor(0, 0, 0)
        isAlternateRow = false
    }

    // Helper function to add field
    const addField = (label: string, value: string, isSubField = false) => {
        checkPageBreak()
        const indent = isSubField ? margin + 10 : margin

        // Add subtle background for alternate rows
        if (!isSubField && !isAlternateRow) {
            doc.setFillColor(245, 250, 250) // Very light teal tint
            doc.rect(margin - 2, yPosition - 4, contentWidth + 4, 10, "F")
        }

        // Add label
        doc.setFont("helvetica", "bold")
        doc.setFontSize(9)
        doc.text(label + ":", indent, yPosition)

        // Calculate positions
        const labelWidth = doc.getTextWidth(label + ": ")
        const maxValueWidth = contentWidth - labelWidth - (isSubField ? 10 : 0)

        // Add value with improved spacing
        doc.setFont("helvetica", "normal")

        // Handle long text wrapping
        const lines = doc.splitTextToSize(value || "N/A", maxValueWidth)
        doc.text(lines, indent + labelWidth + 5, yPosition) // Added +5 for extra spacing

        // Increase vertical spacing between fields
        yPosition += lines.length * 5 + 2 // Added +2 for extra spacing

        // Toggle alternate row flag if not a subfield
        if (!isSubField) {
            isAlternateRow = !isAlternateRow
        }
    }

    // Helper function to add a divider
    const addDivider = () => {
        checkPageBreak(5)
        doc.setDrawColor(220, 220, 220) // Light gray
        doc.line(margin, yPosition - 2, margin + contentWidth, yPosition - 2)
        yPosition += 5
    }

    // Header
    doc.setFillColor(1, 169, 165) // #01a9a5 in RGB
    doc.rect(0, 0, pageWidth, 25, "F")
    doc.setTextColor(255, 255, 255)
    doc.setFontSize(18)
    doc.setFont("helvetica", "bold")
    doc.text("Customer Profile Report", margin, 15)

    doc.setFontSize(10)
    doc.setFont("helvetica", "normal")
    doc.text(`Generated on: ${new Date().toLocaleDateString()}`, pageWidth - margin - 60, 15)

    yPosition = 35
    doc.setTextColor(0, 0, 0)

    // Customer Name and ID
    doc.setFontSize(16)
    doc.setFont("helvetica", "bold")
    doc.text(`${onboarding.personalInfo.firstName} ${onboarding.personalInfo.lastName}`, margin, yPosition)
    yPosition += 8

    doc.setFontSize(10)
    doc.setFont("helvetica", "normal")
    doc.text(`Client ID: ${onboarding.clientID}`, margin, yPosition)
    yPosition += 15

    // Personal Information Section
    addSectionHeader("Personal Information")
    addField(
        "Full Name",
        `${onboarding.personalInfo.firstName} ${onboarding.personalInfo.middleName || ""} ${onboarding.personalInfo.lastName}`.trim(),
    )
    addField("Date of Birth", formatDob(onboarding.personalInfo.dateOfBirth))
    addField("Nationality", countryNameByCode(onboarding.citizenship))
    addField("Email Address", onboarding.personalInfo.email)
    addField("Phone Number", onboarding.personalInfo.phone)
    addField("Auth Phone Number", onboarding.personalInfo.authPhoneNumber)
    if (onboarding.personalInfo.mothersMaidenName) {
        addField("Mother's Maiden Name", onboarding.personalInfo.mothersMaidenName)
    }
    yPosition += 5
    addDivider()
    yPosition += 5

    // Address Information Section
    addSectionHeader("Address Information")
    const fullAddress = `${onboarding.address.street} ${onboarding.address.buildingNumber}${onboarding.address.apartmentNumber ? ", Apt " + onboarding.address.apartmentNumber : ""}, ${onboarding.address.city}, ${onboarding.address.stateProvince} ${onboarding.address.zipCode}, ${countryNameByCode(onboarding.address.country)}`
    addField("Primary Address", fullAddress)
    yPosition += 5
    addDivider()
    yPosition += 5

    // ID Document Section
    if (onboarding.idDocument) {
        addSectionHeader("Identity Document")
        addField("Document Type", onboarding.idDocument.customerIdType)
        addField("Document Number", onboarding.idDocument.number)
        addField("Issue Date", onboarding.idDocument.issueDate)
        addField("Expiry Date", onboarding.idDocument.expiryDate)
        addField("Issuing Authority", onboarding.idDocument.idAuthority)
        addField("Issuing Country", countryNameByCode(onboarding.idDocument.issuingCountry))
        yPosition += 5
        addDivider()
        yPosition += 5
    }

    // Tax Information Section
    if (onboarding.taxInfo) {
        addSectionHeader("Tax Information")
        addField("Tax Country", countryNameByCode(onboarding.taxInfo.country))
        addField("Tax ID Number", onboarding.taxInfo.taxIdNumber)
        yPosition += 5
        addDivider()
        yPosition += 5
    }

    // Account Information Section
    if (account) {
        addSectionHeader("Account Information")
        addField("Account Number (IBAN)", account.accountNumber)
        addField(
            "Account Holder",
            account.accountHolder || `${onboarding.personalInfo.firstName} ${onboarding.personalInfo.lastName}`,
        )
        addField("Bank Name", account.bankName || "Ryvyl EU")
        addField("SWIFT Code", account.bankNumber || "Not Specified")

        if (accountBalance !== null) {
            const currency = country_currency.find((r) => r.numericCode === account.currency)?.currencyCode || "EUR"
            const formattedBalance = new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: currency,
            }).format(accountBalance)
            addField("Available Balance", formattedBalance)
        }
        yPosition += 5
        addDivider()
        yPosition += 5
    }

    // Cards Information Section
    if (cards && cards.length > 0) {
        addSectionHeader("Payment Cards")
        cards.forEach((card, index) => {
            addField(`Card ${index + 1}`, "")
            addField("Cardholder Name", card.embossName1, true)
            addField("Card Number", `•••• •••• •••• ${card.cardMask.slice(-4)}`, true)
            addField("Expiry Date", card.expDate, true)
            addField("Status", card.status, true)
            addField("Created Date", formatDate(card.createdAt), true)
            yPosition += 5
        })
        yPosition += 5
        addDivider()
        yPosition += 5
    }

    // Risk Assessment Section
    addSectionHeader("Risk Assessment")
    addField("Risk Score", onboarding.riskLevel?.toString() || "0")
    addField("Risk Level", calculateRiskLevel(onboarding.riskLevel))
    addField("Risk Status", onboarding.riskStatus)
    yPosition += 5
    addDivider()
    yPosition += 5

    // Application Details Section
    addSectionHeader("Application Details")
    addField("Application Status", onboarding.applicationStatus)
    addField("Application Date", formatDate(onboarding.createdAt))
    addField("Last Updated", formatDate(onboarding.updatedAt))
    if (onboarding.company) {
        addField("Company", onboarding.company.company_name)
    }
    yPosition += 5
    addDivider()
    yPosition += 5

    // Technical Information Section
    addSectionHeader("Technical Information")
    addField("Record ID", onboarding._id)
    addField("Client Code", onboarding.clientID)
    yPosition += 5
    addDivider()
    yPosition += 5

    // Footer
    const pageCount = doc.getNumberOfPages()
    for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i)
        doc.setFontSize(8)
        doc.setTextColor(128, 128, 128)
        doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin - 20, doc.internal.pageSize.height - 10)

        // Add a teal footer line
        doc.setDrawColor(1, 169, 165)
        doc.setLineWidth(0.5)
        doc.line(margin, doc.internal.pageSize.height - 15, pageWidth - margin, doc.internal.pageSize.height - 15)

        doc.text("Confidential - Customer Profile Report", margin, doc.internal.pageSize.height - 10)
    }

    // Generate filename and save
    const fileName = `${onboarding.personalInfo.firstName}_${onboarding.personalInfo.lastName}_Profile_${new Date().toISOString().split("T")[0]}.pdf`
    doc.save(fileName)
}
