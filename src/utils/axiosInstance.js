import axios from "axios"

const axiosInstance = axios.create({
    // baseURL: "https://ryvyl-cards-zxql.vercel.app/api/",
    baseURL: process.env.NEXT_PUBLIC_API_URL + "/api/",
    withCredentials: true,
})

// Helper function to get user info from token
const getUserInfoFromToken = () => {
    try {
        const token = localStorage.getItem("authToken")
        if (!token) return null

        const payload = token.split(".")[1]
        if (!payload) return null

        const decoded = JSON.parse(atob(payload))
        return {
            userId: decoded.id || decoded.sub || decoded.userId,
            userEmail: decoded.email,
            userName: decoded.name || decoded.username,
        }
    } catch (error) {
        console.error("Error decoding token:", error)
        return null
    }
}

// Helper function to set token in cookie (for server-side access)
const setTokenCookie = (token) => {
    try {
        // Set cookie that expires in 7 days
        const expirationDate = new Date()
        expirationDate.setDate(expirationDate.getDate() + 7)

        document.cookie = `authToken=${token}; expires=${expirationDate.toUTCString()}; path=/; SameSite=Lax; Secure=${window.location.protocol === "https:"}`
    } catch (error) {
        console.error("Error setting token cookie:", error)
    }
}

// Helper function to remove token cookie
const removeTokenCookie = () => {
    try {
        document.cookie = "authToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax"
    } catch (error) {
        console.error("Error removing token cookie:", error)
    }
}

// Helper function to determine action type from request
const getActionType = (method, url, data) => {
    const endpoint = url.replace(process.env.NEXT_PUBLIC_API_URL + "/api/", "")

    // Map common endpoints to action types
    const actionMappings = {
        // Authentication actions
        "auth/login": "user_login",
        "auth/logout": "user_logout",
        "auth/register": "user_register",
        "auth/forgot-password": "password_reset_request",
        "auth/reset-password": "password_reset",

        // Card actions
        "cards/create": "card_created",
        "cards/activate": "card_activated",
        "cards/deactivate": "card_deactivated",
        "cards/block": "card_blocked",
        "cards/unblock": "card_unblocked",

        // Transaction actions
        "transactions/create": "transaction_created",
        "transactions/approve": "transaction_approved",
        "transactions/decline": "transaction_declined",

        // Profile actions
        "profile/update": "profile_updated",
        "profile/change-password": "password_changed",
        "profile/upload-avatar": "avatar_uploaded",

        // Settings actions
        "settings/update": "settings_updated",
        "settings/notifications": "notification_settings_updated",
        "settings/security": "security_settings_updated",
    }

    // Check for exact endpoint match
    if (actionMappings[endpoint]) {
        return actionMappings[endpoint]
    }

    // Generate action based on method and endpoint
    const pathSegments = endpoint.split("/").filter(Boolean)
    const resource = pathSegments[0] || "unknown"

    switch (method.toUpperCase()) {
        case "POST":
            if (endpoint.includes("/upload")) return `${resource}_file_uploaded`
            if (endpoint.includes("/send")) return `${resource}_sent`
            if (endpoint.includes("/approve")) return `${resource}_approved`
            if (endpoint.includes("/reject")) return `${resource}_rejected`
            return `${resource}_created`

        case "PUT":
        case "PATCH":
            if (endpoint.includes("/activate")) return `${resource}_activated`
            if (endpoint.includes("/deactivate")) return `${resource}_deactivated`
            if (endpoint.includes("/enable")) return `${resource}_enabled`
            if (endpoint.includes("/disable")) return `${resource}_disabled`
            return `${resource}_updated`

        case "DELETE":
            return `${resource}_deleted`

        case "GET":
            if (endpoint.includes("/export")) return `${resource}_exported`
            if (endpoint.includes("/download")) return `${resource}_downloaded`
            return `${resource}_viewed`

        default:
            return `${resource}_action`
    }
}

// Helper function to get action description
const getActionDescription = (method, url, data, response) => {
    const endpoint = url.replace(process.env.NEXT_PUBLIC_API_URL + "/api/", "")
    const actionType = getActionType(method, url, data)

    // Create human-readable descriptions
    const descriptions = {
        user_login: "User logged into the system",
        user_logout: "User logged out of the system",
        user_register: "New user account created",
        password_reset_request: "Password reset requested",
        password_reset: "Password was reset",
        card_created: "New card was created",
        card_activated: "Card was activated",
        card_deactivated: "Card was deactivated",
        card_blocked: "Card was blocked",
        card_unblocked: "Card was unblocked",
        transaction_created: "New transaction was initiated",
        transaction_approved: "Transaction was approved",
        transaction_declined: "Transaction was declined",
        profile_updated: "User profile was updated",
        password_changed: "User password was changed",
        avatar_uploaded: "Profile avatar was uploaded",
        settings_updated: "User settings were updated",
        notification_settings_updated: "Notification preferences were updated",
        security_settings_updated: "Security settings were updated",
    }

    if (descriptions[actionType]) {
        return descriptions[actionType]
    }

    // Generate generic description
    const resource = endpoint.split("/")[0] || "resource"
    const action = method.toLowerCase()

    switch (action) {
        case "post":
            return `Created new ${resource}`
        case "put":
        case "patch":
            return `Updated ${resource}`
        case "delete":
            return `Deleted ${resource}`
        case "get":
            return `Retrieved ${resource} data`
        default:
            return `Performed ${action} on ${resource}`
    }
}

// Helper function to log API action
const logApiAction = async (requestConfig, response, error) => {
    try {
        const userInfo = getUserInfoFromToken()
        if (!userInfo?.userId) return // Don't log if no user

        const method = requestConfig.method?.toUpperCase() || "GET"
        const url = requestConfig.url || ""
        const fullUrl = url.startsWith("http") ? url : `${requestConfig.baseURL}${url}`

        // Don't log the activity logging endpoint itself to avoid infinite loops
        if (url.includes("/activity") || url.includes("/log")) return

        const actionType = getActionType(method, fullUrl, requestConfig.data)
        const description = getActionDescription(method, fullUrl, requestConfig.data, response)

        const activityData = {
            // Request information
            url: fullUrl,
            pathname: `/api/${url.replace(process.env.NEXT_PUBLIC_API_URL + "/api/", "")}`,
            method: method,
            timestamp: new Date().toISOString(),

            // Action details
            actionType: actionType,
            description: description,

            // Request data (sanitized)
            requestData: requestConfig.data
                ? {
                    // Remove sensitive data
                    ...requestConfig.data,
                    password: requestConfig.data.password ? "[REDACTED]" : undefined,
                    token: requestConfig.data.token ? "[REDACTED]" : undefined,
                    secret: requestConfig.data.secret ? "[REDACTED]" : undefined,
                }
                : null,

            // Response data (sanitized)
            responseData: response?.data
                ? {
                    success: response.data.success,
                    message: response.data.message,
                    // Don't log full response data for privacy
                }
                : null,

            // Error information
            errorData: error
                ? {
                    status: error.response?.status,
                    message: error.message,
                    code: error.code,
                }
                : null,

            // User information
            user: {
                userId: userInfo.userId,
                userEmail: userInfo.userEmail,
                userName: userInfo.userName,
                isAuthenticated: true,
                authenticationMethod: "jwt",
                hasJWT: true,
                hasBearerToken: true,
                hasSessionToken: false,
                tokenSource: "localStorage",
            },

            // Device and browser info
            device: {
                isMobile: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent),
                isTablet: /iPad|Android(?!.*Mobile)/.test(navigator.userAgent),
                isDesktop: !/Mobile|Android|iPhone|iPad/.test(navigator.userAgent),
                isBot: false,
                platform: navigator.platform || null,
                userAgent: navigator.userAgent,
            },

            // Context
            context: {
                isApiRequest: true,
                isStaticAsset: false,
                hasQueryParams: fullUrl.includes("?"),
                isUserAction: true, // This is a user-initiated action
                actionCategory: actionType.split("_")[0], // e.g., 'card', 'user', 'transaction'
                actionVerb: actionType.split("_")[1] || "action", // e.g., 'created', 'updated', 'deleted'
            },

            // Geographic info (if available)
            geo: {
                country: null,
                region: null,
                city: null,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            },

            // Additional metadata
            metadata: {
                endpoint: url,
                userAgent: navigator.userAgent,
                referrer: document.referrer || null,
                currentPage: window.location.pathname,
                sessionId: sessionStorage.getItem("sessionId") || null,
            },
        }

        // Send to activity logging endpoint (non-blocking)
        fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/activity`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("authToken")}`,
            },
            body: JSON.stringify(activityData),
        }).catch((logError) => {
            console.error("Failed to log API activity:", logError)
        })
    } catch (logError) {
        console.error("Error in API activity logging:", logError)
    }
}

// Response interceptor
axiosInstance.interceptors.response.use(
    (response) => {
        // Log successful API actions
        logApiAction(response.config, response)

        // Handle login success - set cookie for server-side access
        if (response.config.url?.includes("/auth/login") && response.data?.token) {
            const token = response.data.token
            localStorage.setItem("authToken", token)
            setTokenCookie(token)
        }

        return response
    },
    (error) => {
        // Log failed API actions
        logApiAction(error.config, null, error)

        const currentUrl = encodeURIComponent(window.location.pathname + window.location.search)

        if (error.response && error.response.status === 401) {
            // Clear tokens on 401
            localStorage.removeItem("authToken")
            removeTokenCookie()

            if (typeof window !== "undefined") {
                console.error(error)
                // window.location.href = `/login?redirect=${currentUrl}`;
            }
        }

        return Promise.reject(error)
    },
)

// Request interceptor
axiosInstance.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem("authToken")

        if (token) {
            config.headers["Authorization"] = `Bearer ${token}`
            config.headers["x-client-time"] = new Date().toISOString()
            config.headers["Access-Control-Allow-Origin"] = `*`

            // IMPORTANT: Set the cookie so server can access the token
            // This ensures the server-side API can read the token from cookies
            if (typeof document !== "undefined") {
                const expirationDate = new Date()
                expirationDate.setDate(expirationDate.getDate() + 7)
                document.cookie = `authToken=${token}; expires=${expirationDate.toUTCString()}; path=/; SameSite=Lax; Secure=${window.location.protocol === "https:"}`
            }
        }

        return config
    },
    (error) => {
        return Promise.reject(error)
    },
)

// Export a function to manually set the auth token (useful for login)
export const setAuthToken = (token) => {
    localStorage.setItem("authToken", token)
    setTokenCookie(token)
}

// Export a function to clear the auth token (useful for logout)
export const clearAuthToken = () => {
    localStorage.removeItem("authToken")
    removeTokenCookie()
}

// Function to sync existing localStorage token to cookie
export const syncTokenToCookie = () => {
    const token = localStorage.getItem("authToken")
    if (token && typeof document !== "undefined") {
        const expirationDate = new Date()
        expirationDate.setDate(expirationDate.getDate() + 7)
        document.cookie = `authToken=${token}; expires=${expirationDate.toUTCString()}; path=/; SameSite=Lax; Secure=${window.location.protocol === "https:"}`
    }
}

export default axiosInstance
