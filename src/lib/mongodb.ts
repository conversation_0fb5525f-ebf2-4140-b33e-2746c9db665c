import { MongoClient, type Db, type Collection } from "mongodb"

const MONGODB_URI = "mongodb+srv://hostingstratified20:<EMAIL>/ryvyl"
const DB_NAME = "ryvyl"

let client: MongoClient
let db: Db

export async function connectToDatabase(): Promise<Db> {
  if (db) {
    return db
  }

  try {
    client = new MongoClient(MONGODB_URI)
    await client.connect()
    db = client.db(DB_NAME)
    console.log("Connected to MongoDB")
    return db
  } catch (error) {
    console.error("Failed to connect to MongoDB:", error)
    throw error
  }
}

export async function getCollection(collectionName = "DHLDeliveryMethods"): Promise<Collection> {
  try {
    const database = await connectToDatabase()
    return database.collection(collectionName)
  } catch (error) {
    console.error("Error getting collection:", error)
    throw error
  }
}

export interface PocztaPostRecord {
  _id?: string
  // Dynamic fields from Polish Post delivery method data will be added here
  [key: string]: any
  // Metadata fields
  _fileName: string
  _sheetName: string
  _rowIndex: number
  _totalRows: number
  _uploadedAt: Date
  _createdAt: Date
  _updatedAt: Date
}

export interface FileMetadata {
  _id?: string
  fileName: string
  sheetName: string
  totalRows: number
  totalColumns: number
  headers: string[]
  uploadedAt: Date
  createdAt: Date
}

export async function saveRecordsToMongoDB(
    records: Array<{
      fileName: string
      sheetName: string
      headers: string[]
      rowData: Record<string, any>
      rowIndex: number
      totalRows: number
    }>,
    onProgress: (saved: number, total: number) => void,
): Promise<string[]> {
  const BATCH_SIZE = 1000
  const savedIds: string[] = []
  let savedCount = 0

  try {
    const database = await connectToDatabase()
    const collection: Collection<PocztaPostRecord> = database.collection("PocztaPost")

    // Split records into batches
    for (let i = 0; i < records.length; i += BATCH_SIZE) {
      const batchRecords = records.slice(i, i + BATCH_SIZE)

      // Transform records to flat structure with metadata
      const transformedRecords = batchRecords.map((record) => {
        const now = new Date()

        // Create a flat document with all Excel columns as top-level fields
        const flatRecord: PocztaPostRecord = {
          // Add all Excel data as individual fields
          ...record.rowData,

          // Add metadata with underscore prefix to avoid conflicts
          _fileName: record.fileName,
          _sheetName: record.sheetName,
          _rowIndex: record.rowIndex,
          _totalRows: record.totalRows,
          _uploadedAt: now,
          _createdAt: now,
          _updatedAt: now,
        }

        // Clean up any undefined or null values and convert them to null
        Object.keys(flatRecord).forEach((key) => {
          if (flatRecord[key] === undefined || flatRecord[key] === "") {
            flatRecord[key] = null
          }
        })

        return flatRecord
      })

      try {
        // Insert the batch
        const result = await collection.insertMany(transformedRecords, { ordered: false })

        // Add the IDs to our saved list
        Object.values(result.insertedIds).forEach((id) => savedIds.push(id.toString()))

        // Update progress
        savedCount += batchRecords.length
        onProgress(savedCount, records.length)

        // Small delay to prevent overwhelming the UI
        await new Promise((resolve) => setTimeout(resolve, 50))
      } catch (batchError) {
        console.error(`Error saving batch ${i / BATCH_SIZE + 1}:`, batchError)
        throw new Error(`Failed to save records at batch ${i / BATCH_SIZE + 1}: ${batchError}`)
      }
    }

    return savedIds
  } catch (error) {
    console.error("Error connecting to MongoDB:", error)
    throw new Error(`Database connection failed: ${error}`)
  }
}

export async function saveFileMetadataToMongoDB(metadata: Omit<FileMetadata, "_id">): Promise<string> {
  try {
    const database = await connectToDatabase()
    const collection: Collection<FileMetadata> = database.collection("FileMetadata")

    const metadataWithTimestamp = {
      ...metadata,
      uploadedAt: new Date(),
      createdAt: new Date(),
    }

    const result = await collection.insertOne(metadataWithTimestamp)
    return result.insertedId.toString()
  } catch (error) {
    console.error("Error saving file metadata:", error)
    throw error
  }
}

export async function getRecordsFromMongoDB(
    fileName?: string,
    limit = 100,
    skip = 0,
    searchFilter?: any,
): Promise<PocztaPostRecord[]> {
  try {
    const database = await connectToDatabase()
    const collection: Collection<PocztaPostRecord> = database.collection("PocztaPost")

    let filter: any = {}

    // Only add fileName filter if specifically requested (for backward compatibility)
    // But for duplicate detection, we ignore fileName completely
    if (fileName && fileName !== "all") {
      filter._fileName = fileName
    }

    // Add search filter if provided
    if (searchFilter && Object.keys(searchFilter).length > 0) {
      if (Object.keys(filter).length > 0) {
        filter = { $and: [filter, searchFilter] }
      } else {
        filter = searchFilter
      }
    }

    const records = await collection.find(filter).sort({ _uploadedAt: -1 }).limit(limit).skip(skip).toArray()

    return records
  } catch (error) {
    console.error("Error fetching Polish Post delivery methods:", error)
    throw error
  }
}

export async function getRecordCountFromMongoDB(fileName?: string, searchFilter?: any): Promise<number> {
  try {
    const database = await connectToDatabase()
    const collection: Collection<PocztaPostRecord> = database.collection("PocztaPost")

    let filter: any = {}

    // Only add fileName filter if specifically requested
    if (fileName && fileName !== "all") {
      filter._fileName = fileName
    }

    // Add search filter if provided
    if (searchFilter && Object.keys(searchFilter).length > 0) {
      if (Object.keys(filter).length > 0) {
        filter = { $and: [filter, searchFilter] }
      } else {
        filter = searchFilter
      }
    }

    const count = await collection.countDocuments(filter)
    return count
  } catch (error) {
    console.error("Error counting Polish Post delivery methods:", error)
    throw error
  }
}

export async function getFileMetadataFromMongoDB(): Promise<FileMetadata[]> {
  try {
    const database = await connectToDatabase()
    const collection: Collection<FileMetadata> = database.collection("FileMetadata")

    const metadata = await collection.find({}).sort({ uploadedAt: -1 }).toArray()

    return metadata
  } catch (error) {
    console.error("Error fetching file metadata:", error)
    throw error
  }
}

export async function getUniqueFileNames(): Promise<string[]> {
  try {
    const database = await connectToDatabase()
    const collection: Collection<PocztaPostRecord> = database.collection("PocztaPost")

    const fileNames = await collection.distinct("_fileName")
    return fileNames
  } catch (error) {
    console.error("Error fetching unique file names:", error)
    throw error
  }
}

export async function getRecordsByDateRange(startDate: Date, endDate: Date): Promise<PocztaPostRecord[]> {
  try {
    const database = await connectToDatabase()
    const collection: Collection<PocztaPostRecord> = database.collection("PocztaPost")

    const records = await collection
        .find({
          _uploadedAt: {
            $gte: startDate,
            $lte: endDate,
          },
        })
        .sort({ _uploadedAt: -1 })
        .toArray()

    return records
  } catch (error) {
    console.error("Error fetching Polish Post delivery methods by date range:", error)
    throw error
  }
}

// New function to check for country uniqueness across entire database
export async function checkCountryExists(country: string): Promise<boolean> {
  try {
    const database = await connectToDatabase()
    const collection: Collection<PocztaPostRecord> = database.collection("PocztaPost")

    const existingRecord = await collection.findOne({ Country: country })
    return !!existingRecord
  } catch (error) {
    console.error("Error checking country existence:", error)
    return false
  }
}

// DHL Delivery Methods interfaces
export interface DHLDeliveryRecord {
  _id?: string
  // Dynamic fields from DHL delivery method data will be added here
  [key: string]: any
  // Metadata fields
  _fileName: string
  _sheetName: string
  _rowIndex: number
  _totalRows: number
  _uploadedAt: Date
  _createdAt: Date
  _updatedAt: Date
}

export async function saveDHLRecordsToMongoDB(
    records: Array<{
      fileName: string
      sheetName: string
      headers: string[]
      rowData: Record<string, any>
      rowIndex: number
      totalRows: number
    }>,
    onProgress: (saved: number, total: number) => void,
): Promise<string[]> {
  const BATCH_SIZE = 1000
  const savedIds: string[] = []
  let savedCount = 0

  try {
    const database = await connectToDatabase()
    const collection: Collection<DHLDeliveryRecord> = database.collection("DHLDeliveryMethods")

    // Split records into batches
    for (let i = 0; i < records.length; i += BATCH_SIZE) {
      const batchRecords = records.slice(i, i + BATCH_SIZE)

      // Transform records to flat structure with metadata
      const transformedRecords = batchRecords.map((record) => {
        const now = new Date()

        // Create a flat document with all Excel columns as top-level fields
        const flatRecord: DHLDeliveryRecord = {
          // Add all Excel data as individual fields
          ...record.rowData,

          // Add metadata with underscore prefix to avoid conflicts
          _fileName: record.fileName,
          _sheetName: record.sheetName,
          _rowIndex: record.rowIndex,
          _totalRows: record.totalRows,
          _uploadedAt: now,
          _createdAt: now,
          _updatedAt: now,
        }

        // Clean up any undefined or null values and convert them to null
        Object.keys(flatRecord).forEach((key) => {
          if (flatRecord[key] === undefined || flatRecord[key] === "") {
            flatRecord[key] = null
          }
        })

        return flatRecord
      })

      try {
        // Insert the batch
        const result = await collection.insertMany(transformedRecords, { ordered: false })

        // Add the IDs to our saved list
        Object.values(result.insertedIds).forEach((id) => savedIds.push(id.toString()))

        // Update progress
        savedCount += batchRecords.length
        onProgress(savedCount, records.length)

        // Small delay to prevent overwhelming the UI
        await new Promise((resolve) => setTimeout(resolve, 50))
      } catch (batchError) {
        console.error(`Error saving DHL batch ${i / BATCH_SIZE + 1}:`, batchError)
        throw new Error(`Failed to save DHL records at batch ${i / BATCH_SIZE + 1}: ${batchError}`)
      }
    }

    return savedIds
  } catch (error) {
    console.error("Error connecting to MongoDB for DHL records:", error)
    throw new Error(`DHL database connection failed: ${error}`)
  }
}

export async function getDHLRecordsFromMongoDB(
    fileName?: string,
    limit = 100,
    skip = 0,
    searchFilter?: any,
): Promise<DHLDeliveryRecord[]> {
  try {
    const database = await connectToDatabase()
    const collection: Collection<DHLDeliveryRecord> = database.collection("DHLDeliveryMethods")

    let filter: any = {}

    // Only add fileName filter if specifically requested
    if (fileName && fileName !== "all") {
      filter._fileName = fileName
    }

    // Add search filter if provided
    if (searchFilter && Object.keys(searchFilter).length > 0) {
      if (Object.keys(filter).length > 0) {
        filter = { $and: [filter, searchFilter] }
      } else {
        filter = searchFilter
      }
    }

    const records = await collection.find(filter).sort({ _uploadedAt: -1 }).limit(limit).skip(skip).toArray()

    return records
  } catch (error) {
    console.error("Error fetching DHL delivery methods:", error)
    throw error
  }
}

export async function getDHLRecordCountFromMongoDB(fileName?: string, searchFilter?: any): Promise<number> {
  try {
    const database = await connectToDatabase()
    const collection: Collection<DHLDeliveryRecord> = database.collection("DHLDeliveryMethods")

    let filter: any = {}

    // Only add fileName filter if specifically requested
    if (fileName && fileName !== "all") {
      filter._fileName = fileName
    }

    // Add search filter if provided
    if (searchFilter && Object.keys(searchFilter).length > 0) {
      if (Object.keys(filter).length > 0) {
        filter = { $and: [filter, searchFilter] }
      } else {
        filter = searchFilter
      }
    }

    const count = await collection.countDocuments(filter)
    return count
  } catch (error) {
    console.error("Error counting DHL delivery methods:", error)
    throw error
  }
}

export async function getUniqueDHLFileNames(): Promise<string[]> {
  try {
    const database = await connectToDatabase()
    const collection: Collection<DHLDeliveryRecord> = database.collection("DHLDeliveryMethods")

    const fileNames = await collection.distinct("_fileName")
    return fileNames
  } catch (error) {
    console.error("Error fetching unique DHL file names:", error)
    throw error
  }
}

export async function getDHLRecordsByDateRange(startDate: Date, endDate: Date): Promise<DHLDeliveryRecord[]> {
  try {
    const database = await connectToDatabase()
    const collection: Collection<DHLDeliveryRecord> = database.collection("DHLDeliveryMethods")

    const records = await collection
        .find({
          _uploadedAt: {
            $gte: startDate,
            $lte: endDate,
          },
        })
        .sort({ _uploadedAt: -1 })
        .toArray()

    return records
  } catch (error) {
    console.error("Error fetching DHL delivery methods by date range:", error)
    throw error
  }
}

// New function to check for DHL country uniqueness across entire database
export async function checkDHLCountryExists(country: string): Promise<boolean> {
  try {
    const database = await connectToDatabase()
    const collection: Collection<DHLDeliveryRecord> = database.collection("DHLDeliveryMethods")

    const existingRecord = await collection.findOne({ Country: country })
    return !!existingRecord
  } catch (error) {
    console.error("Error checking DHL country existence:", error)
    return false
  }
}

// Utility function to close the connection (optional, for cleanup)
export async function closeDatabaseConnection(): Promise<void> {
  if (client) {
    await client.close()
    console.log("MongoDB connection closed")
  }
}
