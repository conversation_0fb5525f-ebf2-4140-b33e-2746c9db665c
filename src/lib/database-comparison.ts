export interface DatabaseComparisonResult {
  summary: string
  newRecords: number
  updatedRecords: number
  duplicatesFound: number
  totalProcessed: number
  changes: Array<{
    type: "new" | "updated" | "duplicate"
    record: Record<string, any>
    previousRecord?: Record<string, any>
    changedFields?: string[]
  }>
  fieldChanges: Record<
    string,
    {
      added: number
      modified: number
      removed: number
    }
  >
}

// This service now only provides types and client-side utilities
// All database operations are handled by API routes
export class DatabaseComparisonService {
  private static instance: DatabaseComparisonService

  static getInstance(): DatabaseComparisonService {
    if (!DatabaseComparisonService.instance) {
      DatabaseComparisonService.instance = new DatabaseComparisonService()
    }
    return DatabaseComparisonService.instance
  }

  // Client-side utility methods
  getChangedFields(existingRecord: any, newRecord: any): string[] {
    const changedFields: string[] = []

    // Get all fields from both records (excluding metadata fields)
    const allFields = new Set([
      ...Object.keys(existingRecord).filter((key) => !key.startsWith("_")),
      ...Object.keys(newRecord).filter((key) => !key.startsWith("_")),
    ])

    allFields.forEach((field) => {
      const existingValue = existingRecord[field]
      const newValue = newRecord[field]

      // Normalize values for comparison
      const normalizedExisting = this.normalizeValue(existingValue)
      const normalizedNew = this.normalizeValue(newValue)

      if (normalizedExisting !== normalizedNew) {
        changedFields.push(field)
      }
    })

    return changedFields
  }

  private normalizeValue(value: any): string {
    if (value === null || value === undefined) return ""
    if (typeof value === "string") return value.trim()
    return String(value)
  }
}

export const databaseComparisonService = DatabaseComparisonService.getInstance()
