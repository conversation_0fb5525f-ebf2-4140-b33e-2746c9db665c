export interface DuplicateDetectionConfig {
  primaryKey: string[] // Fields that define uniqueness (e.g., ['country', 'region'])
  strategy: "replace" | "skip" | "merge"
}

export interface DuplicateResult {
  isDuplicate: boolean
  existingRecord?: any
  duplicateFields: string[]
}

export class DuplicateDetectionService {
  private static instance: DuplicateDetectionService
  private duplicateCache: Map<string, any> = new Map()

  static getInstance(): DuplicateDetectionService {
    if (!DuplicateDetectionService.instance) {
      DuplicateDetectionService.instance = new DuplicateDetectionService()
    }
    return DuplicateDetectionService.instance
  }

  // Generate a unique key for duplicate detection
  private generateKey(record: Record<string, any>, keyFields: string[]): string {
    return keyFields
      .map((field) =>
        String(record[field] || "")
          .toLowerCase()
          .trim(),
      )
      .join("|")
  }

  // Check if a record is a duplicate
  checkDuplicate(
    record: Record<string, any>,
    config: DuplicateDetectionConfig,
    existingRecords?: any[],
  ): DuplicateResult {
    const key = this.generateKey(record, config.primaryKey)

    // Check in cache first
    if (this.duplicateCache.has(key)) {
      return {
        isDuplicate: true,
        existingRecord: this.duplicateCache.get(key),
        duplicateFields: config.primaryKey,
      }
    }

    // Check in existing records if provided
    if (existingRecords) {
      const existingRecord = existingRecords.find((existing) => {
        const existingKey = this.generateKey(existing, config.primaryKey)
        return existingKey === key
      })

      if (existingRecord) {
        this.duplicateCache.set(key, existingRecord)
        return {
          isDuplicate: true,
          existingRecord,
          duplicateFields: config.primaryKey,
        }
      }
    }

    return {
      isDuplicate: false,
      duplicateFields: [],
    }
  }

  // Add record to cache
  addToCache(record: Record<string, any>, keyFields: string[]): void {
    const key = this.generateKey(record, keyFields)
    this.duplicateCache.set(key, record)
  }

  // Clear cache
  clearCache(): void {
    this.duplicateCache.clear()
  }

  // Get duplicate statistics
  getDuplicateStats(): {
    totalCached: number
    cacheKeys: string[]
  } {
    return {
      totalCached: this.duplicateCache.size,
      cacheKeys: Array.from(this.duplicateCache.keys()),
    }
  }

  // Process records and handle duplicates
  processRecordsWithDuplicateHandling(
    records: Record<string, any>[],
    config: DuplicateDetectionConfig,
    existingRecords?: any[],
    updateCallback?: (existingRecord: any, newRecord: any) => Promise<void>,
  ): {
    processedRecords: Record<string, any>[]
    duplicateCount: number
    replacedCount: number
    duplicateDetails: Array<{
      newRecord: Record<string, any>
      existingRecord: Record<string, any>
      action: "replaced" | "skipped"
    }>
  } {
    const processedRecords: Record<string, any>[] = []
    const duplicateDetails: Array<{
      newRecord: Record<string, any>
      existingRecord: Record<string, any>
      action: "replaced" | "skipped"
    }> = []

    let duplicateCount = 0
    let replacedCount = 0

    for (const record of records) {
      const duplicateResult = this.checkDuplicate(record, config, existingRecords)

      if (duplicateResult.isDuplicate) {
        duplicateCount++

        if (config.strategy === "replace") {
          // For replace strategy, we'll handle the update in the API
          // Don't add to processedRecords, but track the replacement
          replacedCount++
          duplicateDetails.push({
            newRecord: record,
            existingRecord: duplicateResult.existingRecord!,
            action: "replaced",
          })

          // Update cache with new record
          this.addToCache(record, config.primaryKey)
        } else if (config.strategy === "skip") {
          // Skip the duplicate record
          duplicateDetails.push({
            newRecord: record,
            existingRecord: duplicateResult.existingRecord!,
            action: "skipped",
          })
        }
      } else {
        // Not a duplicate, add to processed records
        processedRecords.push(record)
        this.addToCache(record, config.primaryKey)
      }
    }

    return {
      processedRecords,
      duplicateCount,
      replacedCount,
      duplicateDetails,
    }
  }
}

export const duplicateDetectionService = DuplicateDetectionService.getInstance()
