import type { DatabaseRecord } from "./database"
import type { DuplicateDetectionConfig } from "./duplicate-detection"
import type { DatabaseComparisonResult } from "./database-comparison"
import { validateDHLRecords, checkDuplicateCountriesInDatabase, type ValidationR<PERSON>ult } from "./dhl-validation"

export interface EnhancedDHLUploadOptions {
  duplicateDetection: DuplicateDetectionConfig
  compareWithDatabase: boolean
  validateBeforeSave: boolean
}

export interface EnhancedDHLUploadResult {
  savedRecords: number
  duplicatesHandled: number
  replacedRecords: number
  comparisonResult?: DatabaseComparisonResult
  duplicateDetails: Array<{
    newRecord: Record<string, any>
    existingRecord: Record<string, any>
    action: "replaced" | "skipped"
  }>
  validationResult?: ValidationResult
}

export async function validateDHLRecordsBeforeSave(records: DatabaseRecord[]): Promise<ValidationResult> {
  // First validate records against rules
  const validationResult = validateDHLRecords(records)

  // Then check for duplicates in the database
  if (validationResult.isValid) {
    const duplicateErrors = await checkDuplicateCountriesInDatabase(records)

    if (duplicateErrors.length > 0) {
      validationResult.isValid = false
      validationResult.errors.push(...duplicateErrors)
      validationResult.invalidRecords += duplicateErrors.length
      validationResult.validRecords -= duplicateErrors.length
    }
  }

  return validationResult
}

export async function saveDHLRecordsWithDuplicateDetection(
    records: DatabaseRecord[],
    options: EnhancedDHLUploadOptions,
    onProgress: (saved: number, total: number, status?: string) => void,
): Promise<EnhancedDHLUploadResult> {
  const BATCH_SIZE = 500
  let totalSaved = 0
  let totalDuplicates = 0
  let totalReplaced = 0
  let comparisonResult: DatabaseComparisonResult | undefined
  let validationResult: ValidationResult | undefined

  const allDuplicateDetails: Array<{
    newRecord: Record<string, any>
    existingRecord: Record<string, any>
    action: "replaced" | "skipped"
  }> = []

  onProgress(0, records.length, "Initializing DHL upload...")

  // Validate records before saving if option is enabled
  if (options.validateBeforeSave) {
    onProgress(0, records.length, "Validating records...")
    validationResult = await validateDHLRecordsBeforeSave(records)

    // If validation failed, return early with validation results
    if (!validationResult.isValid) {
      return {
        savedRecords: 0,
        duplicatesHandled: 0,
        replacedRecords: 0,
        duplicateDetails: [],
        validationResult,
      }
    }
  }

  // Process records in batches
  for (let i = 0; i < records.length; i += BATCH_SIZE) {
    const batchRecords = records.slice(i, i + BATCH_SIZE)

    onProgress(i, records.length, `Processing DHL batch ${Math.floor(i / BATCH_SIZE) + 1}...`)

    try {
      const response = await fetch("/api/save-dhl-records", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          records: batchRecords,
          options: {
            ...options,
            validateBeforeSave: false, // We already validated
          },
          isFirstBatch: i === 0,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to save DHL batch")
      }

      const result = await response.json()

      totalSaved += result.savedRecords || 0
      totalDuplicates += result.duplicatesHandled || 0
      totalReplaced += result.replacedRecords || 0

      if (result.duplicateDetails) {
        allDuplicateDetails.push(...result.duplicateDetails)
      }

      if (result.comparisonResult && !comparisonResult) {
        comparisonResult = result.comparisonResult
      }

      onProgress(i + batchRecords.length, records.length, "Saving DHL batch...")

      // Small delay between batches
      await new Promise((resolve) => setTimeout(resolve, 100))
    } catch (error) {
      console.error(`Error saving DHL batch ${Math.floor(i / BATCH_SIZE) + 1}:`, error)
      throw new Error(`Failed to save DHL records at batch ${Math.floor(i / BATCH_SIZE) + 1}: ${error}`)
    }
  }

  onProgress(records.length, records.length, "DHL upload completed!")

  return {
    savedRecords: totalSaved,
    duplicatesHandled: totalDuplicates,
    replacedRecords: totalReplaced,
    comparisonResult,
    duplicateDetails: allDuplicateDetails,
    validationResult,
  }
}

export async function saveValidDHLRecords(
    records: DatabaseRecord[],
    validationResult: ValidationResult,
    options: EnhancedDHLUploadOptions,
    onProgress: (saved: number, total: number, status?: string) => void,
): Promise<EnhancedDHLUploadResult> {
  // Filter out invalid records - handle both 'row' and 'rowIndex' properties safely
  const validRecords = records.filter((record, index) => {
    // Check if this record has any validation errors
    const hasError = validationResult.errors.some((error) => {
      // Safely handle both 'row' and 'rowIndex' properties
      const errorRow = error.row !== undefined ? error.row : error.rowIndex
      return errorRow === index
    })
    return !hasError
  })

  // Save only valid records
  return saveDHLRecordsWithDuplicateDetection(
      validRecords,
      {
        ...options,
        validateBeforeSave: false, // We already validated
      },
      onProgress,
  )
}
