import type { DatabaseRecord } from "./database"
import type { DuplicateDetectionConfig } from "./duplicate-detection"
import type { DatabaseComparisonResult } from "./database-comparison"

export interface EnhancedUploadOptions {
  duplicateDetection: DuplicateDetectionConfig
  compareWithDatabase: boolean
}

export interface EnhancedUploadResult {
  savedRecords: number
  duplicatesHandled: number
  replacedRecords: number
  comparisonResult?: DatabaseComparisonResult
  duplicateDetails: Array<{
    newRecord: Record<string, any>
    existingRecord: Record<string, any>
    action: "replaced" | "skipped"
  }>
}

export async function saveRecordsWithDuplicateDetection(
  records: DatabaseRecord[],
  options: EnhancedUploadOptions,
  onProgress: (saved: number, total: number, status?: string) => void,
): Promise<EnhancedUploadResult> {
  const BATCH_SIZE = 500
  let totalSaved = 0
  let totalDuplicates = 0
  let totalReplaced = 0
  let comparisonResult: DatabaseComparisonResult | undefined
  const allDuplicateDetails: Array<{
    newRecord: Record<string, any>
    existingRecord: Record<string, any>
    action: "replaced" | "skipped"
  }> = []

  onProgress(0, records.length, "Initializing enhanced upload...")

  // Process records in batches
  for (let i = 0; i < records.length; i += BATCH_SIZE) {
    const batchRecords = records.slice(i, i + BATCH_SIZE)

    onProgress(i, records.length, `Processing batch ${Math.floor(i / BATCH_SIZE) + 1}...`)

    try {
      const response = await fetch("/api/save-records-enhanced", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          records: batchRecords,
          options,
          isFirstBatch: i === 0,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to save batch")
      }

      const result = await response.json()

      totalSaved += result.savedRecords || 0
      totalDuplicates += result.duplicatesHandled || 0
      totalReplaced += result.replacedRecords || 0

      if (result.duplicateDetails) {
        allDuplicateDetails.push(...result.duplicateDetails)
      }

      if (result.comparisonResult && !comparisonResult) {
        comparisonResult = result.comparisonResult
      }

      onProgress(i + batchRecords.length, records.length, "Saving batch...")

      // Small delay between batches
      await new Promise((resolve) => setTimeout(resolve, 100))
    } catch (error) {
      console.error(`Error saving batch ${Math.floor(i / BATCH_SIZE) + 1}:`, error)
      throw new Error(`Failed to save records at batch ${Math.floor(i / BATCH_SIZE) + 1}: ${error}`)
    }
  }

  onProgress(records.length, records.length, "Upload completed!")

  return {
    savedRecords: totalSaved,
    duplicatesHandled: totalDuplicates,
    replacedRecords: totalReplaced,
    comparisonResult,
    duplicateDetails: allDuplicateDetails,
  }
}
