import type { DatabaseRecord } from "./database"

export interface ValidationRule {
  field: string
  rule: (value: any, record: Record<string, any>) => boolean
  errorMessage: string
}

export interface ValidationError {
  rowIndex: number
  field: string
  value: any
  message: string
  row?: number // Make this optional since we use rowIndex as primary
  type?: "missing_required" | "invalid_format" | "duplicate" | "invalid_range"
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  validRecords: number
  invalidRecords: number
  totalRecords?: number
  summary?: string
}

// Default validation rules for DHL delivery methods
export const defaultDHLValidationRules: ValidationRule[] = [
  {
    field: "Country",
    rule: (value) => !!value && value.toString().trim() !== "",
    errorMessage: "Country name cannot be empty",
  },
  {
    field: "DHL_ZONE_NAME",
    rule: (value) => value === null || value === undefined || value.toString().trim() !== "",
    errorMessage: "Zone name cannot be empty if provided",
  },
  {
    field: "DHL_EXPRESS 50g",
    rule: (value) => {
      if (value === null || value === undefined || value === "") return true
      const numValue = Number.parseFloat(value.toString().replace(/[^\d.-]/g, ""))
      return !isNaN(numValue) && numValue >= 0
    },
    errorMessage: "Express 50g price must be a valid positive number",
  },
  {
    field: "DHL_EXPRESS 100g",
    rule: (value) => {
      if (value === null || value === undefined || value === "") return true
      const numValue = Number.parseFloat(value.toString().replace(/[^\d.-]/g, ""))
      return !isNaN(numValue) && numValue >= 0
    },
    errorMessage: "Express 100g price must be a valid positive number",
  },
  {
    field: "DHL_Air 50g",
    rule: (value) => {
      if (value === null || value === undefined || value === "") return true
      const numValue = Number.parseFloat(value.toString().replace(/[^\d.-]/g, ""))
      return !isNaN(numValue) && numValue >= 0
    },
    errorMessage: "Air 50g price must be a valid positive number",
  },
  {
    field: "DHL_Air 100g",
    rule: (value) => {
      if (value === null || value === undefined || value === "") return true
      const numValue = Number.parseFloat(value.toString().replace(/[^\d.-]/g, ""))
      return !isNaN(numValue) && numValue >= 0
    },
    errorMessage: "Air 100g price must be a valid positive number",
  },
  {
    field: "DHL_ROAD_50g",
    rule: (value) => {
      if (value === null || value === undefined || value === "") return true
      const numValue = Number.parseFloat(value.toString().replace(/[^\d.-]/g, ""))
      return !isNaN(numValue) && numValue >= 0
    },
    errorMessage: "Road 50g price must be a valid positive number",
  },
  {
    field: "DHL_ROAD_100g",
    rule: (value) => {
      if (value === null || value === undefined || value === "") return true
      const numValue = Number.parseFloat(value.toString().replace(/[^\d.-]/g, ""))
      return !isNaN(numValue) && numValue >= 0
    },
    errorMessage: "Road 100g price must be a valid positive number",
  },
  {
    field: "DHL_EXPRESS_ROAD_BULK 0.50kg EUR",
    rule: (value) => {
      if (value === null || value === undefined || value === "") return true
      const numValue = Number.parseFloat(value.toString().replace(/[^\d.-]/g, ""))
      return !isNaN(numValue) && numValue >= 0
    },
    errorMessage: "Express Road Bulk price must be a valid positive number",
  },
]

const REQUIRED_FIELDS = ["Country", "DHL_ZONE_NAME", "DHL_EXPRESS_ZONE"]

const PRICE_FIELDS = [
  "DHL_EXPRESS 50g",
  "DHL_EXPRESS 100g",
  "DHL_Air 50g",
  "DHL_Air 100g",
  "DHL_ROAD_50g",
  "DHL_ROAD_100g",
  "DHL_EXPRESS_ROAD_BULK 0.50kg EUR",
]

const DELIVERY_TIME_FIELDS = [
  "DHL_EXPRESS  DELIVERY TIMES",
  "DHL_AIR  DELIVERY TIMES",
  "DHL_ROAD  DELIVERY TIMES",
  "DHL_EXPRESS_ROAD_BULK DELIVERY TIMES",
]

// Function to validate a single record
export function validateDHLRecord(
    record: Record<string, any>,
    rules: ValidationRule[] = defaultDHLValidationRules,
): { isValid: boolean; errors: { field: string; value: any; message: string }[] } {
  const errors: { field: string; value: any; message: string }[] = []

  rules.forEach((rule) => {
    const value = record[rule.field]
    if (!rule.rule(value, record)) {
      errors.push({
        field: rule.field,
        value,
        message: rule.errorMessage,
      })
    }
  })

  return {
    isValid: errors.length === 0,
    errors,
  }
}

// Function to validate multiple records
export function validateDHLRecords(
    records: DatabaseRecord[],
    rules: ValidationRule[] = defaultDHLValidationRules,
): ValidationResult {
  const errors: ValidationError[] = []
  let validRecords = 0
  let invalidRecords = 0

  // Check for duplicate countries within the records
  const countries = new Map<string, number>()

  records.forEach((record, index) => {
    const country = record.rowData.Country?.toString().toLowerCase().trim()

    if (country) {
      if (countries.has(country)) {
        errors.push({
          rowIndex: record.rowIndex,
          field: "Country",
          value: record.rowData.Country,
          message: `Duplicate country found. First occurrence at row ${countries.get(country)! + 1}`,
        })
        invalidRecords++
        return
      }
      countries.set(country, record.rowIndex)
    }

    // Validate against rules
    const validation = validateDHLRecord(record.rowData, rules)

    if (!validation.isValid) {
      validation.errors.forEach((error) => {
        errors.push({
          rowIndex: record.rowIndex,
          field: error.field,
          value: error.value,
          message: error.message,
        })
      })
      invalidRecords++
    } else {
      validRecords++
    }
  })

  return {
    isValid: errors.length === 0,
    errors,
    validRecords,
    invalidRecords,
  }
}

// Function to check for duplicate countries in the database
export async function checkDuplicateCountriesInDatabase(records: DatabaseRecord[]): Promise<ValidationError[]> {
  const errors: ValidationError[] = []

  try {
    const countries = records
        .filter((record) => record.rowData.Country)
        .map((record) => ({
          country: record.rowData.Country.toString().toLowerCase().trim(),
          rowIndex: record.rowIndex,
        }))

    if (countries.length === 0) return errors

    // Check for duplicates in database
    const response = await fetch("/api/check-dhl-duplicates", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        countries: countries.map((c) => c.country),
      }),
    })

    if (!response.ok) {
      throw new Error("Failed to check for duplicate countries")
    }

    const result = await response.json()
    const duplicates = result.duplicates || []

    // Add errors for duplicates
    countries.forEach((item) => {
      if (duplicates.includes(item.country)) {
        errors.push({
          rowIndex: item.rowIndex,
          field: "Country",
          value: item.country,
          message: `Country already exists in the database: ${item.country}`,
        })
      }
    })

    return errors
  } catch (error) {
    console.error("Error checking for duplicate countries:", error)
    return errors
  }
}

export async function validateDHLData(records: Record<string, any>[]): Promise<ValidationResult> {
  const errors: ValidationError[] = []
  const seenCountries = new Set<string>()

  for (let i = 0; i < records.length; i++) {
    const record = records[i]

    // Check required fields
    for (const field of REQUIRED_FIELDS) {
      if (!record[field] || record[field].toString().trim() === "") {
        errors.push({
          rowIndex: i, // Changed from 'row: i'
          field,
          message: `${field} is required and cannot be empty`,
          value: record[field],
          type: "missing_required",
        })
      }
    }

    // Check for duplicate countries within the upload
    const country = record["Country"]?.toString().toLowerCase().trim()
    if (country) {
      if (seenCountries.has(country)) {
        errors.push({
          rowIndex: i, // Changed from 'row: i'
          field: "Country",
          message: `Duplicate country found: ${record["Country"]}`,
          value: record["Country"],
          type: "duplicate",
        })
      } else {
        seenCountries.add(country)
      }
    }

    // Validate price fields
    for (const field of PRICE_FIELDS) {
      const value = record[field]
      if (value && value !== "") {
        const numericValue = Number.parseFloat(value.toString().replace(/[^\d.-]/g, ""))
        if (isNaN(numericValue)) {
          errors.push({
            rowIndex: i, // Changed from 'row: i'
            field,
            message: `Invalid price format in ${field}`,
            value: value,
            type: "invalid_format",
          })
        } else if (numericValue < 0) {
          errors.push({
            rowIndex: i, // Changed from 'row: i'
            field,
            message: `Price cannot be negative in ${field}`,
            value: value,
            type: "invalid_range",
          })
        }
      }
    }

    // Validate delivery time fields
    for (const field of DELIVERY_TIME_FIELDS) {
      const value = record[field]
      if (value && value !== "") {
        const timeStr = value.toString().trim()
        // Check if it contains numbers and common time indicators
        if (!/\d/.test(timeStr)) {
          errors.push({
            rowIndex: i, // Changed from 'row: i'
            field,
            message: `Delivery time should contain numeric values in ${field}`,
            value: value,
            type: "invalid_format",
          })
        }
      }
    }

    // Validate zone fields
    const zoneName = record["DHL_ZONE_NAME"]
    const expressZone = record["DHL_EXPRESS_ZONE"]

    if (zoneName && zoneName.toString().trim().length > 100) {
      errors.push({
        rowIndex: i, // Changed from 'row: i'
        field: "DHL_ZONE_NAME",
        message: "Zone name is too long (max 100 characters)",
        value: zoneName,
        type: "invalid_format",
      })
    }

    if (expressZone && expressZone.toString().trim().length > 50) {
      errors.push({
        rowIndex: i, // Changed from 'row: i'
        field: "DHL_EXPRESS_ZONE",
        message: "Express zone is too long (max 50 characters)",
        value: expressZone,
        type: "invalid_format",
      })
    }
  }

  const invalidRecords = new Set(errors.map((e) => e.rowIndex)).size
  const validRecords = records.length - invalidRecords
  const isValid = errors.length === 0

  let summary = ""
  if (isValid) {
    summary = `All ${records.length} records passed validation`
  } else {
    summary = `${errors.length} validation errors found in ${invalidRecords} records`
  }

  return {
    isValid,
    totalRecords: records.length,
    validRecords,
    invalidRecords,
    errors,
    summary,
  }
}

export function getValidationSummary(validationResult: ValidationResult): string {
  if (validationResult.isValid) {
    return `✅ All ${validationResult.totalRecords} records are valid and ready to save`
  }

  const errorTypes = validationResult.errors.reduce(
      (acc, error) => {
        const type = error.type || "other"
        acc[type] = (acc[type] || 0) + 1
        return acc
      },
      {} as Record<string, number>,
  )

  let summary = `❌ Found ${validationResult.errors.length} errors in ${validationResult.invalidRecords} records:\n`

  if (errorTypes.missing_required) {
    summary += `• ${errorTypes.missing_required} missing required fields\n`
  }
  if (errorTypes.invalid_format) {
    summary += `• ${errorTypes.invalid_format} invalid format errors\n`
  }
  if (errorTypes.duplicate) {
    summary += `• ${errorTypes.duplicate} duplicate entries\n`
  }
  if (errorTypes.invalid_range) {
    summary += `• ${errorTypes.invalid_range} invalid range errors\n`
  }

  if (validationResult.validRecords > 0) {
    summary += `\n✅ ${validationResult.validRecords} records are valid and can be saved`
  }

  return summary
}
