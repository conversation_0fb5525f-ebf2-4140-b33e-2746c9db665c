
export const eeaCountries = [
  { name: "Austria", code2: "AT", code3: "AUT", numeric: "040", dialingCode: "+43", mobileFormat: "6xx xxxxxx" },
  { name: "Belgium", code2: "BE", code3: "BEL", numeric: "056", dialingCode: "+32", mobileFormat: "4xx xxxxxx" },
  { name: "Bulgaria", code2: "BG", code3: "BGR", numeric: "100", dialingCode: "+359", mobileFormat: "8xx xxxxxx" },
  { name: "Croatia", code2: "HR", code3: "HRV", numeric: "191", dialingCode: "+385", mobileFormat: "9x xxxxxxx" },
  { name: "Cyprus", code2: "CY", code3: "CYP", numeric: "196", dialingCode: "+357", mobileFormat: "9x xxxxxx" },
  { name: "Czech Republic", code2: "CZ", code3: "CZE", numeric: "203", dialingCode: "+420", mobileFormat: "6xx xxx xxx or 7xx xxx xxx" },
  { name: "Denmark", code2: "DK", code3: "DNK", numeric: "208", dialingCode: "+45", mobileFormat: "xx xx xx xx" },
  { name: "Estonia", code2: "EE", code3: "EST", numeric: "233", dialingCode: "+372", mobileFormat: "5xxx xxxx" },
  { name: "Finland", code2: "FI", code3: "FIN", numeric: "246", dialingCode: "+358", mobileFormat: "4x xxxxxxx or 5x xxxxxxx" },
  { name: "France", code2: "FR", code3: "FRA", numeric: "250", dialingCode: "+33", mobileFormat: "6xx xxxxxx or 7xx xxxxxx" },
  { name: "Germany", code2: "DE", code3: "DEU", numeric: "276", dialingCode: "+49", mobileFormat: "15x/16x/17x xxxxxxxx" },
  { name: "Greece", code2: "GR", code3: "GRC", numeric: "300", dialingCode: "+30", mobileFormat: "69x xxxxxxx" },
  { name: "Hungary", code2: "HU", code3: "HUN", numeric: "348", dialingCode: "+36", mobileFormat: "20/30/70 xxx xxxx" },
  { name: "Iceland", code2: "IS", code3: "ISL", numeric: "352", dialingCode: "+354", mobileFormat: "6xx/7xx/8xx xxxx" },
  { name: "Ireland", code2: "IE", code3: "IRL", numeric: "372", dialingCode: "+353", mobileFormat: "8x xxxxxxx" },
  { name: "Italy", code2: "IT", code3: "ITA", numeric: "380", dialingCode: "+39", mobileFormat: "3xx xxxxxxx" },
  { name: "Latvia", code2: "LV", code3: "LVA", numeric: "428", dialingCode: "+371", mobileFormat: "2xxxxxxx" },
  { name: "Liechtenstein", code2: "LI", code3: "LIE", numeric: "438", dialingCode: "+423", mobileFormat: "7xx xxxx" },
  { name: "Lithuania", code2: "LT", code3: "LTU", numeric: "440", dialingCode: "+370", mobileFormat: "6xx xxxxx" },
  { name: "Luxembourg", code2: "LU", code3: "LUX", numeric: "442", dialingCode: "+352", mobileFormat: "6xx xxxxx" },
  { name: "Malta", code2: "MT", code3: "MLT", numeric: "470", dialingCode: "+356", mobileFormat: "9xxxxxxx or 7xxxxxxx" },
  { name: "Netherlands", code2: "NL", code3: "NLD", numeric: "528", dialingCode: "+31", mobileFormat: "6x xxxxxxx" },
  { name: "Norway", code2: "NO", code3: "NOR", numeric: "578", dialingCode: "+47", mobileFormat: "4xx xxxxx or 9xx xxxxx" },
  { name: "Poland", code2: "PL", code3: "POL", numeric: "616", dialingCode: "+48", mobileFormat: "5xx/6xx/7xx/8xx xxx xxx" },
  { name: "Portugal", code2: "PT", code3: "PRT", numeric: "620", dialingCode: "+351", mobileFormat: "9x xxxxxxx" },
  { name: "Romania", code2: "RO", code3: "ROU", numeric: "642", dialingCode: "+40", mobileFormat: "7xx xxx xxx" },
  { name: "Slovakia", code2: "SK", code3: "SVK", numeric: "703", dialingCode: "+421", mobileFormat: "9xx xxx xxx" },
  { name: "Slovenia", code2: "SI", code3: "SVN", numeric: "705", dialingCode: "+386", mobileFormat: "3x/4x/5x/6x/7x xxx xxx" },
  { name: "Spain", code2: "ES", code3: "ESP", numeric: "724", dialingCode: "+34", mobileFormat: "6xx/7xx xxxxxx" },
  { name: "Sweden", code2: "SE", code3: "SWE", numeric: "752", dialingCode: "+46", mobileFormat: "7xx xxx xxx" }
];
