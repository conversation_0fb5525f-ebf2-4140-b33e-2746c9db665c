import type { DatabaseRecord } from "./database"
import type { DuplicateDetectionConfig } from "./duplicate-detection"

export interface UploadJob {
  id: string
  fileName: string
  totalRecords: number
  savedRecords: number
  status: "pending" | "running" | "completed" | "error" | "paused"
  startTime: number
  endTime?: number
  errorMessage?: string
  records: DatabaseRecord[]
  currentBatch: number
  totalBatches: number
  duplicateConfig?: DuplicateDetectionConfig & { compareWithDatabase: boolean }
}

export interface UploadProgress {
  jobId: string
  savedRecords: number
  totalRecords: number
  status: UploadJob["status"]
  percentage: number
  estimatedTimeRemaining?: number
  errorMessage?: string
}

class BackgroundUploadService {
  private jobs: Map<string, UploadJob> = new Map()
  private activeJobId: string | null = null
  private isProcessing = false
  private progressCallbacks: Set<(progress: UploadProgress) => void> = new Set()
  private storageKey = "excel_upload_jobs"

  constructor() {
    this.loadJobsFromStorage()
    this.resumeActiveJobs()
  }

  // Generate unique job ID
  private generateJobId(): string {
    return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Save jobs to localStorage
  private saveJobsToStorage(): void {
    try {
      const jobsArray = Array.from(this.jobs.entries()).map(([id, job]) => [id, job])
      localStorage.setItem(this.storageKey, JSON.stringify(jobsArray))
    } catch (error) {
      console.error("Failed to save jobs to storage:", error)
    }
  }

  // Load jobs from localStorage
  private loadJobsFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.storageKey)
      if (stored) {
        const jobsArray = JSON.parse(stored)
        this.jobs = new Map(jobsArray)
      }
    } catch (error) {
      console.error("Failed to load jobs from storage:", error)
    }
  }

  // Resume any pending or running jobs
  private resumeActiveJobs(): void {
    for (const [jobId, job] of this.jobs) {
      if (job.status === "running" || job.status === "pending") {
        // Mark as pending to restart
        job.status = "pending"
        this.processJob(jobId)
        break // Only process one job at a time
      }
    }
  }

  // Start a new upload job
  public startUpload(
    fileName: string,
    records: DatabaseRecord[],
    duplicateConfig?: DuplicateDetectionConfig & { compareWithDatabase: boolean },
  ): string {
    const jobId = this.generateJobId()
    const BATCH_SIZE = 500
    const totalBatches = Math.ceil(records.length / BATCH_SIZE)

    const job: UploadJob = {
      id: jobId,
      fileName,
      totalRecords: records.length,
      savedRecords: 0,
      status: "pending",
      startTime: Date.now(),
      records,
      currentBatch: 0,
      totalBatches,
      duplicateConfig,
    }

    this.jobs.set(jobId, job)
    this.saveJobsToStorage()

    // Start processing if not already processing
    if (!this.isProcessing) {
      this.processJob(jobId)
    }

    return jobId
  }

  // Process a job in the background
  private async processJob(jobId: string): Promise<void> {
    const job = this.jobs.get(jobId)
    if (!job || job.status === "completed" || job.status === "error") {
      return
    }

    this.isProcessing = true
    this.activeJobId = jobId
    job.status = "running"
    this.saveJobsToStorage()
    this.notifyProgress(jobId)

    try {
      await this.processJobBatches(job)
    } catch (error) {
      job.status = "error"
      job.errorMessage = error instanceof Error ? error.message : "Unknown error"
      job.endTime = Date.now()
      console.error("Upload job failed:", error)
    }

    this.isProcessing = false
    this.activeJobId = null
    this.saveJobsToStorage()
    this.notifyProgress(jobId)

    // Check for next pending job
    this.processNextPendingJob()
  }

  // Process job in batches using enhanced API
  private async processJobBatches(job: UploadJob): Promise<void> {
    const BATCH_SIZE = 500

    for (let i = job.currentBatch; i < job.totalBatches; i++) {
      // Check if job was paused or cancelled
      if (job.status !== "running") {
        return
      }

      const startIndex = i * BATCH_SIZE
      const endIndex = Math.min(startIndex + BATCH_SIZE, job.records.length)
      const batchRecords = job.records.slice(startIndex, endIndex)

      try {
        // Use enhanced API
        const response = await fetch("/api/save-records-enhanced", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            records: batchRecords,
            options: job.duplicateConfig
              ? {
                  duplicateDetection: {
                    primaryKey: job.duplicateConfig.primaryKey,
                    strategy: job.duplicateConfig.strategy,
                  },
                  compareWithDatabase: job.duplicateConfig.compareWithDatabase,
                }
              : {
                  duplicateDetection: {
                    primaryKey: ["id"],
                    strategy: "replace" as const,
                  },
                  compareWithDatabase: false,
                },
            isFirstBatch: i === 0,
          }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Failed to save batch")
        }

        const result = await response.json()

        job.currentBatch = i + 1
        job.savedRecords = startIndex + (result.savedRecords || batchRecords.length)
        this.saveJobsToStorage()
        this.notifyProgress(job.id)

        // Small delay between batches
        await new Promise((resolve) => setTimeout(resolve, 100))
      } catch (error) {
        throw new Error(`Failed to process batch ${i + 1}: ${error}`)
      }
    }

    // Job completed successfully
    job.status = "completed"
    job.endTime = Date.now()
    job.savedRecords = job.totalRecords
  }

  // Process next pending job
  private processNextPendingJob(): void {
    for (const [jobId, job] of this.jobs) {
      if (job.status === "pending") {
        this.processJob(jobId)
        break
      }
    }
  }

  // Notify progress to all listeners
  private notifyProgress(jobId: string): void {
    const job = this.jobs.get(jobId)
    if (!job) return

    const percentage = (job.savedRecords / job.totalRecords) * 100
    const elapsed = Date.now() - job.startTime
    const rate = job.savedRecords / (elapsed / 1000)
    const remaining = job.totalRecords - job.savedRecords
    const estimatedTimeRemaining = remaining > 0 && rate > 0 ? (remaining / rate) * 1000 : 0

    const progress: UploadProgress = {
      jobId,
      savedRecords: job.savedRecords,
      totalRecords: job.totalRecords,
      status: job.status,
      percentage,
      estimatedTimeRemaining,
      errorMessage: job.errorMessage,
    }

    this.progressCallbacks.forEach((callback) => {
      try {
        callback(progress)
      } catch (error) {
        console.error("Progress callback error:", error)
      }
    })
  }

  // Subscribe to progress updates
  public onProgress(callback: (progress: UploadProgress) => void): () => void {
    this.progressCallbacks.add(callback)

    // Return unsubscribe function
    return () => {
      this.progressCallbacks.delete(callback)
    }
  }

  // Get current job status
  public getJobStatus(jobId: string): UploadJob | null {
    return this.jobs.get(jobId) || null
  }

  // Get all jobs
  public getAllJobs(): UploadJob[] {
    return Array.from(this.jobs.values()).sort((a, b) => b.startTime - a.startTime)
  }

  // Pause a job
  public pauseJob(jobId: string): void {
    const job = this.jobs.get(jobId)
    if (job && job.status === "running") {
      job.status = "paused"
      this.saveJobsToStorage()
      this.notifyProgress(jobId)
    }
  }

  // Resume a job
  public resumeJob(jobId: string): void {
    const job = this.jobs.get(jobId)
    if (job && job.status === "paused") {
      job.status = "pending"
      this.saveJobsToStorage()
      if (!this.isProcessing) {
        this.processJob(jobId)
      }
    }
  }

  // Cancel a job
  public cancelJob(jobId: string): void {
    const job = this.jobs.get(jobId)
    if (job) {
      job.status = "error"
      job.errorMessage = "Cancelled by user"
      job.endTime = Date.now()
      this.saveJobsToStorage()
      this.notifyProgress(jobId)
    }
  }

  // Clear completed jobs
  public clearCompletedJobs(): void {
    for (const [jobId, job] of this.jobs) {
      if (job.status === "completed" || job.status === "error") {
        this.jobs.delete(jobId)
      }
    }
    this.saveJobsToStorage()
  }

  // Get active job
  public getActiveJob(): UploadJob | null {
    return this.activeJobId ? this.jobs.get(this.activeJobId) || null : null
  }
}

// Create singleton instance
export const backgroundUploadService = new BackgroundUploadService()
