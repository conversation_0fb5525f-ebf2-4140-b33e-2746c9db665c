//@ts-nocheck

export interface UploadMetrics {
  jobId: string
  fileName: string
  startTime: number
  endTime?: number
  totalRecords: number
  processedRecords: number
  successfulRecords: number
  failedRecords: number
  duplicateRecords: number
  replacedRecords: number
  averageRecordsPerSecond: number
  peakRecordsPerSecond: number
  totalBatches: number
  completedBatches: number
  errorCount: number
  warningCount: number
  status: "pending" | "running" | "completed" | "failed" | "cancelled"
  errors: Array<{
    timestamp: number
    message: string
    batchIndex?: number
    recordIndex?: number
  }>
  warnings: Array<{
    timestamp: number
    message: string
    batchIndex?: number
    recordIndex?: number
  }>
  performanceData: Array<{
    timestamp: number
    recordsProcessed: number
    recordsPerSecond: number
    memoryUsage?: number
  }>
  validationMetrics?: {
    validationTime: number
    validRecords: number
    invalidRecords: number
    validationErrors: number
  }
  duplicateDetectionMetrics?: {
    detectionTime: number
    duplicatesFound: number
    duplicatesReplaced: number
    duplicatesSkipped: number
  }
  databaseMetrics?: {
    insertTime: number
    updateTime: number
    queryTime: number
    connectionTime: number
  }
}

export interface UploadAnalytics {
  totalUploads: number
  successfulUploads: number
  failedUploads: number
  totalRecordsProcessed: number
  averageUploadTime: number
  averageRecordsPerSecond: number
  peakRecordsPerSecond: number
  mostCommonErrors: Array<{
    message: string
    count: number
    percentage: number
  }>
  uploadTrends: Array<{
    date: string
    uploads: number
    records: number
    successRate: number
  }>
  performanceTrends: Array<{
    date: string
    averageSpeed: number
    peakSpeed: number
  }>
  fileTypeDistribution: Record<string, number>
  errorDistribution: Record<string, number>
}

class UploadAnalyticsService {
  private static instance: UploadAnalyticsService
  private metrics: Map<string, UploadMetrics> = new Map()
  private storageKey = "upload_analytics_metrics"

  static getInstance(): UploadAnalyticsService {
    if (!UploadAnalyticsService.instance) {
      UploadAnalyticsService.instance = new UploadAnalyticsService()
    }
    return UploadAnalyticsService.instance
  }

  constructor() {
    this.loadMetricsFromStorage()
  }

  // Initialize metrics for a new upload job
  initializeMetrics(jobId: string, fileName: string, totalRecords: number, totalBatches: number): UploadMetrics {
    const metrics: UploadMetrics = {
      jobId,
      fileName,
      startTime: Date.now(),
      totalRecords,
      processedRecords: 0,
      successfulRecords: 0,
      failedRecords: 0,
      duplicateRecords: 0,
      replacedRecords: 0,
      averageRecordsPerSecond: 0,
      peakRecordsPerSecond: 0,
      totalBatches,
      completedBatches: 0,
      errorCount: 0,
      warningCount: 0,
      status: "pending",
      errors: [],
      warnings: [],
      performanceData: [],
    }

    this.metrics.set(jobId, metrics)
    this.saveMetricsToStorage()
    return metrics
  }

  // Update metrics during upload progress
  updateProgress(
      jobId: string,
      processedRecords: number,
      successfulRecords: number,
      failedRecords: number,
      duplicateRecords: number = 0,
      replacedRecords: number = 0,
  ): void {
    const metrics = this.metrics.get(jobId)
    if (!metrics) return

    const now = Date.now()
    const elapsedSeconds = (now - metrics.startTime) / 1000
    const recordsPerSecond = elapsedSeconds > 0 ? processedRecords / elapsedSeconds : 0

    metrics.processedRecords = processedRecords
    metrics.successfulRecords = successfulRecords
    metrics.failedRecords = failedRecords
    metrics.duplicateRecords = duplicateRecords
    metrics.replacedRecords = replacedRecords

    // Calculate average records per second with proper null checking
    if (metrics.averageRecordsPerSecond === 0) {
      metrics.averageRecordsPerSecond = recordsPerSecond
    } else {
      metrics.averageRecordsPerSecond = (metrics.averageRecordsPerSecond + recordsPerSecond) / 2
    }

    metrics.peakRecordsPerSecond = Math.max(metrics.peakRecordsPerSecond || 0, recordsPerSecond)

    this.saveMetrics(jobId, metrics)

    // Add performance data point
    this.addPerformanceData(jobId, processedRecords, recordsPerSecond)
  }

  // Add performance data point
  addPerformanceData(jobId: string, recordsProcessed: number, recordsPerSecond: number): void {
    const metrics = this.metrics.get(jobId)
    if (!metrics) return

    metrics.performanceData.push({
      timestamp: Date.now(),
      recordsProcessed,
      recordsPerSecond,
      memoryUsage: this.getMemoryUsage(),
    })

    // Keep only last 100 data points to prevent memory bloat
    if (metrics.performanceData.length > 100) {
      metrics.performanceData = metrics.performanceData.slice(-100)
    }

    this.saveMetrics(jobId, metrics)
  }

  // Update batch completion
  updateBatchCompletion(jobId: string, batchIndex: number): void {
    const metrics = this.metrics.get(jobId)
    if (!metrics) return

    metrics.completedBatches = Math.max(metrics.completedBatches, batchIndex + 1)
    this.saveMetrics(jobId, metrics)
  }

  // Add error
  addError(jobId: string, message: string, batchIndex?: number, recordIndex?: number): void {
    const metrics = this.metrics.get(jobId)
    if (!metrics) return

    metrics.errors.push({
      timestamp: Date.now(),
      message,
      batchIndex,
      recordIndex,
    })
    metrics.errorCount = metrics.errors.length

    this.saveMetrics(jobId, metrics)
  }

  // Add warning
  addWarning(jobId: string, message: string, batchIndex?: number, recordIndex?: number): void {
    const metrics = this.metrics.get(jobId)
    if (!metrics) return

    metrics.warnings.push({
      timestamp: Date.now(),
      message,
      batchIndex,
      recordIndex,
    })
    metrics.warningCount = metrics.warnings.length

    this.saveMetrics(jobId, metrics)
  }

  // Update job status
  updateStatus(jobId: string, status: UploadMetrics["status"]): void {
    const metrics = this.metrics.get(jobId)
    if (!metrics) return

    metrics.status = status
    if (status === "completed" || status === "failed" || status === "cancelled") {
      metrics.endTime = Date.now()
    }

    this.saveMetrics(jobId, metrics)
  }

  // Add validation metrics
  addValidationMetrics(
      jobId: string,
      validationTime: number,
      validRecords: number,
      invalidRecords: number,
      validationErrors: number,
  ): void {
    const metrics = this.metrics.get(jobId)
    if (!metrics) return

    metrics.validationMetrics = {
      validationTime,
      validRecords,
      invalidRecords,
      validationErrors,
    }

    this.saveMetrics(jobId, metrics)
  }

  // Add duplicate detection metrics
  addDuplicateDetectionMetrics(
      jobId: string,
      detectionTime: number,
      duplicatesFound: number,
      duplicatesReplaced: number,
      duplicatesSkipped: number,
  ): void {
    const metrics = this.metrics.get(jobId)
    if (!metrics) return

    metrics.duplicateDetectionMetrics = {
      detectionTime,
      duplicatesFound,
      duplicatesReplaced,
      duplicatesSkipped,
    }

    this.saveMetrics(jobId, metrics)
  }

  // Add database metrics
  addDatabaseMetrics(
      jobId: string,
      insertTime: number,
      updateTime: number,
      queryTime: number,
      connectionTime: number,
  ): void {
    const metrics = this.metrics.get(jobId)
    if (!metrics) return

    metrics.databaseMetrics = {
      insertTime,
      updateTime,
      queryTime,
      connectionTime,
    }

    this.saveMetrics(jobId, metrics)
  }

  // Get metrics for a specific job
  getMetrics(jobId: string): UploadMetrics | undefined {
    return this.metrics.get(jobId)
  }

  // Get all metrics
  getAllMetrics(): UploadMetrics[] {
    return Array.from(this.metrics.values()).sort((a, b) => b.startTime - a.startTime)
  }

  // Get analytics summary
  getAnalytics(dateRange?: { start: Date; end: Date }): UploadAnalytics {
    const allMetrics = this.getAllMetrics()
    let filteredMetrics = allMetrics

    if (dateRange) {
      filteredMetrics = allMetrics.filter(
          (m) => m.startTime >= dateRange.start.getTime() && m.startTime <= dateRange.end.getTime(),
      )
    }

    const totalUploads = filteredMetrics.length
    const successfulUploads = filteredMetrics.filter((m) => m.status === "completed").length
    const failedUploads = filteredMetrics.filter((m) => m.status === "failed").length

    const totalRecordsProcessed = filteredMetrics.reduce((sum, m) => sum + m.processedRecords, 0)
    const totalUploadTime = filteredMetrics.reduce((sum, m) => {
      if (m.endTime) {
        return sum + (m.endTime - m.startTime)
      }
      return sum
    }, 0)

    const averageUploadTime = totalUploads > 0 ? totalUploadTime / totalUploads : 0
    const averageRecordsPerSecond =
        filteredMetrics.length > 0
            ? filteredMetrics.reduce((sum, m) => sum + (m.averageRecordsPerSecond || 0), 0) / filteredMetrics.length
            : 0
    const peakRecordsPerSecond = Math.max(...filteredMetrics.map((m) => m.peakRecordsPerSecond || 0), 0)

    // Calculate most common errors
    const errorCounts: Record<string, number> = {}
    filteredMetrics.forEach((m) => {
      m.errors.forEach((error) => {
        errorCounts[error.message] = (errorCounts[error.message] || 0) + 1
      })
    })

    const totalErrors = Object.values(errorCounts).reduce((sum, count) => sum + count, 0)
    const mostCommonErrors = Object.entries(errorCounts)
        .map(([message, count]) => ({
          message,
          count,
          percentage: totalErrors > 0 ? (count / totalErrors) * 100 : 0,
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)

    // Calculate upload trends (daily)
    const uploadTrends = this.calculateDailyTrends(filteredMetrics)

    // Calculate performance trends
    const performanceTrends = this.calculatePerformanceTrends(filteredMetrics)

    // Calculate file type distribution
    const fileTypeDistribution: Record<string, number> = {}
    filteredMetrics.forEach((m) => {
      const extension = m.fileName.split(".").pop()?.toLowerCase() || "unknown"
      fileTypeDistribution[extension] = (fileTypeDistribution[extension] || 0) + 1
    })

    // Calculate error distribution by type
    const errorDistribution: Record<string, number> = {}
    filteredMetrics.forEach((m) => {
      if (m.status === "failed") {
        errorDistribution["failed"] = (errorDistribution["failed"] || 0) + 1
      }
      if (m.errorCount > 0) {
        errorDistribution["with_errors"] = (errorDistribution["with_errors"] || 0) + 1
      }
      if (m.warningCount > 0) {
        errorDistribution["with_warnings"] = (errorDistribution["with_warnings"] || 0) + 1
      }
    })

    return {
      totalUploads,
      successfulUploads,
      failedUploads,
      totalRecordsProcessed,
      averageUploadTime,
      averageRecordsPerSecond,
      peakRecordsPerSecond,
      mostCommonErrors,
      uploadTrends,
      performanceTrends,
      fileTypeDistribution,
      errorDistribution,
    }
  }

  // Calculate daily trends
  private calculateDailyTrends(metrics: UploadMetrics[]): Array<{
    date: string
    uploads: number
    records: number
    successRate: number
  }> {
    const dailyData: Record<string, { uploads: number; records: number; successful: number }> = {}

    metrics.forEach((m) => {
      const date = new Date(m.startTime).toISOString().split("T")[0]
      if (!dailyData[date]) {
        dailyData[date] = { uploads: 0, records: 0, successful: 0 }
      }
      dailyData[date].uploads++
      dailyData[date].records += m.processedRecords
      if (m.status === "completed") {
        dailyData[date].successful++
      }
    })

    return Object.entries(dailyData)
        .map(([date, data]) => ({
          date,
          uploads: data.uploads,
          records: data.records,
          successRate: data.uploads > 0 ? (data.successful / data.uploads) * 100 : 0,
        }))
        .sort((a, b) => a.date.localeCompare(b.date))
  }

  // Calculate performance trends
  private calculatePerformanceTrends(metrics: UploadMetrics[]): Array<{
    date: string
    averageSpeed: number
    peakSpeed: number
  }> {
    const dailyPerformance: Record<string, { speeds: number[]; peaks: number[] }> = {}

    metrics.forEach((m) => {
      const date = new Date(m.startTime).toISOString().split("T")[0]
      if (!dailyPerformance[date]) {
        dailyPerformance[date] = { speeds: [], peaks: [] }
      }
      if (m.averageRecordsPerSecond > 0) {
        dailyPerformance[date].speeds.push(m.averageRecordsPerSecond)
      }
      if (m.peakRecordsPerSecond > 0) {
        dailyPerformance[date].peaks.push(m.peakRecordsPerSecond)
      }
    })

    return Object.entries(dailyPerformance)
        .map(([date, data]) => ({
          date,
          averageSpeed: data.speeds.length > 0 ? data.speeds.reduce((sum, s) => sum + s, 0) / data.speeds.length : 0,
          peakSpeed: data.peaks.length > 0 ? Math.max(...data.peaks) : 0,
        }))
        .sort((a, b) => a.date.localeCompare(b.date))
  }

  // Get memory usage (if available)
  private getMemoryUsage(): number | undefined {
    if (typeof performance !== "undefined" && "memory" in performance) {
      return (performance as any).memory?.usedJSHeapSize
    }
    return undefined
  }

  // Save metrics to localStorage
  private saveMetricsToStorage(): void {
    try {
      const metricsArray = Array.from(this.metrics.entries())
      localStorage.setItem(this.storageKey, JSON.stringify(metricsArray))
    } catch (error) {
      console.error("Failed to save metrics to storage:", error)
    }
  }

  // Load metrics from localStorage
  private loadMetricsFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.storageKey)
      if (stored) {
        const metricsArray = JSON.parse(stored)
        this.metrics = new Map(metricsArray)
      }
    } catch (error) {
      console.error("Failed to load metrics from storage:", error)
    }
  }

  // Save individual metrics
  private saveMetrics(jobId: string, metrics: UploadMetrics): void {
    this.metrics.set(jobId, metrics)
    this.saveMetricsToStorage()
  }

  // Clear old metrics (keep only last 30 days)
  clearOldMetrics(daysToKeep: number = 30): void {
    const cutoffTime = Date.now() - daysToKeep * 24 * 60 * 60 * 1000
    const metricsToKeep = new Map<string, UploadMetrics>()

    this.metrics.forEach((metrics, jobId) => {
      if (metrics.startTime > cutoffTime) {
        metricsToKeep.set(jobId, metrics)
      }
    })

    this.metrics = metricsToKeep
    this.saveMetricsToStorage()
  }

  // Export metrics as JSON
  exportMetrics(): string {
    return JSON.stringify(Array.from(this.metrics.entries()), null, 2)
  }

  // Import metrics from JSON
  importMetrics(jsonData: string): void {
    try {
      const metricsArray = JSON.parse(jsonData)
      this.metrics = new Map(metricsArray)
      this.saveMetricsToStorage()
    } catch (error) {
      console.error("Failed to import metrics:", error)
      throw new Error("Invalid metrics data format")
    }
  }

  // Get performance insights
  getPerformanceInsights(jobId: string): {
    averageSpeed: number
    peakSpeed: number
    speedVariation: number
    bottlenecks: string[]
    recommendations: string[]
  } {
    const metrics = this.metrics.get(jobId)
    if (!metrics) {
      return {
        averageSpeed: 0,
        peakSpeed: 0,
        speedVariation: 0,
        bottlenecks: [],
        recommendations: [],
      }
    }

    const speeds = metrics.performanceData.map((p) => p.recordsPerSecond)
    const averageSpeed = metrics.averageRecordsPerSecond || 0
    const peakSpeed = metrics.peakRecordsPerSecond || 0
    const speedVariation = speeds.length > 1 ? this.calculateVariation(speeds) : 0

    const bottlenecks: string[] = []
    const recommendations: string[] = []

    // Analyze bottlenecks
    if (averageSpeed < 100) {
      bottlenecks.push("Low processing speed")
      recommendations.push("Consider optimizing batch size or database connections")
    }

    if (speedVariation > 0.5) {
      bottlenecks.push("High speed variation")
      recommendations.push("Check for network issues or resource constraints")
    }

    if (metrics.errorCount > metrics.totalRecords * 0.1) {
      bottlenecks.push("High error rate")
      recommendations.push("Review data quality and validation rules")
    }

    if (metrics.duplicateRecords > metrics.totalRecords * 0.2) {
      bottlenecks.push("High duplicate rate")
      recommendations.push("Implement better duplicate detection strategies")
    }

    return {
      averageSpeed,
      peakSpeed,
      speedVariation,
      bottlenecks,
      recommendations,
    }
  }

  // Calculate coefficient of variation
  private calculateVariation(values: number[]): number {
    if (values.length === 0) return 0

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    const standardDeviation = Math.sqrt(variance)

    return mean > 0 ? standardDeviation / mean : 0
  }
}

// Export singleton instance
export const uploadAnalyticsService = UploadAnalyticsService.getInstance()

// Export types
export type { UploadMetrics, UploadAnalytics }
