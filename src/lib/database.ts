export interface DatabaseRecord {
  fileName: string
  sheetName: string
  headers: string[]
  rowData: Record<string, any>
  rowIndex: number
  totalRows: number
}

export async function saveRecordsToDatabase(
  records: DatabaseRecord[],
  onProgress: (saved: number, total: number) => void,
): Promise<string[]> {
  const BATCH_SIZE = 500
  const savedIds: string[] = []
  let savedCount = 0

  // Split records into batches for client-side processing
  for (let i = 0; i < records.length; i += BATCH_SIZE) {
    const batchRecords = records.slice(i, i + BATCH_SIZE)

    try {
      // Send batch to API
      const response = await fetch("/api/save-records", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          records: batchRecords,
          metadata:
            i === 0
              ? {
                  fileName: batchRecords[0]?.fileName,
                  sheetName: batchRecords[0]?.sheetName,
                  totalRows: records.length,
                  totalColumns: batchRecords[0]?.headers?.length || 0,
                  headers: batchRecords[0]?.headers || [],
                }
              : null,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to save batch")
      }

      const result = await response.json()

      // Update progress
      savedCount += batchRecords.length
      onProgress(savedCount, records.length)

      // Small delay to prevent overwhelming the server
      await new Promise((resolve) => setTimeout(resolve, 100))
    } catch (error) {
      console.error(`Error saving batch ${i / BATCH_SIZE + 1}:`, error)
      throw new Error(`Failed to save records at batch ${i / BATCH_SIZE + 1}`)
    }
  }

  return savedIds
}

export async function saveFileMetadata(metadata: {
  fileName: string
  sheetName: string
  totalRows: number
  totalColumns: number
  headers: string[]
  uploadedAt: Date
}) {
  // This is now handled in the saveRecordsToDatabase function
  return Promise.resolve("metadata-handled-in-batch")
}
