export interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
  retryableErrors: string[]
}

export class UploadRetryService {
  private static instance: UploadRetryService
  private config: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000, // 1 second
    maxDelay: 30000, // 30 seconds
    backoffMultiplier: 2,
    retryableErrors: [
      "network error",
      "timeout",
      "connection failed",
      "server error",
      "rate limit",
      "temporary failure",
    ],
  }

  static getInstance(): UploadRetryService {
    if (!UploadRetryService.instance) {
      UploadRetryService.instance = new UploadRetryService()
    }
    return UploadRetryService.instance
  }

  updateConfig(config: Partial<RetryConfig>): void {
    this.config = { ...this.config, ...config }
  }

  async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: {
      jobId: string
      batchIndex: number
      operationName: string
    },
  ): Promise<T> {
    let lastError: Error
    let attempt = 0

    while (attempt <= this.config.maxRetries) {
      try {
        if (attempt > 0) {
          const delay = this.calculateDelay(attempt)
          console.log(
            `Retrying ${context.operationName} (attempt ${attempt + 1}/${this.config.maxRetries + 1}) after ${delay}ms delay`,
          )
          await this.delay(delay)
        }

        const result = await operation()

        if (attempt > 0) {
          console.log(`${context.operationName} succeeded after ${attempt} retries`)
        }

        return result
      } catch (error) {
        lastError = error as Error
        attempt++

        const isRetryable = this.isRetryableError(lastError)
        const hasRetriesLeft = attempt <= this.config.maxRetries

        console.error(
          `${context.operationName} failed (attempt ${attempt}/${this.config.maxRetries + 1}):`,
          lastError.message,
        )

        if (!isRetryable) {
          console.error(`Error is not retryable: ${lastError.message}`)
          throw lastError
        }

        if (!hasRetriesLeft) {
          console.error(`Max retries (${this.config.maxRetries}) exceeded for ${context.operationName}`)
          throw new Error(`Max retries exceeded. Last error: ${lastError.message}`)
        }

        // Record retry attempt
        this.recordRetryAttempt(context.jobId, context.batchIndex, attempt, lastError.message)
      }
    }

    throw lastError!
  }

  private calculateDelay(attempt: number): number {
    const exponentialDelay = this.config.baseDelay * Math.pow(this.config.backoffMultiplier, attempt - 1)
    const jitteredDelay = exponentialDelay * (0.5 + Math.random() * 0.5) // Add jitter
    return Math.min(jitteredDelay, this.config.maxDelay)
  }

  private isRetryableError(error: Error): boolean {
    const errorMessage = error.message.toLowerCase()
    return this.config.retryableErrors.some((retryableError) => errorMessage.includes(retryableError.toLowerCase()))
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  private recordRetryAttempt(jobId: string, batchIndex: number, attempt: number, errorMessage: string): void {
    try {
      const retryLog = {
        jobId,
        batchIndex,
        attempt,
        errorMessage,
        timestamp: new Date().toISOString(),
      }

      const existingLogs = JSON.parse(localStorage.getItem("upload_retry_logs") || "[]")
      existingLogs.push(retryLog)

      // Keep only last 100 retry logs
      const trimmedLogs = existingLogs.slice(-100)
      localStorage.setItem("upload_retry_logs", JSON.stringify(trimmedLogs))
    } catch (error) {
      console.error("Failed to record retry attempt:", error)
    }
  }

  getRetryLogs(): Array<{
    jobId: string
    batchIndex: number
    attempt: number
    errorMessage: string
    timestamp: string
  }> {
    try {
      return JSON.parse(localStorage.getItem("upload_retry_logs") || "[]")
    } catch {
      return []
    }
  }

  clearRetryLogs(): void {
    localStorage.removeItem("upload_retry_logs")
  }
}

export const uploadRetryService = UploadRetryService.getInstance()
