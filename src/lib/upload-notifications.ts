export class UploadNotificationService {
  private static instance: UploadNotificationService
  private permission: NotificationPermission = "default"

  constructor() {
    this.requestPermission()
  }

  static getInstance(): UploadNotificationService {
    if (!UploadNotificationService.instance) {
      UploadNotificationService.instance = new UploadNotificationService()
    }
    return UploadNotificationService.instance
  }

  async requestPermission(): Promise<boolean> {
    if (!("Notification" in window)) {
      console.warn("This browser does not support notifications")
      return false
    }

    if (this.permission === "granted") {
      return true
    }

    if (this.permission === "default") {
      this.permission = await Notification.requestPermission()
    }

    return this.permission === "granted"
  }

  async showNotification(title: string, options: NotificationOptions = {}): Promise<void> {
    const hasPermission = await this.requestPermission()
    if (!hasPermission) return

    try {
      const notification = new Notification(title, {
        icon: "/favicon.ico",
        badge: "/favicon.ico",
        ...options,
      })

      // Auto-close after 5 seconds
      setTimeout(() => {
        notification.close()
      }, 5000)

      return new Promise((resolve) => {
        notification.onclick = () => {
          window.focus()
          notification.close()
          resolve()
        }
        notification.onclose = () => resolve()
      })
    } catch (error) {
      console.error("Failed to show notification:", error)
    }
  }

  notifyUploadStarted(fileName: string, recordCount: number): void {
    this.showNotification("Upload Started", {
      body: `Started uploading ${recordCount.toLocaleString()} records from ${fileName}`,
      tag: "upload-started",
    })
  }

  notifyUploadCompleted(fileName: string, recordCount: number, duration: number): void {
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    const timeStr = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`

    this.showNotification("Upload Completed! ✅", {
      body: `Successfully uploaded ${recordCount.toLocaleString()} records from ${fileName} in ${timeStr}`,
      tag: "upload-completed",
    })
  }

  notifyUploadFailed(fileName: string, error: string): void {
    this.showNotification("Upload Failed ❌", {
      body: `Failed to upload ${fileName}: ${error}`,
      tag: "upload-failed",
    })
  }

  notifyUploadPaused(fileName: string): void {
    this.showNotification("Upload Paused ⏸️", {
      body: `Upload of ${fileName} has been paused`,
      tag: "upload-paused",
    })
  }

  notifyUploadResumed(fileName: string): void {
    this.showNotification("Upload Resumed ▶️", {
      body: `Upload of ${fileName} has been resumed`,
      tag: "upload-resumed",
    })
  }
}

export const uploadNotificationService = UploadNotificationService.getInstance()
