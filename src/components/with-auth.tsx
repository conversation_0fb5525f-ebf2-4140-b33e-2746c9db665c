"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import axiosInstance from "@/utils/axiosInstance";


const withAuth = (WrappedComponent: React.ComponentType) => {
    const Wrapper = (props: any) => {
        const router = useRouter();

        useEffect(() => {
            const checkAuth = async () => {
                try {
                    // Check if the user is authenticated by verifying a token or session cookie
                    const response = await axiosInstance.get('/users/me', { withCredentials: true });
                    if (!response.data) {
                        router.push('/login'); // Redirect to login if no valid session
                    }
                } catch (error) {
                    router.push('/login'); // Redirect to login on error or no authentication
                }
            };

            checkAuth();
        }, [router]);

        return <WrappedComponent {...props} />;
    };

    return Wrapper;
};

export default withAuth;
