"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Database, CheckCircle, XCircle, Clock, Loader2 } from "lucide-react"

interface DatabaseProgressProps {
  isVisible: boolean
  savedRecords: number
  totalRecords: number
  isComplete: boolean
  isError: boolean
  errorMessage?: string
  onClose: () => void
  estimatedTimeRemaining?: number
}

export function DatabaseProgress({
  isVisible,
  savedRecords,
  totalRecords,
  isComplete,
  isError,
  errorMessage,
  onClose,
  estimatedTimeRemaining,
}: DatabaseProgressProps) {
  const [startTime] = useState(Date.now())
  const [elapsedTime, setElapsedTime] = useState(0)

  useEffect(() => {
    if (!isVisible || isComplete || isError) return

    const interval = setInterval(() => {
      setElapsedTime(Date.now() - startTime)
    }, 1000)

    return () => clearInterval(interval)
  }, [isVisible, isComplete, isError, startTime])

  if (!isVisible) return null

  const progressPercentage = totalRecords > 0 ? (savedRecords / totalRecords) * 100 : 0
  const recordsPerSecond = elapsedTime > 0 ? savedRecords / (elapsedTime / 1000) : 0

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  const getStatusIcon = () => {
    if (isError) return <XCircle className="w-5 h-5 text-red-500" />
    if (isComplete) return <CheckCircle className="w-5 h-5 text-green-500" />
    return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />
  }

  const getStatusText = () => {
    if (isError) return "Error"
    if (isComplete) return "Complete"
    return "Saving..."
  }

  const getStatusColor = () => {
    if (isError) return "text-red-600"
    if (isComplete) return "text-green-600"
    return "text-blue-600"
  }

  return (
    <Card className="fixed bottom-4 right-4 w-96 shadow-lg border-2 z-50">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-lg">
          <div className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            Delivery Methods Save Progress
          </div>
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <Badge variant={isError ? "destructive" : isComplete ? "default" : "secondary"}>{getStatusText()}</Badge>
          </div>
        </CardTitle>
        <CardDescription>{isError ? errorMessage : "Saving Polish Post delivery methods"}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isError && (
          <>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span className={getStatusColor()}>
                  {savedRecords.toLocaleString()} / {totalRecords.toLocaleString()} delivery methods
                </span>
              </div>
              <Progress value={progressPercentage} className="h-2" />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{progressPercentage.toFixed(1)}% complete</span>
                <span>{recordsPerSecond.toFixed(1)} delivery methods/sec</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  <span className="font-medium">Elapsed</span>
                </div>
                <div className="text-muted-foreground">{formatTime(elapsedTime)}</div>
              </div>
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  <span className="font-medium">Remaining</span>
                </div>
                <div className="text-muted-foreground">
                  {estimatedTimeRemaining ? formatTime(estimatedTimeRemaining) : "Calculating..."}
                </div>
              </div>
            </div>

            {isComplete && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center gap-2 text-green-700">
                  <CheckCircle className="w-4 h-4" />
                  <span className="font-medium">Successfully saved all delivery methods!</span>
                </div>
                <div className="text-sm text-green-600 mt-1">Total time: {formatTime(elapsedTime)}</div>
              </div>
            )}
          </>
        )}

        {isError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <div className="flex items-center gap-2 text-red-700">
              <XCircle className="w-4 h-4" />
              <span className="font-medium">Save failed</span>
            </div>
            <div className="text-sm text-red-600 mt-1">{errorMessage}</div>
          </div>
        )}

        <div className="flex justify-end gap-2">
          {(isComplete || isError) && (
            <Button onClick={onClose} size="sm">
              Close
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
