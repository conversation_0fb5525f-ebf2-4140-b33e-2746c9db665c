"use client"

import { Progress } from "@/components/ui/progress"
import { CheckCircle2, XCircle } from "lucide-react"

interface PasswordStrengthMeterProps {
    password: string
}

export function PasswordStrengthMeter({ password }: PasswordStrengthMeterProps) {
    // Calculate password strength
    const strength = calculatePasswordStrength(password)

    // Get color based on strength
    const getColorClass = () => {
        if (strength === 0) return "bg-gray-200"
        if (strength < 40) return "bg-red-500"
        if (strength < 70) return "bg-yellow-500"
        return "bg-green-500"
    }

    // Get strength label
    const getStrengthLabel = () => {
        if (password.length === 0) return ""
        if (strength < 40) return "Weak"
        if (strength < 70) return "Medium"
        return "Strong"
    }

    // Password requirements
    const requirements = [
        {
            label: "At least 8 characters",
            met: password.length >= 8,
        },
        {
            label: "Contains uppercase letter",
            met: /[A-Z]/.test(password),
        },
        {
            label: "Contains lowercase letter",
            met: /[a-z]/.test(password),
        },
        {
            label: "Contains number",
            met: /[0-9]/.test(password),
        },
        {
            label: "Contains special character",
            met: /[^A-Za-z0-9]/.test(password),
        },
    ]

    return (
        <div className="space-y-3">
            <div className="space-y-1">
                <div className="flex justify-between items-center">
                    <span className="text-xs text-muted-foreground">Password strength</span>
                    <span
                        className={`text-xs font-medium ${
                            strength < 40 ? "text-red-500" : strength < 70 ? "text-yellow-500" : "text-green-500"
                        }`}
                    >
            {getStrengthLabel()}
          </span>
                </div>
                <Progress value={strength} className={`h-1.5 ${getColorClass()}`} />
            </div>

            {password.length > 0 && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {requirements.map((req, index) => (
                        <div key={index} className="flex items-center gap-2 text-xs">
                            {req.met ? (
                                <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
                            ) : (
                                <XCircle className="h-3.5 w-3.5 text-red-500" />
                            )}
                            <span className={req.met ? "text-green-700" : "text-muted-foreground"}>{req.label}</span>
                        </div>
                    ))}
                </div>
            )}
        </div>
    )
}

// Calculate password strength score (0-100)
function calculatePasswordStrength(password: string): number {
    if (!password) return 0

    let score = 0

    // Length contribution (up to 25 points)
    score += Math.min(password.length * 2, 25)

    // Character variety contribution
    if (/[A-Z]/.test(password)) score += 15 // uppercase
    if (/[a-z]/.test(password)) score += 15 // lowercase
    if (/[0-9]/.test(password)) score += 15 // digits
    if (/[^A-Za-z0-9]/.test(password)) score += 15 // special chars

    // Complexity patterns
    if (/[A-Z].*[A-Z]/.test(password)) score += 5 // multiple uppercase
    if (/[a-z].*[a-z]/.test(password)) score += 5 // multiple lowercase
    if (/[0-9].*[0-9]/.test(password)) score += 5 // multiple digits
    if (/[^A-Za-z0-9].*[^A-Za-z0-9]/.test(password)) score += 5 // multiple special

    // Penalize for patterns
    if (/^[A-Za-z]+$/.test(password)) score -= 10 // letters only
    if (/^[0-9]+$/.test(password)) score -= 15 // numbers only
    if (/^[A-Za-z0-9]+$/.test(password)) score -= 5 // alphanumeric only

    // Ensure score is between 0-100
    return Math.max(0, Math.min(100, score))
}
