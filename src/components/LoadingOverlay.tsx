interface LoadingOverlayProps {
    logoSrc?: string
    size?: number
    color?: string
}

export function LoadingOverlay({ logoSrc="/images/logo/icon-dark.svg", size = 80, color = "#ffffff" }: LoadingOverlayProps) {
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="relative" style={{ width: size, height: size }}>
                {/* Spinning circle */}
                <svg width={size} height={size} viewBox="0 0 100 100" preserveAspectRatio="xMidYMid" className="animate-spin">
                    <circle
                        cx="50"
                        cy="50"
                        fill="none"
                        stroke={color}
                        strokeWidth="5"
                        r="40"
                        strokeDasharray="164.93361431346415 56.97787143782138"
                    />
                </svg>

                {/* Static logo */}
                <div className="absolute inset-0 flex items-center justify-center">
                    <img src={logoSrc || "/images/logo/icon-dark.svg"} alt="Logo" className="w-1/2 p-1 h-1/2 object-contain animate-pulse" />
                </div>
            </div>
        </div>
    )
}

