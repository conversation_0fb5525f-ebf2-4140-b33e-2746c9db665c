"use client"

import * as React from "react"
import NextLink from "next/link"
import { useRouter } from "next/navigation"
import { cn } from "@/lib/utils"

interface LinkProps extends React.ComponentPropsWithoutRef<typeof NextLink> {
    onNavigate?: (e: React.MouseEvent<HTMLAnchorElement>) => void
}

const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(({ className, onNavigate, onClick, ...props }, ref) => {
    const router = useRouter()

    const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
        // Call the original onClick if it exists
        if (onClick) {
            onClick(e)
        }

        // If not prevented already and it's a left click without modifier keys
        if (!e.defaultPrevented && e.button === 0 && !e.metaKey && !e.ctrlKey && !e.shiftKey && !e.altKey) {
            e.preventDefault()

            // Call onNavigate callback if provided
            if (onNavigate) {
                onNavigate(e)
            }

            // Use regular navigation instead of startTransition
            router.push(props.href.toString())
        }
    }

    return <NextLink className={cn(className)} onClick={handleClick} ref={ref} {...props} />
})
Link.displayName = "Link"

export { Link }
