import type React from "react"
import Image from "next/image"

interface ImagePopupProps {
    src: string
    alt: string
    onClose: () => void
}

const ImagePopup: React.FC<ImagePopupProps> = ({ src, alt, onClose }) => {
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="relative">
                <Image src={src || "/placeholder.svg"} alt={alt} width={600} height={400} objectFit="contain" />
                <button onClick={onClose} className="absolute top-2 right-2 text-white bg-black bg-opacity-50 rounded-full p-2">
                    Close
                </button>
            </div>
        </div>
    )
}

export default ImagePopup

