"use client"
import { useState, useEffect } from "react"
import type React from "react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Check, ChevronDown, X } from "lucide-react"
import { allCountries } from "@/utils/data"

export interface PhoneInputValue {
    countryCode: string
    nationalNumber: string
    isValid: boolean
    fullNumber: string
}

interface Country {
    code: string
    name: string
    flag: string
}

interface IntlTelInputProps {
    id?: string
    label?: string
    value?: PhoneInputValue
    onChange?: (value: PhoneInputValue) => void
    required?: boolean
    disabled?: boolean
    className?: string
    inputClassName?: string
    errorMessage?: string
    showHelper?: boolean
}

export const IntlTelInput = ({
                                 id,
                                 label,
                                 value,
                                 onChange,
                                 required = false,
                                 disabled = false,
                                 className = "",
                                 inputClassName = "",
                                 errorMessage,
                                 showHelper = true,
                             }: IntlTelInputProps) => {
    const [phoneInput, setPhoneInput] = useState<PhoneInputValue>({
        countryCode: value?.countryCode || "+1",
        nationalNumber: value?.nationalNumber || "",
        isValid: value?.isValid || false,
        fullNumber: value?.fullNumber || "+1",
    })
    const [isCountrySelectOpen, setIsCountrySelectOpen] = useState(false)
    const [searchQuery, setSearchQuery] = useState("")
    const [selectedCountryName, setSelectedCountryName] = useState<string>("United States")

    useEffect(() => {
        if (value) {
            setPhoneInput(value)
        }
    }, [value])

    useEffect(() => {
        const isValid = validatePhoneNumber(phoneInput.nationalNumber, phoneInput.countryCode)
        const fullNumber = phoneInput.countryCode + phoneInput.nationalNumber

        const updatedValue = {
            ...phoneInput,
            isValid,
            fullNumber,
        }

        setPhoneInput(updatedValue)
        onChange?.(updatedValue)
    }, [phoneInput.countryCode, phoneInput.nationalNumber])

    const getMaxLengthForCountry = (countryCode: string): number => {
        switch (countryCode) {
            case "+1":
                return 10
            case "+44":
                return 11
            case "+48":
                return 9
            case "+49":
                return 11
            case "+33":
                return 9
            case "+39":
                return 10
            case "+34":
                return 9
            case "+86":
                return 11
            case "+91":
                return 10
            case "+81":
                return 11
            case "+7":
                return 10
            case "+55":
                return 11
            case "+61":
                return 9
            case "+52":
                return 10
            case "+31":
                return 9
            default:
                return 15
        }
    }

    const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const nationalNumber = e.target.value.replace(/\D/g, "")
        const maxLength = getMaxLengthForCountry(phoneInput.countryCode)
        const truncatedNumber = nationalNumber.slice(0, maxLength)
        setPhoneInput((prev) => ({ ...prev, nationalNumber: truncatedNumber }))
    }

    const handleCountrySelect = (country: Country) => {
        const maxLength = getMaxLengthForCountry(country.code)
        const nationalNumber = phoneInput.nationalNumber.slice(0, maxLength)

        setPhoneInput((prev) => ({
            ...prev,
            countryCode: country.code,
            nationalNumber,
        }))
        setSelectedCountryName(country.name)
        setIsCountrySelectOpen(false)
        setSearchQuery("")
    }

    const validatePhoneNumber = (number: string, countryCode: string): boolean => {
        if (!number) return false
        switch (countryCode) {
            case "+1":
                return /^\d{10}$/.test(number)
            case "+44":
                return /^\d{10,11}$/.test(number)
            case "+48":
                return /^\d{9}$/.test(number)
            case "+49":
                return /^\d{10,11}$/.test(number)
            case "+33":
                return /^\d{9}$/.test(number)
            case "+39":
                return /^\d{10}$/.test(number)
            case "+34":
                return /^\d{9}$/.test(number)
            case "+86":
                return /^\d{11}$/.test(number)
            case "+91":
                return /^\d{10}$/.test(number)
            case "+81":
                return /^\d{10,11}$/.test(number)
            case "+7":
                return /^\d{10}$/.test(number)
            case "+55":
                return /^\d{10,11}$/.test(number)
            case "+61":
                return /^\d{9}$/.test(number)
            case "+52":
                return /^\d{10}$/.test(number)
            case "+31":
                return /^\d{9}$/.test(number)
            default:
                return number.length >= 6 && number.length <= 15
        }
    }

    const formatPhoneNumber = (number: string, countryCode: string): string => {
        if (!number) return ""
        switch (countryCode) {
            case "+1":
                if (number.length <= 3) return number
                if (number.length <= 6) return `(${number.slice(0, 3)}) ${number.slice(3)}`
                return `(${number.slice(0, 3)}) ${number.slice(3, 6)}-${number.slice(6, 10)}`
            case "+44":
                if (number.length <= 4) return number
                return `${number.slice(0, 4)} ${number.slice(4)}`
            case "+48":
                if (number.length <= 3) return number
                if (number.length <= 6) return `${number.slice(0, 3)} ${number.slice(3)}`
                return `${number.slice(0, 3)} ${number.slice(3, 6)} ${number.slice(6, 9)}`
            case "+91":
                if (number.length <= 5) return number
                return `${number.slice(0, 5)} ${number.slice(5)}`
            case "+86":
                if (number.length <= 3) return number
                if (number.length <= 7) return `${number.slice(0, 3)} ${number.slice(3)}`
                return `${number.slice(0, 3)} ${number.slice(3, 7)} ${number.slice(7)}`
            default:
                return number.replace(/(\d{3})(?=\d)/g, "$1 ").trim()
        }
    }

    const getFormattedExample = (countryCode: string): string => {
        switch (countryCode) {
            case "+1":
                return "e.g. (555) 123-4567"
            case "+44":
                return "e.g. 7911 123456"
            case "+48":
                return "e.g. 512 345 678"
            case "+49":
                return "e.g. 170 1234567"
            case "+33":
                return "e.g. 6 12 34 56 78"
            case "+39":
                return "e.g. 345 123 4567"
            case "+34":
                return "e.g. 612 345 678"
            case "+86":
                return "e.g. 139 1234 5678"
            case "+91":
                return "e.g. 98765 43210"
            case "+81":
                return "e.g. 90 1234 5678"
            case "+7":
                return "e.g. 9XX XXX XXXX"
            case "+55":
                return "e.g. 11 98765 4321"
            case "+61":
                return "e.g. 4XX XXX XXX"
            case "+52":
                return "e.g. 55 XXXX XXXX"
            case "+31":
                return "e.g. 6 XXXX XXXX"
            default:
                return "Enter phone number"
        }
    }

    const filteredCountries = allCountries.filter(
        (country) => country.name.toLowerCase().includes(searchQuery.toLowerCase()) || country.code.includes(searchQuery),
    )

    // Find the country by code and name to handle shared country codes like +1
    const getSelectedCountry = () => {
        // For +1 country code, we need to check the selected country name
        if (phoneInput.countryCode === "+1") {
            // Find the specific country with code +1 and matching name
            const specificCountry = allCountries.find((c) => c.code === "+1" && c.name === selectedCountryName)

            // If found, return it
            if (specificCountry) return specificCountry

            // If not found (first load), default to United States
            return (
                allCountries.find((c) => c.code === "+1" && c.name === "United States") ||
                allCountries.find((c) => c.code === "+1") ||
                allCountries[0]
            )
        }

        // For other country codes, just find by code
        return allCountries.find((c) => c.code === phoneInput.countryCode) || allCountries[0]
    }

    const selectedCountry = getSelectedCountry()

    return (
        <div className={`space-y-2 ${className}`}>
            {label && (
                <Label htmlFor={id} className={required ? "after:content-['*'] after:ml-0.5 after:text-red-500" : ""}>
                    {label}
                </Label>
            )}

            <div className="flex items-stretch">
                <div className="relative">
                    <Button
                        type="button"
                        variant="outline"
                        className="flex h-10 items-center gap-1 rounded-r-none border-r-0 pr-1"
                        onClick={() => {
                            setIsCountrySelectOpen(!isCountrySelectOpen)
                            setSearchQuery("")
                        }}
                        disabled={disabled}
                    >
                        <span className="text-lg">{selectedCountry.flag}</span>
                        <span>{selectedCountry.code}</span>
                        <ChevronDown className="h-4 w-4" />
                    </Button>

                    {isCountrySelectOpen && (
                        <div className="absolute z-10 mt-1 w-64 max-h-60 overflow-auto rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                            <div className="p-2 sticky top-0 bg-white">
                                <Input
                                    placeholder="Search country..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="h-8"
                                />
                            </div>
                            <div className="p-1">
                                {filteredCountries.length === 0 ? (
                                    <div className="px-2 py-1.5 text-sm text-gray-500">No countries found</div>
                                ) : (
                                    filteredCountries.map((country) => (
                                        <div
                                            key={`${country.code}-${country.name}`}
                                            className={`flex items-center gap-2 rounded px-2 py-1.5 cursor-pointer hover:bg-gray-100 ${
                                                phoneInput.countryCode === country.code && selectedCountryName === country.name
                                                    ? "bg-gray-100"
                                                    : ""
                                            }`}
                                            onClick={() => handleCountrySelect(country)}
                                        >
                                            <span className="text-lg">{country.flag}</span>
                                            <span className="flex-1">{country.name}</span>
                                            <span className="text-gray-500">{country.code}</span>
                                            {phoneInput.countryCode === country.code && selectedCountryName === country.name && (
                                                <Check className="h-4 w-4 text-primary" />
                                            )}
                                        </div>
                                    ))
                                )}
                            </div>
                        </div>
                    )}
                </div>

                <div className="relative flex-1">
                    <Input
                        id={id}
                        className={`pl-3 rounded-l-none ${inputClassName}`}
                        placeholder={getFormattedExample(phoneInput.countryCode)}
                        value={formatPhoneNumber(phoneInput.nationalNumber, phoneInput.countryCode)}
                        onChange={handlePhoneNumberChange}
                        onFocus={() => setIsCountrySelectOpen(false)}
                        disabled={disabled}
                    />

                    {phoneInput.nationalNumber && (
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                            {phoneInput.isValid ? (
                                <Check className="h-4 w-4 text-green-500" />
                            ) : (
                                <X className="h-4 w-4 text-red-500" />
                            )}
                        </div>
                    )}
                </div>
            </div>

            {showHelper && (
                <p className="text-xs text-gray-500">
                    {errorMessage && !phoneInput.isValid && phoneInput.nationalNumber
                        ? errorMessage
                        : phoneInput.isValid
                            ? "Valid phone number"
                            : phoneInput.nationalNumber
                                ? "Please enter a valid phone number"
                                : getFormattedExample(phoneInput.countryCode)}
                </p>
            )}
        </div>
    )
}

