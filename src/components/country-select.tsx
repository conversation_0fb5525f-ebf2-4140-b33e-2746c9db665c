"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {countries} from "@/utils/data";


interface CountrySelectorProps {
    value: string
    onChange: (value: string) => void
    onBlur?: () => void
    error?: string
    touched?: boolean
    disabled?: boolean
    required?: boolean
    placeholder?: string
}

export function CountrySelector({
                                    value,
                                    onChange,
                                    onBlur,
                                    error,
                                    touched = false,
                                    disabled = false,
                                    required = false,
                                    placeholder = "Select a country",
                                }: CountrySelectorProps) {
    const [open, setOpen] = React.useState(false)

    // Find the country object that matches the current value
    const selectedCountry = React.useMemo(() => {
        return countries.find((country) => country.isoNumeric === value || country.name === value)
    }, [value])

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className={cn(
                        "w-full justify-between",
                        !value && "text-muted-foreground",
                        touched && error ? "border-red-500" : "",
                        value && !error ? "border-green-500" : "",
                        disabled ? "opacity-50 cursor-not-allowed" : "",
                    )}
                    onClick={() => !disabled && setOpen(!open)}
                    disabled={disabled}
                    onBlur={() => {
                        if (onBlur) onBlur()
                    }}
                    type="button" // Important to prevent form submission
                >
                    {selectedCountry ? selectedCountry.name : placeholder}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
                <Command>
                    <CommandInput placeholder="Search country..." />
                    <CommandList>
                        <CommandEmpty>No country found.</CommandEmpty>
                        <CommandGroup className="max-h-[300px] overflow-y-auto">
                            {countries.map((country) => (
                                <CommandItem
                                    key={country.name}
                                    value={country.name}
                                    onSelect={() => {
                                        onChange(country.name)
                                        setOpen(false)
                                    }}
                                >
                                    <Check
                                        className={cn(
                                            "mr-2 h-4 w-4",
                                            selectedCountry?.name === country.name ? "opacity-100" : "opacity-0",
                                        )}
                                    />
                                    {country.name}
                                </CommandItem>
                            ))}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    )
}



// Helper function to get country label from value
export function getCountryLabel(value: string): string {
    const country = countries.find((country) => country.iso2 === value ||country.isoNumeric === value || country.name === value)
    return country ? country.name : value
}

// Helper function to get country value from label
export function getCountryValue(label: string): string {
    const country = countries.find((country) => country.name === label)
    return country ? country.name : ""
}
