"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Database, Search, RefreshCw, Calendar, Filter, Eye, EyeOff, ChevronLeft, ChevronRight } from "lucide-react"
import type { PocztaPostRecord } from "@/lib/mongodb"

const formatColumnHeader = (header: string): string => {
  // For the specific Poczta Post headers, keep them as-is but make them more readable
  if (header.startsWith("Poczta_Post")) {
    return header.replace("Poczta_Post", "Poczta Post")
  }

  return header
    .replace(/_/g, " ")
    .replace(/([A-Z])/g, " $1")
    .replace(/\b\w/g, (l) => l.toUpperCase())
    .trim()
}

const formatCurrency = (value: any): string => {
  if (value === null || value === undefined || value === "") return "—"
  const numValue = Number.parseFloat(value.toString().replace(/[^\d.-]/g, ""))
  if (isNaN(numValue)) return value.toString()
  return new Intl.NumberFormat("en-EU", {
    style: "currency",
    currency: "EUR",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(numValue)
}

const isCurrencyField = (header: string): boolean => {
  // Check for Poczta Post price columns
  return (
    header.includes("Poczta_Post") &&
    (header.includes("50g") || header.includes("100g") || header.includes("350g") || header.includes("500g"))
  )
}

interface DatabaseViewerProps {
  isOpen: boolean
  onClose: () => void
}

export function DatabaseViewer({ isOpen, onClose }: DatabaseViewerProps) {
  const [records, setRecords] = useState<PocztaPostRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [fileNames, setFileNames] = useState<string[]>([])
  const [selectedFileName, setSelectedFileName] = useState<string>("all")
  const [displayColumns, setDisplayColumns] = useState<string[]>([])
  const [visibleColumns, setVisibleColumns] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [searchField, setSearchField] = useState<string>("all")
  const [searchResults, setSearchResults] = useState<PocztaPostRecord[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [pageSize, setPageSize] = useState(25)

  const fetchFileNames = async () => {
    try {
      const response = await fetch("/api/get-records?getFileNames=true")
      const data = await response.json()
      if (data.success) {
        setFileNames(data.fileNames)
      }
    } catch (error) {
      console.error("Error fetching file names:", error)
    }
  }

  const fetchRecords = async (page = 1, fileName?: string, search?: string, searchField?: string) => {
    setLoading(true)
    try {
      const skip = (page - 1) * pageSize
      const params = new URLSearchParams({
        limit: pageSize.toString(),
        skip: skip.toString(),
      })

      if (fileName && fileName !== "all") {
        params.append("fileName", fileName)
      }

      if (search && search.trim()) {
        params.append("search", search.trim())
        if (searchField && searchField !== "all") {
          params.append("searchField", searchField)
        }
      }

      const response = await fetch(`/api/get-records?${params}`)
      const data = await response.json()

      if (data.success) {
        setRecords(data.records)
        setTotalPages(data.totalPages)
        setTotalCount(data.totalCount)
        setCurrentPage(page)

        // Extract column names from the first record (excluding metadata fields)
        if (data.records.length > 0) {
          const firstRecord = data.records[0]
          const dataColumns = Object.keys(firstRecord).filter((key) => !key.startsWith("_") && key !== "_id")
          const metadataColumns = Object.keys(firstRecord).filter(
            (key) => key.startsWith("_") && key !== "_id" && key !== "id",
          )

          setDisplayColumns([...dataColumns, ...metadataColumns])
          // Show all data columns by default for the specific headers
          setVisibleColumns(dataColumns)
        }
      }
    } catch (error) {
      console.error("Error fetching records:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (isOpen) {
      fetchFileNames()
      fetchRecords()
    }
  }, [isOpen, pageSize])

  const handleSearch = () => {
    setCurrentPage(1) // Reset to first page when searching
    fetchRecords(1, selectedFileName === "all" ? undefined : selectedFileName, searchTerm, searchField)
  }

  const handleClearSearch = () => {
    setSearchTerm("")
    setSearchField("all")
    setCurrentPage(1)
    fetchRecords(1, selectedFileName === "all" ? undefined : selectedFileName)
  }

  const handlePageChange = (page: number) => {
    fetchRecords(
      page,
      selectedFileName === "all" ? undefined : selectedFileName,
      searchTerm || undefined,
      searchField !== "all" ? searchField : undefined,
    )
  }

  const toggleColumnVisibility = (column: string) => {
    setVisibleColumns((prev) => (prev.includes(column) ? prev.filter((col) => col !== column) : [...prev, column]))
  }

  const formatValue = (value: any) => {
    if (value === null || value === undefined) return "—"
    if (typeof value === "object") return JSON.stringify(value)
    if (typeof value === "string" && value.length > 100) {
      return value.substring(0, 97) + "..."
    }
    return value.toString()
  }

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const isMetadataColumn = (column: string) => column.startsWith("_")

  if (!isOpen) return null

  return (
    <Card className="w-full   h-[80vh] flex flex-col">
      <div className="flex flex-1 min-h-0">
        {/* Sidebar - Filters and Column Controls */}
        <div className="w-80 border-r bg-muted/20 flex flex-col flex-shrink-0">
          <div className="p-4 space-y-4 flex-shrink-0">
            {/* Search Section */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Search Delivery Methods</Label>

              {/* Search Field Selector */}
              <Select value={searchField} onValueChange={setSearchField}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Search in..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Fields</SelectItem>
                  {displayColumns
                    .filter((col) => !isMetadataColumn(col))
                    .map((column) => (
                      <SelectItem key={column} value={column}>
                        {formatColumnHeader(column)}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>

              {/* Search Input */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder={
                    searchField === "all"
                      ? "Search all delivery method fields..."
                      : `Search delivery methods in ${formatColumnHeader(searchField)}...`
                  }
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleSearch()
                    }
                  }}
                  className="pl-10"
                />
              </div>

              {/* Search Actions */}
              <div className="flex gap-2">
                <Button onClick={handleSearch} disabled={loading} size="sm" className="flex-1">
                  <Search className="w-4 h-4 mr-2" />
                  Search
                </Button>
                {searchTerm && (
                  <Button onClick={handleClearSearch} disabled={loading} size="sm" variant="outline">
                    Clear
                  </Button>
                )}
              </div>

              {/* Search Results Info */}
              {searchTerm && (
                <div className="text-xs text-muted-foreground bg-blue-50 p-2 rounded border">
                  <div className="flex items-center gap-1">
                    <Search className="w-3 h-3" />
                    <span>
                      Searching for "{searchTerm}"{searchField !== "all" && ` in ${formatColumnHeader(searchField)}`}
                    </span>
                  </div>
                  <div className="mt-1">Found {totalCount.toLocaleString()} delivery methods</div>
                </div>
              )}
            </div>

            <Separator />

            {/* File Filter */}
            <div>
              <Label className="text-sm font-medium">Filter by Upload File</Label>
              <Select value={selectedFileName} onValueChange={setSelectedFileName}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select file..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Files ({totalCount})</SelectItem>
                  {fileNames.map((fileName) => (
                    <SelectItem key={fileName} value={fileName}>
                      {fileName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Records per page */}
            <div>
              <Label className="text-sm font-medium">Records per page</Label>
              <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10 records</SelectItem>
                  <SelectItem value="25">25 records</SelectItem>
                  <SelectItem value="50">50 records</SelectItem>
                  <SelectItem value="100">100 records</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Apply Filters Button */}
            <Button onClick={handleSearch} disabled={loading} size="sm" className="w-full">
              <Filter className="w-4 h-4 mr-2" />
              Apply Filters
            </Button>

            <Button
              onClick={() => fetchRecords(currentPage)}
              disabled={loading}
              size="sm"
              variant="outline"
              className="w-full"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`} />
              Refresh
            </Button>
          </div>

          <Separator />

          {/* Column Visibility Controls */}
          <div className="flex-1 p-4 min-h-0">
            <Label className="text-sm font-medium mb-3 block">Visible Columns ({visibleColumns.length})</Label>
            <div className="h-full overflow-y-auto">
              <div className="space-y-2">
                {displayColumns.map((column) => (
                  <div
                    key={column}
                    className={`flex items-center justify-between p-2 rounded-lg border cursor-pointer transition-colors ${
                      visibleColumns.includes(column)
                        ? "bg-primary/10 border-primary/20"
                        : "bg-background hover:bg-muted/50"
                    }`}
                    onClick={() => toggleColumnVisibility(column)}
                  >
                    <div className="flex items-center gap-2 min-w-0 flex-1">
                      {visibleColumns.includes(column) ? (
                        <Eye className="w-4 h-4 text-primary flex-shrink-0" />
                      ) : (
                        <EyeOff className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                      )}
                      <span className="text-sm truncate">{formatColumnHeader(column)}</span>
                      {isMetadataColumn(column) && (
                        <Badge variant="secondary" className="text-xs flex-shrink-0">
                          Meta
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* Table Header with Stats */}
          <div className="p-4 border-b bg-muted/10 flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="text-sm">
                  <span className="font-medium">Delivery Methods:</span> {records.length} methods
                </div>
                <div className="text-sm">
                  <span className="font-medium">Columns:</span> {visibleColumns.length} visible
                </div>
                {searchTerm && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                    <Search className="w-3 h-3 mr-1" />"{searchTerm}"
                  </Badge>
                )}
                {selectedFileName !== "all" && (
                  <Badge variant="outline">
                    <Filter className="w-3 h-3 mr-1" />
                    {selectedFileName}
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1 || loading}
                  variant="outline"
                  size="sm"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <span className="text-sm px-2">
                  {currentPage} / {totalPages}
                </span>
                <Button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages || loading}
                  variant="outline"
                  size="sm"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Data Table with Horizontal Scroll */}
          <div className="flex-1 min-h-0 overflow-hidden">
            <div className="h-full w-full overflow-auto border rounded-lg">
              <div style={{ minWidth: `${Math.max(800, visibleColumns.length * 200)}px` }}>
                <Table>
                  <TableHeader className="sticky top-0 bg-background z-10 border-b">
                    <TableRow>
                      <TableHead className="w-16 text-center sticky left-0 bg-background border-r z-20 shadow-sm">
                        #
                      </TableHead>
                      {visibleColumns.map((column, index) => (
                        <TableHead
                          key={column}
                          className={`min-w-[200px] px-4 whitespace-nowrap ${
                            isMetadataColumn(column) ? "bg-muted/50" : ""
                          }`}
                          style={{ width: "200px" }}
                        >
                          <div className="flex items-center gap-2">
                            <span className="font-medium truncate" title={formatColumnHeader(column)}>
                              {formatColumnHeader(column)}
                            </span>
                            {isMetadataColumn(column) && (
                              <Badge variant="secondary" className="text-xs flex-shrink-0">
                                Meta
                              </Badge>
                            )}
                          </div>
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={visibleColumns.length + 1} className="text-center py-8">
                          <div className="flex items-center justify-center gap-2">
                            <RefreshCw className="w-4 h-4 animate-spin" />
                            Loading delivery methods...
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : records.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={visibleColumns.length + 1} className="text-center py-8">
                          <div className="text-muted-foreground">
                            <Database className="w-8 h-8 mx-auto mb-2 opacity-50" />
                            No delivery methods found
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      records.map((record, index) => (
                        <TableRow key={record._id} className="hover:bg-muted/50">
                          <TableCell className="text-center text-xs text-muted-foreground font-mono sticky left-0 bg-background border-r z-10 shadow-sm">
                            {(currentPage - 1) * pageSize + index + 1}
                          </TableCell>
                          {visibleColumns.map((column) => (
                            <TableCell
                              key={column}
                              className={`min-w-[200px] px-4 whitespace-nowrap ${
                                isMetadataColumn(column) ? "bg-muted/20" : ""
                              }`}
                              style={{ width: "200px" }}
                            >
                              {column.includes("Date") || column.includes("At") ? (
                                <div className="flex items-center gap-1 text-xs">
                                  <Calendar className="w-3 h-3 text-muted-foreground flex-shrink-0" />
                                  <span className="truncate" title={formatDate(record[column])}>
                                    {formatDate(record[column])}
                                  </span>
                                </div>
                              ) : isCurrencyField(column) ? (
                                <div
                                  className="text-sm truncate cursor-help font-mono"
                                  title={record[column]?.toString()}
                                  style={{ maxWidth: "180px" }}
                                >
                                  {formatCurrency(record[column])}
                                </div>
                              ) : (
                                <div
                                  className="text-sm truncate cursor-help"
                                  title={record[column]?.toString()}
                                  style={{ maxWidth: "180px" }}
                                >
                                  {formatValue(record[column])}
                                </div>
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>

            {/* Horizontal Scroll Indicator */}
            {visibleColumns.length > 4 && (
              <div className="flex justify-center py-2 text-xs text-muted-foreground bg-muted/20">
                <span>← Scroll horizontally to view all {visibleColumns.length} columns →</span>
              </div>
            )}
          </div>

          {/* Footer with Pagination and Stats */}
          <div className="p-4 border-t bg-muted/10 flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, totalCount)} of{" "}
                {totalCount.toLocaleString()} delivery methods
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1 || loading}
                  variant="outline"
                  size="sm"
                >
                  First
                </Button>
                <Button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1 || loading}
                  variant="outline"
                  size="sm"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <div className="flex items-center gap-1">
                  <Input
                    type="number"
                    min={1}
                    max={totalPages}
                    value={currentPage}
                    onChange={(e) => {
                      const page = Number(e.target.value)
                      if (page >= 1 && page <= totalPages) {
                        handlePageChange(page)
                      }
                    }}
                    className="w-16 h-8 text-center text-sm"
                  />
                  <span className="text-sm text-muted-foreground">of {totalPages}</span>
                </div>
                <Button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages || loading}
                  variant="outline"
                  size="sm"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
                <Button
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages || loading}
                  variant="outline"
                  size="sm"
                >
                  Last
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}
