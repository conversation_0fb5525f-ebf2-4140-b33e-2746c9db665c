//@ts-nocheck

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { FaFileCsv, FaFilePdf } from "react-icons/fa6";


type DataExporterProps<T> = {
    data: T[];
    filename?: string;
    title?: string;
    companyName?: string;
    logoUrl?: string;
    buttonVariant?: "default" | "primary" | "minimal";
    buttonSize?: "small" | "medium" | "large";
};

const DataExporter = <T extends Record<string, any>>({
                                                         data,
                                                         filename = "data",
                                                         title = "Data Report",
                                                         companyName = "Ryvyl EU.",
                                                         logoUrl,
                                                         buttonVariant = "default",
                                                         buttonSize = "medium",
                                                     }: DataExporterProps<T>) => {
    const downloadCSV = () => {
        if (data.length === 0) {
            alert("No data to download");
            return;
        }

        const formatHeader = (header: string) => {
            return header
                .replace(/[^a-zA-Z0-9 ]/g, " ") // Replace special characters with space
                .replace(/_/g, " ") // Replace underscores with space
                .replace(/\s+/g, " ") // Remove extra spaces
                .trim()
                .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize each word
        };

        const headers = Object.keys(data[0]).map(formatHeader).join(",") + "\n";
        const csvContent =
            headers +
            data
                .map((row) => Object.values(row).map((value) => `"${value}"`).join(","))
                .join("\n");

        const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        const timestamp = new Date().toISOString().replace(/[-T:.Z]/g, "");
        link.setAttribute("href", url);
        link.setAttribute("download", `${filename}_csv_report_${timestamp}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const downloadPDF = () => {
        if (data.length === 0) {
            alert("No data to download");
            return;
        }

        // Create PDF document with slightly larger margins for better aesthetics
        const doc = new jsPDF({
            orientation: "portrait",
            unit: "mm",
            format: "a4",
        });

        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        const margin = 15;
        const timestamp = new Date().toISOString().replace(/[-T:.Z]/g, "");
        const currentDate = new Date().toLocaleDateString(undefined, {
            year: "numeric",
            month: "long",
            day: "numeric",
        });
        const currentTime = new Date().toLocaleTimeString(undefined, {
            hour: "2-digit",
            minute: "2-digit",
        });

        // Add gradient header
        doc.setFillColor(22, 160, 133);
        doc.rect(0, 0, pageWidth, 40, "F");
        doc.setFillColor(41, 128, 185);
        doc.rect(0, 40, pageWidth, 5, "F");

        // Add title
        doc.setFont("helvetica", "bold");
        doc.setFontSize(24);
        doc.setTextColor(255, 255, 255);
        doc.text(title, pageWidth / 2, 25, { align: "center" });

        // Add company name and date
        doc.setFontSize(12);
        doc.text(companyName, pageWidth / 2, 35, { align: "center" });

        // Add metadata section
        doc.setFillColor(245, 245, 245);
        doc.rect(margin, 50, pageWidth - (margin * 2), 20, "F");
        doc.setDrawColor(200, 200, 200);
        doc.rect(margin, 50, pageWidth - (margin * 2), 20, "S");

        doc.setFont("helvetica", "normal");
        doc.setTextColor(80, 80, 80);
        doc.setFontSize(11);
        doc.text(`Generated on: ${currentDate} at ${currentTime}`, margin + 5, 60);
        doc.text(`Total records: ${data.length}`, margin + 5, 65);

        // Format table headers
        const formatHeader = (header: string) => {
            return header
                .replace(/[^a-zA-Z0-9 ]/g, " ")
                .replace(/_/g, " ")
                .trim()
                .replace(/\b\w/g, (char) => char.toUpperCase());
        };

        const headers = Object.keys(data[0]).map(formatHeader);
        const rows = data.map(row => Object.values(row));

        // Create table
        autoTable(doc, {
            startY: 75,
            head: [headers],
            body: rows,
            theme: 'grid',
            headStyles: {
                fillColor: [41, 128, 185],
                textColor: 255,
                fontStyle: 'bold',
                lineWidth: 0.1,
                lineColor: [220, 220, 220]
            },
            bodyStyles: {
                lineWidth: 0.1,
                lineColor: [220, 220, 220]
            },
            alternateRowStyles: {
                fillColor: [245, 245, 245]
            },
            margin: { top: margin, right: margin, bottom: margin + 15, left: margin },
            didDrawPage: (data) => {
                // Add page number at the bottom
                const pageNumber = doc.internal.getNumberOfPages();
                doc.setFontSize(10);
                doc.setTextColor(150, 150, 150);
                doc.text(
                    `Page ${pageNumber}`,
                    pageWidth / 2,
                    pageHeight - 10,
                    { align: "center" }
                );

                // Add footer on each page
                doc.setDrawColor(41, 128, 185);
                doc.setLineWidth(0.5);
                doc.line(margin, pageHeight - 20, pageWidth - margin, pageHeight - 20);

                doc.setFontSize(9);
                doc.setTextColor(100, 100, 100);
                doc.text(
                    `${title} - ${companyName}`,
                    margin,
                    pageHeight - 15
                );
                doc.text(
                    `Report ID: ${timestamp}`,
                    pageWidth - margin,
                    pageHeight - 15,
                    { align: "right" }
                );

                // Add header on each page (except the first)
                if (pageNumber > 1) {
                    doc.setFillColor(41, 128, 185);
                    doc.rect(0, 0, pageWidth, 15, "F");

                    doc.setFontSize(10);
                    doc.setTextColor(255, 255, 255);
                    doc.text(title, margin, 10);
                }
            }
        });

        doc.save(`${filename}_pdf_report_${timestamp}.pdf`);
    };

    // Button style configurations
    const getButtonClasses = () => {
        let baseClasses = "flex items-center gap-2 transition-all duration-300 ";

        // Button size
        switch (buttonSize) {
            case "small":
                baseClasses += "text-xs px-2 py-1 ";
                break;
            case "large":
                baseClasses += "text-lg px-6 py-3 ";
                break;
            default: // medium
                baseClasses += "text-sm px-4 py-2 ";
        }

        // Button variant
        switch (buttonVariant) {
            case "primary":
                return baseClasses + "bg-gradient-to-r from-teal-500 to-blue-500 hover:from-teal-600 hover:to-blue-600 text-white shadow-md hover:shadow-lg";
            case "minimal":
                return baseClasses + "bg-transparent hover:bg-gray-100 text-gray-700 border border-gray-300 hover:border-gray-400";
            default: // default
                return baseClasses + "bg-white hover:bg-gray-50 text-gray-800 border border-gray-200 shadow-sm hover:shadow";
        }
    };

    const getIconSize = () => {
        switch (buttonSize) {
            case "small": return "h-3 w-3";
            case "large": return "h-5 w-5";
            default: return "h-4 w-4";
        }
    };

    return (
        <div className="flex flex-wrap gap-3">
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <Button
                            onClick={downloadCSV}
                            variant="outline"
                            className="border-emerald-500 text-emerald-600 hover:bg-emerald-50 hover:text-emerald-700 flex items-center gap-2 px-4 py-2"
                        >
                            <FaFileCsv className="h-5 w-5 stroke-[1.5px]"/>

                        </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>Export as Excel</p>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <Button
                            onClick={downloadPDF}
                            variant="outline"
                            className="border-red-500 text-red-600 hover:bg-red-50 hover:text-red-700 flex items-center gap-2 px-4 py-2"
                        >
                            <FaFilePdf className="h-8 w-8 stroke-[4px]"/>

                        </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>Export as PDF document</p>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>
        </div>
    );
};

export default DataExporter;