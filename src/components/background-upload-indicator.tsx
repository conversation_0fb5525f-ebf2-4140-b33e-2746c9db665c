"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Database, Loader2, CheckCircle, XCircle, Eye } from "lucide-react"
import { backgroundUploadService, type UploadProgress } from "@/lib/background-upload"

interface BackgroundUploadIndicatorProps {
  onOpenManager: () => void
}

export function BackgroundUploadIndicator({ onOpenManager }: BackgroundUploadIndicatorProps) {
  const [activeProgress, setActiveProgress] = useState<UploadProgress | null>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Subscribe to progress updates
    const unsubscribe = backgroundUploadService.onProgress((progress) => {
      setActiveProgress(progress)
      setIsVisible(progress.status === "running" || progress.status === "pending")
    })

    // Check for existing active jobs
    const activeJob = backgroundUploadService.getActiveJob()
    if (activeJob && (activeJob.status === "running" || activeJob.status === "pending")) {
      setIsVisible(true)
    }

    return unsubscribe
  }, [])

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  const getStatusIcon = () => {
    if (!activeProgress) return <Database className="w-4 h-4" />

    switch (activeProgress.status) {
      case "running":
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case "error":
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <Database className="w-4 h-4" />
    }
  }

  if (!isVisible || !activeProgress) return null

  return (
    <Card className="fixed bottom-4 left-4 w-80 shadow-lg border-2 z-40">
      <CardContent className="pt-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getStatusIcon()}
              <span className="font-medium text-sm">Delivery Methods Upload</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">
                {activeProgress.status}
              </Badge>
              <Button onClick={onOpenManager} variant="ghost" size="sm" className="h-6 w-6 p-0">
                <Eye className="w-3 h-3" />
              </Button>
            </div>
          </div>

          {/* Progress */}
          <div className="space-y-2">
            <Progress value={activeProgress.percentage} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>
                {activeProgress.savedRecords.toLocaleString()} / {activeProgress.totalRecords.toLocaleString()}
              </span>
              <span>{activeProgress.percentage.toFixed(1)}%</span>
            </div>
          </div>

          {/* Time Remaining */}
          {activeProgress.estimatedTimeRemaining && activeProgress.status === "running" && (
            <div className="text-xs text-muted-foreground text-center">
              {formatTime(activeProgress.estimatedTimeRemaining)} remaining
            </div>
          )}

          {/* Error Message */}
          {activeProgress.status === "error" && activeProgress.errorMessage && (
            <div className="text-xs text-red-600 bg-red-50 p-2 rounded">{activeProgress.errorMessage}</div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
