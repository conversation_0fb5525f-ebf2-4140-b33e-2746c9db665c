"use client"

import { useState, useEffect } from "react"
import { format, formatDistanceToNow } from "date-fns"
import {
    Activity,
    Clock,
    MapPin,
    ChevronLeft,
    ChevronRight,
    X,
    Smartphone,
    RefreshCw,
    Home,
    Globe,
    Monitor,
    AlertTriangle,
    Database,
    Settings,
    CreditCard,
    User,
    Shield,
} from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { cn } from "@/lib/utils"
import axios from "axios"

interface ActivityData {
    _id: string
    url: string
    pathname: string
    method: string
    timestamp: string
    actionType?: string
    description?: string
    user: {
        userId?: string
        userEmail?: string
        userName?: string
        isAuthenticated: boolean
    }
    geo: {
        country?: string
        region?: string
        city?: string
    }
    device: {
        isMobile: boolean
        isTablet?: boolean
        isDesktop?: boolean
        platform?: string
    }
    context: {
        isPageVisit?: boolean
        isUserAction?: boolean
        isApiRequest?: boolean
        actionCategory?: string
        actionVerb?: string
    }
    createdAt: string
}

interface ActivityResponse {
    success: boolean
    data: ActivityData[]
    pagination: {
        currentPage: number
        totalPages: number
        totalCount: number
        hasNextPage: boolean
    }
    filter: {
        userId: string
        activityType: string
    }
}

// Get appropriate icon for the activity
const getActivityIcon = (activity: ActivityData) => {
    if (activity.context?.isPageVisit) {
        if (activity.pathname === "/" || activity.pathname === "/cardholder") return Home
        if (activity.pathname.includes("/activity")) return Activity
        if (activity.pathname.includes("/admin")) return Monitor
        return Globe
    }

    if (activity.context?.isUserAction && activity.actionType) {
        const category = activity.context.actionCategory || activity.actionType.split("_")[0]
        switch (category) {
            case "card":
                return CreditCard
            case "user":
            case "auth":
                return User
            case "profile":
                return User
            case "settings":
                return Settings
            case "security":
                return Shield
            default:
                return Database
        }
    }

    return Activity
}

// Get activity title
const getActivityTitle = (activity: ActivityData) => {
    if (activity.context?.isPageVisit) {
        if (activity.pathname === "/" || activity.pathname === "/cardholder") return "Dashboard"
        if (activity.pathname.includes("/activity")) return "Activity Dashboard"
        return activity.pathname.split("/").filter(Boolean).join(" > ") || "Home"
    }

    if (activity.actionType) {
        return activity.actionType.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
    }

    return "API Action"
}

// Get activity description
const getActivityDescription = (activity: ActivityData) => {
    if (activity.description) {
        return activity.description
    }

    if (activity.context?.isPageVisit) {
        return `Visited ${getActivityTitle(activity)}`
    }

    return `Performed ${activity.method} request to ${activity.pathname}`
}

// Get severity color based on activity type
const getSeverityColor = (activity: ActivityData) => {
    if (activity.context?.isPageVisit) {
        return "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
    }

    const verb = activity.context?.actionVerb || activity.actionType?.split("_")[1]
    switch (verb) {
        case "created":
        case "activated":
        case "approved":
            return "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400"
        case "deleted":
        case "blocked":
        case "declined":
            return "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
        case "updated":
        case "changed":
            return "bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400"
        default:
            return "bg-slate-100 text-slate-700 dark:bg-slate-900/30 dark:text-slate-400"
    }
}

interface ActivitySidebarProps {
    className?: string
}

export function ActivitySidebar({ className }: ActivitySidebarProps) {
    const [isCollapsed, setIsCollapsed] = useState(false)
    const [isVisible, setIsVisible] = useState(true)
    const [logs, setLogs] = useState<ActivityData[]>([])
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [activityType, setActivityType] = useState<string>("all")

    const fetchLogs = async () => {
        setLoading(true)
        setError(null)
        try {
            const response = await axios.get<ActivityResponse>("/api/activity", {
                params: {
                    limit: 20,
                    type: activityType,
                },
            })

            if (response.data.success) {
                setLogs(response.data.data)
            } else {
                setError("Failed to fetch activities")
            }
        } catch (error: any) {
            console.error("Failed to fetch activities:", error)
            if (error.response?.status === 401) {
                setError("Please log in to view your activity")
            } else {
                setError("Failed to fetch activities")
            }
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchLogs()
    }, [activityType])

    useEffect(() => {
        // Auto-refresh every 30 seconds
        const interval = setInterval(fetchLogs, 30000)
        return () => clearInterval(interval)
    }, [activityType])

    const recentLogs = logs.slice(0, 15)

    const ActivityItem = ({ activity, compact = false }: { activity: ActivityData; compact?: boolean }) => {
        const ActivityIcon = getActivityIcon(activity)
        const timestamp = new Date(activity.createdAt)
        const title = getActivityTitle(activity)
        const description = getActivityDescription(activity)
        const severityColor = getSeverityColor(activity)

        if (compact) {
            return (
                <div className="flex items-center gap-2 p-2 border-b border-slate-100 dark:border-slate-800 last:border-b-0 hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors">
                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-slate-100 dark:bg-slate-800 flex-shrink-0">
                        <ActivityIcon className="h-3 w-3 text-slate-600 dark:text-slate-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                        <div className="text-xs font-medium text-slate-900 dark:text-slate-100 truncate">{title}</div>
                        <div className="text-xs text-slate-500 dark:text-slate-500">
                            {formatDistanceToNow(timestamp, { addSuffix: true })}
                        </div>
                    </div>
                    <div className="flex items-center gap-1">
                        {activity.device.isMobile && <Smartphone className="h-3 w-3 text-blue-500" />}
                        <div
                            className={`h-2 w-2 rounded-full flex-shrink-0 ${activity.context?.isPageVisit ? "bg-blue-500" : "bg-emerald-500"}`}
                        />
                    </div>
                </div>
            )
        }

        return (
            <div className="flex items-start gap-3 p-3 border-b border-slate-100 dark:border-slate-800 last:border-b-0 hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-slate-100 dark:bg-slate-800 flex-shrink-0">
                    <ActivityIcon className="h-4 w-4 text-slate-600 dark:text-slate-400" />
                </div>

                <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                        <p className="text-sm font-medium text-slate-900 dark:text-slate-100 truncate">{title}</p>
                        <Badge className={`text-xs ${severityColor} flex-shrink-0`}>
                            {activity.context?.isPageVisit ? "Page Visit" : "Action"}
                        </Badge>
                    </div>

                    <p className="text-xs text-slate-600 dark:text-slate-400 mb-2 line-clamp-2">{description}</p>

                    <div className="flex items-center gap-3 text-xs text-slate-500 dark:text-slate-500">
                        <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span title={format(timestamp, "PPpp")}>{formatDistanceToNow(timestamp, { addSuffix: true })}</span>
                        </div>

                        {activity.geo.city && (
                            <div className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                <span className="truncate">{activity.geo.city}</span>
                            </div>
                        )}

                        <div className="flex items-center gap-1">
                            {activity.device.isMobile && <Smartphone className="h-3 w-3 text-blue-500" />}
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    if (!isVisible) {
        return (
            <div className="fixed right-4 top-1/2 -translate-y-1/2 z-50">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsVisible(true)}
                    className="h-12 w-12 rounded-full shadow-lg bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700"
                >
                    <Activity className="h-4 w-4" />
                </Button>
            </div>
        )
    }

    return (
        <div
            className={cn(
                "fixed right-0 top-0 h-full z-40 transition-all duration-300 ease-in-out",
                isCollapsed ? "w-16" : "w-80",
                className,
            )}
        >
            <Card className="h-full rounded-none border-l border-slate-200 dark:border-slate-800 shadow-lg">
                <CardHeader className="pb-3 border-b border-slate-200 dark:border-slate-800">
                    <div className="flex items-center justify-between">
                        {!isCollapsed && <CardTitle className="text-lg font-semibold">Activity</CardTitle>}
                        <div className="flex items-center gap-1">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={fetchLogs}
                                disabled={loading}
                                className="h-8 w-8 p-0"
                                title="Refresh"
                            >
                                <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
                            </Button>
                            <Button variant="ghost" size="sm" onClick={() => setIsCollapsed(!isCollapsed)} className="h-8 w-8 p-0">
                                {isCollapsed ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                            </Button>
                            <Button variant="ghost" size="sm" onClick={() => setIsVisible(false)} className="h-8 w-8 p-0">
                                <X className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                    {!isCollapsed && (
                        <div className="space-y-2">
                            <div className="flex items-center justify-between">
                                <p className="text-xs text-muted-foreground">Recent user activity</p>
                                {logs.length > 0 && (
                                    <Badge variant="secondary" className="text-xs">
                                        {logs.length} activities
                                    </Badge>
                                )}
                            </div>
                            <Select value={activityType} onValueChange={setActivityType}>
                                <SelectTrigger className="h-8 text-xs">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Activity</SelectItem>
                                    <SelectItem value="page_visits">Page Visits</SelectItem>
                                    <SelectItem value="api_actions">API Actions</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    )}
                </CardHeader>

                <CardContent className="p-0">
                    {loading && recentLogs.length === 0 ? (
                        <div className="p-8 text-center">
                            <RefreshCw className="h-12 w-12 text-slate-400 mx-auto mb-4 animate-spin" />
                            {!isCollapsed && <p className="text-slate-500 text-sm">Loading activity...</p>}
                        </div>
                    ) : error ? (
                        <div className="p-8 text-center">
                            <AlertTriangle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                            {!isCollapsed && (
                                <div>
                                    <p className="text-red-500 text-sm mb-2">{error}</p>
                                    <Button variant="outline" size="sm" onClick={fetchLogs}>
                                        Retry
                                    </Button>
                                </div>
                            )}
                        </div>
                    ) : recentLogs.length === 0 ? (
                        <div className="p-8 text-center">
                            <Activity className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                            {!isCollapsed && <p className="text-slate-500 text-sm">No recent activity</p>}
                        </div>
                    ) : (
                        <ScrollArea className="h-[calc(100vh-160px)]">
                            {recentLogs.map((activity) => (
                                <ActivityItem key={activity._id} activity={activity} compact={isCollapsed} />
                            ))}
                        </ScrollArea>
                    )}
                </CardContent>

                {!isCollapsed && (
                    <div className="absolute bottom-0 left-0 right-0 p-3 border-t border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-950">
                        <Button
                            variant="outline"
                            size="sm"
                            className="w-full text-xs"
                            onClick={() => window.open("/activity-dashboard", "_blank")}
                        >
                            View All Activity
                        </Button>
                    </div>
                )}
            </Card>
        </div>
    )
}
