//@ts-nocheck

"use client"


import { useEffect, useMemo, useState, useRef, useCallback } from "react"
import {
    Shield,
    Mail,
    User2,
    Loader2,
    ChevronDown,
    ChevronRight,
    History,
    CheckCircle2,
    Info,
    Clock,
    UserCheck,
    Settings,
    Eye,
    Plus,
    Edit,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import axiosInstance from "@/utils/axiosInstance"
import { alertHelper } from "@/utils/alertHelper"

type Module = {
    name: string
    options: string[]
    url?: string
    isGroup?: boolean
    subModules?: Module[]
}

type Contact = {
    _id: string
    name?: string
    role?: string
    email?: string
    phone?: string
    contactType?: string
}

type PermissionAuditItem = {
    key: string
    enabled: boolean
}

type PermissionLogItem = {
    action: string
    details?: string
    username?: string
    email?: string
    userId?: string
    createdAt: string
    updatedAt: string
}

type ActivityLog = {
    id: string
    timestamp: string
    action: string
    user: string
    details?: string
}

export default function SendCredentialsDialog({
                                                  open,
                                                  onOpenChange,
                                                  companyName,
                                                  companyEmail,
                                                  companyId,
                                                  contacts = [],
                                                  previousPermissions = [],
                                                  previousPermissionAudit = [],
                                                  permissionsLog = [],
                                              }: {
    open: boolean
    onOpenChange: (v: boolean) => void
    companyName?: string
    companyEmail?: string
    companyId?: string
    contacts?: Contact[]
    previousPermissions?: string[]
    previousPermissionAudit?: PermissionAuditItem[]
    permissionsLog?: PermissionLogItem[]
}) {
    const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([])
    const [loadingActivity, setLoadingActivity] = useState(false)
    const [activityError, setActivityError] = useState<string | null>(null)
    const pmModules = useMemo<Module[]>(
        () => [
            {
                name: "Customers",
                isGroup: true,
                options: [],
                subModules: [
                    { name: "Individual", options: ["View"], url: "/manager/customers/individual" },
                    { name: "B2B", options: ["View"], url: "/manager/customers/b2b" },
                ],
            },
            {
                name: "Cards",
                options: ["Create Virtual", "Create Physical","View Details", "View Transactions",
                    "Freeze Card", "Change Nickname","Pin Management",
                    "Set Card Limits","Force Pin Lock","Resign Card","Report Card",
                    "Replace Card","Change 3d Password","Change 3d Phone","View 3DS OTP"],
                url: "/cards",
            },
            {
                name: "Create B2B Client",
                options: ["Create"],
                url: "/manager/b2b",
            },
            {
                name: "Settings",
                options: ["View", "Modify"],
                url: "/manager/settings",
            },
        ],
        [],
    )

    const [saving, setSaving] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [permissions, setPermissions] = useState<Record<string, boolean>>({})
    const [expandedModules, setExpandedModules] = useState<Record<string, boolean>>({})
    const lastHydratedRoleId = useRef<string | null>(null)
    const [prefillMode, setPrefillMode] = useState<"previous" | "manual">("manual")

    const previousEnabledKeys = useMemo(() => {
        // First try the permissions array (enabled permissions only)
        if (previousPermissions && previousPermissions.length > 0) {
            return previousPermissions
        }
        // Fallback to permissionAudit enabled items
        if (previousPermissionAudit && previousPermissionAudit.length > 0) {
            return previousPermissionAudit.filter((p) => p.enabled).map((p) => p.key)
        }
        return []
    }, [previousPermissions, previousPermissionAudit])

    function isSamePermissionsState(a: Record<string, boolean>, b: Record<string, boolean>) {
        const aKeys = Object.keys(a)
        const bKeys = Object.keys(b)
        if (aKeys.length !== bKeys.length) return false
        for (const k of aKeys) {
            if (a[k] !== b[k]) return false
        }
        return true
    }

    const adminContact = useMemo(
        () => contacts.find((c) => c.role && c.role.toLowerCase() === "administrator"),
        [contacts],
    )
    const targetEmail = adminContact?.email || companyEmail || ""
    const targetName = adminContact?.name || companyName || ""

    const groupedPermissions = useMemo(() => {
        const groups: Record<string, string[]> = {}
        pmModules.forEach((m) => {
            if (m.isGroup && m.subModules) {
                m.subModules.forEach((sub) => {
                    groups[sub.name] = [...(groups[sub.name] || []), ...sub.options]
                })
            } else {
                groups[m.name] = [...(groups[m.name] || []), ...m.options]
            }
        })
        Object.keys(groups).forEach((k) => groups[k].sort())
        return groups
    }, [pmModules])

    const validModuleNames = useMemo(() => Object.keys(groupedPermissions), [groupedPermissions])

    const allAvailablePermissionKeys = useMemo(() => {
        const keys: string[] = []
        Object.entries(groupedPermissions).forEach(([moduleName, actions]) => {
            actions.forEach((a) => keys.push(`${moduleName}_${a}`))
        })
        return keys
    }, [groupedPermissions])

    const selectedCount = useMemo(
        () => Object.entries(permissions).filter(([k, v]) => v && k.includes("_")).length,
        [permissions],
    )

    const previousAuditEnabledCount = previousEnabledKeys.length

    function normalizePermissionToLocalKey(
        raw: string,
        moduleNames: string[],
        groups: Record<string, string[]>,
    ): string | null {
        if (!raw || !raw.includes("_")) return null
        const parts = raw.split("_")
        const action = parts.pop() as string
        const modulePart = parts.join("_")
        const lowerModulePart = modulePart.toLowerCase()

        let best: string | null = null
        for (const name of moduleNames) {
            if (lowerModulePart.includes(name.toLowerCase())) {
                if (!best || name.length > best.length) best = name
            }
        }

        if (!best && parts.length) {
            const last = parts[parts.length - 1]
            const direct = moduleNames.find((n) => n.toLowerCase() === last.toLowerCase())
            if (direct) best = direct
        }

        if (best && groups[best]?.includes(action)) {
            return `${best}_${action}`
        }

        const exact = moduleNames.find((n) => n.toLowerCase() === lowerModulePart)
        if (exact && groups[exact]?.includes(action)) {
            return `${exact}_${action}`
        }

        return null
    }

    function hydrateFromPermissionKeys(keys: string[]) {
        const next: Record<string, boolean> = {}

        keys.forEach((key) => {
            // Direct mapping for exact matches
            if (allAvailablePermissionKeys.includes(key)) {
                next[key] = true
            } else {
                // Try normalization for legacy formats
                const normalized = normalizePermissionToLocalKey(key, validModuleNames, groupedPermissions)
                if (normalized) {
                    next[normalized] = true
                }
            }
        })

        // Set module-level switches if any action is on
        Object.entries(groupedPermissions).forEach(([moduleName, actions]) => {
            const any = actions.some((a) => next[`${moduleName}_${a}`])
            if (any) next[moduleName] = true
        })

        setPermissions(next)

        const expand: Record<string, boolean> = {}
        Object.keys(groupedPermissions).forEach((moduleName) => {
            const actions = groupedPermissions[moduleName]
            const any = actions.some((a) => next[`${moduleName}_${a}`])
            if (any) expand[moduleName] = true
        })
        pmModules.forEach((m) => {
            if (m.isGroup && m.subModules) {
                const anySub = m.subModules.some((sub) => expand[sub.name])
                if (anySub) expand[m.name] = true
            }
        })
        setExpandedModules((prev) => ({ ...prev, ...expand }))
    }



    useEffect(() => {
        if (!open) return
        let mounted = true

        const initializeDialog = async () => {
            setError(null)
            try {


                // Handle permissions hydration
                if (previousEnabledKeys.length > 0) {
                    setPrefillMode("previous")
                    hydrateFromPermissionKeys(previousEnabledKeys)
                } else {
                    setPrefillMode("manual")
                }
            } catch (e: any) {
                console.error("Error in dialog initialization:", e?.response?.data || e?.message)
                if (mounted) setError(e?.response?.data?.message || "Failed to initialize dialog")
            }
        }

        initializeDialog()

        return () => {
            mounted = false
        }
    }, [open, companyId])

    // Add a separate useEffect for handling permission changes when previousEnabledKeys changes
    useEffect(() => {
        if (!open) return

        if (previousEnabledKeys.length > 0) {
            console.log("Loading previous permissions:", previousEnabledKeys)
            setPrefillMode("previous")
            hydrateFromPermissionKeys(previousEnabledKeys)
        } else {
            console.log("No previous permissions found, using manual mode")
            setPrefillMode("manual")
        }
    }, [previousEnabledKeys])

    const handleModuleToggle = (moduleName: string) => {
        const moduleOptions = groupedPermissions[moduleName] || []
        const hasAny = moduleOptions.some((opt) => permissions[`${moduleName}_${opt}`])
        const next = { ...permissions }
        if (hasAny) {
            moduleOptions.forEach((opt) => {
                delete next[`${moduleName}_${opt}`]
            })
            delete next[moduleName]
        } else {
            next[moduleName] = true
            moduleOptions.forEach((opt) => {
                next[`${moduleName}_${opt}`] = true
            })
        }
        setPermissions(next)
        setExpandedModules((prev) => ({ ...prev, [moduleName]: !prev[moduleName] }))
    }

    const handleOptionToggle = (moduleName: string, option: string) => {
        setPermissions((prev) => {
            const next = { ...prev, [`${moduleName}_${option}`]: !prev[`${moduleName}_${option}`] }
            const actions = groupedPermissions[moduleName] || []
            const any = actions.some((a) => next[`${moduleName}_${a}`])
            if (any) next[moduleName] = true
            else delete next[moduleName]
            return next
        })
    }

    const applyPrevious = () => {
        hydrateFromPermissionKeys(previousEnabledKeys)
        setPrefillMode("previous")
    }

    const selectAll = () => {
        const next: Record<string, boolean> = {}
        Object.entries(groupedPermissions).forEach(([moduleName, actions]) => {
            next[moduleName] = true
            actions.forEach((a) => (next[`${moduleName}_${a}`] = true))
        })
        setPermissions(next)
    }

    const clearAll = () => setPermissions({})

    const getActionIcon = (action: string) => {
        const actionLower = action.toLowerCase()
        if (actionLower.includes("view")) return <Eye className="h-3 w-3" />
        if (actionLower.includes("create")) return <Plus className="h-3 w-3" />
        if (actionLower.includes("modify") || actionLower.includes("edit")) return <Edit className="h-3 w-3" />
        if (actionLower.includes("permission")) return <Shield className="h-3 w-3" />
        if (actionLower.includes("user")) return <UserCheck className="h-3 w-3" />
        return <Settings className="h-3 w-3" />
    }

    const getActionColor = (action: string) => {
        const actionLower = action.toLowerCase()
        if (actionLower.includes("view")) return "bg-blue-100 text-blue-800 border-blue-200"
        if (actionLower.includes("create")) return "bg-primary-100 text-primary-800 border-primary-200"
        if (actionLower.includes("modify") || actionLower.includes("edit"))
            return "bg-orange-100 text-orange-800 border-orange-200"
        if (actionLower.includes("permission")) return "bg-purple-100 text-purple-800 border-purple-200"
        if (actionLower.includes("user")) return "bg-indigo-100 text-indigo-800 border-indigo-200"
        return "bg-gray-100 text-gray-800 border-gray-200"
    }

    const renderModuleCards = () => {
        return pmModules.map((module) => {
            if (module.isGroup && module.subModules) {
                const groupExpanded = expandedModules[module.name]
                const hasGroupPermissions = module.subModules.some(
                    (sub) => permissions[sub.name] || sub.options.some((opt) => permissions[`${sub.name}_${opt}`]),
                )

                return (
                    <Card key={module.name} className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                        <CardHeader className="p-4 pb-2">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setExpandedModules((prev) => ({ ...prev, [module.name]: !prev[module.name] }))}
                                        className="h-6 w-6 p-0 hover:bg-blue-100"
                                    >
                                        {groupExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                                    </Button>
                                    <div>
                                        <CardTitle className="text-base font-semibold text-blue-800 flex items-center gap-2">
                                            {"📁"} {module.name}
                                        </CardTitle>
                                        <p className="text-xs text-blue-600">{module.subModules.length} pages in this section</p>
                                    </div>
                                </div>
                                <Badge variant={hasGroupPermissions ? "default" : "secondary"} className="text-xs">
                                    {hasGroupPermissions ? "Has Permissions" : "No Permissions"}
                                </Badge>
                            </div>
                        </CardHeader>

                        {groupExpanded && (
                            <CardContent className="p-4 pt-0">
                                <div className="space-y-3 pl-6 border-l-2 border-blue-200">
                                    {module.subModules.map((subModule) => {
                                        const isSubExpanded = expandedModules[subModule.name]
                                        const hasSubModulePermission = permissions[subModule.name]
                                        const selectedSubOptions = subModule.options.filter(
                                            (opt) => permissions[`${subModule.name}_${opt}`],
                                        ).length

                                        return (
                                            <Card
                                                key={subModule.name}
                                                className={`border transition-all ${
                                                    hasSubModulePermission
                                                        ? "border-primary-200 bg-gradient-to-r from-primary-50 to-primary-50 shadow-sm"
                                                        : "border-gray-200 bg-white hover:bg-gray-50"
                                                }`}
                                            >
                                                <CardHeader className="p-3 pb-2">
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-center gap-3">
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() =>
                                                                    setExpandedModules((prev) => ({ ...prev, [subModule.name]: !prev[subModule.name] }))
                                                                }
                                                                className="h-5 w-5 p-0 hover:bg-primary-100"
                                                            >
                                                                {isSubExpanded ? (
                                                                    <ChevronDown className="h-3 w-3" />
                                                                ) : (
                                                                    <ChevronRight className="h-3 w-3" />
                                                                )}
                                                            </Button>
                                                            <div>
                                                                <CardTitle className="text-sm font-medium flex items-center gap-2">
                                                                    {"📄"} {subModule.name}
                                                                </CardTitle>
                                                                {subModule.url && (
                                                                    <p className="text-xs text-muted-foreground font-mono bg-gray-100 px-2 py-1 rounded mt-1">
                                                                        {subModule.url}
                                                                    </p>
                                                                )}
                                                                {hasSubModulePermission && (
                                                                    <p className="text-xs text-primary-700 font-medium">
                                                                        {selectedSubOptions} of {subModule.options.length} permissions selected
                                                                    </p>
                                                                )}
                                                            </div>
                                                        </div>
                                                        <Switch
                                                            checked={hasSubModulePermission || false}
                                                            onCheckedChange={() => handleModuleToggle(subModule.name)}
                                                        />
                                                    </div>
                                                </CardHeader>

                                                {isSubExpanded && (
                                                    <CardContent className="p-3 pt-0">
                                                        <div className="grid gap-2 pl-6">
                                                            {subModule.options.map((opt) => (
                                                                <div
                                                                    key={opt}
                                                                    className="flex items-center justify-between py-2 px-3 rounded-lg bg-white border border-gray-100 hover:border-gray-200 transition-colors"
                                                                >
                                                                    <div className="flex items-center gap-3">
                                                                        <div className={`p-1 rounded-full ${getActionColor(opt)}`}>
                                                                            {getActionIcon(opt)}
                                                                        </div>
                                                                        <Label
                                                                            htmlFor={`${subModule.name}_${opt}`}
                                                                            className="text-sm font-medium cursor-pointer"
                                                                        >
                                                                            {opt}
                                                                        </Label>
                                                                    </div>
                                                                    <Switch
                                                                        id={`${subModule.name}_${opt}`}
                                                                        checked={permissions[`${subModule.name}_${opt}`] || false}
                                                                        onCheckedChange={() => handleOptionToggle(subModule.name, opt)}
                                                                        size="sm"
                                                                    />
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </CardContent>
                                                )}
                                            </Card>
                                        )
                                    })}
                                </div>
                            </CardContent>
                        )}
                    </Card>
                )
            } else {
                const isExpanded = expandedModules[module.name]
                const hasModulePermission = permissions[module.name]
                const selectedOptions = module.options.filter((opt) => permissions[`${module.name}_${opt}`]).length

                return (
                    <Card
                        key={module.name}
                        className={`border transition-all ${
                            hasModulePermission
                                ? "border-primary-200 bg-gradient-to-r from-primary-50 to-primary-50 shadow-sm"
                                : "border-gray-200 bg-white hover:bg-gray-50"
                        }`}
                    >
                        <CardHeader className="p-4 pb-2">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setExpandedModules((prev) => ({ ...prev, [module.name]: !prev[module.name] }))}
                                        className="h-6 w-6 p-0 hover:bg-primary-100"
                                    >
                                        {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                                    </Button>
                                    <div>
                                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                                            {"📄"} {module.name}
                                        </CardTitle>
                                        {module.url && (
                                            <p className="text-xs text-muted-foreground font-mono bg-gray-100 px-2 py-1 rounded mt-1">
                                                {module.url}
                                            </p>
                                        )}
                                        {hasModulePermission && (
                                            <p className="text-xs text-primary-700 font-medium">
                                                {selectedOptions} of {module.options.length} permissions selected
                                            </p>
                                        )}
                                    </div>
                                </div>
                                <Switch
                                    checked={hasModulePermission || false}
                                    onCheckedChange={() => handleModuleToggle(module.name)}
                                />
                            </div>
                        </CardHeader>

                        {isExpanded && (
                            <CardContent className="p-4 pt-0">
                                <div className="grid gap-3 pl-9">
                                    {module.options.map((opt) => (
                                        <div
                                            key={opt}
                                            className="flex items-center justify-between py-2 px-3 rounded-lg bg-white border border-gray-100 hover:border-gray-200 transition-colors"
                                        >
                                            <div className="flex items-center gap-3">
                                                <div className={`p-1 rounded-full ${getActionColor(opt)}`}>{getActionIcon(opt)}</div>
                                                <Label htmlFor={`${module.name}_${opt}`} className="text-sm font-medium cursor-pointer">
                                                    {opt}
                                                </Label>
                                            </div>
                                            <Switch
                                                id={`${module.name}_${opt}`}
                                                checked={permissions[`${module.name}_${opt}`] || false}
                                                onCheckedChange={() => handleOptionToggle(module.name, opt)}
                                                size="sm"
                                            />
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        )}
                    </Card>
                )
            }
        })
    }

    const handleSend = async () => {
        if (!companyId || !targetEmail || !targetName) {
            alertHelper.showToast("Missing required information to send credentials", "error")
            return
        }

        const permissionAudit = allAvailablePermissionKeys.map((key) => ({
            key,
            enabled: Boolean(permissions[key]),
        }))
        const enabledPermissions = permissionAudit.filter((p) => p.enabled).map((p) => p.key)

        setSaving(true)
        try {
            const payload = {
                name: targetName,
                email: targetEmail,
                status: "Active",
                dashboard: "programmeManager",
                recordId: companyId,
                permissions: enabledPermissions,
                permissionAudit,
            }
            const res = await axiosInstance.post(`/company/${companyId}/createUser`, payload)
            if (res.status === 201 || res.status === 200) {
                alertHelper.showToast(
                    `Programme Manager login details sent to ${adminContact ? "Administrator" : "company"} email address!`,
                    "success",
                )
                onOpenChange(false)
            } else {
                alertHelper.showToast("Failed to send credentials", "error")
            }
        } catch (e: any) {
            console.error(e)
            alertHelper.showToast(e?.response?.data?.message || "Failed to send credentials", "error")
        } finally {
            setSaving(false)
        }
    }

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-6xl">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <Shield className="h-5 w-5" />
                        Assign PM Role & Permissions
                    </DialogTitle>
                    <DialogDescription>Pick a role, review permissions, and send credentials.</DialogDescription>
                </DialogHeader>

                <Card className="border-primary/20 bg-gradient-to-r from-blue-50 to-indigo-50">
                    <CardContent className="px-4 py-3 flex items-center gap-3">
                        <div className="rounded-full bg-white border w-9 h-9 flex items-center justify-center shadow-sm">
                            <User2 className="h-4 w-4 text-primary" />
                        </div>
                        <div className="flex-1">
                            <div className="text-sm">
                                <span className="font-medium">{targetName || "N/A"}</span>
                            </div>
                            <div className="text-xs text-muted-foreground flex items-center gap-1">
                                <Mail className="h-3 w-3" />
                                <span className="font-mono">{targetEmail || "no-email@unknown"}</span>
                            </div>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                            Company ID: {companyId || "N/A"}
                        </Badge>
                    </CardContent>
                </Card>

                {previousEnabledKeys && previousEnabledKeys.length > 0 && (
                    <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-cyan-50">
                        <CardHeader className="py-3">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <History className="h-4 w-4 text-blue-700" />
                                    <CardTitle className="text-sm">Previous permissions detected</CardTitle>
                                </div>
                                <Badge variant="outline" className="text-xs bg-white">
                                    {previousEnabledKeys.length} previously enabled
                                </Badge>
                            </div>
                            <CardDescription className="text-xs">
                                You can apply the previously saved permissions or use the selected role defaults.
                            </CardDescription>
                        </CardHeader>
                        {previousPermissionAudit && previousPermissionAudit.length > 0 && (
                            <CardContent className="pt-0">
                                <div className="text-xs text-blue-900 flex flex-wrap gap-2">
                                    {previousPermissionAudit
                                        .filter((i) => i.enabled)
                                        .slice(0, 12)
                                        .map((i) => (
                                            <span
                                                key={i.key}
                                                className="inline-flex items-center gap-1 rounded-full border border-blue-200 bg-white px-3 py-1 shadow-sm"
                                            >
                        <CheckCircle2 className="h-3 w-3 text-blue-600" />
                        <span className="font-medium">{i.key}</span>
                      </span>
                                        ))}
                                    {previousPermissionAudit.filter((i) => i.enabled).length > 12 && (
                                        <span className="text-blue-700 font-medium">
                      +{previousPermissionAudit.filter((i) => i.enabled).length - 12} more
                    </span>
                                    )}
                                </div>
                            </CardContent>
                        )}
                    </Card>
                )}

                <div className="grid grid-cols-12 gap-6">
                    <Card className="col-span-12 lg:col-span-5">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="flex items-center gap-2">
                                        <History className="h-5 w-5" />
                                        Permissions History
                                    </CardTitle>
                                    <CardDescription>Company permission changes and activity logs</CardDescription>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Badge variant="outline">{permissionsLog.length + activityLogs.length} entries</Badge>
                                     
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <ScrollArea className="h-[520px] pr-4">
                                {loadingActivity ? (
                                    <div className="flex items-center justify-center h-40 text-muted-foreground">
                                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                        Loading activity logs...
                                    </div>
                                ) : (
                                    <div className="space-y-3">
                                        {/* Show permissions log first */}
                                        {permissionsLog.map((log, index) => (
                                            <Card
                                                key={`perm-${index}`}
                                                className="border border-purple-200 bg-gradient-to-r from-purple-50 to-pink-50 hover:shadow-sm transition-shadow"
                                            >
                                                <CardContent className="p-4">
                                                    <div className="space-y-2">
                                                        <div className="flex items-start justify-between">
                                                            <div className="flex-1">
                                                                <div className="flex items-center gap-2 mb-1">
                                                                    <div className="p-1 rounded-full bg-purple-100 text-purple-700">
                                                                        <Shield className="h-3 w-3" />
                                                                    </div>
                                                                    <h3 className="font-medium text-sm text-purple-900">{log.action}</h3>
                                                                </div>
                                                                <p className="text-xs text-purple-700">
                                                                    by {log.username || "Unknown"} ({log.email || "No email"})
                                                                </p>
                                                            </div>
                                                            <Badge variant="outline" className="text-xs bg-white border-purple-200">
                                                                {new Date(log.createdAt).toLocaleDateString()}
                                                            </Badge>
                                                        </div>
                                                        {log.details && (
                                                            <p className="text-xs text-purple-800 bg-purple-100/50 p-2 rounded border border-purple-200">
                                                                {log.details}
                                                            </p>
                                                        )}
                                                        <div className="flex items-center gap-1 text-xs text-purple-600">
                                                            <Clock className="h-3 w-3" />
                                                            <span>{new Date(log.createdAt).toLocaleString()}</span>
                                                        </div>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        ))}

                                        {/* Show activity logs */}
                                        {activityError ? (
                                            <div className="text-center py-8 text-muted-foreground">
                                                <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                                <p className="text-sm text-red-600 mb-2">Failed to load activity logs</p>
                                                <p className="text-xs">{activityError}</p>
                                                <Button variant="outline" size="sm" onClick={fetchActivityLogs} className="mt-3 bg-transparent">
                                                    Retry
                                                </Button>
                                            </div>
                                        ) : activityLogs.length === 0 && permissionsLog.length === 0 ? (
                                            <div className="text-center py-8 text-muted-foreground">
                                                <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                                <p>No activity logs found</p>
                                                <p className="text-sm">Activity will appear here as actions are performed</p>
                                            </div>
                                        ) : (
                                            activityLogs.map((log) => (
                                                <Card
                                                    key={log.id}
                                                    className="border border-gray-200 bg-gradient-to-r from-gray-50 to-slate-50 hover:shadow-sm transition-shadow"
                                                >
                                                    <CardContent className="p-4">
                                                        <div className="space-y-2">
                                                            <div className="flex items-start justify-between">
                                                                <div className="flex-1">
                                                                    <div className="flex items-center gap-2 mb-1">
                                                                        <div className="p-1 rounded-full bg-gray-100 text-gray-700">
                                                                            <Settings className="h-3 w-3" />
                                                                        </div>
                                                                        <h3 className="font-medium text-sm">{log.action}</h3>
                                                                    </div>
                                                                    <p className="text-xs text-muted-foreground">by {log.user}</p>
                                                                </div>
                                                                <Badge variant="outline" className="text-xs">
                                                                    {new Date(log.timestamp).toLocaleDateString()}
                                                                </Badge>
                                                            </div>
                                                            {log.details && (
                                                                <p className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">{log.details}</p>
                                                            )}
                                                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                                                <Clock className="h-3 w-3" />
                                                                <span>{new Date(log.timestamp).toLocaleString()}</span>
                                                            </div>
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                            ))
                                        )}
                                    </div>
                                )}
                            </ScrollArea>
                        </CardContent>
                    </Card>

                    <Card className="col-span-12 lg:col-span-7">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                Permissions
                                <Badge variant="secondary" className="text-xs">
                                    {selectedCount} selected
                                </Badge>
                                {prefillMode === "previous" && (
                                    <Badge variant="default" className="text-xs bg-primary-100 text-primary-800">
                                        Prefilled from previous
                                    </Badge>
                                )}
                            </CardTitle>
                            <CardDescription>Select permissions manually or apply previously saved ones.</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap items-center gap-2 mb-4">
                                <Button
                                    variant={prefillMode === "previous" ? "default" : "outline"}
                                    size="sm"
                                    onClick={applyPrevious}
                                    disabled={previousEnabledKeys.length === 0}
                                    className={prefillMode === "previous" ? "bg-primary-600 hover:bg-primary-700" : ""}
                                >
                                    Use previous
                                </Button>
                                <Button
                                    variant={prefillMode === "manual" ? "default" : "outline"}
                                    size="sm"
                                    onClick={() => {
                                        clearAll()
                                        setPrefillMode("manual")
                                    }}
                                >
                                    Manual selection
                                </Button>
                                <div className="mx-2 h-5 w-px bg-border" />
                                <Button variant="outline" size="sm" onClick={selectAll} className="hover:bg-primary-50 bg-transparent">
                                    Select all
                                </Button>
                                <Button variant="outline" size="sm" onClick={clearAll} className="hover:bg-red-50 bg-transparent">
                                    Clear all
                                </Button>
                            </div>

                            <ScrollArea className="h-[520px] pr-4">
                                <div className="space-y-3">{renderModuleCards()}</div>
                            </ScrollArea>
                        </CardContent>
                    </Card>
                </div>

                <DialogFooter className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Info className="h-3.5 w-3.5" />
                        <span>
              You are currently using:{" "}
                            <span className="font-medium">
                {prefillMode === "previous" ? "Previous permissions" : "Manual selection"}
              </span>
            </span>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" onClick={() => onOpenChange(false)} disabled={saving}>
                            Cancel
                        </Button>
                        <Button
                            onClick={handleSend}
                            disabled={saving || !targetEmail || !companyId}
                            className="bg-primary-600 hover:bg-primary-700"
                        >
                            {saving ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Sending...
                                </>
                            ) : (
                                "Save & Send credentials"
                            )}
                        </Button>
                    </div>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
