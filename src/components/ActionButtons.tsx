//@ts-nocheck

import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { CheckCircleIcon, XCircleIcon } from "@heroicons/react/24/outline";
import { Edit2Icon, TrashIcon, PencilIcon } from 'lucide-react';
import { Button } from "@/components/ui/button";
import axiosInstance from "@/utils/axiosInstance";
import { alertHelper } from "@/utils/alertHelper";
import {useAppSelector} from "@/store/hooks";

export const ActionsButtonGroup = ({
                                       entity,
                                       entityType,
                                       entityName,
                                       userHasApprovePermission,
                                       userHasDeletePermission,
                                       handleDeleteClick,
                                       fetchEntities,
                                       editFields = [], // Add this prop to define which fields can be edited
                                   }) => {
    const [isDialogOpen, setIsDialogOpen] = useState({
        approve: false,
        decline: false,
        modify: false,
        delete: false,
        delete: false,
        edit: false, // Add edit dialog state
    });
    const user = useAppSelector((state) => state.user.user)

    const [declineReason, setDeclineReason] = useState("");
    const [modifyInstructions, setModifyInstructions] = useState("");

    // Add edit form state - more generic approach
    const [editFormData, setEditFormData] = useState({});

    const handleDialogToggle = (type, state) => {
        setIsDialogOpen((prev) => ({ ...prev, [type]: state }));

        // Initialize edit form when opening edit dialog
        if (type === "edit" && state) {
            const initialData = {};
            editFields.forEach(field => {
                initialData[field.key] = entity?.[field.key] || "";
            });
            setEditFormData(initialData);
        }
    };

    const handleApprove = async () => {
        try {
            const response = await axiosInstance.post(`${entityType}/approve`, {
                entityId: entity?._id,
            });
            if (response.status === 200) {
                alertHelper.showToast(`${entityType} approved successfully!`, 'success');
                fetchEntities();
            } else {
                alertHelper.showToast(`Approval failed with status: ${response.status}`, 'error');
            }
        } catch (error) {
            console.error(`Error approving ${entityType}:`, error);
            alertHelper.showToast(`An error occurred while approving the ${entityType}.`, 'error');
        } finally {
            handleDialogToggle("approve", false);
        }
    };

    const handleDecline = async () => {
        try {
            if (!declineReason.trim()) {
                alertHelper.showToast('Please provide a reason for declining.', 'error');
                return;
            }
            const response = await axiosInstance.post(`${entityType}/decline`, {
                entityId: entity?._id,
                reason: declineReason,
            });
            if (response.status === 200) {
                alertHelper.showToast(`${entityType} declined successfully!`, 'success');
                fetchEntities();
            } else {
                alertHelper.showToast(`Decline failed with status: ${response.status}`, 'error');
            }
        } catch (error) {
            console.error(`Error declining ${entityType}:`, error);
            alertHelper.showToast(`An error occurred while declining the ${entityType}.`, 'error');
        } finally {
            setDeclineReason("");
            handleDialogToggle("decline", false);
        }
    };

    const handleModify = async () => {
        try {
            if (!modifyInstructions.trim()) {
                alertHelper.showToast('Please provide modification instructions.', 'error');
                return;
            }
            const response = await axiosInstance.post(`${entityType}/modify`, {
                entityId: entity?._id,
                instructions: modifyInstructions,
            });
            if (response.status === 200) {
                alertHelper.showToast(`${entityType} modification request submitted successfully!`, 'success');
                fetchEntities();
            } else {
                alertHelper.showToast(`Modification failed with status: ${response.status}`, 'error');
            }
        } catch (error) {
            console.error(`Error modifying ${entityType}:`, error);
            alertHelper.showToast(`An error occurred while modifying the ${entityType}.`, 'error');
        } finally {
            setModifyInstructions("");
            handleDialogToggle("modify", false);
        }
    };

    // Add edit handler
    const handleEdit = async () => {
        try {
            // Validate required fields
            const requiredFields = editFields.filter(field => field.required);
            for (const field of requiredFields) {
                if (!editFormData[field.key]?.toString().trim()) {
                    alertHelper.showToast(`Please provide ${field.label.toLowerCase()}.`, 'error');
                    return;
                }
            }

            const response = await axiosInstance.put(`${entityType}/${entity?._id}`, editFormData);

            if (response.status === 200) {
                alertHelper.showToast(`${entityType} updated successfully!`, 'success');
                fetchEntities();
            } else {
                alertHelper.showToast(`Edit failed with status: ${response.status}`, 'error');
            }
        } catch (error) {
            console.error(`Error editing ${entityType}:`, error);
            alertHelper.showToast(`An error occurred while editing the ${entityType}.`, 'error');
        } finally {
            handleDialogToggle("edit", false);
        }
    };

    const handleEditInputChange = (field, value) => {
        setEditFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Check if any required field is empty
    const isEditFormValid = () => {
        const requiredFields = editFields.filter(field => field.required);
        return requiredFields.every(field => editFormData[field.key]?.toString().trim());
    };

    return (
        <div className="flex space-x-4">


            {/* Edit Button - Add this new section */}
            {  editFields.length > 0 && (
                <>
                    <Tooltip>
                        <TooltipTrigger>
                            <Button
                                className="p-2 rounded-md bg-purple-500 text-white hover:bg-purple-600"
                                onClick={() => handleDialogToggle("edit", true)}
                            >
                                <PencilIcon className="h-6 w-6" />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Edit</TooltipContent>
                    </Tooltip>
                    <Dialog
                        open={isDialogOpen.edit}
                        onOpenChange={(state) => handleDialogToggle("edit", state)}
                    >
                        <DialogContent className="max-w-md max-h-[80vh] overflow-y-auto">
                            <DialogHeader>
                                <DialogTitle>Edit {entityType}</DialogTitle>
                                <DialogDescription>
                                    Update the details for "{entityName}".
                                </DialogDescription>
                            </DialogHeader>
                            <div className="py-4 space-y-4">
                                {editFields.map((field) => (
                                    <div key={field.key}>
                                        <Label htmlFor={`edit-${field.key}`}>
                                            {field.label}
                                            {field.required && <span className="text-red-500 ml-1">*</span>}
                                        </Label>
                                        {field.type === 'textarea' ? (
                                            <Textarea
                                                id={`edit-${field.key}`}
                                                value={editFormData[field.key] || ""}
                                                onChange={(e) => handleEditInputChange(field.key, e.target.value)}
                                                placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}...`}
                                                className="mt-2"
                                                rows={field.rows || 3}
                                                required={field.required}
                                            />
                                        ) : (
                                            <Input
                                                id={`edit-${field.key}`}
                                                type={field.type || 'text'}
                                                value={editFormData[field.key] || ""}
                                                onChange={(e) => handleEditInputChange(field.key, e.target.value)}
                                                placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}...`}
                                                className="mt-2"
                                                required={field.required}
                                            />
                                        )}
                                    </div>
                                ))}
                            </div>
                            <DialogFooter>
                                <Button
                                    className="btn btn-outline"
                                    onClick={() => handleDialogToggle("edit", false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    className="btn btn-primary"
                                    onClick={handleEdit}
                                    disabled={!isEditFormValid()}
                                >
                                    Save Changes
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                </>
            )}

            {userHasDeletePermission && (
                <>
                    {/* Delete Button */}
                    <Tooltip>
                        <TooltipTrigger>
                            <Button
                                className="p-2 rounded-md bg-red-500 text-white hover:bg-red-600"
                                onClick={() => handleDialogToggle("delete", true)}
                            >
                                <TrashIcon className="h-6 w-6" />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Delete</TooltipContent>
                    </Tooltip>
                    <Dialog
                        open={isDialogOpen.delete}
                        onOpenChange={(state) => handleDialogToggle("delete", state)}
                    >
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Delete {entityType}</DialogTitle>
                                <DialogDescription>
                                    Deletion of this record will be sent to the administrator for approval. Are you sure
                                    to proceed?
                                </DialogDescription>
                            </DialogHeader>
                            <DialogFooter>
                                <Button
                                    onClick={() => handleDialogToggle("delete", false)}
                                    className="btn btn-outline"
                                >
                                    Cancel
                                </Button>
                                <Button
                                    onClick={() => {
                                        handleDeleteClick(entity?._id);
                                        handleDialogToggle("delete", false);
                                    }}
                                    className="btn btn-danger"
                                >
                                    Confirm Delete
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                </>
            )}
        </div>
    );
};
