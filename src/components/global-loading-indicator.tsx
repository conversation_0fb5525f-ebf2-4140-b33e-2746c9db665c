"use client"

import { usePathname, useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import { cn } from "@/lib/utils"

export function GlobalLoadingIndicator() {
    const pathname = usePathname()
    const searchParams = useSearchParams()
    const [isLoading, setIsLoading] = useState(false)

    // Create a key that changes when the route changes
    const routeKey = pathname + searchParams.toString()

    useEffect(() => {
        // Show loading state
        setIsLoading(true)

        // Hide loading after a short delay
        const timer = setTimeout(() => {
            setIsLoading(false)
        }, 500)

        return () => clearTimeout(timer)
    }, [routeKey])

    return (
        <div
            className={cn(
                "fixed top-0 left-0 right-0 z-50 h-1 bg-transparent overflow-hidden transition-all duration-300",
                isLoading ? "opacity-100" : "opacity-0",
            )}
        >
            <div className="h-full bg-primary animate-progress-bar" />
        </div>
    )
}
