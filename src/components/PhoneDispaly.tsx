//@ts-nocheck
import React from 'react'
import 'react-phone-number-input/style.css'
import PhoneInput from 'react-phone-number-input'
import { cn } from '@/lib/utils'

interface PhoneNumberDisplayProps {
    /**
     * The phone number to display (in E.164 format, e.g. +12025550123)
     */
    phoneNumber: string
    /**
     * Whether to show the country flag
     * @default true
     */
    showFlag?: boolean
    /**
     * Whether to show the international format (with country code)
     * @default true
     */
    international?: boolean
    /**
     * Additional CSS classes to apply to the container
     */
    className?: string
    /**
     * Additional CSS classes to apply to the phone input
     */
    inputClassName?: string
}

/**
 * A reusable component for displaying phone numbers with country flags
 *
 * @example
 * // Basic usage
 * <PhoneNumberDisplay phoneNumber="+12025550123" />
 *
 * @example
 * // Without international format
 * <PhoneNumberDisplay phoneNumber="+12025550123" international={false} />
 *
 * @example
 * // With custom styling
 * <PhoneNumberDisplay
 *   phoneNumber="+12025550123"
 *   className="my-4 p-2 border rounded"
 *   inputClassName="text-blue-500"
 * />
 */
export function PhoneNumberDisplay({
                                       phoneNumber,
                                       showFlag = true,
                                       international = true,
                                       className,
                                       inputClassName
                                   }: PhoneNumberDisplayProps) {
    // Handle empty or invalid phone numbers
    if (!phoneNumber) {
        return <span className={className}>No phone number</span>
    }

    // Ensure phone number is in E.164 format (starts with +)
    const formattedNumber = phoneNumber.startsWith('+')
        ? phoneNumber
        : `+${phoneNumber}`

    return (
        <div className={cn("flex items-center", className)}>
            <PhoneInput
                value={formattedNumber}
                disabled={true}
                international={international}
                countryCallingCodeEditable={false}
                displayInitialValueAsLocalNumber={!international}
                flagComponent={showFlag ? undefined : () => null}
                className={cn("disabled:opacity-100", inputClassName)}
            />
        </div>
    )
}

// Default export for convenience
export default PhoneNumberDisplay