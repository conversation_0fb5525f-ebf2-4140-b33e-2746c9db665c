"use client"

import {usePathname} from 'next/navigation'
import {ChevronRight} from 'lucide-react'
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import React from 'react'; // Import React

export function AutoBreadcrumb() {
    const pathname = usePathname()
    const pathSegments = pathname.split('/').filter(segment => segment !== '')

    return (
        <Breadcrumb>
            <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink href="/lite/admin/dashboard">Ryvyl</BreadcrumbLink>
                </BreadcrumbItem>
                {pathSegments.map((segment, index) => {
                    const href = `/${pathSegments.slice(0, index + 1).join('/')}`
                    const isLast = index === pathSegments.length - 1
                    const label = segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ')

                    return (
                        <React.Fragment key={href}>
                            <BreadcrumbSeparator className="hidden md:block">
                                <ChevronRight className="h-4 w-4"/>
                            </BreadcrumbSeparator>
                            <BreadcrumbItem>
                                {isLast ? (
                                    <BreadcrumbPage>{label}</BreadcrumbPage>
                                ) : (
                                    <BreadcrumbLink href={href}>{label}</BreadcrumbLink>
                                )}
                            </BreadcrumbItem>
                        </React.Fragment>
                    )
                })}
            </BreadcrumbList>
        </Breadcrumb>
    )
}
