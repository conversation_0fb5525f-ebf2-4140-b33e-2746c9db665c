"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Database, Play, Pause, X, CheckCircle, XCircle, Clock, Loader2, Trash2 } from "lucide-react"
import { backgroundUploadService, type UploadJob, type UploadProgress } from "@/lib/background-upload"

interface BackgroundUploadManagerProps {
  isOpen: boolean
  onClose: () => void
}

export function BackgroundUploadManager({ isOpen, onClose }: BackgroundUploadManagerProps) {
  const [jobs, setJobs] = useState<UploadJob[]>([])
  const [activeProgress, setActiveProgress] = useState<UploadProgress | null>(null)

  useEffect(() => {
    if (!isOpen) return

    // Load initial jobs
    setJobs(backgroundUploadService.getAllJobs())

    // Subscribe to progress updates
    const unsubscribe = backgroundUploadService.onProgress((progress) => {
      setActiveProgress(progress)
      // Refresh jobs list
      setJobs(backgroundUploadService.getAllJobs())
    })

    return unsubscribe
  }, [isOpen])

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  const formatDuration = (startTime: number, endTime?: number) => {
    const duration = (endTime || Date.now()) - startTime
    return formatTime(duration)
  }

  const getStatusIcon = (status: UploadJob["status"]) => {
    switch (status) {
      case "running":
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case "error":
        return <XCircle className="w-4 h-4 text-red-500" />
      case "paused":
        return <Pause className="w-4 h-4 text-yellow-500" />
      case "pending":
        return <Clock className="w-4 h-4 text-gray-500" />
      default:
        return <Clock className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: UploadJob["status"]) => {
    switch (status) {
      case "running":
        return "bg-blue-100 text-blue-800"
      case "completed":
        return "bg-green-100 text-green-800"
      case "error":
        return "bg-red-100 text-red-800"
      case "paused":
        return "bg-yellow-100 text-yellow-800"
      case "pending":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const handlePauseResume = (job: UploadJob) => {
    if (job.status === "running") {
      backgroundUploadService.pauseJob(job.id)
    } else if (job.status === "paused") {
      backgroundUploadService.resumeJob(job.id)
    }
  }

  const handleCancel = (job: UploadJob) => {
    backgroundUploadService.cancelJob(job.id)
  }

  const handleClearCompleted = () => {
    backgroundUploadService.clearCompletedJobs()
    setJobs(backgroundUploadService.getAllJobs())
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl h-[80vh] m-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                Delivery Methods Upload Manager
              </CardTitle>
              <CardDescription>Monitor and manage delivery methods upload jobs</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button onClick={handleClearCompleted} variant="outline" size="sm">
                <Trash2 className="w-4 h-4 mr-2" />
                Clear Completed
              </Button>
              <Button onClick={onClose} variant="outline">
                Close
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Active Job Progress */}
          {activeProgress && activeProgress.status === "running" && (
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="pt-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-blue-700">Currently Uploading</span>
                    <Badge className="bg-blue-100 text-blue-800">
                      {activeProgress.savedRecords.toLocaleString()} / {activeProgress.totalRecords.toLocaleString()}
                    </Badge>
                  </div>
                  <Progress value={activeProgress.percentage} className="h-2" />
                  <div className="flex justify-between text-sm text-blue-600">
                    <span>{activeProgress.percentage.toFixed(1)}% complete</span>
                    <span>
                      {activeProgress.estimatedTimeRemaining
                        ? `${formatTime(activeProgress.estimatedTimeRemaining)} remaining`
                        : "Calculating..."}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Jobs List */}
          <ScrollArea className="h-[50vh]">
            <div className="space-y-3">
              {jobs.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Database className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No delivery method uploads found</p>
                </div>
              ) : (
                jobs.map((job) => (
                  <Card key={job.id} className="border">
                    <CardContent className="pt-4">
                      <div className="space-y-3">
                        {/* Job Header */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            {getStatusIcon(job.status)}
                            <div>
                              <div className="font-medium">{job.fileName}</div>
                              <div className="text-sm text-muted-foreground">
                                {job.totalRecords.toLocaleString()} delivery methods
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className={getStatusColor(job.status)}>{job.status}</Badge>
                            {(job.status === "running" || job.status === "paused") && (
                              <Button
                                onClick={() => handlePauseResume(job)}
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0"
                              >
                                {job.status === "running" ? (
                                  <Pause className="w-3 h-3" />
                                ) : (
                                  <Play className="w-3 h-3" />
                                )}
                              </Button>
                            )}
                            {job.status !== "completed" && (
                              <Button
                                onClick={() => handleCancel(job)}
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0"
                              >
                                <X className="w-3 h-3" />
                              </Button>
                            )}
                          </div>
                        </div>

                        {/* Progress Bar */}
                        <div className="space-y-2">
                          <Progress value={(job.savedRecords / job.totalRecords) * 100} className="h-2" />
                          <div className="flex justify-between text-sm text-muted-foreground">
                            <span>
                              {job.savedRecords.toLocaleString()} / {job.totalRecords.toLocaleString()} saved
                            </span>
                            <span>{((job.savedRecords / job.totalRecords) * 100).toFixed(1)}%</span>
                          </div>
                        </div>

                        {/* Job Details */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Started:</span>
                            <div className="text-muted-foreground">{new Date(job.startTime).toLocaleTimeString()}</div>
                          </div>
                          <div>
                            <span className="font-medium">Duration:</span>
                            <div className="text-muted-foreground">{formatDuration(job.startTime, job.endTime)}</div>
                          </div>
                          <div>
                            <span className="font-medium">Batch:</span>
                            <div className="text-muted-foreground">
                              {job.currentBatch} / {job.totalBatches}
                            </div>
                          </div>
                          <div>
                            <span className="font-medium">Status:</span>
                            <div className="text-muted-foreground">
                              {job.status === "error" ? job.errorMessage : job.status}
                            </div>
                          </div>
                        </div>

                        {/* Error Message */}
                        {job.status === "error" && job.errorMessage && (
                          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                            <div className="flex items-center gap-2 text-red-700">
                              <XCircle className="w-4 h-4" />
                              <span className="font-medium">Error</span>
                            </div>
                            <div className="text-sm text-red-600 mt-1">{job.errorMessage}</div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  )
}
