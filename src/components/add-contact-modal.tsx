"use client"

import { useState, useEffect } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { alertHelper } from "@/utils/alertHelper"
import axiosInstance from "@/utils/axiosInstance"
import PhoneInput, { isValidPhoneNumber } from "react-phone-number-input"
import "react-phone-number-input/style.css"

// Define the form schema with validation
const contactFormSchema = z.object({
    name: z.string().min(2, { message: "Name must be at least 2 characters" }),
    role: z.string().min(2, { message: "Role must be at least 2 characters" }),
    email: z.string().email({ message: "Please enter a valid email address" }),
    phone: z
        .string()
        .min(1, { message: "Phone number is required" })
        .refine(
            (phone) => {
                return phone ? isValidPhoneNumber(phone) : false
            },
            { message: "Please enter a valid phone number" },
        ),
    contactType: z.string(),
    notes: z.string().optional(),
    company: z.string(),
})

type ContactFormValues = z.infer<typeof contactFormSchema>

interface ContactFormProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    companyId: string
    existingContact?: any
    onSuccess: (data: any) => void
}

export function ContactForm({ open, onOpenChange, companyId, existingContact, onSuccess }: ContactFormProps) {
    const [isSubmitting, setIsSubmitting] = useState(false)

    // Initialize the form with default values or existing contact data
    const form = useForm<ContactFormValues>({
        resolver: zodResolver(contactFormSchema),
        defaultValues: existingContact
            ? {
                name: existingContact.name,
                role: existingContact.role,
                email: existingContact.email,
                phone: existingContact.phone,
                company: companyId,
                contactType: existingContact.contactType || "primary",
                notes: existingContact.notes || "",
            }
            : {
                name: "",
                role: "",
                email: "",
                phone: "",
                company: companyId,
                contactType: "primary",
                notes: "",
            },
    })

    useEffect(() => {
        if (existingContact) {
            // Reset form with the values from the existingContact
            form.reset({
                name: existingContact.name,
                role: existingContact.role,
                company: companyId,
                email: existingContact.email,
                phone: existingContact.phone,
                contactType: existingContact.contactType || "primary",
                notes: existingContact.notes || "",
            })
        } else {
            // Reset to default empty values when adding a new contact
            form.reset({
                name: "",
                role: "",
                email: "",
                phone: "",
                company: companyId,
                contactType: "primary",
                notes: "",
            })
        }
    }, [existingContact, form, companyId])

    async function onSubmit(data: ContactFormValues) {
        try {
            setIsSubmitting(true)

            const response = existingContact
                ? await axiosInstance.put(`contacts/${existingContact.id}`, data)
                : await axiosInstance.post(`contacts`, data)
            if (response.status === 201 || response.status === 200) {
                alertHelper.showToast("Contact saved successfully!", "success")
            }

            // Pass the data back to the parent component
            onSuccess(data)

            // Reset form and close dialog
            form.reset()
            onOpenChange(false)
        } catch (error: any) {
            console.error("Error saving contact:", error)
            alertHelper.showToast("Failed to save contact. Please try again.", "error")
        } finally {
            setIsSubmitting(false)
        }
    }

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>{existingContact ? "Edit Contact" : "Add New Contact"}</DialogTitle>
                    <DialogDescription>
                        {existingContact
                            ? "Update the contact information below."
                            : "Fill in the details to add a new contact to this company."}
                    </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Name</FormLabel>
                                    <FormControl>
                                        <Input placeholder="John Doe" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="role"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Role</FormLabel>
                                    <FormControl>
                                        <Input placeholder="CEO, CTO, etc." {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="email"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Email</FormLabel>
                                        <FormControl>
                                            <Input type="email" placeholder="<EMAIL>" {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="phone"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Phone</FormLabel>
                                        <FormControl>
                                            <PhoneInput
                                                international
                                                countryCallingCodeEditable={true}
                                                defaultCountry="US"
                                                value={field.value}
                                                onChange={field.onChange}
                                                className="w-full"
                                                inputClassName="w-full p-2 border rounded-md border-input bg-background"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        <FormField
                            control={form.control}
                            name="contactType"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Contact Type</FormLabel>
                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select contact type" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            <SelectItem value="primary">Primary Contact</SelectItem>
                                            <SelectItem value="technical">Technical Contact</SelectItem>
                                            <SelectItem value="financial">Financial/Billing Contact</SelectItem>
                                            <SelectItem value="customer">Customer Service Contact</SelectItem>
                                            <SelectItem value="other">Other</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="notes"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Notes (Optional)</FormLabel>
                                    <FormControl>
                                        <Textarea placeholder="Additional information about this contact..." {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                                {isSubmitting ? "Saving..." : existingContact ? "Update Contact" : "Add Contact"}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    )
}
