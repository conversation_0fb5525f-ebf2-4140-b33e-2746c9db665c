//@ts-nocheck
"use client"

import React, {useEffect, useState} from "react"
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {
    ChevronDown,
    ChevronLeft,
    ChevronRight,
    ChevronsLeft,
    ChevronsRight,
    ChevronUp,
    FileDown,
    Filter,
    Loader2,
    Printer,
    Search,
} from "lucide-react"
import {Button} from "@/components/ui/button"
import {Checkbox} from "@/components/ui/checkbox"
import {Input} from "@/components/ui/input"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from "@/components/ui/dropdown-menu"
import {Badge} from "@/components/ui/badge"
import {formatDate} from "@/utils/helpers"

// Define column type
export type DataTableColumn<T> = {
    header: string
    accessorKey: keyof T
    cell?: (item: T) => React.ReactNode
    enableSorting?: boolean
    enableFiltering?: boolean
}

// Define filter option type
export type FilterOption = {
    label: string
    value: string | null
}

// Define props for DataTable
export type DataTableProps<T extends Record<string, any>> = {
    data: T[]
    columns: DataTableColumn<T>[]
    title?: string
    description?: string
    loading?: boolean
    error?: string | null
    rowsPerPageOptions?: number[]
    defaultRowsPerPage?: number
    filterOptions?: {
        key: keyof T
        options: FilterOption[]
    }
    enableSelection?: boolean
    enableExport?: boolean
    onRowClick?: (item: T) => void
    getRowId?: (item: T) => string
    renderRowExtras?: (item: T, index: number) => React.ReactNode
    headerActions?: React.ReactNode
}

export function DataTable<T extends Record<string, any>>({
                                                             data,
                                                             columns,
                                                             title = "Data Table",
                                                             description,
                                                             loading = false,
                                                             error = null,
                                                             rowsPerPageOptions = [5, 10, 20, 50],
                                                             defaultRowsPerPage = 10,
                                                             filterOptions,
                                                             enableSelection = true,
                                                             enableExport = true,
                                                             onRowClick,
                                                             getRowId = (item: T) => item.id || item._id || JSON.stringify(item),
                                                             renderRowExtras,
                                                             headerActions,
                                                         }: DataTableProps<T>) {
    // State
    const [items, setItems] = useState<T[]>(data)
    const [filteredItems, setFilteredItems] = useState<T[]>(data)
    const [searchTerm, setSearchTerm] = useState("")
    const [statusFilter, setStatusFilter] = useState<string | null>(null)
    const [sortConfig, setSortConfig] = useState<{
        key: keyof T | null
        direction: "ascending" | "descending"
    }>({key: null, direction: "ascending"})
    const [currentPage, setCurrentPage] = useState(1)
    const [rowsPerPage, setRowsPerPage] = useState(defaultRowsPerPage)
    const [selectedItems, setSelectedItems] = useState<string[]>([])
    const [allItemsSelected, setAllItemsSelected] = useState(false)

    // Update items when data changes
    useEffect(() => {
        setItems(data)
        setFilteredItems(data)
    }, [data])

    // Apply filters when search term or status filter changes
    useEffect(() => {
        let filtered = items

        // Apply text search filter
        if (searchTerm) {
            filtered = filtered.filter((item) => {
                // Search through all filterable columns
                return columns
                    .filter((col) => col.enableFiltering !== false)
                    .some((column) => {
                        const value = item[column.accessorKey]
                        if (value === null || value === undefined) return false
                        return String(value).toLowerCase().includes(searchTerm.toLowerCase())
                    })
            })
        }

        // Apply status filter if selected
        if (statusFilter && filterOptions) {
            filtered = filtered.filter((item) => {
                return String(item[filterOptions.key]) === statusFilter
            })
        }

        setFilteredItems(filtered)
        setCurrentPage(1) // Reset to first page when filters change
    }, [searchTerm, statusFilter, items, columns, filterOptions])

    // Handle "select all" checkbox
    useEffect(() => {
        if (allItemsSelected) {
            const currentPageItemIds = currentItems.map((item) => getRowId(item))
            setSelectedItems(currentPageItemIds)
        } else if (selectedItems.length === Math.min(rowsPerPage, filteredItems.length)) {
            // If all items on current page are selected but "select all" is unchecked
            setSelectedItems([])
        }
    }, [allItemsSelected, currentPage, rowsPerPage, getRowId])

    // Check if all items on current page are selected
    useEffect(() => {
        const currentPageItemIds = currentItems.map((item) => getRowId(item))

        const allSelected = currentPageItemIds.length > 0 && currentPageItemIds.every((id) => selectedItems.includes(id))

        setAllItemsSelected(allSelected)
    }, [selectedItems, currentPage, getRowId])

    const toggleItemSelection = (itemId: string) => {
        setSelectedItems((prev) => (prev.includes(itemId) ? prev.filter((id) => id !== itemId) : [...prev, itemId]))
    }

    const toggleAllItems = () => {
        setAllItemsSelected(!allItemsSelected)
    }

    const requestSort = (key: keyof T) => {
        let direction: "ascending" | "descending" = "ascending"
        if (sortConfig.key === key && sortConfig.direction === "ascending") {
            direction = "descending"
        }
        setSortConfig({key, direction})
    }

    const sortedItems = React.useMemo(() => {
        const sortableItems = [...filteredItems]
        if (sortConfig.key !== null) {
            sortableItems.sort((a, b) => {
                const aValue = a[sortConfig.key!]
                const bValue = b[sortConfig.key!]

                // Handle different types of values
                if (aValue === null || aValue === undefined) return 1
                if (bValue === null || bValue === undefined) return -1

                if (typeof aValue === "string" && typeof bValue === "string") {
                    return sortConfig.direction === "ascending" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue)
                }

                if (aValue < bValue) {
                    return sortConfig.direction === "ascending" ? -1 : 1
                }
                if (aValue > bValue) {
                    return sortConfig.direction === "ascending" ? 1 : -1
                }
                return 0
            })
        }
        return sortableItems
    }, [filteredItems, sortConfig])

    const SortIcon = ({columnKey}: { columnKey: keyof T }) => {
        if (sortConfig.key !== columnKey) {
            return <ChevronUp className="ml-1 h-4 w-4 text-muted-foreground"/>
        }
        return sortConfig.direction === "ascending" ? (
            <ChevronUp className="ml-1 h-4 w-4"/>
        ) : (
            <ChevronDown className="ml-1 h-4 w-4"/>
        )
    }

    const downloadCSV = () => {
        // Create headers from column definitions
        const headers = columns.map((col) => col.header)

        // Create rows from data
        const rows = sortedItems.map((item) =>
            columns.map((col) => {
                const value = item[col.accessorKey]
                // Handle special cases like dates, objects, etc.
                if (value instanceof Date) return formatDate(value.toISOString())
                if (typeof value === "object" && value !== null) return JSON.stringify(value)
                return String(value)
            }),
        )

        // Combine headers and rows
        const csvContent = [headers.join(","), ...rows.map((row) => row.join(","))].join("\n")

        // Create download link
        const blob = new Blob([csvContent], {type: "text/csv;charset=utf-8;"})
        const url = URL.createObjectURL(blob)
        const link = document.createElement("a")
        link.setAttribute("href", url)
        link.setAttribute("download", `${title.toLowerCase().replace(/\s+/g, "-")}.csv`)
        link.style.visibility = "hidden"
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    }

    const printToPDF = () => {
        // Create a printable version of the table
        const printWindow = window.open("", "_blank")

        if (!printWindow) {
            alert("Please allow popups to print the table")
            return
        }

        // Create the HTML content for the print window
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${title}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 20px;
            }
            h1 {
              font-size: 24px;
              margin-bottom: 10px;
            }
            p {
              font-size: 14px;
              color: #666;
              margin-bottom: 20px;
            }
            table {
              width: 100%;
              border-collapse: collapse;
            }
            th {
              background-color: #f0f0f0;
              padding: 10px;
              text-align: left;
              font-weight: bold;
              border-bottom: 2px solid #ddd;
            }
            td {
              padding: 10px;
              border-bottom: 1px solid #ddd;
            }
            tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            @media print {
              body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
              }
              button {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <h1>${title}</h1>
          ${description ? `<p>${description}</p>` : ""}
          <p>Generated on ${new Date().toLocaleDateString()}</p>
          <button onclick="window.print(); window.close();" style="padding: 8px 16px; background: #0070f3; color: white; border: none; border-radius: 4px; cursor: pointer; margin-bottom: 20px;">
            Print to PDF
          </button>
          <table>
            <thead>
              <tr>
                ${columns.map((col) => `<th>${col.header}</th>`).join("")}
              </tr>
            </thead>
            <tbody>
              ${sortedItems
            .map(
                (item) => `
                <tr>
                  ${columns
                    .map((col) => {
                        const value = item[col.accessorKey]
                        if (col.cell) {
                            // For custom cell renderers, just use the raw value
                            return `<td>${value}</td>`
                        }
                        // Handle different types of values
                        if (value instanceof Date) return `<td>${value.toLocaleDateString()}</td>`
                        if (value === null || value === undefined) return `<td></td>`
                        return `<td>${value}</td>`
                    })
                    .join("")}
                </tr>
              `,
            )
            .join("")}
            </tbody>
          </table>
        </body>
      </html>
    `

        // Write the HTML to the new window and trigger print
        printWindow.document.open()
        printWindow.document.write(html)
        printWindow.document.close()
    }

    // Calculate pagination
    const indexOfLastItem = currentPage * rowsPerPage
    const indexOfFirstItem = indexOfLastItem - rowsPerPage
    const currentItems = sortedItems.slice(indexOfFirstItem, indexOfLastItem)
    const totalPages = Math.ceil(sortedItems.length / rowsPerPage)

    return (
        <Card className="border-0 shadow-none">

            <CardContent className="p-0">
                <div className="rounded-md border overflow-hidden">
                    <Table>
                        <TableHeader>
                            <TableRow className="bg-primary/10">
                                {enableSelection && (
                                    <TableHead className="w-12">
                                        <Checkbox
                                            checked={allItemsSelected && currentItems.length > 0}
                                            onCheckedChange={toggleAllItems}
                                            disabled={currentItems.length === 0}
                                        />
                                    </TableHead>
                                )}

                                {columns.map((column) => (
                                    <TableHead
                                        key={String(column.accessorKey)}
                                        className={`font-semibold text-primary ${column.enableSorting !== false ? "cursor-pointer" : ""}`}
                                        onClick={() => column.enableSorting !== false && requestSort(column.accessorKey)}
                                    >
                                        {column.header}
                                        {column.enableSorting !== false && <SortIcon columnKey={column.accessorKey}/>}
                                    </TableHead>
                                ))}
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {loading ? (
                                <TableRow>
                                    <TableCell
                                        colSpan={enableSelection ? columns.length + 1 : columns.length}
                                        className="h-24 text-center"
                                    >
                                        <Loader2 className="mx-auto h-6 w-6 animate-spin"/>
                                        <p className="mt-2 text-sm text-muted-foreground">Loading data...</p>
                                    </TableCell>
                                </TableRow>
                            ) : error ? (
                                <TableRow>
                                    <TableCell
                                        colSpan={enableSelection ? columns.length + 1 : columns.length}
                                        className="h-24 text-center text-red-500"
                                    >
                                        {error}
                                    </TableCell>
                                </TableRow>
                            ) : currentItems.length === 0 ? (
                                <TableRow>
                                    <TableCell
                                        colSpan={enableSelection ? columns.length + 1 : columns.length}
                                        className="h-24 text-center"
                                    >
                                        No data found.
                                    </TableCell>
                                </TableRow>
                            ) : (
                                currentItems.map((item, index) => (
                                    <React.Fragment key={getRowId(item)}>
                                        <TableRow
                                            className={index % 2 === 0 ? "bg-background" : "bg-muted/50"}
                                            onClick={() => onRowClick && onRowClick(item)}
                                            style={onRowClick ? {cursor: "pointer"} : undefined}
                                        >
                                            {enableSelection && (
                                                <TableCell onClick={(e) => e.stopPropagation()}>
                                                    <Checkbox
                                                        checked={selectedItems.includes(getRowId(item))}
                                                        onCheckedChange={() => toggleItemSelection(getRowId(item))}
                                                    />
                                                </TableCell>
                                            )}

                                            {columns.map((column) => (
                                                <TableCell key={String(column.accessorKey)}>
                                                    {column.cell ? column.cell(item) : String(item[column.accessorKey] || "")}
                                                </TableCell>
                                            ))}
                                        </TableRow>
                                        {renderRowExtras && renderRowExtras(item, index)}
                                    </React.Fragment>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </div>
                <div className="flex items-center justify-between p-4">
                    <div className="text-sm text-muted-foreground">
                        {enableSelection && `${selectedItems.length} of ${filteredItems.length} row(s) selected.`}
                        {!enableSelection && `${filteredItems.length} total rows`}
                    </div>
                    <div className="flex items-center space-x-6 lg:space-x-8">
                        <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium">Rows per page</p>
                            <Select
                                value={rowsPerPage.toString()}
                                onValueChange={(value) => {
                                    setRowsPerPage(Number(value))
                                    setCurrentPage(1) // Reset to first page when changing rows per page
                                }}
                            >
                                <SelectTrigger className="h-8 w-[70px]">
                                    <SelectValue placeholder={rowsPerPage.toString()}/>
                                </SelectTrigger>
                                <SelectContent>
                                    {rowsPerPageOptions.map((option) => (
                                        <SelectItem key={option} value={option.toString()}>
                                            {option}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                            Page {currentPage} of {totalPages}
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                disabled={currentPage === 1}
                                onClick={() => setCurrentPage(1)}
                            >
                                <ChevronsLeft className="h-4 w-4"/>
                                <span className="sr-only">First page</span>
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                disabled={currentPage === 1}
                                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                            >
                                <ChevronLeft className="h-4 w-4"/>
                                <span className="sr-only">Previous page</span>
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                disabled={currentPage >= totalPages}
                                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                            >
                                <ChevronRight className="h-4 w-4"/>
                                <span className="sr-only">Next page</span>
                            </Button>
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                disabled={currentPage >= totalPages}
                                onClick={() => setCurrentPage(totalPages)}
                            >
                                <ChevronsRight className="h-4 w-4"/>
                                <span className="sr-only">Last page</span>
                            </Button>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}

