"use client"

import type { <PERSON> } from "react"
import Image from "next/image"
import { Check } from "lucide-react"

interface CustomCheckboxProps {
    id: string
    label: string
    checked: boolean
    name?: string
    cardType?: string
    onChange: (isChecked: boolean) => void
    imageUrl?: string
    imageAlt?: string
    disabled?: boolean
    size?: "small" | "medium" | "large"
}

export const CustomCheckbox: FC<CustomCheckboxProps> = ({
                                                            id,
                                                            label,
                                                            name,
                                                            checked,
                                                            cardType,
                                                            onChange,
                                                            imageUrl,
                                                            imageAlt,
                                                            disabled = false,
                                                            size = "medium",
                                                        }) => {
    // Size configurations
    const sizeConfig = {
        small: {
            container: "p-3",
            checkmark: "h-4 w-4 top-2 right-2",
            checkIcon: "h-2.5 w-2.5",
            imageContainer: "mb-2",
            imageVtl: "h-8 w-[50px]",
            imageStandard: "h-16 w-[42px]",
            label: "text-xs",
            labelNoImage: "text-sm",
            badge: "mt-1 px-2 py-0.5 text-xs",
            badgeDot: "h-1 w-1",
        },
        medium: {
            container: "p-4",
            checkmark: "h-5 w-5 top-3 right-3",
            checkIcon: "h-3 w-3",
            imageContainer: "mb-3",
            imageVtl: "h-10 w-[62px]",
            imageStandard: "h-20 w-[52px]",
            label: "text-sm",
            labelNoImage: "text-base",
            badge: "mt-2 px-2.5 py-0.5 text-xs",
            badgeDot: "h-1.5 w-1.5",
        },
        large: {
            container: "p-6",
            checkmark: "h-6 w-6 top-4 right-4",
            checkIcon: "h-3.5 w-3.5",
            imageContainer: "mb-4",
            imageVtl: "h-12 w-[74px]",
            imageStandard: "h-24 w-[62px]",
            label: "text-base",
            labelNoImage: "text-lg",
            badge: "mt-3 px-3 py-1 text-sm",
            badgeDot: "h-2 w-2",
        },
    }

    const config = sizeConfig[size]

    return (
        <div className="relative">
      <input
          type="checkbox"
          className="sr-only peer"
          id={id}
          name={name}
          checked={checked}
          onChange={(e) => !disabled && onChange(e.target.checked)}
          disabled={disabled}
      />
      <label
          htmlFor={id}
          className={`
          relative block w-full cursor-pointer transition-all duration-200
          ${disabled ? "cursor-not-allowed opacity-50" : "hover:shadow-sm"}
        `}
      >
        <div
            className={`
            relative overflow-hidden rounded-lg border transition-all duration-200
            ${checked ? "border-blue-600 bg-blue-50 shadow-sm" : "border-gray-200 bg-white hover:border-gray-300"}
            ${disabled ? "bg-gray-50 border-gray-100" : ""}
          `}
        >
          {/* Checkmark */}
            <div
                className={`
              absolute z-10 flex items-center justify-center rounded-full border transition-all duration-200 ${config.checkmark}
              ${checked ? "border-blue-600 bg-blue-600" : "border-gray-300 bg-white opacity-0 group-hover:opacity-100"}
            `}
            >
            <Check
                className={`text-white transition-opacity duration-200 ${config.checkIcon} ${checked ? "opacity-100" : "opacity-0"}`}
                strokeWidth={2.5}
            />
          </div>

            {/* Content */}
            <div className={config.container}>
            {/* Image Section */}
                {imageUrl && (
                    <div className={`flex justify-center ${config.imageContainer}`}>
                {cardType === "vtl" ? (
                    <div className="relative overflow-hidden rounded border border-gray-200 bg-gray-50">
                    <div className={`relative ${config.imageVtl}`}>
                      <Image
                          src={imageUrl || "/placeholder.svg"}
                          alt={imageAlt || label}
                          fill
                          className="object-cover"
                          sizes="62px"
                          unoptimized
                      />
                    </div>
                  </div>
                ) : (
                    <div className="relative overflow-hidden rounded border border-gray-200 bg-gray-50">
                    <div className={`relative ${config.imageStandard}`}>
                      <Image
                          src={imageUrl || "/placeholder.svg"}
                          alt={imageAlt || label}
                          fill
                          className="object-cover"
                          sizes="52px"
                          unoptimized
                      />
                    </div>
                  </div>
                )}
              </div>
                )}

                {/* Label */}
                <div className="text-center">
              <h3
                  className={`
                  font-medium leading-snug transition-colors duration-200
                  ${checked ? "text-blue-900" : "text-gray-900"}
                  ${!imageUrl ? config.labelNoImage : config.label}
                  ${disabled ? "text-gray-500" : ""}
                `}
              >
                {label}
              </h3>

                    {/* Selected Badge */}
                    {checked && !disabled && (
                        <div
                            className={`inline-flex items-center gap-1.5 rounded-full bg-blue-100 font-medium text-blue-700 ${config.badge}`}
                        >
                  <div className={`rounded-full bg-blue-600 ${config.badgeDot}`} />
                  Selected
                </div>
                    )}
            </div>
          </div>

            {/* Subtle Selected Indicator */}
            {checked && <div className="absolute left-0 top-0 h-full w-1 bg-blue-600" />}
        </div>
      </label>
    </div>
    )
}
