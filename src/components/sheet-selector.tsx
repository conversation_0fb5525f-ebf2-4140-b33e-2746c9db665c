"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { FileSpreadsheet, RefreshCw } from "lucide-react"
import * as XLSX from "xlsx"

interface SheetSelectorProps {
  workbookData?: ArrayBuffer
  currentSheet: string
  sheetNames: string[]
  onSheetChange: (sheetName: string, data: { headers: string[]; rows: any[][] }) => void
  loading?: boolean
}

export function SheetSelector({
  workbookData,
  currentSheet,
  sheetNames,
  onSheetChange,
  loading = false,
}: SheetSelectorProps) {
  const [isChanging, setIsChanging] = useState(false)

  const handleSheetChange = async (sheetName: string) => {
    if (!workbookData || isChanging) return

    setIsChanging(true)
    try {
      const workbook = XLSX.read(workbookData, { type: "array" })
      const sheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 })

      const headers = jsonData[0] as string[]
      const rows = jsonData.slice(1) as any[][]

      onSheetChange(sheetName, { headers, rows })
    } catch (error) {
      console.error("Error switching sheet:", error)
    } finally {
      setIsChanging(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileSpreadsheet className="w-5 h-5" />
          Sheet Selection
        </CardTitle>
        <CardDescription>Choose which sheet to analyze from your Excel file</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Select value={currentSheet} onValueChange={handleSheetChange} disabled={loading || isChanging}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a sheet" />
                </SelectTrigger>
                <SelectContent>
                  {sheetNames.map((name) => (
                    <SelectItem key={name} value={name}>
                      <div className="flex items-center gap-2">
                        <span>{name}</span>
                        {name === currentSheet && (
                          <Badge variant="secondary" className="text-xs">
                            Current
                          </Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {(loading || isChanging) && <RefreshCw className="w-4 h-4 animate-spin text-muted-foreground" />}
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Available Sheets:</span>
              <div className="mt-1 space-y-1">
                {sheetNames.map((name) => (
                  <div key={name} className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${name === currentSheet ? "bg-green-500" : "bg-gray-300"}`} />
                    <span className={name === currentSheet ? "font-medium" : ""}>{name}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <span className="font-medium">Quick Actions:</span>
              <div className="mt-1 space-y-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs"
                  onClick={() => handleSheetChange(sheetNames[0])}
                  disabled={currentSheet === sheetNames[0] || loading || isChanging}
                >
                  First Sheet
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs"
                  onClick={() => handleSheetChange(sheetNames[sheetNames.length - 1])}
                  disabled={currentSheet === sheetNames[sheetNames.length - 1] || loading || isChanging}
                >
                  Last Sheet
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
