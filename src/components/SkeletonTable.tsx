import {Skeleton} from "@/components/ui/skeleton"

export function SkeletonTable() {
    return (
        <div className="w-full">
            <div className="border rounded-lg">
                {/* Table Header */}
                <div className="grid grid-cols-4 gap-4 p-4 border-b">
                    <Skeleton className="h-6 w-full"/>
                    <Skeleton className="h-6 w-full"/>
                    <Skeleton className="h-6 w-full"/>
                    <Skeleton className="h-6 w-full"/>
                </div>

                {/* Table Rows */}
                {[...Array(5)].map((_, index) => (
                    <div key={index} className="grid grid-cols-4 gap-4 p-4 border-b last:border-b-0">
                        <Skeleton className="h-4 w-full"/>
                        <Skeleton className="h-4 w-full"/>
                        <Skeleton className="h-4 w-3/4"/>
                        <Skeleton className="h-4 w-1/2"/>
                    </div>
                ))}
            </div>
        </div>
    )
}

