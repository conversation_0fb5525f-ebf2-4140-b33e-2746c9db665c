"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Database, TrendingUp, Download } from "lucide-react"
import type { DatabaseComparisonResult } from "@/lib/database-comparison"

interface DatabaseComparisonResultsProps {
  comparisonResult: DatabaseComparisonResult
  duplicateDetails: Array<{
    newRecord: Record<string, any>
    existingRecord: Record<string, any>
    action: "replaced" | "skipped"
  }>
  isVisible: boolean
  onClose: () => void
}

export function DatabaseComparisonResults({
  comparisonResult,
  duplicateDetails,
  isVisible,
  onClose,
}: DatabaseComparisonResultsProps) {
  const downloadReport = () => {
    let report = `DATABASE COMPARISON REPORT\n`
    report += `Generated: ${new Date().toLocaleString()}\n\n`
    report += `SUMMARY\n${comparisonResult.summary}\n\n`

    report += `STATISTICS\n`
    report += `New Records: ${comparisonResult.newRecords}\n`
    report += `Updated Records: ${comparisonResult.updatedRecords}\n`
    report += `Duplicates Found: ${comparisonResult.duplicatesFound}\n`
    report += `Total Processed: ${comparisonResult.totalProcessed}\n\n`

    if (comparisonResult.changes.length > 0) {
      report += `RECORD CHANGES\n`
      comparisonResult.changes.forEach((change, index) => {
        report += `${index + 1}. ${change.type.toUpperCase()}: `
        if (change.type === "updated" && change.changedFields) {
          report += `Fields changed: ${change.changedFields.join(", ")}\n`
        } else {
          report += `Record processed\n`
        }
      })
      report += `\n`
    }

    if (Object.keys(comparisonResult.fieldChanges).length > 0) {
      report += `FIELD CHANGES\n`
      Object.entries(comparisonResult.fieldChanges).forEach(([field, changes]) => {
        report += `${field}: +${changes.added} added, ~${changes.modified} modified, -${changes.removed} removed\n`
      })
    }

    const blob = new Blob([report], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `database_comparison_${Date.now()}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-6xl h-[85vh] m-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                Delivery Methods Comparison Results
              </CardTitle>
              <CardDescription>{comparisonResult.summary}</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button onClick={downloadReport} variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export Report
              </Button>
              <Button onClick={onClose} variant="outline">
                Close
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="changes">Record Changes</TabsTrigger>
              <TabsTrigger value="duplicates">Duplicates</TabsTrigger>
              <TabsTrigger value="fields">Field Analysis</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-green-600">New Methods</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">{comparisonResult.newRecords}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-blue-600">Updated Methods</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-600">{comparisonResult.updatedRecords}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-orange-600">Duplicates</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-orange-600">{comparisonResult.duplicatesFound}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Processed</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{comparisonResult.totalProcessed}</div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Processing Summary
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span>New Records Added</span>
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        {comparisonResult.newRecords}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Records Updated</span>
                      <Badge variant="outline" className="text-blue-600 border-blue-600">
                        {comparisonResult.updatedRecords}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Exact Duplicates</span>
                      <Badge variant="outline" className="text-orange-600 border-orange-600">
                        {comparisonResult.duplicatesFound}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Success Rate</span>
                      <Badge variant="outline">
                        {(
                          ((comparisonResult.newRecords + comparisonResult.updatedRecords) /
                            comparisonResult.totalProcessed) *
                          100
                        ).toFixed(1)}
                        %
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="changes" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Method Changes</CardTitle>
                  <CardDescription>
                    Showing first {Math.min(50, comparisonResult.changes.length)} method changes
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-96">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Type</TableHead>
                          <TableHead>Method Info</TableHead>
                          <TableHead>Changed Fields</TableHead>
                          <TableHead>Action</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {comparisonResult.changes.slice(0, 50).map((change, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              <Badge
                                variant="outline"
                                className={
                                  change.type === "new"
                                    ? "text-green-600 border-green-600"
                                    : change.type === "updated"
                                      ? "text-blue-600 border-blue-600"
                                      : "text-orange-600 border-orange-600"
                                }
                              >
                                {change.type}
                              </Badge>
                            </TableCell>
                            <TableCell className="max-w-48 truncate">
                              {Object.entries(change.record)
                                .slice(0, 2)
                                .map(([key, value]) => `${key}: ${value}`)
                                .join(", ")}
                            </TableCell>
                            <TableCell>
                              {change.changedFields ? (
                                <div className="flex flex-wrap gap-1">
                                  {change.changedFields.slice(0, 3).map((field) => (
                                    <Badge key={field} variant="secondary" className="text-xs">
                                      {field}
                                    </Badge>
                                  ))}
                                  {change.changedFields.length > 3 && (
                                    <Badge variant="secondary" className="text-xs">
                                      +{change.changedFields.length - 3} more
                                    </Badge>
                                  )}
                                </div>
                              ) : (
                                "—"
                              )}
                            </TableCell>
                            <TableCell>
                              {change.type === "new" && "Added to delivery methods"}
                              {change.type === "updated" && "Updated in delivery methods"}
                              {change.type === "duplicate" && "Skipped (exact match)"}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="duplicates" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Duplicate Handling Details</CardTitle>
                  <CardDescription>
                    Records that were identified as duplicates and how they were handled
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-96">
                    {duplicateDetails.length > 0 ? (
                      <div className="space-y-3">
                        {duplicateDetails.slice(0, 50).map((detail, index) => (
                          <div key={index} className="border rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <Badge
                                variant="outline"
                                className={
                                  detail.action === "replaced"
                                    ? "text-blue-600 border-blue-600"
                                    : "text-orange-600 border-orange-600"
                                }
                              >
                                {detail.action}
                              </Badge>
                              <span className="text-xs text-muted-foreground">Duplicate #{index + 1}</span>
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="font-medium">New Record:</span>
                                <div className="text-muted-foreground">
                                  {Object.entries(detail.newRecord)
                                    .slice(0, 3)
                                    .map(([key, value]) => `${key}: ${value}`)
                                    .join(", ")}
                                </div>
                              </div>
                              <div>
                                <span className="font-medium">Existing Record:</span>
                                <div className="text-muted-foreground">
                                  {Object.entries(detail.existingRecord)
                                    .slice(0, 3)
                                    .map(([key, value]) => `${key}: ${value}`)
                                    .join(", ")}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        No duplicate handling details available
                      </div>
                    )}
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="fields" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Field-Level Changes</CardTitle>
                  <CardDescription>Analysis of changes at the field level</CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-96">
                    {Object.keys(comparisonResult.fieldChanges).length > 0 ? (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Field Name</TableHead>
                            <TableHead>Added</TableHead>
                            <TableHead>Modified</TableHead>
                            <TableHead>Removed</TableHead>
                            <TableHead>Total Changes</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {Object.entries(comparisonResult.fieldChanges).map(([field, changes]) => (
                            <TableRow key={field}>
                              <TableCell className="font-medium">{field}</TableCell>
                              <TableCell>
                                <Badge variant="outline" className="text-green-600 border-green-600">
                                  +{changes.added}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline" className="text-blue-600 border-blue-600">
                                  ~{changes.modified}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline" className="text-red-600 border-red-600">
                                  -{changes.removed}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Badge variant="secondary">{changes.added + changes.modified + changes.removed}</Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">No field-level changes detected</div>
                    )}
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
