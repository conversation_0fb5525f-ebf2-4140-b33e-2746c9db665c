"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { AlertTriangle, Settings } from "lucide-react"
import type { DuplicateDetectionConfig } from "@/lib/duplicate-detection"

interface DuplicateDetectionConfigProps {
  headers: string[]
  onConfigChange: (config: DuplicateDetectionConfig & { compareWithDatabase: boolean }) => void
  isVisible: boolean
  onClose: () => void
}

export function DuplicateDetectionConfig({
  headers,
  onConfigChange,
  isVisible,
  onClose,
}: DuplicateDetectionConfigProps) {
  const [primaryKey, setP<PERSON>ry<PERSON>ey] = useState<string[]>(["country", "region"])
  const [strategy, setStrategy] = useState<"replace" | "skip" | "merge">("replace")
  const [compareWithDatabase, setCompareWithDatabase] = useState(true)

  const handleAddField = (field: string) => {
    if (!primaryKey.includes(field)) {
      const newPrimaryKey = [...primaryKey, field]
      setPrimaryKey(newPrimaryKey)
      updateConfig(newPrimaryKey, strategy, compareWithDatabase)
    }
  }

  const handleRemoveField = (field: string) => {
    const newPrimaryKey = primaryKey.filter((f) => f !== field)
    setPrimaryKey(newPrimaryKey)
    updateConfig(newPrimaryKey, strategy, compareWithDatabase)
  }

  const handleStrategyChange = (newStrategy: "replace" | "skip" | "merge") => {
    setStrategy(newStrategy)
    updateConfig(primaryKey, newStrategy, compareWithDatabase)
  }

  const handleCompareToggle = (enabled: boolean) => {
    setCompareWithDatabase(enabled)
    updateConfig(primaryKey, strategy, enabled)
  }

  const updateConfig = (key: string[], strat: "replace" | "skip" | "merge", compare: boolean) => {
    onConfigChange({
      primaryKey: key,
      strategy: strat,
      compareWithDatabase: compare,
    })
  }

  const getStrategyDescription = (strategy: "replace" | "skip" | "merge") => {
    switch (strategy) {
      case "replace":
        return "Update existing delivery methods with new data (no duplicates created)"
      case "skip":
        return "Keep existing delivery methods, ignore new duplicates"
      case "merge":
        return "Merge new data with existing delivery methods"
      default:
        return ""
    }
  }

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-2xl m-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Duplicate Detection Configuration
          </CardTitle>
          <CardDescription>Configure how duplicate delivery methods should be detected and handled</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Primary Key Configuration */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Primary Key Fields</Label>
            <p className="text-xs text-muted-foreground">
              Select fields that uniquely identify a delivery method (e.g., country + region)
            </p>

            <div className="flex flex-wrap gap-2 mb-3">
              {primaryKey.map((field) => (
                <Badge
                  key={field}
                  variant="default"
                  className="cursor-pointer"
                  onClick={() => handleRemoveField(field)}
                >
                  {field} ×
                </Badge>
              ))}
            </div>

            <Select onValueChange={handleAddField}>
              <SelectTrigger>
                <SelectValue placeholder="Add field to primary key..." />
              </SelectTrigger>
              <SelectContent>
                {headers
                  .filter((header) => !primaryKey.includes(header))
                  .map((header) => (
                    <SelectItem key={header} value={header}>
                      {header}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          {/* Strategy Configuration */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Duplicate Handling Strategy</Label>

            <div className="space-y-2">
              {(["replace", "skip", "merge"] as const).map((strat) => (
                <div
                  key={strat}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    strategy === strat ? "border-primary bg-primary/5" : "border-muted hover:border-muted-foreground/50"
                  }`}
                  onClick={() => handleStrategyChange(strat)}
                >
                  <div className="flex items-center gap-2">
                    <div
                      className={`w-4 h-4 rounded-full border-2 ${
                        strategy === strat ? "border-primary bg-primary" : "border-muted-foreground"
                      }`}
                    >
                      {strategy === strat && <div className="w-2 h-2 bg-white rounded-full m-0.5" />}
                    </div>
                    <div>
                      <div className="font-medium capitalize">{strat}</div>
                      <div className="text-sm text-muted-foreground">{getStrategyDescription(strat)}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Database Comparison */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Compare with Existing Methods</Label>
                <p className="text-xs text-muted-foreground">Compare new data with previously saved delivery methods</p>
              </div>
              <Switch checked={compareWithDatabase} onCheckedChange={handleCompareToggle} />
            </div>
          </div>

          {/* Warning */}
          {primaryKey.length === 0 && (
            <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <AlertTriangle className="w-4 h-4 text-yellow-600" />
              <span className="text-sm text-yellow-700">Please select at least one field for the primary key</span>
            </div>
          )}

          {/* Preview */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Configuration Preview</Label>
            <div className="p-3 bg-muted rounded-lg text-sm">
              <div>
                <strong>Primary Key:</strong> {primaryKey.join(" + ") || "None selected"}
              </div>
              <div>
                <strong>Strategy:</strong> {strategy}
              </div>
              <div>
                <strong>Database Comparison:</strong> {compareWithDatabase ? "Enabled" : "Disabled"}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button onClick={onClose} variant="outline">
              Cancel
            </Button>
            <Button onClick={onClose} disabled={primaryKey.length === 0}>
              Apply Configuration
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
