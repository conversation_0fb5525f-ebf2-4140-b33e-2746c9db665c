"use client"

import { usePathname, useSearchParams } from "next/navigation"
import { useEffect, useState, type ReactNode } from "react"
import { cn } from "@/lib/utils"

interface PageTransitionProps {
    children: ReactNode
}

export function PageTransition({ children }: PageTransitionProps) {
    const pathname = usePathname()
    const searchParams = useSearchParams()
    const [isTransitioning, setIsTransitioning] = useState(false)
    const [content, setContent] = useState(children)

    // Create a key that changes when the route changes
    const routeKey = pathname + searchParams.toString()

    useEffect(() => {
        // Start transition
        setIsTransitioning(true)

        // After a short delay, update content and end transition
        const timer = setTimeout(() => {
            setContent(children)
            setIsTransitioning(false)
        }, 300)

        return () => clearTimeout(timer)
    }, [routeKey, children])

    return (
        <div className={cn("transition-opacity duration-300 ease-in-out", isTransitioning ? "opacity-0" : "opacity-100")}>
            {content}
        </div>
    )
}
