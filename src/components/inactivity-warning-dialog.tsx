"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import { AlertTriangle } from "lucide-react"

interface InactivityWarningDialogProps {
    open: boolean
    countdown: string
    onContinue: () => void
    onLogout: () => void
}

export function InactivityWarningDialog({ open, countdown, onContinue, onLogout }: InactivityWarningDialogProps) {
    return (
        <Dialog open={open}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5 text-amber-500" />
                        <span>Session Timeout Warning</span>
                    </DialogTitle>
                    <DialogDescription>Your session is about to expire due to inactivity.</DialogDescription>
                </DialogHeader>

                <div className="py-4">
                    <p className="text-center text-lg font-semibold">
                        You will be logged out in <span className="text-amber-500">{countdown}</span>
                    </p>
                    <p className="mt-2 text-sm text-muted-foreground text-center">
                        Click &apos;Continue Session&apos; to stay logged in.
                    </p>
                </div>

                <DialogFooter className="flex sm:justify-between">
                    <Button variant="outline" onClick={onLogout}>
                        Logout Now
                    </Button>
                    <Button onClick={onContinue}>Continue Session</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
