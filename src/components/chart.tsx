"use client"

import { <PERSON><PERSON><PERSON> as Tremor<PERSON><PERSON><PERSON><PERSON> } from "@tremor/react"
import { <PERSON><PERSON><PERSON> as Tremor<PERSON><PERSON><PERSON><PERSON> } from "@tremor/react"
import { Donut<PERSON><PERSON> as TremorDonut<PERSON><PERSON> } from "@tremor/react"


export function Bar<PERSON>hart({ data, index, categories, colors, valueFormatter, className }) {
    return (
        <TremorBarChart
            data={data}
            index={index}
            categories={categories}
            colors={colors}
            valueFormatter={valueFormatter}
            className={className}
        />
    )
}

export function LineChart({ data, index, categories, colors, valueFormatter, className }) {
    return (
        <TremorLineChart
            data={data}
            index={index}
            categories={categories}
            colors={colors}
            valueFormatter={valueFormatter}
            className={className}
        />
    )
}

export function Donut<PERSON>hart({ data, index, category, colors, valueFormatter, className }) {
    return (
        <TremorDonutChart
            data={data}
            index={index}
            category={category}
            colors={colors}
            valueFormatter={valueFormatter}
            className={className}
        />
    )
}

