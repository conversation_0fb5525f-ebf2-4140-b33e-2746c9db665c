//@ts-nocheck
"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { useEffect, useState } from "react"
import axiosInstance from "@/utils/axiosInstance"
import {
    TrendingUp,
    CreditCard,
    Users,
    Target,
    AlertTriangle,
    Activity,
    ChevronRight,
    BarChart3,
    Mail,
    Settings,
} from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/hooks/use-toast"

interface BinAllocationSummaryProps {
    companyId: string
    cipId: string
    className?: string
}

interface BinAllocation {
    _id: string
    cardRange: string
    binStart: string
    binEnd: string
    total_usage: number
    cards: any[]
    product_version: {
        _id: string
        version_name: string
        version_code: string
    }
}

interface CompanySettings {
    criticalThreshold: number
    emailNotifications: boolean
    notificationEmails: string[]
}

// Email notification function
const sendCriticalStockEmail = async (companyId: string, criticalProducts: any[], threshold: number) => {
    try {
        const emailData = {
            companyId,
            criticalProducts: criticalProducts.map((product) => ({
                productName: product.allocation.product_version.version_name,
                versionCode: product.allocation.product_version.version_code,
                cardRange: product.allocation.cardRange,
                remaining: product.stats.remaining,
                utilizationRate: product.stats.utilizationRate,
                threshold,
            })),
            threshold,
            timestamp: new Date().toISOString(),
        }

        const response = await axiosInstance.post("/notifications/critical-stock-email", emailData)
        return response.data
    } catch (error) {
        console.error("Error sending critical stock email:", error)
        throw error
    }
}

// Mini Donut Chart Component
const MiniDonutChart = ({ percentage, size = 60, strokeWidth = 6, color = "#3b82f6" }) => {
    const radius = (size - strokeWidth) / 2
    const circumference = radius * 2 * Math.PI
    const strokeDasharray = circumference
    const strokeDashoffset = circumference - (percentage / 100) * circumference

    return (
        <div className="relative inline-flex items-center justify-center">
            <svg width={size} height={size} className="transform -rotate-90">
                <circle cx={size / 2} cy={size / 2} r={radius} stroke="#e5e7eb" strokeWidth={strokeWidth} fill="none" />
                <circle
                    cx={size / 2}
                    cy={size / 2}
                    r={radius}
                    stroke={color}
                    strokeWidth={strokeWidth}
                    fill="none"
                    strokeDasharray={strokeDasharray}
                    strokeDashoffset={strokeDashoffset}
                    strokeLinecap="round"
                    className="transition-all duration-500 ease-in-out"
                />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xs font-bold text-slate-700">{percentage.toFixed(0)}%</span>
            </div>
        </div>
    )
}

// Mini Bar Chart Component
const MiniBarChart = ({ data, height = 40, width = 80 }) => {
    const maxValue = Math.max(...data)
    return (
        <div className="flex items-end justify-between gap-1" style={{ height, width }}>
            {data.map((value, index) => (
                <div
                    key={index}
                    className="bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-sm transition-all duration-300 hover:from-blue-600 hover:to-blue-500"
                    style={{
                        height: `${(value / maxValue) * 100}%`,
                        width: `${width / data.length - 2}px`,
                        minHeight: "2px",
                    }}
                />
            ))}
        </div>
    )
}

// Usage Trend Component
const UsageTrend = ({ trend = "up", percentage }) => {
    const isUp = trend === "up"
    return (
        <div className={`flex items-center gap-1 text-xs ${isUp ? "text-green-600" : "text-red-600"}`}>
            <div
                className={`w-0 h-0 border-l-2 border-r-2 border-transparent ${
                    isUp ? "border-b-2 border-b-green-600" : "border-t-2 border-t-red-600"
                }`}
            />
            <span className="font-medium">{percentage}%</span>
        </div>
    )
}

// Allocation Gauge Component
const AllocationGauge = ({ used, total, size = 50 }) => {
    const percentage = (used / total) * 100
    const getColor = (pct) => {
        if (pct > 90) return "#ef4444"
        if (pct > 75) return "#f59e0b"
        if (pct > 50) return "#eab308"
        return "#10b981"
    }

    return (
        <div className="relative">
            <svg width={size} height={size / 2 + 10} viewBox={`0 0 ${size} ${size / 2 + 10}`}>
                <path
                    d={`M 5 ${size / 2} A ${size / 2 - 5} ${size / 2 - 5} 0 0 1 ${size - 5} ${size / 2}`}
                    stroke="#e5e7eb"
                    strokeWidth="4"
                    fill="none"
                />
                <path
                    d={`M 5 ${size / 2} A ${size / 2 - 5} ${size / 2 - 5} 0 0 1 ${size - 5} ${size / 2}`}
                    stroke={getColor(percentage)}
                    strokeWidth="4"
                    fill="none"
                    strokeDasharray={`${(percentage / 100) * Math.PI * (size / 2 - 5)} ${Math.PI * (size / 2 - 5)}`}
                    strokeLinecap="round"
                    className="transition-all duration-500"
                />
            </svg>
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2">
                <span className="text-xs font-bold text-slate-700">{percentage.toFixed(0)}%</span>
            </div>
        </div>
    )
}

export function BinAllocationSummary({ companyId, cipId, className = "" }: BinAllocationSummaryProps) {
    const [binAllocations, setBinAllocations] = useState<BinAllocation[]>([])
    const [companySettings, setCompanySettings] = useState<CompanySettings>({
        criticalThreshold: 1000,
        emailNotifications: true,
        notificationEmails: [],
    })
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [emailSending, setEmailSending] = useState(false)
    const [lastEmailSent, setLastEmailSent] = useState<string | null>(null)
    const { toast } = useToast()

    // Calculate allocation statistics using actual card usage data
    const calculateAllocationStats = (binStart: string, binEnd: string, totalUsage = 0, cardsArray: any[] = []) => {
        const startNum = Number.parseInt(binStart.replace(/\D/g, ""))
        const endNum = Number.parseInt(binEnd.replace(/\D/g, ""))
        const totalAllocation = endNum - startNum + 1
        const actualIssued = Math.max(totalUsage, cardsArray.length)
        const remaining = Math.max(0, totalAllocation - actualIssued)
        const utilizationRate = totalAllocation > 0 ? (actualIssued / totalAllocation) * 100 : 0

        return {
            totalAllocation,
            issued: actualIssued,
            remaining,
            utilizationRate,
        }
    }

    // Fetch company settings including critical threshold
    const fetchCompanySettings = async () => {
        try {
            const response = await axiosInstance.get(`/company/${companyId}`)
            const settings = response.data || {}

            setCompanySettings({
                criticalThreshold: settings.company.critical_bins || 1000,

            })
        } catch (error) {
            console.error("Error fetching company settings:", error)
            // Use default settings if API fails
            setCompanySettings({
                criticalThreshold: 1000,
                emailNotifications: true,
                notificationEmails: [],
            })
        }
    }

    const fetchBinAllocations = async () => {
        try {
            setLoading(true)
            const response = await axiosInstance.get(`/bin/cip/${cipId}`)
            setBinAllocations(response.data || [])
            setError(null)
        } catch (error: any) {
            console.error("Error fetching BIN allocations:", error)
            setError("Failed to fetch allocation data")
            setBinAllocations([])
        } finally {
            setLoading(false)
        }
    }

    // Check for critical stock and send email if needed
    const checkCriticalStockAndNotify = async (allocations: BinAllocation[]) => {
        if (!companySettings.emailNotifications || companySettings.notificationEmails.length === 0) {
            return
        }

        const criticalProducts = allocations
            .map((allocation) => ({
                allocation,
                stats: calculateAllocationStats(
                    allocation.binStart,
                    allocation.binEnd,
                    allocation.total_usage || 0,
                    allocation.cards || [],
                ),
            }))
            .filter(({ stats }) => stats.remaining < companySettings.criticalThreshold)

        if (criticalProducts.length > 0) {
            // Check if we've already sent an email today for this company
            const today = new Date().toDateString()
            const emailKey = `critical-email-${companyId}-${today}`

            if (lastEmailSent !== today) {
                try {
                    setEmailSending(true)
                    await sendCriticalStockEmail(companyId, criticalProducts, companySettings.criticalThreshold)
                    setLastEmailSent(today)
                    localStorage.setItem(emailKey, today)

                    toast({
                        title: "Critical Stock Alert Sent",
                        description: `Email notification sent for ${criticalProducts.length} product(s) with critical stock levels.`,
                        variant: "default",
                    })
                } catch (error) {
                    console.error("Failed to send critical stock email:", error)
                    toast({
                        title: "Email Notification Failed",
                        description: "Failed to send critical stock alert email. Please check your notification settings.",
                        variant: "destructive",
                    })
                } finally {
                    setEmailSending(false)
                }
            }
        }
    }

    // Manual email trigger
    const handleManualEmailTrigger = async () => {
        const criticalProducts = binAllocations
            .map((allocation) => ({
                allocation,
                stats: calculateAllocationStats(
                    allocation.binStart,
                    allocation.binEnd,
                    allocation.total_usage || 0,
                    allocation.cards || [],
                ),
            }))
            .filter(({ stats }) => stats.remaining < companySettings.criticalThreshold)

        if (criticalProducts.length === 0) {
            toast({
                title: "No Critical Stock",
                description: "No products currently have critical stock levels.",
                variant: "default",
            })
            return
        }

        try {
            setEmailSending(true)
            await sendCriticalStockEmail(companyId, criticalProducts, companySettings.criticalThreshold)

            toast({
                title: "Alert Email Sent",
                description: `Critical stock alert sent for ${criticalProducts.length} product(s).`,
                variant: "default",
            })
        } catch (error) {
            toast({
                title: "Email Failed",
                description: "Failed to send critical stock alert email.",
                variant: "destructive",
            })
        } finally {
            setEmailSending(false)
        }
    }

    useEffect(() => {
        if (companyId && cipId) {
            fetchCompanySettings()
            fetchBinAllocations()
        }
    }, [companyId, cipId])

    useEffect(() => {
        if (binAllocations.length > 0 && companySettings.criticalThreshold) {
            checkCriticalStockAndNotify(binAllocations)
        }
    }, [binAllocations, companySettings])

    // Calculate total statistics
    const totalStats = binAllocations.reduce(
        (acc, allocation) => {
            const stats = calculateAllocationStats(
                allocation.binStart,
                allocation.binEnd,
                allocation.total_usage || 0,
                allocation.cards || [],
            )
            return {
                totalAllocation: acc.totalAllocation + stats.totalAllocation,
                totalIssued: acc.totalIssued + stats.issued,
                totalRemaining: acc.totalRemaining + stats.remaining,
            }
        },
        { totalAllocation: 0, totalIssued: 0, totalRemaining: 0 },
    )

    // Calculate low stock products using dynamic threshold
    const lowStockProducts = binAllocations.filter((allocation) => {
        const stats = calculateAllocationStats(
            allocation.binStart,
            allocation.binEnd,
            allocation.total_usage || 0,
            allocation.cards || [],
        )
        return stats.remaining < companySettings.criticalThreshold
    })

    const utilizationRate =
        totalStats.totalAllocation > 0 ? (totalStats.totalIssued / totalStats.totalAllocation) * 100 : 0

    // Helper function to get status color and label using dynamic threshold
    const getStatusInfo = (utilizationRate: number, remaining: number) => {
        if (remaining < companySettings.criticalThreshold) {
            return {
                color: "text-red-600",
                bgColor: "bg-red-50",
                borderColor: "border-red-200",
                status: "Critical",
                statusColor: "bg-red-500",
                chartColor: "#ef4444",
            }
        } else if (utilizationRate > 75) {
            return {
                color: "text-orange-600",
                bgColor: "bg-orange-50",
                borderColor: "border-orange-200",
                status: "High Usage",
                statusColor: "bg-orange-500",
                chartColor: "#f59e0b",
            }
        } else if (utilizationRate > 50) {
            return {
                color: "text-yellow-600",
                bgColor: "bg-yellow-50",
                borderColor: "border-yellow-200",
                status: "Moderate",
                statusColor: "bg-yellow-500",
                chartColor: "#eab308",
            }
        } else {
            return {
                color: "text-green-600",
                bgColor: "bg-green-50",
                borderColor: "border-green-200",
                status: "Healthy",
                statusColor: "bg-green-500",
                chartColor: "#10b981",
            }
        }
    }

    // Generate mock usage trend data for demonstration
    const generateMockTrendData = () => {
        return Array.from({ length: 7 }, () => Math.floor(Math.random() * 100) + 20)
    }

    if (loading) {
        return (
            <Card className={`shadow-lg ${className}`}>
                <CardContent className="p-6">
                    <div className="flex items-center justify-center h-32">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    </div>
                </CardContent>
            </Card>
        )
    }

    return (
        <Card className={`shadow-lg ${className}`}>
            <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                    <CardTitle className="text-lg font-bold text-slate-800 flex items-center gap-2">
                        <div className="bg-primary-100 p-2 rounded-lg">
                            <Activity className="h-5 w-5 text-primary-600" />
                        </div>
                        BIN Allocation Summary
                    </CardTitle>
                    <div className="flex items-center gap-2">
                        <Badge variant="outline" className="bg-primary-50 text-primary-700 border-primary-200">
                            {binAllocations.length} Product{binAllocations.length !== 1 ? "s" : ""}
                        </Badge>
                        {lowStockProducts.length > 0 && (
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={handleManualEmailTrigger}
                                disabled={emailSending}
                                className="text-red-600 border-red-200 hover:bg-red-50 bg-transparent"
                            >
                                {emailSending ? (
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                                ) : (
                                    <Mail className="h-4 w-4 mr-2" />
                                )}
                                Send Alert
                            </Button>
                        )}
                    </div>
                </div>
                {/* Critical Threshold Info */}
                <div className="flex items-center gap-2 text-sm text-slate-600">
                    <Settings className="h-4 w-4" />
                    <span>Critical threshold: {companySettings.criticalThreshold.toLocaleString()} cards</span>
                    {companySettings.emailNotifications && (
                        <Badge variant="secondary" className="text-xs">
                            Email alerts enabled
                        </Badge>
                    )}
                </div>
            </CardHeader>
            <CardContent className="space-y-6">


                {/* Enhanced Low Stock Alert */}
                {lowStockProducts.length > 0 && (
                    <div className="p-4 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-lg">
                        <div className="flex items-start justify-between">
                            <div className="flex items-start gap-3">
                                <AlertTriangle className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
                                <div className="flex-1">
                                    <h6 className="text-sm font-semibold text-red-800 mb-1">Critical Stock Alert</h6>
                                    <p className="text-xs text-red-700 mb-2">
                                        {lowStockProducts.length} product{lowStockProducts.length !== 1 ? "s" : ""}{" "}
                                        {lowStockProducts.length === 1 ? "has" : "have"} less than{" "}
                                        {companySettings.criticalThreshold.toLocaleString()} cards remaining
                                    </p>
                                    {companySettings.emailNotifications && (
                                        <div className="flex items-center gap-2 text-xs text-red-600">
                                            <Mail className="h-3 w-3" />
                                            <span>Email notifications are enabled</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="flex items-center gap-2">
                                <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300 text-xs">
                                    {lowStockProducts.length}
                                </Badge>
                                {emailSending && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>}
                            </div>
                        </div>
                    </div>
                )}

                {/* Product Breakdown with Charts */}
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <h6 className="text-sm font-semibold text-slate-700 flex items-center gap-2">
                            <BarChart3 className="h-4 w-4" />
                            Product Breakdown
                        </h6>
                        <Badge variant="secondary" className="text-xs">
                            {binAllocations.length} Products
                        </Badge>
                    </div>
                    <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
                        {binAllocations.map((allocation, index) => {
                            const stats = calculateAllocationStats(
                                allocation.binStart,
                                allocation.binEnd,
                                allocation.total_usage || 0,
                                allocation.cards || [],
                            )
                            const statusInfo = getStatusInfo(stats.utilizationRate, stats.remaining)
                            const mockTrendData = generateMockTrendData()
                            const trendDirection = Math.random() > 0.5 ? "up" : "down"
                            const trendPercentage = (Math.random() * 20 + 5).toFixed(1)

                            return (
                                <div
                                    key={allocation._id}
                                    className={`group relative border rounded-xl p-4 transition-all duration-200 hover:shadow-lg ${statusInfo.borderColor} ${statusInfo.bgColor}`}
                                >
                                    {/* Header with Charts */}
                                    <div className="flex items-start justify-between mb-4">
                                        <div className="flex-1 min-w-0 pr-4">
                                            <div className="flex items-center gap-2 mb-1">
                                                <h4 className="font-semibold text-slate-900 text-sm leading-tight">
                                                    {allocation.product_version.version_name}
                                                </h4>
                                                <div className={`w-2 h-2 rounded-full ${statusInfo.statusColor}`}></div>
                                            </div>
                                            <p className="text-xs text-slate-600 mb-2">Version: {allocation.product_version.version_code}</p>
                                            <Badge
                                                variant="outline"
                                                className={`text-xs px-2 py-0.5 ${statusInfo.color} border-current bg-white/50`}
                                            >
                                                {statusInfo.status}
                                            </Badge>
                                        </div>
                                        {/* Chart Section */}

                                    </div>


                                    {/* Technical Details */}
                                    <div className="grid grid-cols-1 gap-2 text-xs mb-4">
                                        <div className="flex justify-between items-center py-2 px-3 bg-white/70 rounded-lg border border-white/50">
                                            <span className="text-slate-600 font-medium">Card Range:</span>
                                            <span className="font-mono text-slate-800 bg-slate-100 px-2 py-1 rounded">
                        {allocation.cardRange}
                      </span>
                                        </div>
                                        <div className="grid grid-cols-2 gap-2">
                                            <div className="flex justify-between items-center py-2 px-3 bg-white/70 rounded-lg border border-white/50">
                                                <span className="text-slate-600 font-medium">BIN Start:</span>
                                                <span className="font-mono text-slate-800 bg-slate-100 px-2 py-1 rounded text-xs">
                          {allocation.binStart}
                        </span>
                                            </div>
                                            <div className="flex justify-between items-center py-2 px-3 bg-white/70 rounded-lg border border-white/50">
                                                <span className="text-slate-600 font-medium">BIN End:</span>
                                                <span className="font-mono text-slate-800 bg-slate-100 px-2 py-1 rounded text-xs">
                          {allocation.binEnd}
                        </span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Statistics Grid */}
                                    <div className="grid grid-cols-4 gap-3 mb-4">
                                        <div className="text-center p-2 bg-white/70 rounded-lg border border-white/50">
                                            <div
                                                className={`text-sm font-bold ${stats.remaining < companySettings.criticalThreshold ? "text-red-700" : "text-slate-900"}`}
                                            >
                                                {stats.remaining.toLocaleString()}
                                            </div>
                                            <div className="text-xs text-slate-600">Available</div>
                                        </div>
                                    </div>

                                    {/* Critical Stock Warning */}
                                    {stats.remaining < companySettings.criticalThreshold && (
                                        <div className="p-3 bg-red-100 border border-red-200 rounded-lg flex items-center gap-2">
                                            <AlertTriangle className="h-4 w-4 text-red-600 flex-shrink-0" />
                                            <span className="text-xs text-red-700 font-medium">
                        Critical: Only {stats.remaining.toLocaleString()} cards remaining (below{" "}
                                                {companySettings.criticalThreshold.toLocaleString()} threshold)
                      </span>
                                        </div>
                                    )}

                                    {/* Hover Action Indicator */}
                                    <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <ChevronRight className="h-4 w-4 text-slate-400" />
                                    </div>
                                </div>
                            )
                        })}
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}
