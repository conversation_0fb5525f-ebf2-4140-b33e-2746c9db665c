//@ts-nocheck
"use client"

import React, {useEffect} from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  Upload,
  FileSpreadsheet,
  TrendingUp,
  Download,
  Database,
  Settings,
  ArrowLeft,
  Plus,
  CheckCircle,
  AlertTriangle,
  FileText,
  Activity,
  Globe,
  Shield,
  Zap,
  Package,
} from "lucide-react"
import * as XLSX from "xlsx"
import { DHLDatabaseViewer } from "@/components/dhl-database-viewer"
import {
  saveDHLRecordsWithDuplicateDetection,
  saveValidDHLRecords,
  type EnhancedDHLUploadResult,
} from "@/lib/enhanced-dhl-database"
import type { ValidationResult } from "@/lib/dhl-validation"
import type { DatabaseRecord } from "@/lib/database"
import type { DuplicateDetectionConfig as DuplicateConfig } from "@/lib/duplicate-detection"
import {useAppSelector} from "@/store/hooks";

interface ExcelData {
  headers: string[]
  rows: any[][]
  fileName: string
  sheetNames: string[]
  currentSheet: string
}

interface ColumnStats {
  name: string
  type: "number" | "text" | "date" | "mixed"
  count: number
  nullCount: number
  uniqueCount: number
  min?: number
  max?: number
  mean?: number
  median?: number
  mode?: any
}

interface SaveProgress {
  isVisible: boolean
  savedRecords: number
  totalRecords: number
  isComplete: boolean
  isError: boolean
  errorMessage?: string
  estimatedTimeRemaining?: number
}

type ViewMode = "database" | "upload"

const validateExcelFile = (headers: string[], rows: any[][]): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  // Required headers for DHL delivery methods (exact match)
  const requiredHeaders = [
    "Country",
    "DHL_ZONE_NAME",
    "DHL_EXPRESS_ZONE",
    "DHL_EXPRESS 50g",
    "DHL_EXPRESS 100g",
    "DHL_EXPRESS  DELIVERY TIMES",
    "DHL_Air 50g",
    "DHL_Air 100g",
    "DHL_AIR  DELIVERY TIMES",
    "DHL_ROAD_50g",
    "DHL_ROAD_100g",
    "DHL_ROAD  DELIVERY TIMES",
    "DHL_EXPRESS_ROAD_BULK 0.50kg EUR",
    "DHL_EXPRESS_ROAD_BULK DELIVERY TIMES",
  ]

  // Check for exact header matches
  const missingHeaders = requiredHeaders.filter((required) => !headers.includes(required))

  if (missingHeaders.length > 0) {
    errors.push(`Missing required headers: ${missingHeaders.join(", ")}`)
  }

  // Check for extra headers that shouldn't be there
  const extraHeaders = headers.filter((header) => !requiredHeaders.includes(header))
  if (extraHeaders.length > 0) {
    errors.push(`Unexpected headers found: ${extraHeaders.join(", ")}. Please use only the required headers.`)
  }

  // Check if headers are in correct order
  const correctOrder = requiredHeaders.every((header, index) => headers[index] === header)
  if (headers.length === requiredHeaders.length && !correctOrder) {
    errors.push(`Headers are not in the correct order. Expected order: ${requiredHeaders.join(", ")}`)
  }

  // Check data quality - allow empty rows during upload
  if (rows.length === 0) {
    errors.push("File contains no data rows")
  }

  // Check for price column formats
  const priceColumns = [
    "DHL_EXPRESS 50g",
    "DHL_EXPRESS 100g",
    "DHL_Air 50g",
    "DHL_Air 100g",
    "DHL_ROAD_50g",
    "DHL_ROAD_100g",
    "DHL_EXPRESS_ROAD_BULK 0.50kg EUR",
  ]

  priceColumns.forEach((priceColumn) => {
    const columnIndex = headers.indexOf(priceColumn)
    if (columnIndex !== -1 && rows.length > 0) {
      const invalidPrices = rows.filter((row) => {
        const priceValue = row[columnIndex]
        return (
            priceValue && priceValue !== "" && isNaN(Number.parseFloat(priceValue.toString().replace(/[^\d.-]/g, "")))
        )
      })

      if (invalidPrices.length > rows.length * 0.5) {
        errors.push(`More than 50% of values in ${priceColumn} are invalid`)
      }
    }
  })

  return {
    isValid: errors.length === 0,
    errors,
  }
}

// Modify the validateForSaving function to warn about empty countries but not block saving
const validateForSaving = (
    headers: string[],
    rows: any[][],
): { isValid: boolean; errors: string[]; warnings: string[] } => {
  const errors: string[] = []
  const warnings: string[] = []

  // Check for empty rows
  const emptyRows = rows.filter((row) => {
    // Check if all cells in the row are empty or null
    const isEmpty = row.every((cell) => !cell || cell.toString().trim() === "")
    return isEmpty
  })

  if (emptyRows.length > 0) {
    warnings.push(`Found ${emptyRows.length} empty row(s). These will be ignored during saving.`)
  }

  // Check for rows with missing critical data (Country field)
  const rowsWithMissingCountry = rows.filter((row) => {
    const countryValue = row[0] // Country is the first column
    return !countryValue || countryValue.toString().trim() === ""
  })

  if (rowsWithMissingCountry.length > 0) {
    warnings.push(
        `Found ${rowsWithMissingCountry.length} row(s) with missing Country data. These will be ignored during saving.`,
    )
  }

  return {
    isValid: true, // Always valid, we'll just filter out the problematic rows
    errors,
    warnings,
  }
}

const generateSampleExcelFile = () => {
  // Only headers, no data as requested
  const sampleData = [
    [
      "Country",
      "DHL_ZONE_NAME",
      "DHL_EXPRESS_ZONE",
      "DHL_EXPRESS 50g",
      "DHL_EXPRESS 100g",
      "DHL_EXPRESS  DELIVERY TIMES",
      "DHL_Air 50g",
      "DHL_Air 100g",
      "DHL_AIR  DELIVERY TIMES",
      "DHL_ROAD_50g",
      "DHL_ROAD_100g",
      "DHL_ROAD  DELIVERY TIMES",
      "DHL_EXPRESS_ROAD_BULK 0.50kg EUR",
      "DHL_EXPRESS_ROAD_BULK DELIVERY TIMES",
    ],
  ]

  const ws = XLSX.utils.aoa_to_sheet(sampleData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, "DHL Delivery Methods")
  XLSX.writeFile(wb, "dhl_delivery_methods_template.xlsx")
}

const formatColumnHeader = (header: string): string => {
  // For the specific DHL headers, keep them as-is but make them more readable
  if (header.includes("DHL_")) {
    return header.replace(/_/g, " ")
  }

  return header
      .replace(/_/g, " ")
      .replace(/([A-Z])/g, " $1")
      .replace(/\b\w/g, (l) => l.toUpperCase())
      .trim()
}

export default function DHLDeliveryMethods() {
  const [viewMode, setViewMode] = useState<ViewMode>("database")
  const [excelData, setExcelData] = useState<ExcelData | null>(null)
  const [loading, setLoading] = useState(false)
  const [columnStats, setColumnStats] = useState<ColumnStats[]>([])
  const [selectedColumns, setSelectedColumns] = useState<string[]>([])
  const [saveProgress, setSaveProgress] = useState<SaveProgress>({
    isVisible: false,
    savedRecords: 0,
    totalRecords: 0,
    isComplete: false,
    isError: false,
  })
  const [showUploadManager, setShowUploadManager] = useState(false)
  const [showDuplicateConfig, setShowDuplicateConfig] = useState(false)
  const [showComparisonResults, setShowComparisonResults] = useState(false)
  const [showValidationResults, setShowValidationResults] = useState(false)
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null)
  const [duplicateConfig, setDuplicateConfig] = useState<DuplicateConfig & { compareWithDatabase: boolean }>({
    primaryKey: ["Country"], // Only Country for strict uniqueness
    strategy: "replace",
    compareWithDatabase: true,
  })
  const [lastUploadResult, setLastUploadResult] = useState<EnhancedDHLUploadResult | null>(null)
  const [validateBeforeSave, setValidateBeforeSave] = useState(true)
  // User permissions
  const user = useAppSelector((state) => state.user.user)
  const [roles, setRoles] = useState<Role[]>([])

  useEffect(() => {
    setRoles(user.roles || [])
  }, [user.roles])

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setLoading(true)
    try {
      const arrayBuffer = await file.arrayBuffer()
      const workbook = XLSX.read(arrayBuffer, { type: "array" })
      const sheetNames = workbook.SheetNames
      const firstSheet = workbook.Sheets[sheetNames[0]]
      const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 })

      const headers = jsonData[0] as string[]
      const rows = jsonData.slice(1) as any[][]

      // Validate the Excel file
      const validation = validateExcelFile(headers, rows)

      if (!validation.isValid) {
        alert(
            `Invalid file format:\n\n${validation.errors.join("\n")}\n\nPlease upload a valid DHL delivery methods file with the exact required headers or download the template file.`,
        )
        setLoading(false)
        return
      }

      const newData: ExcelData = {
        headers,
        rows,
        fileName: file.name,
        sheetNames,
        currentSheet: sheetNames[0],
      }

      setExcelData(newData)
      calculateColumnStats(headers, rows)
      setSelectedColumns(headers.slice(0, 2))

      // Set duplicate detection to use only Country for strict uniqueness
      setDuplicateConfig((prev) => ({
        ...prev,
        primaryKey: ["Country"], // Only Country matters for uniqueness
      }))
    } catch (error) {
      console.error("Error reading Excel file:", error)
      alert("Error reading Excel file. Please ensure it's a valid Excel file (.xlsx or .xls)")
    } finally {
      setLoading(false)
    }
  }
  const getAllPermissions = (roles: Role[]): string[] => {
    const allPermissions = roles.flatMap((role) => role.permissions)
    return Array.from(new Set(allPermissions))
  }
  const hasPermission = (roles: Role[], permission: string): boolean => {
    if (!roles || roles.length === 0) {
      return true
    }
    const allPermissions = getAllPermissions(roles)
    return allPermissions.includes(permission)
  }

  const userHasImportPermission = hasPermission(roles, "DHL Bulk Import_Import")
  const userHasViewPermission = hasPermission(roles, "DHL Bulk Import_View")

  const calculateColumnStats = (headers: string[], rows: any[][]) => {
    const stats: ColumnStats[] = headers.map((header, index) => {
      const columnData = rows.map((row) => row[index]).filter((val) => val !== undefined && val !== null && val !== "")
      const nullCount = rows.length - columnData.length

      // Determine data type
      const numericData = columnData.filter((val) => !isNaN(Number(val)) && val !== "").map(Number)
      const isNumeric = numericData.length > columnData.length * 0.8

      let type: "number" | "text" | "date" | "mixed" = "text"
      if (isNumeric) type = "number"
      else if (columnData.some((val) => !isNaN(Date.parse(val)))) type = "date"
      else if (columnData.length > 0) type = "mixed"

      // Calculate statistics for numeric columns
      let min, max, mean, median
      if (type === "number" && numericData.length > 0) {
        min = Math.min(...numericData)
        max = Math.max(...numericData)
        mean = numericData.reduce((a, b) => a + b, 0) / numericData.length
        const sorted = [...numericData].sort((a, b) => a - b)
        median =
            sorted.length % 2 === 0
                ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
                : sorted[Math.floor(sorted.length / 2)]
      }

      // Calculate mode
      const frequency: { [key: string]: number } = {}
      columnData.forEach((val) => {
        const key = String(val)
        frequency[key] = (frequency[key] || 0) + 1
      })
      const mode = Object.keys(frequency).reduce((a, b) => (frequency[a] > frequency[b] ? a : b), "")

      return {
        name: header,
        type,
        count: columnData.length,
        nullCount,
        uniqueCount: new Set(columnData).size,
        min,
        max,
        mean,
        median,
        mode: type === "number" ? Number(mode) : mode,
      }
    })

    setColumnStats(stats)
  }

  // Modify the saveToDatabase function to filter out rows with empty countries
  const saveToDatabase = async () => {
    if (!excelData) return

    // Validate for saving (check for empty rows)
    const saveValidation = validateForSaving(excelData.headers, excelData.rows)

    // Show warnings but don't block saving
    if (saveValidation.warnings.length > 0) {
      const proceed = window.confirm(
          `Warning:\n\n${saveValidation.warnings.join("\n")}\n\nDo you want to proceed with saving valid rows?`,
      )
      if (!proceed) return
    }

    // Filter out rows with empty countries
    const validRows = excelData.rows.filter((row) => {
      const countryValue = row[0] // Country is the first column
      return countryValue && countryValue.toString().trim() !== ""
    })

    if (validRows.length === 0) {
      alert("No valid rows to save. All rows have empty Country values.")
      return
    }

    // Reset progress state
    setSaveProgress({
      isVisible: true,
      savedRecords: 0,
      totalRecords: validRows.length, // Use filtered count
      isComplete: false,
      isError: false,
    })

    try {
      // Prepare records for database - only include rows with countries
      const records: DatabaseRecord[] = validRows.map((row, index) => {
        const rowData: Record<string, any> = {}
        excelData.headers.forEach((header, colIndex) => {
          rowData[header] = row[colIndex] || null
        })

        return {
          fileName: excelData.fileName,
          sheetName: excelData.currentSheet,
          headers: excelData.headers,
          rowData,
          rowIndex: index,
          totalRows: validRows.length,
        }
      })

      // Save with duplicate detection and database comparison
      const result = await saveDHLRecordsWithDuplicateDetection(
          records,
          {
            duplicateDetection: duplicateConfig,
            compareWithDatabase: duplicateConfig.compareWithDatabase,
            validateBeforeSave,
          },
          (saved, total, status) => {
            setSaveProgress((prev) => ({
              ...prev,
              savedRecords: saved,
              estimatedTimeRemaining: saved > 0 ? ((total - saved) / saved) * 1000 : undefined,
            }))
          },
      )

      // Store result for display
      setLastUploadResult(result)

      // If validation failed, show validation results
      if (result.validationResult && !result.validationResult.isValid) {
        setValidationResult(result.validationResult)
        setShowValidationResults(true)

        // Hide progress modal
        setSaveProgress((prev) => ({
          ...prev,
          isVisible: false,
        }))

        return
      }

      // Mark as complete
      setSaveProgress((prev) => ({
        ...prev,
        isComplete: true,
      }))

      // Show comparison results if available
      if (result.comparisonResult) {
        setShowComparisonResults(true)
      }

      // Show success message and return to database view
      alert(
          `Upload completed! New countries: ${result.savedRecords}, Updated countries: ${result.replacedRecords}, Duplicates handled: ${result.duplicatesHandled}`,
      )

      // Return to database view after successful upload
      setTimeout(() => {
        setViewMode("database")
        setExcelData(null) // Clear the uploaded data
      }, 2000)
    } catch (error) {
      console.error("Error saving to database:", error)
      setSaveProgress((prev) => ({
        ...prev,
        isError: true,
        errorMessage: error instanceof Error ? error.message : "Unknown error occurred",
      }))
    }
  }

  // Modify the handleProceedWithValid function to also filter out rows with empty countries
  const handleProceedWithValid = async () => {
    if (!excelData || !validationResult) return
    setShowValidationResults(false)

    // Filter out rows with empty countries
    const validRowsWithCountries = excelData.rows.filter((row, index) => {
      // Check if this record has any validation errors
      const hasError = validationResult.errors.some((error) => {
        // Safely handle both 'row' and 'rowIndex' properties
        const errorRow = error.row !== undefined ? error.row : error.rowIndex
        return errorRow === index
      })

      // Also check if country is empty
      const countryValue = row[0] // Country is the first column
      const hasCountry = countryValue && countryValue.toString().trim() !== ""

      return !hasError && hasCountry
    })

    if (validRowsWithCountries.length === 0) {
      alert("No valid rows to save. All rows either have validation errors or empty Country values.")
      return
    }

    // Reset progress state
    setSaveProgress({
      isVisible: true,
      savedRecords: 0,
      totalRecords: validRowsWithCountries.length,
      isComplete: false,
      isError: false,
    })

    try {
      // Prepare records for database - only include valid rows with countries
      const records: DatabaseRecord[] = validRowsWithCountries.map((row, index) => {
        const rowData: Record<string, any> = {}
        excelData.headers.forEach((header, colIndex) => {
          rowData[header] = row[colIndex] || null
        })

        return {
          fileName: excelData.fileName,
          sheetName: excelData.currentSheet,
          headers: excelData.headers,
          rowData,
          rowIndex: index,
          totalRows: validRowsWithCountries.length,
        }
      })

      // Save only valid records
      const result = await saveValidDHLRecords(
          records,
          validationResult,
          {
            duplicateDetection: duplicateConfig,
            compareWithDatabase: duplicateConfig.compareWithDatabase,
            validateBeforeSave: false, // Already validated
          },
          (saved, total, status) => {
            setSaveProgress((prev) => ({
              ...prev,
              savedRecords: saved,
              estimatedTimeRemaining: saved > 0 ? ((total - saved) / saved) * 1000 : undefined,
            }))
          },
      )

      // Store result for display
      setLastUploadResult(result)

      // Mark as complete
      setSaveProgress((prev) => ({
        ...prev,
        isComplete: true,
      }))

      // Show comparison results if available
      if (result.comparisonResult) {
        setShowComparisonResults(true)
      }

      // Show success message and return to database view
      alert(
          `Upload completed! New countries: ${result.savedRecords}, Updated countries: ${result.replacedRecords}, Duplicates handled: ${result.duplicatesHandled}`,
      )

      // Return to database view after successful upload
      setTimeout(() => {
        setViewMode("database")
        setExcelData(null) // Clear the uploaded data
      }, 2000)
    } catch (error) {
      console.error("Error saving to database:", error)
      setSaveProgress((prev) => ({
        ...prev,
        isError: true,
        errorMessage: error instanceof Error ? error.message : "Unknown error occurred",
      }))
    }
  }

  const changeSheet = async (sheetName: string) => {
    if (!excelData) return

    setLoading(true)
    try {
      // We need to re-read the file to get the specific sheet
      // For now, we'll simulate this by updating the current sheet name
      // In a real implementation, you'd store the original file data
      const updatedData = { ...excelData, currentSheet: sheetName }
      setExcelData(updatedData)

      // Note: In a full implementation, you would:
      // 1. Store the original ArrayBuffer when first uploading
      // 2. Re-parse the specific sheet here
      // 3. Update the data accordingly

      console.log(`Switched to sheet: ${sheetName}`)
    } finally {
      setLoading(false)
    }
  }

  const exportData = () => {
    if (!excelData) return

    const ws = XLSX.utils.aoa_to_sheet([excelData.headers, ...excelData.rows])
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, "Analyzed Data")
    XLSX.writeFile(wb, `analyzed_${excelData.fileName}`)
  }

  const getChartData = () => {
    if (!excelData || selectedColumns.length < 2) return []

    const xIndex = excelData.headers.indexOf(selectedColumns[0])
    const yIndex = excelData.headers.indexOf(selectedColumns[1])

    return excelData.rows
        .slice(0, 10)
        .map((row) => ({
          x: row[xIndex],
          y: Number(row[yIndex]) || 0,
        }))
        .filter((item) => item.x && !isNaN(item.y))
  }

  // Database View Component
  const DatabaseView = () => (
      <div className="min-h-screen  ">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center space-x-4">
                <div className="flex items-center justify-center w-12 h-12 bg-primary rounded-xl">
                  <Package className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">DHL Delivery Methods</h1>
                  <p className="text-sm text-gray-500">Manage global shipping configurations</p>
                </div>
              </div>
              {userHasImportPermission && (
              <Button
                  onClick={() => setViewMode("upload")}
                  className="bg-primary hover:bg-primary/90 text-white px-6 py-2 rounded-lg font-medium"
              >
                <Plus className="w-4 h-4 mr-2" />
                Upload Data
              </Button>
              )}
            </div>
          </div>
        </div>

        <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Main Content */}
          <div className="space-y-6">
            <Card className="bg-white border-0 shadow-sm">
              <CardHeader className="border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg font-semibold text-gray-900">Database Management</CardTitle>
                    <CardDescription className="text-gray-500">
                      View and manage your DHL delivery methods database
                    </CardDescription>
                  </div>
                  <div className="flex space-x-3">
                    <Button
                        onClick={() => setShowUploadManager(true)}
                        variant="outline"
                        className="border-gray-200 text-gray-700 hover:bg-gray-50"
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      Settings
                    </Button>
                  </div>
                </div>
              </CardHeader>
            </Card>

            <Card className="bg-white border-0 shadow-sm">
              <DHLDatabaseViewer isOpen={true} onClose={() => {}} />
            </Card>
          </div>
        </div>
      </div>
  )

  // Upload View Component
  const UploadView = () => (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center space-x-4">
                <Button
                    onClick={() => setViewMode("database")}
                    variant="outline"
                    className="border-gray-200 text-gray-700 hover:bg-gray-50"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Upload & Analyze</h1>
                  <p className="text-sm text-gray-500">Import DHL delivery methods data</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {!excelData ? (
              <div className="max-w-4xl mx-auto">
                {/* Upload Section */}
                <Card className="bg-white border-0 shadow-sm mb-8">
                  <CardContent className="p-8">
                    <div className="text-center">
                      <div className="flex items-center justify-center w-16 h-16 bg-primary/10 rounded-2xl mx-auto mb-6">
                        <Upload className="w-8 h-8 text-primary" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">Upload Excel File</h3>
                      <p className="text-gray-500 mb-8">
                        Select an Excel file with DHL delivery methods data to begin analysis
                      </p>

                      <div className="border-2 border-dashed border-gray-300 rounded-xl p-12 hover:border-gray-400 transition-colors">
                        <FileSpreadsheet className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                        <Label htmlFor="file-upload" className="cursor-pointer">
                          <span className="text-lg font-medium text-gray-700">Drop files here or click to browse</span>
                        </Label>
                        <Input
                            id="file-upload"
                            type="file"
                            accept=".xlsx,.xls"
                            onChange={handleFileUpload}
                            className="hidden"
                        />
                        <p className="text-sm text-gray-500 mt-2">Supports .xlsx and .xls files</p>
                      </div>

                      <div className="mt-6">
                        <Button
                            onClick={generateSampleExcelFile}
                            variant="outline"
                            className="border-gray-200 text-gray-700 hover:bg-gray-50"
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Download Template
                        </Button>
                      </div>

                      {loading && (
                          <div className="mt-8">
                            <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-primary mb-2"></div>
                            <p className="text-gray-600">Processing file...</p>
                          </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Information Grid */}
                <div className="grid md:grid-cols-2 gap-6">
                  <Card className="bg-white border-0 shadow-sm">
                    <CardHeader>
                      <CardTitle className="flex items-center text-gray-900">
                        <FileText className="w-5 h-5 mr-2" />
                        Required Format
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h4 className="font-medium text-gray-900 mb-2">Headers (in exact order):</h4>
                          <div className="text-xs font-mono text-gray-600 space-y-1 max-h-32 overflow-y-auto">
                            {[
                              "Country",
                              "DHL_ZONE_NAME",
                              "DHL_EXPRESS_ZONE",
                              "DHL_EXPRESS 50g",
                              "DHL_EXPRESS 100g",
                              "DHL_EXPRESS DELIVERY TIMES",
                              "DHL_Air 50g",
                              "DHL_Air 100g",
                              "DHL_AIR DELIVERY TIMES",
                              "DHL_ROAD_50g",
                              "DHL_ROAD_100g",
                              "DHL_ROAD DELIVERY TIMES",
                              "DHL_EXPRESS_ROAD_BULK 0.50kg EUR",
                              "DHL_EXPRESS_ROAD_BULK DELIVERY TIMES",
                            ].map((header, index) => (
                                <div key={index} className="flex items-center">
                                  <div className="w-1.5 h-1.5 bg-primary rounded-full mr-2"></div>
                                  {header}
                                </div>
                            ))}
                          </div>
                        </div>
                        <p className="text-sm text-gray-600">Headers must match exactly and be in the specified order</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white border-0 shadow-sm">
                    <CardHeader>
                      <CardTitle className="flex items-center text-gray-900">
                        <Shield className="w-5 h-5 mr-2" />
                        Data Rules
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                          <div>
                            <p className="font-medium text-gray-900">Unique Countries</p>
                            <p className="text-sm text-gray-600">Each country must be unique across the database</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                          <div>
                            <p className="font-medium text-gray-900">Update Policy</p>
                            <p className="text-sm text-gray-600">Existing countries will be updated with new data</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                          <div>
                            <p className="font-medium text-gray-900">Validation</p>
                            <p className="text-sm text-gray-600">All data is validated before saving</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
          ) : (
              <div className="space-y-6">
                {/* File Info */}
                <Card className="bg-white border-0 shadow-sm">
                  <CardHeader className="border-b border-gray-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg">
                          <FileSpreadsheet className="w-6 h-6 text-green-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg font-semibold text-gray-900">{excelData.fileName}</CardTitle>
                          <CardDescription className="text-gray-500">
                            {excelData.rows.length.toLocaleString()} rows × {excelData.headers.length} columns
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex space-x-3">
                        <Select value={excelData.currentSheet} onValueChange={changeSheet}>
                          <SelectTrigger className="w-40">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {excelData.sheetNames.map((name) => (
                                <SelectItem key={name} value={name}>
                                  {name}
                                </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Button
                            onClick={() => setShowDuplicateConfig(true)}
                            variant="outline"
                            className="border-gray-200 text-gray-700 hover:bg-gray-50"
                        >
                          <Settings className="w-4 h-4 mr-2" />
                          Configure
                        </Button>
                        <Button onClick={saveToDatabase} className="bg-primary hover:bg-primary/90 text-white">
                          <Database className="w-4 h-4 mr-2" />
                          Save to Database
                        </Button>
                        <Button
                            onClick={exportData}
                            variant="outline"
                            className="border-gray-200 text-gray-700 hover:bg-gray-50"
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Export
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                </Card>

                {/* Configuration Status */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className={`border-0 shadow-sm ${validateBeforeSave ? "bg-primary/5" : "bg-gray-50"}`}>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-lg ${validateBeforeSave ? "bg-primary/10" : "bg-gray-100"}`}>
                            {validateBeforeSave ? (
                                <CheckCircle className="h-5 w-5 text-primary" />
                            ) : (
                                <AlertTriangle className="h-5 w-5 text-gray-600" />
                            )}
                          </div>
                          <div>
                            <p className={`font-semibold ${validateBeforeSave ? "text-primary" : "text-gray-800"}`}>
                              Data Validation
                            </p>
                            <p className={`text-sm ${validateBeforeSave ? "text-primary" : "text-gray-600"}`}>
                              {validateBeforeSave ? "Enabled" : "Disabled"}
                            </p>
                          </div>
                        </div>
                        <Button
                            onClick={() => setValidateBeforeSave(!validateBeforeSave)}
                            variant="ghost"
                            size="sm"
                            className={
                              validateBeforeSave ? "text-primary hover:bg-primary/10" : "text-gray-700 hover:bg-gray-100"
                            }
                        >
                          <Zap className="w-4 h-4 mr-1" />
                          Toggle
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-gray-50 border-0 shadow-sm">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-gray-100 rounded-lg">
                            <Shield className="h-5 w-5 text-gray-600" />
                          </div>
                          <div>
                            <p className="font-semibold text-gray-800">Duplicate Detection</p>
                            <p className="text-sm text-gray-600">
                              {duplicateConfig.strategy} | {duplicateConfig.primaryKey.join(" + ")}
                            </p>
                          </div>
                        </div>
                        <Button
                            onClick={() => setShowDuplicateConfig(true)}
                            variant="ghost"
                            size="sm"
                            className="text-gray-700 hover:bg-gray-100"
                        >
                          <Settings className="w-4 h-4 mr-1" />
                          Configure
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Data Analysis */}
                <Card className="bg-white border-0 shadow-sm">
                  <Tabs defaultValue="overview" className="w-full">
                    <div className="border-b border-gray-100">
                      <TabsList className="grid w-full grid-cols-4 bg-transparent h-12">
                        <TabsTrigger
                            value="overview"
                            className="data-[state=active]:bg-white data-[state=active]:shadow-sm"
                        >
                          Overview
                        </TabsTrigger>
                        <TabsTrigger value="data" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
                          Data
                        </TabsTrigger>
                        <TabsTrigger
                            value="statistics"
                            className="data-[state=active]:bg-white data-[state=active]:shadow-sm"
                        >
                          Statistics
                        </TabsTrigger>
                        <TabsTrigger value="charts" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
                          Charts
                        </TabsTrigger>
                      </TabsList>
                    </div>

                    <TabsContent value="overview" className="p-6 space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <Card className="bg-primary/5 border-0">
                          <CardContent className="p-6">
                            <div className="flex items-center">
                              <div className="flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg">
                                <Globe className="w-6 h-6 text-primary" />
                              </div>
                              <div className="ml-4">
                                <p className="text-sm font-medium text-primary">Countries</p>
                                <p className="text-2xl font-bold text-primary">{excelData.rows.length.toLocaleString()}</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        <Card className="bg-gray-50 border-0">
                          <CardContent className="p-6">
                            <div className="flex items-center">
                              <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-lg">
                                <FileText className="w-6 h-6 text-gray-600" />
                              </div>
                              <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Columns</p>
                                <p className="text-2xl font-bold text-gray-900">{excelData.headers.length}</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        <Card className="bg-gray-50 border-0">
                          <CardContent className="p-6">
                            <div className="flex items-center">
                              <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-lg">
                                <Activity className="w-6 h-6 text-gray-600" />
                              </div>
                              <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Data Types</p>
                                <p className="text-2xl font-bold text-gray-900">
                                  {Array.from(new Set(columnStats.map((stat) => stat.type))).length}
                                </p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>

                      <Card className="bg-white border border-gray-200">
                        <CardHeader>
                          <CardTitle className="text-lg font-semibold text-gray-900">Column Analysis</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ScrollArea className="h-80">
                            <div className="space-y-3">
                              {columnStats.map((stat, index) => (
                                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div className="flex items-center space-x-3">
                                      <div className="w-3 h-3 bg-primary rounded-full"></div>
                                      <div>
                                        <span className="font-medium text-gray-900">{formatColumnHeader(stat.name)}</span>
                                        <div className="flex items-center space-x-2 mt-1">
                                          <Badge variant="outline" className="text-xs">
                                            {stat.type}
                                          </Badge>
                                          {duplicateConfig.primaryKey.includes(stat.name) && (
                                              <Badge className="text-xs bg-primary/10 text-primary">Primary</Badge>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                    <div className="text-right">
                                      <div className="text-sm font-medium text-gray-700">
                                        {stat.count.toLocaleString()} values
                                      </div>
                                      <div className="text-xs text-gray-500">{stat.uniqueCount.toLocaleString()} unique</div>
                                    </div>
                                  </div>
                              ))}
                            </div>
                          </ScrollArea>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="data" className="p-6">
                      <Card className="bg-white border border-gray-200 shadow-sm">
                        <CardHeader className="bg-gray-50 border-b border-gray-200">
                          <CardTitle className="text-lg font-semibold text-gray-900">Data Preview</CardTitle>
                          <CardDescription className="text-gray-500">First 100 rows of your data</CardDescription>
                        </CardHeader>
                        <CardContent className="p-0">
                          <ScrollArea className="h-96 w-full">
                            <div className="overflow-x-auto">
                              <Table>
                                <TableHeader className="sticky top-0 bg-white border-b-2 border-gray-200">
                                  <TableRow>
                                    {excelData.headers.map((header, index) => (
                                        <TableHead
                                            key={index}
                                            className="whitespace-nowrap font-semibold text-gray-900 bg-gray-50 border-r border-gray-200 last:border-r-0 px-4 py-3"
                                        >
                                          <div className="flex items-center space-x-2">
                                            <span>{formatColumnHeader(header)}</span>
                                            {duplicateConfig.primaryKey.includes(header) && (
                                                <Badge className="text-xs bg-primary/10 text-primary border-primary/20">
                                                  PK
                                                </Badge>
                                            )}
                                          </div>
                                        </TableHead>
                                    ))}
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {excelData.rows.slice(0, 100).map((row, rowIndex) => (
                                      <TableRow
                                          key={rowIndex}
                                          className={`hover:bg-primary/5 ${rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50"} border-b border-gray-100`}
                                      >
                                        {row.map((cell, cellIndex) => (
                                            <TableCell
                                                key={cellIndex}
                                                className="whitespace-nowrap text-gray-700 border-r border-gray-100 last:border-r-0 px-4 py-3 font-medium"
                                            >
                                              <div className="max-w-32 truncate" title={cell?.toString() || ""}>
                                                {cell?.toString() || "—"}
                                              </div>
                                            </TableCell>
                                        ))}
                                      </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>
                          </ScrollArea>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="statistics" className="p-6">
                      <Card className="bg-white border border-gray-200 shadow-sm">
                        <CardHeader className="bg-gray-50 border-b border-gray-200">
                          <CardTitle className="text-lg font-semibold text-gray-900">Detailed Statistics</CardTitle>
                        </CardHeader>
                        <CardContent className="p-0">
                          <ScrollArea className="h-96">
                            <Table>
                              <TableHeader className="sticky top-0 bg-white border-b-2 border-gray-200">
                                <TableRow>
                                  <TableHead className="font-semibold text-gray-900 bg-gray-50 px-4 py-3 border-r border-gray-200">
                                    Column
                                  </TableHead>
                                  <TableHead className="font-semibold text-gray-900 bg-gray-50 px-4 py-3 border-r border-gray-200">
                                    Type
                                  </TableHead>
                                  <TableHead className="font-semibold text-gray-900 bg-gray-50 px-4 py-3 border-r border-gray-200">
                                    Count
                                  </TableHead>
                                  <TableHead className="font-semibold text-gray-900 bg-gray-50 px-4 py-3 border-r border-gray-200">
                                    Null
                                  </TableHead>
                                  <TableHead className="font-semibold text-gray-900 bg-gray-50 px-4 py-3 border-r border-gray-200">
                                    Unique
                                  </TableHead>
                                  <TableHead className="font-semibold text-gray-900 bg-gray-50 px-4 py-3 border-r border-gray-200">
                                    Min
                                  </TableHead>
                                  <TableHead className="font-semibold text-gray-900 bg-gray-50 px-4 py-3 border-r border-gray-200">
                                    Max
                                  </TableHead>
                                  <TableHead className="font-semibold text-gray-900 bg-gray-50 px-4 py-3 border-r border-gray-200">
                                    Mean
                                  </TableHead>
                                  <TableHead className="font-semibold text-gray-900 bg-gray-50 px-4 py-3 border-r border-gray-200">
                                    Median
                                  </TableHead>
                                  <TableHead className="font-semibold text-gray-900 bg-gray-50 px-4 py-3">Mode</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {columnStats.map((stat, index) => (
                                    <TableRow
                                        key={index}
                                        className={`hover:bg-primary/5 ${index % 2 === 0 ? "bg-white" : "bg-gray-50"} border-b border-gray-100`}
                                    >
                                      <TableCell className="font-semibold text-gray-900 px-4 py-3 border-r border-gray-100">
                                        <div className="flex items-center space-x-2">
                                          <span>{formatColumnHeader(stat.name)}</span>
                                          {duplicateConfig.primaryKey.includes(stat.name) && (
                                              <Badge className="text-xs bg-primary/10 text-primary border-primary/20">PK</Badge>
                                          )}
                                        </div>
                                      </TableCell>
                                      <TableCell className="px-4 py-3 border-r border-gray-100">
                                        <Badge variant="outline" className="capitalize font-medium border-gray-300">
                                          {stat.type}
                                        </Badge>
                                      </TableCell>
                                      <TableCell className="text-gray-700 font-medium px-4 py-3 border-r border-gray-100">
                                        {stat.count.toLocaleString()}
                                      </TableCell>
                                      <TableCell className="text-gray-700 font-medium px-4 py-3 border-r border-gray-100">
                                        {stat.nullCount.toLocaleString()}
                                      </TableCell>
                                      <TableCell className="text-gray-700 font-medium px-4 py-3 border-r border-gray-100">
                                        {stat.uniqueCount.toLocaleString()}
                                      </TableCell>
                                      <TableCell className="text-gray-700 font-medium px-4 py-3 border-r border-gray-100">
                                        {stat.min?.toFixed(2) || "—"}
                                      </TableCell>
                                      <TableCell className="text-gray-700 font-medium px-4 py-3 border-r border-gray-100">
                                        {stat.max?.toFixed(2) || "—"}
                                      </TableCell>
                                      <TableCell className="text-gray-700 font-medium px-4 py-3 border-r border-gray-100">
                                        {stat.mean?.toFixed(2) || "—"}
                                      </TableCell>
                                      <TableCell className="text-gray-700 font-medium px-4 py-3 border-r border-gray-100">
                                        {stat.median?.toFixed(2) || "—"}
                                      </TableCell>
                                      <TableCell className="text-gray-700 font-medium px-4 py-3">
                                        <div className="max-w-24 truncate" title={stat.mode?.toString() || ""}>
                                          {stat.mode?.toString() || "—"}
                                        </div>
                                      </TableCell>
                                    </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </ScrollArea>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="charts" className="p-6">
                      <Card className="bg-white border border-gray-200">
                        <CardHeader>
                          <CardTitle className="text-lg font-semibold text-gray-900">Data Visualization</CardTitle>
                          <CardDescription className="text-gray-500">Create charts from your data</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <Label className="text-sm font-medium text-gray-700">X-Axis Column</Label>
                              <Select
                                  value={selectedColumns[0]}
                                  onValueChange={(value) => setSelectedColumns([value, selectedColumns[1]])}
                              >
                                <SelectTrigger className="mt-2">
                                  <SelectValue placeholder="Select column" />
                                </SelectTrigger>
                                <SelectContent>
                                  {excelData.headers.map((header) => (
                                      <SelectItem key={header} value={header}>
                                        {formatColumnHeader(header)}
                                      </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-gray-700">Y-Axis Column</Label>
                              <Select
                                  value={selectedColumns[1]}
                                  onValueChange={(value) => setSelectedColumns([selectedColumns[0], value])}
                              >
                                <SelectTrigger className="mt-2">
                                  <SelectValue placeholder="Select column" />
                                </SelectTrigger>
                                <SelectContent>
                                  {excelData.headers
                                      .filter((header) => columnStats.find((stat) => stat.name === header)?.type === "number")
                                      .map((header) => (
                                          <SelectItem key={header} value={header}>
                                            {formatColumnHeader(header)}
                                          </SelectItem>
                                      ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          {selectedColumns.length === 2 && (
                              <div className="space-y-6">
                                <div className="h-80 border-2 border-dashed border-gray-300 rounded-xl flex items-center justify-center bg-gray-50">
                                  <div className="text-center">
                                    <TrendingUp className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                                    <p className="text-lg font-medium text-gray-600 mb-2">Chart Visualization</p>
                                    <p className="text-sm text-gray-500">
                                      {formatColumnHeader(selectedColumns[0])} vs {formatColumnHeader(selectedColumns[1])}
                                    </p>
                                    <div className="mt-4 text-xs text-gray-400">{getChartData().length} data points</div>
                                  </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                  <Card className="bg-gray-50 border border-gray-200">
                                    <CardHeader className="pb-3">
                                      <CardTitle className="text-sm font-semibold text-gray-900">Sample Data</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                      <div className="space-y-2">
                                        {getChartData()
                                            .slice(0, 5)
                                            .map((point, index) => (
                                                <div key={index} className="flex justify-between text-sm">
                                                  <span className="text-gray-600 truncate mr-2">{point.x}</span>
                                                  <span className="font-medium text-gray-900">{point.y}</span>
                                                </div>
                                            ))}
                                      </div>
                                    </CardContent>
                                  </Card>

                                  <Card className="bg-gray-50 border border-gray-200">
                                    <CardHeader className="pb-3">
                                      <CardTitle className="text-sm font-semibold text-gray-900">Statistics</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                      <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                          <span className="text-gray-600">Data Points:</span>
                                          <span className="font-medium text-gray-900">{getChartData().length}</span>
                                        </div>
                                        <div className="flex justify-between">
                                          <span className="text-gray-600">X-Axis:</span>
                                          <span className="font-medium text-gray-900 truncate ml-2">
                                      {formatColumnHeader(selectedColumns[0])}
                                    </span>
                                        </div>
                                        <div className="flex justify-between">
                                          <span className="text-gray-600">Y-Axis:</span>
                                          <span className="font-medium text-gray-900 truncate ml-2">
                                      {formatColumnHeader(selectedColumns[1])}
                                    </span>
                                        </div>
                                      </div>
                                    </CardContent>
                                  </Card>
                                </div>
                              </div>
                          )}
                        </CardContent>
                      </Card>
                    </TabsContent>
                  </Tabs>
                </Card>
              </div>
          )}
        </div>
      </div>
  )

  return (
      <div className="min-h-screen">
        {viewMode === "database" ? <DatabaseView /> : <UploadView />}

        {/* Save Progress Modal */}
        {saveProgress.isVisible && (
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
              <Card className="w-full max-w-md bg-white border-0 shadow-xl">
                <CardHeader className="text-center pb-4">
                  <div className="flex items-center justify-center w-16 h-16 bg-primary/10 rounded-2xl mx-auto mb-4">
                    {saveProgress.isComplete ? (
                        <CheckCircle className="w-8 h-8 text-green-600" />
                    ) : saveProgress.isError ? (
                        <AlertTriangle className="w-8 h-8 text-primary" />
                    ) : (
                        <Database className="w-8 h-8 text-primary" />
                    )}
                  </div>
                  <CardTitle className="text-xl font-semibold text-gray-900">
                    {saveProgress.isComplete
                        ? "Upload Complete!"
                        : saveProgress.isError
                            ? "Upload Error"
                            : "Saving to Database"}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {saveProgress.isError ? (
                      <div className="text-center space-y-4">
                        <div className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
                          <p className="text-primary font-medium mb-2">Error occurred during upload</p>
                          <p className="text-sm text-primary">{saveProgress.errorMessage}</p>
                        </div>
                        <Button
                            onClick={() => setSaveProgress((prev) => ({ ...prev, isVisible: false }))}
                            className="w-full bg-primary hover:bg-primary/90 text-white"
                        >
                          Close
                        </Button>
                      </div>
                  ) : saveProgress.isComplete ? (
                      <div className="text-center space-y-4">
                        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                          <p className="text-green-800 font-medium mb-2">Upload completed successfully!</p>
                          <p className="text-sm text-green-600">{saveProgress.savedRecords.toLocaleString()} records saved</p>
                        </div>
                        <Button
                            onClick={() => setSaveProgress((prev) => ({ ...prev, isVisible: false }))}
                            className="w-full bg-green-600 hover:bg-green-700 text-white"
                        >
                          Close
                        </Button>
                      </div>
                  ) : (
                      <div className="space-y-6">
                        <div className="space-y-3">
                          <div className="flex justify-between text-sm font-medium">
                            <span className="text-gray-600">Progress</span>
                            <span className="text-gray-900">
                        {saveProgress.savedRecords.toLocaleString()} / {saveProgress.totalRecords.toLocaleString()}
                      </span>
                          </div>
                          <Progress value={(saveProgress.savedRecords / saveProgress.totalRecords) * 100} className="h-2" />
                          <div className="text-center text-sm text-gray-500">
                            {Math.round((saveProgress.savedRecords / saveProgress.totalRecords) * 100)}% complete
                          </div>
                        </div>
                        {saveProgress.estimatedTimeRemaining && (
                            <div className="text-center">
                              <p className="text-xs text-gray-500">
                                Estimated time: {Math.round(saveProgress.estimatedTimeRemaining / 1000)}s
                              </p>
                            </div>
                        )}
                      </div>
                  )}
                </CardContent>
              </Card>
            </div>
        )}

        {/* Validation Results Modal */}
        {showValidationResults && validationResult && (
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
              <Card className="w-full max-w-5xl max-h-[90vh] overflow-hidden bg-white border-0 shadow-xl">
                <CardHeader className="bg-orange-50 border-b border-orange-200">
                  <CardTitle className="flex items-center gap-2 text-orange-800">
                    <AlertTriangle className="w-5 h-5" />
                    Validation Results
                  </CardTitle>
                  <CardDescription className="text-orange-600">
                    {validationResult.isValid
                        ? "All records passed validation"
                        : `${validationResult.errors.length} validation errors found`}
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6 space-y-6">
                  <div className="grid grid-cols-3 gap-6">
                    <Card className="bg-green-50 border-green-200">
                      <CardContent className="p-6 text-center">
                        <div className="text-3xl font-bold text-green-600 mb-2">
                          {validationResult.validRecords.toLocaleString()}
                        </div>
                        <div className="text-sm font-medium text-green-700">Valid Records</div>
                      </CardContent>
                    </Card>

                    <Card className="bg-primary/5 border-primary/20">
                      <CardContent className="p-6 text-center">
                        <div className="text-3xl font-bold text-primary mb-2">
                          {validationResult.invalidRecords.toLocaleString()}
                        </div>
                        <div className="text-sm font-medium text-primary">Invalid Records</div>
                      </CardContent>
                    </Card>

                    <Card className="bg-orange-50 border-orange-200">
                      <CardContent className="p-6 text-center">
                        <div className="text-3xl font-bold text-orange-600 mb-2">
                          {validationResult.errors.length.toLocaleString()}
                        </div>
                        <div className="text-sm font-medium text-orange-700">Total Errors</div>
                      </CardContent>
                    </Card>
                  </div>

                  {validationResult.errors.length > 0 && (
                      <Card className="bg-white border border-gray-200">
                        <CardHeader>
                          <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
                            <AlertTriangle className="w-5 h-5 mr-2 text-primary" />
                            Validation Errors
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ScrollArea className="h-80">
                            <div className="space-y-3">
                              {validationResult.errors.slice(0, 50).map((error, index) => (
                                  <div key={index} className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
                                    <div className="flex items-start justify-between">
                                      <div className="flex-1">
                                        <div className="font-semibold text-primary mb-1">
                                          Row {(error.row || error.rowIndex || 0) + 1}
                                        </div>
                                        <div className="text-primary mb-2">{error.message}</div>
                                        {error.field && (
                                            <div className="text-xs text-primary bg-primary/10 px-2 py-1 rounded inline-block">
                                              Field: {error.field}
                                            </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                              ))}
                              {validationResult.errors.length > 50 && (
                                  <div className="text-center p-4 text-gray-500 bg-gray-50 rounded-lg">
                                    ... and {(validationResult.errors.length - 50).toLocaleString()} more errors
                                  </div>
                              )}
                            </div>
                          </ScrollArea>
                        </CardContent>
                      </Card>
                  )}

                  <Separator />

                  <div className="flex justify-end gap-4">
                    <Button
                        variant="outline"
                        onClick={() => setShowValidationResults(false)}
                        className="border-gray-200 text-gray-700 hover:bg-gray-50"
                    >
                      Cancel
                    </Button>
                    {validationResult.validRecords > 0 && (
                        <Button onClick={handleProceedWithValid} className="bg-green-600 hover:bg-green-700 text-white">
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Save {validationResult.validRecords.toLocaleString()} Valid Records
                        </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
        )}
      </div>
  )
}
