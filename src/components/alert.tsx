import {X} from "lucide-react"

interface AlertProps {
    message: string
    type: "success" | "error"
    onClose: () => void
}

export function Alert({message, type, onClose}: AlertProps) {
    const bgColor = type === "success" ? "bg-green-100" : "bg-red-100"
    const textColor = type === "success" ? "text-green-800" : "text-red-800"
    const borderColor = type === "success" ? "border-green-400" : "border-red-400"

    return (
        <div className={`${bgColor} ${borderColor} ${textColor} border-l-4 p-4 mb-4 relative`} role="alert">
            <p>{message}</p>
            <button
                onClick={onClose}
                className="absolute top-0 right-0 mt-4 mr-4 text-gray-400 hover:text-gray-900"
                aria-label="Close"
            >
                <X size={18}/>
            </button>
        </div>
    )
}

