"use client"

import type React from "react"

import { useCallback } from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Upload, FileSpreadsheet, Plus } from "lucide-react"

interface FileUploadZoneProps {
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void
  loading?: boolean
  hasExistingFile?: boolean
  currentFileName?: string
}

export function FileUploadZone({
  onFileUpload,
  loading = false,
  hasExistingFile = false,
  currentFileName,
}: FileUploadZoneProps) {
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
  }, [])

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      const files = e.dataTransfer.files
      if (files.length > 0) {
        const event = {
          target: { files },
        } as React.ChangeEvent<HTMLInputElement>
        onFileUpload(event)
      }
    },
    [onFileUpload],
  )

  if (hasExistingFile) {
    return (
      <Card className="border-dashed border-2">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FileSpreadsheet className="w-8 h-8 text-muted-foreground" />
              <div>
                <p className="font-medium">Current File: {currentFileName}</p>
                <p className="text-sm text-muted-foreground">Upload another file to compare</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Label htmlFor="compare-file-upload" className="cursor-pointer">
                <Button variant="outline" size="sm" asChild disabled={loading}>
                  <span>
                    <Plus className="w-4 h-4 mr-2" />
                    {loading ? "Processing..." : "Upload 2nd File"}
                  </span>
                </Button>
              </Label>
              <Input
                id="compare-file-upload"
                type="file"
                accept=".xlsx,.xls"
                onChange={onFileUpload}
                className="hidden"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="w-5 h-5" />
          Upload Excel File
        </CardTitle>
        <CardDescription>Select an Excel file (.xlsx, .xls) to begin analysis</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div
            className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-muted-foreground/50 transition-colors"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <FileSpreadsheet className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <Label htmlFor="file-upload" className="cursor-pointer">
              <span className="text-sm font-medium">Click to upload</span>
              <span className="text-sm text-muted-foreground"> or drag and drop</span>
            </Label>
            <Input id="file-upload" type="file" accept=".xlsx,.xls" onChange={onFileUpload} className="hidden" />
            <p className="text-xs text-muted-foreground mt-2">Supports .xlsx and .xls files</p>
          </div>
          {loading && (
            <div className="text-center">
              <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <p className="mt-2 text-sm text-muted-foreground">Processing file...</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
