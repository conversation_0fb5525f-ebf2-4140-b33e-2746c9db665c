"use client"

import { createContext, useContext, useState, type ReactNode } from "react"

export interface ActivityLog {
    id: string
    timestamp: Date
    action: string
    description: string
    category: "auth" | "transaction" | "card" | "profile" | "security" | "system"
    severity: "info" | "warning" | "success" | "error"
    metadata?: Record<string, any>
    ipAddress?: string
    userAgent?: string
}

interface ActivityLoggerContextType {
    logs: ActivityLog[]
    addLog: (log: Omit<ActivityLog, "id" | "timestamp">) => void
    clearLogs: () => void
    getLogsByCategory: (category: ActivityLog["category"]) => ActivityLog[]
    getRecentLogs: (limit?: number) => ActivityLog[]
}

const ActivityLoggerContext = createContext<ActivityLoggerContextType | undefined>(undefined)

export function ActivityLoggerProvider({ children }: { children: ReactNode }) {
    const [logs, setLogs] = useState<ActivityLog[]>([
        // Sample initial logs
        {
            id: "1",
            timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
            action: "LOGIN",
            description: "User logged into cardholder dashboard",
            category: "auth",
            severity: "success",
            ipAddress: "*************",
        },
        {
            id: "2",
            timestamp: new Date(Date.now() - 1000 * 60 * 10), // 10 minutes ago
            action: "VIEW_BALANCE",
            description: "Account balance viewed",
            category: "transaction",
            severity: "info",
        },
        {
            id: "3",
            timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
            action: "CARD_REQUEST",
            description: "New virtual card requested",
            category: "card",
            severity: "success",
            metadata: { cardType: "virtual", currency: "EUR" },
        },
        {
            id: "4",
            timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
            action: "PROFILE_UPDATE",
            description: "Profile information updated",
            category: "profile",
            severity: "info",
            metadata: { fields: ["phone", "address"] },
        },
        {
            id: "5",
            timestamp: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
            action: "FAILED_LOGIN",
            description: "Failed login attempt detected",
            category: "security",
            severity: "warning",
            ipAddress: "************",
        },
    ])

    const addLog = (logData: Omit<ActivityLog, "id" | "timestamp">) => {
        const newLog: ActivityLog = {
            ...logData,
            id: Math.random().toString(36).substr(2, 9),
            timestamp: new Date(),
        }
        setLogs((prev) => [newLog, ...prev])
    }

    const clearLogs = () => {
        setLogs([])
    }

    const getLogsByCategory = (category: ActivityLog["category"]) => {
        return logs.filter((log) => log.category === category)
    }

    const getRecentLogs = (limit = 10) => {
        return logs.slice(0, limit)
    }

    return (
        <ActivityLoggerContext.Provider
            value={{
                logs,
                addLog,
                clearLogs,
                getLogsByCategory,
                getRecentLogs,
            }}
        >
            {children}
        </ActivityLoggerContext.Provider>
    )
}

export function useActivityLogger() {
    const context = useContext(ActivityLoggerContext)
    if (context === undefined) {
        throw new Error("useActivityLogger must be used within an ActivityLoggerProvider")
    }
    return context
}
