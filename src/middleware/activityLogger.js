import Activity from "../models/Activity.js"

// Middleware to log user activity
const activityLogger = async (req, res, next) => {
    // Store the original end function
    const originalEnd = res.end
    const startTime = Date.now()

    // Override the end function to capture response details
    res.end = function (chunk, encoding) {
        const responseTime = Date.now() - startTime

        // Log activity asynchronously to avoid blocking the response
        setImmediate(async () => {
            try {
                // Extract IP address
                const ip =
                    req.ip ||
                    req.connection.remoteAddress ||
                    req.socket.remoteAddress ||
                    (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
                    req.headers["x-forwarded-for"]?.split(",")[0] ||
                    req.headers["x-real-ip"] ||
                    "unknown"

                // Create activity record
                const activityData = {
                    url: req.protocol + "://" + req.get("host") + req.originalUrl,
                    pathname: req.path,
                    method: req.method,
                    userAgent: req.get("User-Agent") || "",
                    referer: req.get("Referer") || "",
                    ip: ip,
                    timestamp: new Date(),
                    searchParams: req.query || {},
                    headers: {
                        accept: req.get("Accept") || "",
                        acceptLanguage: req.get("Accept-Language") || "",
                    },
                    responseTime: responseTime,
                    statusCode: res.statusCode,
                }

                // Add session ID if available
                if (req.session && req.session.id) {
                    activityData.sessionId = req.session.id
                }

                // Add user ID if authenticated
                if (req.user && req.user.id) {
                    activityData.userId = req.user.id
                }

                // Create and save activity
                const activity = new Activity(activityData)
                await activity.save()
            } catch (error) {
                console.error("Error logging activity:", error)
                // Don't throw error to avoid affecting the main request
            }
        })

        // Call the original end function
        originalEnd.call(this, chunk, encoding)
    }

    next()
}

export default activityLogger
