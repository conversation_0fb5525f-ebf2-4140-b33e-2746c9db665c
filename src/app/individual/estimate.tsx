'use client'

import {useState} from 'react'
import {RadioGroup, RadioGroupItem} from "@/components/ui/radio-group"
import {Label} from "@/components/ui/label"

// Previous content remains unchanged...

export default function Estimate() {
    const [incomingTransfers, setIncomingTransfers] = useState<string>("")
    const [outgoingTransfers, setOutgoingTransfers] = useState<string>("")

    return (
        <div className="space-y-4">
            <div className="space-y-2">
                <Label className="text-base">
                    Estimated number of monthly transfers <span className="text-red-500">*</span>
                </Label>

                <div className="border rounded-lg overflow-hidden">
                    <table className="w-full border-collapse">
                        <thead>
                        <tr>
                            <th className="w-1/4 p-3 bg-primary text-primary-foreground font-medium text-left"></th>
                            <th className="w-1/4 p-3 bg-primary text-primary-foreground font-medium text-center">under
                                50
                            </th>
                            <th className="w-1/4 p-3 bg-primary text-primary-foreground font-medium text-center">50
                                - 100
                            </th>
                            <th className="w-1/4 p-3 bg-primary text-primary-foreground font-medium text-center">over
                                100
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr className="border-t">
                            <td className="p-3 bg-primary text-primary-foreground font-medium">Incoming</td>
                            <td className="p-3 text-center">
                                <RadioGroup
                                    value={incomingTransfers}
                                    onValueChange={setIncomingTransfers}
                                    className="flex justify-center"
                                >
                                    <RadioGroupItem value="under-50" id="incoming-transfers-under-50"/>
                                </RadioGroup>
                            </td>
                            <td className="p-3 text-center border-l">
                                <RadioGroup
                                    value={incomingTransfers}
                                    onValueChange={setIncomingTransfers}
                                    className="flex justify-center"
                                >
                                    <RadioGroupItem value="50-100" id="incoming-transfers-50-100"/>
                                </RadioGroup>
                            </td>
                            <td className="p-3 text-center border-l">
                                <RadioGroup
                                    value={incomingTransfers}
                                    onValueChange={setIncomingTransfers}
                                    className="flex justify-center"
                                >
                                    <RadioGroupItem value="over-100" id="incoming-transfers-over-100"/>
                                </RadioGroup>
                            </td>
                        </tr>
                        <tr className="border-t">
                            <td className="p-3 bg-primary text-primary-foreground font-medium">Outgoing</td>
                            <td className="p-3 text-center">
                                <RadioGroup
                                    value={outgoingTransfers}
                                    onValueChange={setOutgoingTransfers}
                                    className="flex justify-center"
                                >
                                    <RadioGroupItem value="under-50" id="outgoing-transfers-under-50"/>
                                </RadioGroup>
                            </td>
                            <td className="p-3 text-center border-l">
                                <RadioGroup
                                    value={outgoingTransfers}
                                    onValueChange={setOutgoingTransfers}
                                    className="flex justify-center"
                                >
                                    <RadioGroupItem value="50-100" id="outgoing-transfers-50-100"/>
                                </RadioGroup>
                            </td>
                            <td className="p-3 text-center border-l">
                                <RadioGroup
                                    value={outgoingTransfers}
                                    onValueChange={setOutgoingTransfers}
                                    className="flex justify-center"
                                >
                                    <RadioGroupItem value="over-100" id="outgoing-transfers-over-100"/>
                                </RadioGroup>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    )
}

