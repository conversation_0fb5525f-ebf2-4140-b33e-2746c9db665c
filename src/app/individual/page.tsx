//@ts-nocheck
"use client"
import type { FormEvent } from "react"
import { useEffect, useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { format } from "date-fns"
import axiosInstance from "@/utils/axiosInstance"
import { CustomCheckbox } from "@/components/CustomCheckbox"
import { type Step, StepLoader } from "./stpes"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { useAppDispatch } from "@/store/hooks"
import { useRouter } from "next/navigation"
import {Alert} from "@/components/alert";
import PhoneInput, { isValidPhoneNumber } from 'react-phone-number-input'
import 'react-phone-number-input/style.css'
import {countries} from "@/utils/data";

interface Country {
    _id: string
    country_name: string
    country_code: string
    currency_code: string
    created_at: string
    created_by: string
    is_active: boolean
    status: string
}

export default function IndividualOnboarding() {
    // State for form fields
    // Personal Info

    const [firstName, setFirstName] = useState<string>("")
    const [middleName, setMiddleName] = useState<string>("")
    const [lastName, setLastName] = useState<string>("")
    const [mothersMaidenName, setMothersMaidenName] = useState<string>("")
    const [email, setEmail] = useState<string>("")
    const [phone, setPhone] = useState<string>("")
    const [authPhone, setAuthPhone] = useState<string>("")
    const [dob, setDob] = useState<string>("")
    const [birthCountry, setBirthCountry] = useState<string>("")
    const [citizenship, setCitizenship] = useState<string>("")

    // Address
    const [street, setStreet] = useState<string>("")
    const [buildingNumber, setBuildingNumber] = useState<string>("")
    const [apartmentNumber, setApartmentNumber] = useState<string>("")
    const [city, setCity] = useState<string>("")
    const [stateProvince, setStateProvince] = useState<string>("")
    const [zipCode, setZipCode] = useState<string>("")
    const [country, setCountry] = useState<string>("")

    // ID Document
    const [idDocumentType, setIdDocumentType] = useState<string>("passport")
    const [idDocumentNumber, setIdDocumentNumber] = useState<string>("")
    const [idAuthority, setIdAuthority] = useState<string>("")
    const [idIssueDate, setIdIssueDate] = useState<string>("")
    const [idExpiryDate, setIdExpiryDate] = useState<string>("")
    const [idIssuingCountry, setIdIssuingCountry] = useState<string>("")

    // Tax Info
    const [taxCountry, setTaxCountry] = useState<string>("")
    const [taxIdNumber, setTaxIdNumber] = useState<string>("")

    // Additional fields
    const [legalId, setLegalId] = useState<string>("")
    const [clientCode, setClientCode] = useState<string>("")
    const [applicationId, setApplicationId] = useState<string>("")
    const [riskLevel, setRiskLevel] = useState<string>("")
    const [riskStatus, setRiskStatus] = useState<string>("LOW")
    const [applicationStatus, setApplicationStatus] = useState<string>("APPROVED")
    const dispatch = useAppDispatch()
    const [isAuthenticated, setIsAuthenticated] = useState(false)
    const router = useRouter()
    // State for form submission
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [submitError, setSubmitError] = useState<string | null>(null)
    const [submitSuccess, setSubmitSuccess] = useState<boolean>(false)
    const [companies, setCompanies] = useState<{ _id: string; name: string }[]>([])
    const [selectedCompany, setSelectedCompany] = useState<string>("")
    const [companyDetails, setCompanyDetails] = useState(null)
    const [cips, setCips] = useState([])
    // Add a new state for selected product versions
    const [selectedProductVersions, setSelectedProductVersions] = useState<string[]>([])
    const [savedCountries, setSavedCountries] = useState<Country[]>([])
    const [alert, setAlert] = useState<{ message: any; type: "success" | "error" } | null>(null)
    const [error, setError] = useState<string>("")

    const [steps, setSteps] = useState<Step[]>([
        { name: "Client Creation", status: "idle" },
        { name: "Account Creation", status: "idle" },
        { name: "Virtual Card Creation", status: "idle" },
        { name: "Sending Welcome Email", status: "idle" },
    ])
    const [currentStep, setCurrentStep] = useState(0)

    useEffect(() => {
        fetchData()
    }, [])
    const generateApplicationId = () => {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 15; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return result;
    };
    useEffect(() => {
        setApplicationId(generateApplicationId());
    }, []);
    const fetchData = async () => {
        setIsLoading(true)
        try {
            const [response, countries] = await Promise.all([
                axiosInstance.get("companies"),
                axiosInstance.get("companies/country"),
            ])

            setCompanies(response.data.data)
            setSavedCountries(countries.data)
        } catch (error) {
            console.error("Error fetching data", error)
        } finally {
            setIsLoading(false)
        }
    }
// Add this helper function inside your component
    const extractMessages = (error: any): string[] => {
        if (typeof error === 'string') {
            return [error];
        } else if (Array.isArray(error)) {
            return error.flatMap(extractMessages);
        } else if (error && typeof error === 'object') {
            return Object.values(error).flatMap(extractMessages);
        } else {
            return [JSON.stringify(error)];
        }
    };
    const fetchCompanyDetails = async (companyId) => {
        setIsLoading(true)
        try {
            const response = await axiosInstance.get(`companies/company/${companyId}`)
            setCompanyDetails(response.data)
            setCips(response.data.cip)

            // Find and auto-select versions with "VTL" in their name
            const vtlVersions = []
            response.data.cip.forEach((cip) => {
                cip.productVersionName.forEach((version) => {
                    if (version.version_name.includes("VTL")) {
                        vtlVersions.push(version._id)
                    }
                })
            })

            // Set the selected versions
            if (vtlVersions.length > 0) {
                setSelectedProductVersions(vtlVersions)
            }
        } catch (error) {
            console.error("Error fetching company details", error)
        } finally {
            setIsLoading(false)
        }
    }

    // Add a new function to handle product version selection
    const handleProductVersionChange = (versionId: string, isChecked: boolean) => {
        setSelectedProductVersions((prev) => (isChecked ? [...prev, versionId] : prev.filter((id) => id !== versionId)))
    }

    // Handle form submission
    const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        setIsSubmitting(true)
        setSubmitError(null)

        // Prepare form data according to the API structure
        const formData = {
            clientCode: clientCode || `RYVL-${Math.floor(Math.random() * 1000000)}`,
            personalInfo: {
                firstName,
                secondName: middleName,
                lastName,
                mothersMaidenName,
                birthDate: dob,
                email,
                phoneNumber: phone,
                authPhoneNumber: authPhone || phone,
                birthCountry,
            },
            address: {
                street,
                buildingNumber,
                apartmentNumber,
                city,
                stateProvince,
                zipCode,
                country,
            },
            idDocument: {
                customerIdType: idDocumentType,
                number: idDocumentNumber,
                issueDate: idIssueDate,
                expiryDate: idExpiryDate,
                idAuthority: idAuthority,
                issuingCountry: idIssuingCountry,
            },
            taxInfo: {
                country: taxCountry,
                taxIdNumber,
            },
            legalId,
            citizenship,
            applicationId: applicationId || `app${Math.floor(Math.random() * 1000000000)}`,
            riskLevel,
            riskStatus,
            applicationStatus,
            applicationDate: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
            // Include company and product versions
            company: selectedCompany,
            productVersions: selectedProductVersions,
        }

        try {
            // Step 1: Personal Information
            setSteps((prev) => prev.map((step, index) => (index === 0 ? { ...step, status: "loading" } : step)))
            const response = await axiosInstance.post("/onboarding/personal", formData)
            setSteps((prev) => prev.map((step, index) => (index === 0 ? { ...step, status: "complete" } : step)))

            // Step 2: Account Setup
            setSteps((prev) => prev.map((step, index) => (index === 1 ? { ...step, status: "loading" } : step)))
            const accountResponse = await axiosInstance.post("/client/createAccount", {
                userId: response.data.data._id,
                clientId: response.data.data.clientID,
            })
            setSteps((prev) => prev.map((step, index) => (index === 1 ? { ...step, status: "complete" } : step)))

            // Step 3: Card Registration
            setSteps((prev) => prev.map((step, index) => (index === 2 ? { ...step, status: "loading" } : step)))
            const cardResponse = await axiosInstance.post(`/client/createCard/virtual`, {
                clientId: response.data.data.clientID,
                userId: response.data.data._id,
                embossName1: `${firstName} ${lastName}`,
                currencyCode: 978,
                accNo: accountResponse.data.account.accNo,
            })
            setSteps((prev) => prev.map((step, index) => (index === 2 ? { ...step, status: "complete" } : step)))

            // Step 4: User Registration
            setSteps((prev) => prev.map((step, index) => (index === 3 ? { ...step, status: "loading" } : step)))
            const registrationResponse = await axiosInstance.post("companies/register", {
                name: `${firstName} ${lastName}`,
                email: email,
                status: "Active",
                dashboard: "cardholder",
                recordId: response.data.data._id,
            })

            if (registrationResponse.status === 201) {
                await axiosInstance.post("onboarding/personal/activate", { recordId: response.data.data._id })
            }
            setSteps((prev) => prev.map((step, index) => (index === 3 ? { ...step, status: "complete" } : step)))

            setSubmitSuccess(true)
            setTimeout(() => {
                window.location.reload()
            }, 5000)
        } catch (error: any) {
            const errorData = error.response?.data ?? error.message ?? error;
            const messages = extractMessages(errorData);
            setAlert({ message: messages, type: "error" });
            setSteps((prev) => prev.map((step, index) =>
                (index === currentStep ? { ...step, status: "error" } : step)));
        } finally {
            setIsSubmitting(false)
        }
    }

    const retryStep = (stepIndex: number) => {
        setSteps((prev) => prev.map((step, index) => (index === stepIndex ? { ...step, status: "idle" } : step)))
        setCurrentStep(stepIndex)
    }

    const MINIMUM_AGE = 18

    // State for DOB validation
    const [dobError, setDobError] = useState<string | null>(null)

    // Handler for DOB input change
    const handleDobChange = (value: string) => {
        setDob(value)

        // Validate DOB
        const today = new Date()
        const dobDate = new Date(value)
        const age = today.getFullYear() - dobDate.getFullYear()
        const monthDifference = today.getMonth() - dobDate.getMonth()
        const dayDifference = today.getDate() - dobDate.getDate()

        if (
            age < MINIMUM_AGE ||
            (age === MINIMUM_AGE && (monthDifference < 0 || (monthDifference === 0 && dayDifference < 0)))
        ) {
            setDobError("You must be at least 18 years old.")
        } else {
            setDobError(null)
        }
    }

    if (isSubmitting || submitSuccess) {
        return (
            <>
                <div className="container mx-auto py-10">
                    <StepLoader steps={steps} currentStep={currentStep} onRetry={retryStep} />
                </div>
                {submitSuccess && (
                    <div className="text-green-600 text-center">Your application has been submitted successfully!</div>
                )}
            </>
        )
    }

    if (isLoading) {
        return <LoadingOverlay />
    }

    // @ts-ignore
    return (
        <div className="container mx-auto py-10">
            <Card className="max-w-2xl mx-auto">
                <CardHeader>
                    <CardTitle>Individual Onboarding</CardTitle>
                    <CardDescription>Please fill out the form to complete your onboarding</CardDescription>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-8">
                        {alert && (
                        <Alert
                            type={alert.type}
                            onClose={() => setAlert(null)}
                            message={
                                <ul className="list-disc list-inside">
                                    {alert.message.map((msg, index) => (
                                        <li key={index}>{msg}</li>
                                    ))}
                                </ul>
                            }
                        />
                        )}
                        {/* Company Selection Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Company Selection</h3>
                            <div className="space-y-2">
                                <Label htmlFor="company">Choose Company</Label>
                                <Select
                                    value={selectedCompany}
                                    onValueChange={(value) => {
                                        setSelectedCompany(value)
                                        fetchCompanyDetails(value)
                                    }}
                                >
                                    <SelectTrigger id="company">
                                        <SelectValue placeholder="Select a company" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {companies.map((company) => (
                                            <SelectItem key={company._id} value={company._id}>
                                                {company.company_name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            {/* Product Version Selection */}
                            {cips.length > 0 && (
                                <div className="space-y-2 hidden">
                                    <Label className="mb-2 block">Choose Product Versions</Label>
                                    <div className="grid grid-cols-2 gap-2">
                                        {cips.flatMap((cip) =>
                                            cip.productVersionName.map((version) => (
                                                <div key={`${cip._id}-${version._id}`} className="flex flex-col gap-1">
                                                    <CustomCheckbox
                                                        name="select_version"
                                                        id={`version-${cip._id}-${version._id}`}
                                                        label={`${version.version_name}`}
                                                        onChange={(isChecked) => handleProductVersionChange(version._id, isChecked)}
                                                        checked={selectedProductVersions.includes(version._id)}
                                                    />
                                                </div>
                                            )),
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Client Information Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Client Information</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="applicationId">Application ID</Label>
                                    <Input
                                        maxLength="15"
                                        id="applicationId"
                                        placeholder="app123456789"
                                        value={applicationId}
                                        onChange={(e) => setApplicationId(e.target.value)}
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Personal Information Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Personal Information</h3>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="firstName">First Name</Label>
                                    <Input
                                        id="firstName"
                                        placeholder="First Name"
                                        value={firstName}
                                        onChange={(e) => setFirstName(e.target.value)}
                                        required
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="middleName">Middle Name</Label>
                                    <Input
                                        id="middleName"
                                        placeholder="Middle Name"
                                        value={middleName}
                                        onChange={(e) => setMiddleName(e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="lastName">Last Name</Label>
                                    <Input
                                        id="lastName"
                                        placeholder="Last Name"
                                        value={lastName}
                                        onChange={(e) => setLastName(e.target.value)}
                                        required
                                    />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="mothersMaidenName">Mother's Maiden Name</Label>
                                <Input
                                    id="mothersMaidenName"
                                    placeholder="Mother's Maiden Name"
                                    value={mothersMaidenName}
                                    onChange={(e) => setMothersMaidenName(e.target.value)}
                                    required
                                />
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="email">Email Address</Label>
                                    <Input
                                        id="email"
                                        placeholder="Email Address"
                                        type="email"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        required
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="phone">Phone Number</Label>
                                    <PhoneInput
                                        international
                                        countryCallingCodeEditable={true}
                                        defaultCountry="AT"
                                        id="phone"
                                        placeholder="Phone Number"
                                        value={phone || ""}
                                        onChange={(value) => {
                                            // Only update if the current phone is not valid or the new value is shorter
                                            if (!isValidPhoneNumber(phone || "") || (value && value.length <= (phone || "").length)) {
                                                setPhone(value || "")
                                            }
                                        }}
                                    
                                    className={`flex h-10 w-full rounded-md border ${
                                        phone
                                            ? isValidPhoneNumber(phone || "")
                                                ? "border-green-500 focus-visible:ring-green-500"
                                                : "border-red-500 focus-visible:ring-red-500"
                                            : "border-input"
                                    } bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50`}
                                         required
                                    />
                                {phone && (
                                    <p className={`text-sm ${isValidPhoneNumber(phone || "") ? "text-green-500" : "text-red-500"}`}>
                                        {isValidPhoneNumber(phone || "")
                                            ? "Valid phone number"
                                            : "Please enter a valid phone number"}
                                    </p>
                                )}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="authPhone">Authentication Phone Number</Label>
                                <PhoneInput
                                    international
                                    countryCallingCodeEditable={true}
                                    defaultCountry="AT"
                                    id="authPhone"
                                    placeholder="Authentication Phone Number (if different)"
                                     
                                    value={authPhone || ""}
                                    onChange={(value) => {
                                        // Only update if the current phone is not valid or the new value is shorter
                                        if (!isValidPhoneNumber(authPhone || "") || (value && value.length <= (authPhone || "").length)) {
                                            setAuthPhone(value || "")
                                        }
                                    }}
                                   
                                    className={`flex h-10 w-full rounded-md border ${
                                        authPhone
                                            ? isValidPhoneNumber(authPhone)
                                                ? "border-green-500 focus-visible:ring-green-500"
                                                : "border-red-500 focus-visible:ring-red-500"
                                            : "border-input"
                                    } bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50`}
                                />
                                {authPhone && (
                                    <p className={`text-sm ${isValidPhoneNumber(authPhone) ? "text-green-500" : "text-red-500"}`}>
                                        {isValidPhoneNumber(authPhone)
                                            ? "Valid phone number"
                                            : "Please enter a valid phone number"}
                                    </p>
                                )}
                                {!authPhone && (
                                    <span className="text-sm text-muted-foreground">Leave blank to use primary phone number</span>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="dob">Date of Birth</Label>
                                <Input id="dob" type="date" value={dob} onChange={(e) => handleDobChange(e.target.value)} required />
                                {dobError && <div className="text-red-600 text-sm">{dobError}</div>}
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="birthCountry">Birth Country</Label>
                                    <Select value={birthCountry} onValueChange={setBirthCountry}>
                                        <SelectTrigger id="birthCountry">
                                            <SelectValue placeholder="Select Country" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {savedCountries
                                                .filter((c) => c.status === "active" && c.is_active)
                                                .sort((a, b) => a.country_name.localeCompare(b.country_name))
                                                .map((country) => (
                                                    <SelectItem key={`birth-${country._id}`} value={country.country_code}>
                                                        {country.country_name} ({country.country_code})
                                                    </SelectItem>
                                                ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="citizenship">Citizenship</Label>
                                    <Select value={citizenship} onValueChange={setCitizenship}>
                                        <SelectTrigger id="citizenship">
                                            <SelectValue placeholder="Select Country" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {savedCountries
                                                .filter((c) => c.status === "active" && c.is_active)
                                                .sort((a, b) => a.country_name.localeCompare(b.country_name))
                                                .map((country) => (
                                                    <SelectItem key={`citizenship-${country._id}`} value={country.country_code}>
                                                        {country.country_name} ({country.country_code})
                                                    </SelectItem>
                                                ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </div>

                        {/* Residential Address Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Residential Address</h3>
                            <div className="space-y-2">
                                <Label htmlFor="street">Street</Label>
                                <Input
                                    id="street"
                                    placeholder="Street Address"
                                    value={street}
                                    onChange={(e) => setStreet(e.target.value)}
                                    required
                                />
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="buildingNumber">Building Number</Label>
                                    <Input
                                        id="buildingNumber"
                                        placeholder="Building Number"
                                        value={buildingNumber}
                                        onChange={(e) => setBuildingNumber(e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="apartmentNumber">Apartment Number</Label>
                                    <Input
                                        id="apartmentNumber"
                                        placeholder="Apartment Number"
                                        value={apartmentNumber}
                                        onChange={(e) => setApartmentNumber(e.target.value)}
                                    />
                                </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="city">City</Label>
                                    <Input id="city" placeholder="City" value={city} onChange={(e) => setCity(e.target.value)} required />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="stateProvince">State / Province</Label>
                                    <Input
                                        id="stateProvince"
                                        placeholder="State / Province"
                                        value={stateProvince}
                                        onChange={(e) => setStateProvince(e.target.value)}
                                    />
                                </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="zipCode">Postal / ZIP Code</Label>
                                    <Input
                                        id="zipCode"
                                        placeholder="Postal / ZIP Code"
                                        value={zipCode}
                                        onChange={(e) => setZipCode(e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="country">Country</Label>
                                    <Select value={country} onValueChange={setCountry}>
                                        <SelectTrigger id="country">
                                            <SelectValue placeholder="Select Country" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {savedCountries
                                                .filter((c) => c.status === "active" && c.is_active)
                                                .sort((a, b) => a.country_name.localeCompare(b.country_name))
                                                .map((country) => (
                                                    <SelectItem key={`address-${country._id}`} value={country.country_code}>
                                                        {country.country_name} ({country.country_code})
                                                    </SelectItem>
                                                ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </div>

                        {/* ID Document Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">ID Document</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="idDocumentType">ID Document Type</Label>
                                    <Select value={idDocumentType} onValueChange={setIdDocumentType}>
                                        <SelectTrigger id="idDocumentType">
                                            <SelectValue placeholder="ID Document Type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="ID">ID</SelectItem>
                                            <SelectItem value="PASSPORT">PASSPORT</SelectItem>
                                            <SelectItem value="RESIDENCE_CARD">RESIDENCE CARD</SelectItem>
                                            <SelectItem value="DRIVING_LICENSE">DRIVING LICENSE</SelectItem>
                                            <SelectItem value="MINOR_WITHOUT_ID">MINOR WITHOUT ID</SelectItem>
                                            <SelectItem value="OTHER">OTHER</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="idDocumentNumber">ID Document Number</Label>
                                    <Input
                                        id="idDocumentNumber"
                                        placeholder="ID Document Number"
                                        value={idDocumentNumber}
                                        onChange={(e) => setIdDocumentNumber(e.target.value)}
                                        required
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="idAuthority">ID Authority</Label>
                                    <Input
                                        id="idAuthority"
                                        placeholder="ID Authority"
                                        value={idAuthority}
                                        onChange={(e) => setIdAuthority(e.target.value)}
                                        required
                                    />
                                </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="idIssueDate">Issue Date</Label>
                                    <Input
                                        id="idIssueDate"
                                        type="date"
                                        value={idIssueDate}
                                        onChange={(e) => setIdIssueDate(e.target.value)}
                                        required
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="idExpiryDate">Expiry Date</Label>
                                    <Input
                                        id="idExpiryDate"
                                        type="date"
                                        value={idExpiryDate}
                                        onChange={(e) => setIdExpiryDate(e.target.value)}
                                        required
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="taxCountry">ID Issuing Country</Label>
                                    <Select value={idIssuingCountry} onValueChange={setIdIssuingCountry}>
                                        <SelectTrigger id="idIssuingCountry">
                                            <SelectValue placeholder="Issuing Country" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {savedCountries
                                                .filter((c) => c.status === "active" && c.is_active)
                                                .sort((a, b) => a.country_name.localeCompare(b.country_name))
                                                .map((country) => (
                                                    <SelectItem key={`tax-${country._id}`} value={country.country_code}>
                                                        {country.country_name} ({country.country_code})
                                                    </SelectItem>
                                                ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </div>

                        {/* Tax ID Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Tax Information</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="taxCountry">Tax Country</Label>
                                    <Select value={taxCountry} onValueChange={setTaxCountry}>
                                        <SelectTrigger id="taxCountry">
                                            <SelectValue placeholder="Tax Country" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {savedCountries
                                                .filter((c) => c.status === "active" && c.is_active)
                                                .sort((a, b) => a.country_name.localeCompare(b.country_name))
                                                .map((country) => (
                                                    <SelectItem key={`tax-${country._id}`} value={country.country_code}>
                                                        {country.country_name} ({country.country_code})
                                                    </SelectItem>
                                                ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="taxIdNumber">Tax ID Number</Label>
                                    <Input
                                        id="taxIdNumber"
                                        placeholder="Tax ID Number"
                                        value={taxIdNumber}
                                        onChange={(e) => setTaxIdNumber(e.target.value)}
                                        required
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Additional Information */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Additional Information</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="legalId">Legal ID</Label>
                                    <Input
                                        id="legalId"
                                        placeholder="Legal ID"
                                        value={legalId}
                                        onChange={(e) => setLegalId(e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="riskLevel">Risk Level</Label>
                                    <Input
                                        id="riskLevel"
                                        placeholder="e.g., 300"
                                        value={riskLevel}
                                        onChange={(e) => setRiskLevel(e.target.value)}
                                    />
                                </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="riskStatus">Risk Status</Label>
                                    <Select value={riskStatus} onValueChange={setRiskStatus}>
                                        <SelectTrigger id="riskStatus">
                                            <SelectValue placeholder="Risk Status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="LOW">LOW</SelectItem>
                                            <SelectItem value="MEDIUM">MEDIUM</SelectItem>
                                            <SelectItem value="HIGH">HIGH</SelectItem>
                                            <SelectItem value="VERY HIGH">VERY HIGH</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="applicationStatus">Application Status</Label>
                                    <Select value={applicationStatus} onValueChange={setApplicationStatus}>
                                        <SelectTrigger id="applicationStatus">
                                            <SelectValue placeholder="Application Status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="PENDING">PENDING</SelectItem>
                                            <SelectItem value="APPROVED">APPROVED</SelectItem>
                                            <SelectItem value="REJECTED">REJECTED</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </div>

                        {/* Error Message */}
                        {submitError && <div className="text-red-600 text-center">{submitError}</div>}

                        {/* Submit Button */}
                        <Button type="submit" disabled={isSubmitting || dobError} className="w-full">
                            {isSubmitting ? "Submitting..." : "Submit Application"}
                        </Button>
                    </form>
                </CardContent>
            </Card>
        </div>
    )
}

