//@ts-nocheck
"use client"
import type {FormEvent} from "react" // Import FormEvent
import {useEffect, useState} from "react"
import {Label} from "@/components/ui/label"
import {Input} from "@/components/ui/input"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {Button} from "@/components/ui/button"
import axiosInstance from "@/utils/axiosInstance"
import {LoadingOverlay} from "@/components/LoadingOverlay"
import {CustomCheckbox} from "@/components/CustomCheckbox";

interface Country {
    _id: string
    country_name: string
    country_code: string
    currency_code: string
    created_at: string
    created_by: string
    is_active: boolean
    status: string
}

export default function IndividualOnboarding() {
    // State for form fields
    const [selectedProduct, setSelectedProduct] = useState<string>("consumer-debit")
    const [firstName, setFirstName] = useState<string>("")
    const [middleName, setMiddleName] = useState<string>("")
    const [lastName, setLastName] = useState<string>("")
    const [email, setEmail] = useState<string>("")
    const [phone, setPhone] = useState<string>("")
    const [dob, setDob] = useState<string>("")
    const [streetAddress, setStreetAddress] = useState<string>("")
    const [apartment, setApartment] = useState<string>("")
    const [building, setBuilding] = useState<string>("")
    const [city, setCity] = useState<string>("")
    const [stateProvince, setStateProvince] = useState<string>("")
    const [postalCode, setPostalCode] = useState<string>("")
    const [country, setCountry] = useState<string>("")
    const [idDocumentType, setIdDocumentType] = useState<string>("")
    const [idDocumentNumber, setIdDocumentNumber] = useState<string>("")
    const [idDocumentCountry, setIdDocumentCountry] = useState<string>("")
    const [taxCountry, setTaxCountry] = useState<string>("")
    const [taxIdNumber, setTaxIdNumber] = useState<string>("")
    const [riskScore, setRiskScore] = useState<string>("")
    const [cardTypes, setCardTypes] = useState<string[]>([])
    const [selectedCurrency, setSelectedCurrency] = useState<string[]>([])
    const [savedCountries, setSavedCountries] = useState<Country[]>([])
    const [savedCurrency, setSavedCurrency] = useState<Country[]>([])
    // State for form submission
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
    const [submitError, setSubmitError] = useState<string | null>(null)
    const [submitSuccess, setSubmitSuccess] = useState<boolean>(false)
    const [companies, setCompanies] = useState<{ _id: string; name: string }[]>([])
    const [selectedCompany, setSelectedCompany] = useState<string>("")
    const [companyDetails, setCompanyDetails] = useState(null)
    const [cips, setCips] = useState([])
    // Add a new state for selected product versions
    const [selectedProductVersions, setSelectedProductVersions] = useState<string[]>([])

    useEffect(() => {
        fetchData()
    }, [])

    const fetchData = async () => {
        setIsSubmitting(true)
        try {
            const [response, countries] = await Promise.all([
                axiosInstance.get("companies"),
                axiosInstance.get("companies/country"),
            ])

            setCompanies(response.data.data)
            setSavedCountries(countries.data)
        } catch (error) {
            console.error("Error fetching data", error)
        } finally {
            setIsSubmitting(false)
        }
    }

    const fetchCompanyDetails = async (companyId) => {
        try {
            const response = await axiosInstance.get(`companies/company/${companyId}`)
            setCompanyDetails(response.data)
            setCips(response.data.cip)
        } catch (error) {
            console.error("Error fetching company details", error)
        }
    }



    // Add a new function to handle product version selection
    const handleProductVersionChange = (versionId: string, isChecked: boolean) => {
        setSelectedProductVersions(prev =>
            isChecked
                ? [...prev, versionId]
                : prev.filter(id => id !== versionId)
        )
    }

    // Handle form submission
    const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        setIsSubmitting(true)
        setSubmitError(null)

        // Prepare form data
        const formData = {
            company: selectedCompany,
            product: selectedProduct,
            productVersions: selectedProductVersions, // Add this line
            personalInfo: {
                firstName,
                middleName,
                lastName,
                dateOfBirth: dob,
                email: email,
                phone: phone,
            },
            address: {
                streetAddress,
                building,
                apartment,
                city,
                stateProvince,
                postalCode,
                country,
            },
            idDocument: {
                type: idDocumentType,
                number: idDocumentNumber,
                issuingCountry: idDocumentCountry,
            },
            taxInfo: {
                country: taxCountry,
                taxIdNumber,
            },
            riskLevel: riskScore,
            cardTypes,
            currency: selectedCurrency,
        }

        try {
            const response = await axiosInstance.post("/onboarding/personal", formData)
            console.log(response)
            setSubmitSuccess(true)
        } catch (error) {
            setSubmitError("Failed to submit form. Please try again.")
            console.error("Submission error:", error)
        } finally {
            setIsSubmitting(false)
        }
    }
    const MINIMUM_AGE = 18

    // State for DOB validation
    const [dobError, setDobError] = useState<string | null>(null)

    function getTextAfterDashAndCapitalize(inputString: string): string {
        const parts = inputString.split("-")
        if (parts.length > 1) {
            const result = parts[1].trim()
            return result.charAt(0).toUpperCase() + result.slice(1).toLowerCase()
        }
        return ""
    }

    // Handler for DOB input change
    const handleDobChange = (value: string) => {
        setDob(value)

        // Validate DOB
        const today = new Date()
        const dobDate = new Date(value)
        const age = today.getFullYear() - dobDate.getFullYear()
        const monthDifference = today.getMonth() - dobDate.getMonth()
        const dayDifference = today.getDate() - dobDate.getDate()

        if (
            age < MINIMUM_AGE ||
            (age === MINIMUM_AGE && (monthDifference < 0 || (monthDifference === 0 && dayDifference < 0)))
        ) {
            setDobError("You must be at least 18 years old.")
        } else {
            setDobError(null)
        }
    }
    return <LoadingOverlay/>
    // return (
    //     <div className="container mx-auto py-10">
    //         <Card className="max-w-2xl mx-auto">
    //             <CardHeader>
    //                 <CardTitle>Individual Onboarding</CardTitle>
    //                 <CardDescription>Please fill out the form to select your card requirements</CardDescription>
    //             </CardHeader>
    //             <CardContent>
    //
    //                 {submitSuccess ? (
    //                     <div className="text-green-600 text-center">Your application has been submitted
    //                         successfully!</div>
    //                 ) : (
    //                     <form onSubmit={handleSubmit} className="space-y-8">
    //                         {/* Personal Information Section */}
    //
    //                         <div className="space-y-2">
    //                             <Label htmlFor="company">Choose Company</Label>
    //                             <Select
    //                                 value={selectedCompany}
    //                                 onValueChange={(value) => {
    //                                     setSelectedCompany(value)
    //                                     fetchCompanyDetails(value)
    //                                 }}
    //                             >
    //                                 <SelectTrigger id="company">
    //                                     <SelectValue placeholder="Select a company"/>
    //                                 </SelectTrigger>
    //                                 <SelectContent>
    //                                     {companies.map((company) => (
    //                                         <SelectItem key={company._id} value={company._id}>
    //                                             {company.company_name}
    //                                         </SelectItem>
    //                                     ))}
    //                                 </SelectContent>
    //                             </Select>
    //                         </div>
    //                         <div>
    //                             {cips.length > 0 && (
    //                                 <>
    //                                     <Label className="mb-0">Choose Product</Label>
    //                                     <div className="grid grid-cols-2 gap-2 mt-0">
    //
    //                                         {cips.flatMap((cip) =>
    //                                             cip.productVersionName.map((version) => (
    //                                                 <div key={`${cip._id}-${version._id}`}
    //                                                      className="flex   flex-col  mt-0 gap-2">
    //                                                     <CustomCheckbox
    //                                                         id={`version-${cip._id}-${version._id}`}
    //                                                         label={`${version.version_name}`}
    //                                                         onChange={(isChecked) => handleProductVersionChange(version._id, isChecked)}
    //                                                         checked={selectedProductVersions.includes(version._id)}
    //                                                     />
    //                                                 </div>
    //                                             )),
    //                                         )}
    //                                     </div>
    //                                 </>
    //
    //
    //                             )}
    //                         </div>
    //                         <div className="space-y-4 pt-5">
    //                             <h3 className="text-lg font-medium">Personal Information</h3>
    //                             <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
    //                                 <div className="space-y-2">
    //                                     <Input
    //                                         id="firstName"
    //                                         placeholder="First Name"
    //                                         value={firstName}
    //                                         onChange={(e) => setFirstName(e.target.value)}
    //                                         required
    //                                     />
    //                                     <span className="text-sm text-muted-foreground">First Name</span>
    //                                 </div>
    //                                 <div className="space-y-2">
    //                                     <Input
    //                                         id="middleName"
    //                                         placeholder="Middle Name"
    //                                         value={middleName}
    //                                         onChange={(e) => setMiddleName(e.target.value)}
    //                                     />
    //                                     <span className="text-sm text-muted-foreground">Middle Name</span>
    //                                 </div>
    //                                 <div className="space-y-2">
    //                                     <Input
    //                                         id="lastName"
    //                                         placeholder="Last Name"
    //                                         value={lastName}
    //                                         onChange={(e) => setLastName(e.target.value)}
    //                                         required
    //                                     />
    //                                     <span className="text-sm text-muted-foreground">Last Name</span>
    //                                 </div>
    //                             </div>
    //                             <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    //                                 <div className="space-y-2">
    //                                     <Input
    //                                         id="email"
    //                                         placeholder="Email Address"
    //                                         value={email}
    //                                         onChange={(e) => setEmail(e.target.value)}
    //                                         required
    //                                     />
    //                                     <span className="text-sm text-muted-foreground">Email Address</span>
    //                                 </div>
    //                                 <div className="space-y-2">
    //                                     <Input
    //                                         id="phone"
    //                                         placeholder="Phone Number"
    //                                         value={phone}
    //                                         onChange={(e) => setPhone(e.target.value)}
    //                                         required
    //                                     />
    //                                     <span className="text-sm text-muted-foreground">Phone Number</span>
    //                                 </div>
    //                             </div>
    //                             <div className="space-y-2">
    //                                 <Input id="dob" type="date" value={dob}
    //                                        onChange={(e) => handleDobChange(e.target.value)} required/>
    //                                 <span className="text-sm text-muted-foreground">Date of Birth</span>
    //                                 {dobError && <div className="text-red-600 text-sm">{dobError}</div>}
    //                             </div>
    //                         </div>
    //
    //                         {/* Residential Address Section */}
    //                         <div className="space-y-4">
    //                             <h3 className="text-lg font-medium">Residential Address</h3>
    //                             <Input
    //                                 id="streetAddress"
    //                                 placeholder="Street Address"
    //                                 value={streetAddress}
    //                                 onChange={(e) => setStreetAddress(e.target.value)}
    //                                 required
    //                             />
    //                             <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    //                                 <Input
    //                                     id="buildingNumber"
    //                                     placeholder="Building Number"
    //                                     value={building}
    //                                     onChange={(e) => setBuilding(e.target.value)}
    //                                 />
    //                                 <Input
    //                                     id="apartmentNumber"
    //                                     placeholder="Apartment Number"
    //                                     value={apartment}
    //                                     onChange={(e) => setApartment(e.target.value)}
    //                                 />
    //                             </div>
    //                             <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    //                                 <Input id="city" placeholder="City" value={city}
    //                                        onChange={(e) => setCity(e.target.value)} required/>
    //                                 <Input
    //                                     id="stateProvince"
    //                                     placeholder="State / Province"
    //                                     value={stateProvince}
    //                                     onChange={(e) => setStateProvince(e.target.value)}
    //                                 />
    //                             </div>
    //                             <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    //                                 <Input
    //                                     id="postalCode"
    //                                     placeholder="Postal / ZIP Code"
    //                                     value={postalCode}
    //                                     onChange={(e) => setPostalCode(e.target.value)}
    //                                 />
    //                                 <Select value={country} onValueChange={setCountry}>
    //                                     <SelectTrigger id="productCountry">
    //                                         <SelectValue placeholder="Select Country" />
    //                                     </SelectTrigger>
    //                                     <SelectContent>
    //                                         {savedCountries
    //                                             .filter((c) => c.status === "active" && c.is_active)
    //                                             .sort((a, b) => a.country_name.localeCompare(b.country_name))
    //                                             .map((ccy) => (
    //                                                 <SelectItem key={ccy._id} value={ccy.country_name}>
    //                                                     {ccy.country_name} ({ccy.country_code})
    //                                                 </SelectItem>
    //                                             ))}
    //                                     </SelectContent>
    //                                 </Select>
    //                             </div>
    //                         </div>
    //
    //                         {/* ID Document Section */}
    //                         <div className="space-y-4">
    //                             <h3 className="text-lg font-medium">ID Document</h3>
    //                             <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    //                                 <Select value={idDocumentType} onValueChange={setIdDocumentType}>
    //                                     <SelectTrigger id="idDocumentType">
    //                                         <SelectValue placeholder="ID Document Type"/>
    //                                     </SelectTrigger>
    //                                     <SelectContent>
    //                                         <SelectItem value="passport">Passport</SelectItem>
    //                                         <SelectItem value="national-id">National ID</SelectItem>
    //                                         <SelectItem value="drivers-license">Driver's License</SelectItem>
    //                                     </SelectContent>
    //                                 </Select>
    //                                 <Input
    //                                     id="idDocumentNumber"
    //                                     placeholder="ID Document Number"
    //                                     value={idDocumentNumber}
    //                                     onChange={(e) => setIdDocumentNumber(e.target.value)}
    //                                     required
    //                                 />
    //                             </div>
    //                             <Select value={idDocumentCountry} onValueChange={setIdDocumentCountry}>
    //                                 <SelectTrigger id="idDocumentCountry">
    //                                     <SelectValue placeholder="ID Document Country"/>
    //                                 </SelectTrigger>
    //                                 <SelectContent>
    //                                     {savedCountries
    //                                         .filter((c) => c.status === "active" && c.is_active)
    //                                         .sort((a, b) => a.country_name.localeCompare(b.country_name))
    //                                         .map((ccy) => (
    //                                             <SelectItem key={ccy._id} value={ccy.country_name}>
    //                                                 {ccy.country_name} ({ccy.country_code})
    //                                             </SelectItem>
    //                                         ))}
    //                                 </SelectContent>
    //                             </Select>
    //                         </div>
    //
    //                         {/* Tax ID Section */}
    //                         <div className="space-y-4">
    //                             <h3 className="text-lg font-medium">Tax ID</h3>
    //                             <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    //                                 <Select value={taxCountry} onValueChange={setTaxCountry}>
    //                                     <SelectTrigger id="taxCountry">
    //                                         <SelectValue placeholder="Tax Country"/>
    //                                     </SelectTrigger>
    //                                     <SelectContent>
    //                                         {savedCountries
    //                                             .filter((c) => c.status === "active" && c.is_active)
    //                                             .sort((a, b) => a.country_name.localeCompare(b.country_name))
    //                                             .map((ccy) => (
    //                                                 <SelectItem key={ccy._id} value={ccy.country_name}>
    //                                                     {ccy.country_name} ({ccy.country_code})
    //                                                 </SelectItem>
    //                                             ))}
    //                                     </SelectContent>
    //                                 </Select>
    //                                 <Input
    //                                     id="taxIdNumber"
    //                                     placeholder="Tax ID Number"
    //                                     value={taxIdNumber}
    //                                     onChange={(e) => setTaxIdNumber(e.target.value)}
    //                                     required
    //                                 />
    //                             </div>
    //                         </div>
    //
    //                         <div className="space-y-4">
    //                             <Label htmlFor="riskScore">Risk Score</Label>
    //                             <Input
    //                                 id="riskScore"
    //                                 placeholder="e.g., 0 to 899"
    //                                 type="number"
    //                                 max={899}
    //                                 min={0}
    //                                 value={riskScore}
    //                                 onChange={(e) => setRiskScore(e.target.value)}
    //                             />
    //                         </div>
    //                         {/* Error Message */}
    //                         {submitError && <div className="text-red-600 text-center">{submitError}</div>}
    //
    //                         {/* Submit Button */}
    //                         <Button type="submit" disabled={isSubmitting || dobError} className="w-full">
    //                             {isSubmitting ? "Submitting..." : "Submit Step 1"}
    //                         </Button>
    //                     </form>
    //                 )}
    //             </CardContent>
    //         </Card>
    //     </div>
    // )
}
