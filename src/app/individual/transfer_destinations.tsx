'use client'

import { useState } from 'react'

import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
const destinations = [
    { id: "europe", label: "Europe" },
    { id: "uk", label: "UK" },
    { id: "usa", label: "USA" },
    { id: "cis", label: "CIS" },
    { id: "offshore", label: "Offshore (Central America)" },
    { id: "south-america", label: "South America" },
    { id: "middle-east", label: "Middle East" },
    { id: "asia", label: "Asia" },
] as const

export default function Transfer_destinations() {
    // Previous state declarations remain unchanged...
    const [incomingDestinations, setIncomingDestinations] = useState<string[]>([])
    const [outgoingDestinations, setOutgoingDestinations] = useState<string[]>([])

    const toggleDestination = (type: 'incoming' | 'outgoing', destinationId: string) => {
        if (type === 'incoming') {
            setIncomingDestinations(prev =>
                prev.includes(destinationId)
                    ? prev.filter(id => id !== destinationId)
                    : [...prev, destinationId]
            )
        } else {
            setOutgoingDestinations(prev =>
                prev.includes(destinationId)
                    ? prev.filter(id => id !== destinationId)
                    : [...prev, destinationId]
            )
        }
    }

    return (
        <div className="space-y-4">
            <div className="space-y-2">
                <Label className="text-base">
                    Expected transfer destinations <span className="text-red-500">*</span>
                </Label>

                <div className="border rounded-lg overflow-hidden">
                    <table className="w-full border-collapse">
                        <thead>
                        <tr>
                            <th className="w-1/2 p-3 bg-primary text-primary-foreground font-medium text-left"></th>
                            <th className="w-1/4 p-3 bg-primary text-primary-foreground font-medium text-center">Incoming</th>
                            <th className="w-1/4 p-3 bg-primary text-primary-foreground font-medium text-center">Outgoing</th>
                        </tr>
                        </thead>
                        <tbody>
                        {destinations.map((destination) => (
                            <tr key={destination.id} className="border-t">
                                <td className="p-3 bg-primary text-primary-foreground font-medium">
                                    {destination.label}
                                </td>
                                <td className="p-3 text-center">
                                    <Checkbox
                                        id={`incoming-${destination.id}`}
                                        checked={incomingDestinations.includes(destination.id)}
                                        onCheckedChange={() => toggleDestination('incoming', destination.id)}
                                        className="border-2"
                                    />
                                </td>
                                <td className="p-3 text-center border-l">
                                    <Checkbox
                                        id={`outgoing-${destination.id}`}
                                        checked={outgoingDestinations.includes(destination.id)}
                                        onCheckedChange={() => toggleDestination('outgoing', destination.id)}
                                        className="border-2"
                                    />
                                </td>
                            </tr>
                        ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    )
}

