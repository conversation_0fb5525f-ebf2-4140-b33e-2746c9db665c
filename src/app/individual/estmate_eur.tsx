'use client'

import {useState} from 'react'
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {RadioGroup, RadioGroupItem} from "@/components/ui/radio-group"
import {Label} from "@/components/ui/label"

// Previous content remains unchanged...

export default function Estmate_eur() {

    const [incomingVolume, setIncomingVolume] = useState<string>("")
    const [outgoingVolume, setOutgoingVolume] = useState<string>("")

    return (
        <div className="space-y-4">
            <div className="space-y-2">
                <Label className="text-base">
                    Estimated monthly volumes (in EURO) <span className="text-red-500">*</span>
                </Label>

                <div className="border rounded-lg overflow-hidden">
                    <table className="w-full border-collapse">
                        <thead>
                        <tr>
                            <th className="w-1/4 p-3 bg-primary text-primary-foreground font-medium text-left"></th>
                            <th className="w-1/4 p-3 bg-primary text-primary-foreground font-medium text-center">under
                                30 000
                            </th>
                            <th className="w-1/4 p-3 bg-primary text-primary-foreground font-medium text-center">30 000
                                - 100 000
                            </th>
                            <th className="w-1/4 p-3 bg-primary text-primary-foreground font-medium text-center">over
                                100 000
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr className="border-t">
                            <td className="p-3 bg-primary text-primary-foreground font-medium">Incoming</td>
                            <td className="p-3 text-center">
                                <RadioGroup
                                    value={incomingVolume}
                                    onValueChange={setIncomingVolume}
                                    className="flex justify-center"
                                >
                                    <RadioGroupItem value="under-30000" id="incoming-under-30000"/>
                                </RadioGroup>
                            </td>
                            <td className="p-3 text-center border-l">
                                <RadioGroup
                                    value={incomingVolume}
                                    onValueChange={setIncomingVolume}
                                    className="flex justify-center"
                                >
                                    <RadioGroupItem value="30000-100000" id="incoming-30000-100000"/>
                                </RadioGroup>
                            </td>
                            <td className="p-3 text-center border-l">
                                <RadioGroup
                                    value={incomingVolume}
                                    onValueChange={setIncomingVolume}
                                    className="flex justify-center"
                                >
                                    <RadioGroupItem value="over-100000" id="incoming-over-100000"/>
                                </RadioGroup>
                            </td>
                        </tr>
                        <tr className="border-t">
                            <td className="p-3 bg-primary text-primary-foreground font-medium">Outgoing</td>
                            <td className="p-3 text-center">
                                <RadioGroup
                                    value={outgoingVolume}
                                    onValueChange={setOutgoingVolume}
                                    className="flex justify-center"
                                >
                                    <RadioGroupItem value="under-30000" id="outgoing-under-30000"/>
                                </RadioGroup>
                            </td>
                            <td className="p-3 text-center border-l">
                                <RadioGroup
                                    value={outgoingVolume}
                                    onValueChange={setOutgoingVolume}
                                    className="flex justify-center"
                                >
                                    <RadioGroupItem value="30000-100000" id="outgoing-30000-100000"/>
                                </RadioGroup>
                            </td>
                            <td className="p-3 text-center border-l">
                                <RadioGroup
                                    value={outgoingVolume}
                                    onValueChange={setOutgoingVolume}
                                    className="flex justify-center"
                                >
                                    <RadioGroupItem value="over-100000" id="outgoing-over-100000"/>
                                </RadioGroup>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    )
}

