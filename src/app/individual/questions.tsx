'use client'

import {useState} from 'react'
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {RadioGroup, RadioGroupItem} from "@/components/ui/radio-group"
import {Label} from "@/components/ui/label"
import {Input} from "@/components/ui/input"
import {Checkbox} from "@/components/ui/checkbox"

const accountPurposeOptions = [
    { id: "employment-income", label: "Employment Income" },
    { id: "savings", label: "Savings" },
    { id: "social-benefits", label: "Social Benefits" },
    { id: "dividends", label: "Dividends" },
    { id: "other", label: "Other (please specify)" },
]

const occupationOptions = [
    { value: "employed", label: "Employed" },
    { value: "self-employed", label: "Self-employed" },
    { value: "student", label: "Student" },
    { value: "retired", label: "Retired" },
    { value: "unemployed", label: "Unemployed" },
    { value: "other", label: "Other (please specify)" },
]

export default function Questions() {
    const [selectedPurposes, setSelectedPurposes] = useState<string[]>([])

    // Previous sections remain unchanged...

    return (
        <div className="space-y-4">
            <h3 className="text-lg font-medium">Questionnaire</h3>
            <p className="text-sm text-muted-foreground">
                Tell us more about your service needs and circumstances.
            </p>

            <div className="space-y-4">
                <div className="space-y-4">
                    <Label>
                        Account Purpose (select all that apply) <span className="text-red-500">*</span>
                    </Label>
                    <div className="grid gap-2">
                        {accountPurposeOptions.map((option) => (
                            <div key={option.id} className="flex items-center space-x-2">
                                <Checkbox
                                    id={option.id}
                                    checked={selectedPurposes.includes(option.id)}
                                    onCheckedChange={(checked) => {
                                        if (checked) {
                                            setSelectedPurposes([...selectedPurposes, option.id])
                                        } else {
                                            setSelectedPurposes(selectedPurposes.filter((id) => id !== option.id))
                                        }
                                    }}
                                />
                                <Label
                                    htmlFor={option.id}
                                    className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                    {option.label}
                                </Label>
                            </div>
                        ))}
                    </div>
                </div>

                <div className="space-y-4">
                    <Label>
                        Occupation <span className="text-red-500">*</span>
                    </Label>
                    <RadioGroup defaultValue="employed">
                        {occupationOptions.map((option) => (
                            <div key={option.value} className="flex items-center space-x-2">
                                <RadioGroupItem value={option.value} id={option.value}/>
                                <Label
                                    htmlFor={option.value}
                                    className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                    {option.label}
                                </Label>
                            </div>
                        ))}
                    </RadioGroup>
                </div>

                <div className="space-y-2">
                    <Label htmlFor="annualIncome">Annual Income (in EURO)</Label>
                    <Input
                        id="annualIncome"
                        placeholder="e.g., 45000"
                        type="number"
                    />
                </div>
            </div>
        </div>
    )
}

