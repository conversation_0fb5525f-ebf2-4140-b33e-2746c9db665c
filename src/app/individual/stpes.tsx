// "use client"
//
// import {useEffect, useState} from "react"
// import {Loader2} from "lucide-react"
// import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card"
// import {Button} from "@/components/ui/button"
//
// type Step = {
//     name: string
//     status: "idle" | "loading" | "complete" | "error"
// }
//
// export default function OnboardingPage() {
//     const [steps, setSteps] = useState<Step[]>([
//         {name: "Personal Information", status: "idle"},
//         {name: "Account Setup", status: "idle"},
//         {name: "Card Registration", status: "idle"},
//     ])
//     const [currentStep, setCurrentStep] = useState(0)
//
//     const apiCalls = [
//         () => fetch("/api/onboarding/personal"),
//         () => fetch("/api/client/account"),
//         () => fetch("/api/client/card"),
//     ]
//
//     useEffect(() => {
//         if (currentStep < steps.length) {
//             callApi(currentStep)
//         }
//     }, [currentStep, steps.length]) // Added steps.length to dependencies
//
//     const callApi = async (stepIndex: number) => {
//         setSteps((prev) => prev.map((step, index) => (index === stepIndex ? {...step, status: "loading"} : step)))
//
//         try {
//             await apiCalls[stepIndex]()
//             setSteps((prev) => prev.map((step, index) => (index === stepIndex ? {...step, status: "complete"} : step)))
//             if (stepIndex < steps.length - 1) {
//                 setCurrentStep(stepIndex + 1)
//             }
//         } catch (error) {
//             setSteps((prev) => prev.map((step, index) => (index === stepIndex ? {...step, status: "error"} : step)))
//         }
//     }
//
//     const retryStep = (stepIndex: number) => {
//         setCurrentStep(stepIndex)
//     }
//
//     return (
//         <div className="container mx-auto p-4">
//             <Card className="w-full max-w-2xl mx-auto">
//                 <CardHeader>
//                     <CardTitle>Onboarding Process</CardTitle>
//                 </CardHeader>
//                 <CardContent>
//                     {steps.map((step, index) => (
//                         <div key={index} className="flex items-center mb-4">
//                             <div
//                                 className={`w-8 h-8 rounded-full flex items-center justify-center mr-4 ${
//                                     step.status === "complete"
//                                         ? "bg-green-500 text-white"
//                                         : step.status === "loading"
//                                             ? "bg-blue-500 text-white"
//                                             : step.status === "error"
//                                                 ? "bg-red-500 text-white"
//                                                 : "bg-gray-300"
//                                 }`}
//                             >
//                                 {step.status === "loading" ? (
//                                     <Loader2 className="h-5 w-5 animate-spin"/>
//                                 ) : step.status === "complete" ? (
//                                     "✓"
//                                 ) : step.status === "error" ? (
//                                     "!"
//                                 ) : (
//                                     index + 1
//                                 )}
//                             </div>
//                             <div className="flex-grow">
//                                 <p className="font-semibold">{step.name}</p>
//                                 <p className="text-sm text-muted-foreground">
//                                     {step.status === "idle" && "Waiting..."}
//                                     {step.status === "loading" && "Processing..."}
//                                     {step.status === "complete" && "Completed"}
//                                     {step.status === "error" && "Error occurred"}
//                                 </p>
//                             </div>
//                             {step.status === "error" && (
//                                 <Button variant="outline" onClick={() => retryStep(index)}>
//                                     Retry
//                                 </Button>
//                             )}
//                         </div>
//                     ))}
//                 </CardContent>
//             </Card>
//         </div>
//     )
// }
//

"use client"
import { Loader2 } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

export type Step = {
    name: string
    status: "idle" | "loading" | "complete" | "error"
}

interface StepLoaderProps {
    steps: Step[]
    currentStep: number
    onRetry: (stepIndex: number) => void
}

export function StepLoader({ steps, currentStep, onRetry }: StepLoaderProps) {
    return (
        <Card className="w-full max-w-2xl mx-auto">
            <CardHeader>
                <CardTitle>Onboarding Process</CardTitle>
            </CardHeader>
            <CardContent>
                {steps.map((step, index) => (
                    <div key={index} className="flex items-center mb-4">
                        <div
                            className={`w-8 h-8 rounded-full flex items-center justify-center mr-4 ${
                                step.status === "complete"
                                    ? "bg-green-500 text-white"
                                    : step.status === "loading"
                                        ? "bg-blue-500 text-white"
                                        : step.status === "error"
                                            ? "bg-red-500 text-white"
                                            : "bg-gray-300"
                            }`}
                        >
                            {step.status === "loading" ? (
                                <Loader2 className="h-5 w-5 animate-spin" />
                            ) : step.status === "complete" ? (
                                "✓"
                            ) : step.status === "error" ? (
                                "!"
                            ) : (
                                index + 1
                            )}
                        </div>
                        <div className="flex-grow">
                            <p className="font-semibold">{step.name}</p>
                            <p className="text-sm text-muted-foreground">
                                {step.status === "idle" && "Waiting..."}
                                {step.status === "loading" && "Processing..."}
                                {step.status === "complete" && "Completed"}
                                {step.status === "error" && "Error occurred"}
                            </p>
                        </div>
                        {step.status === "error" && (
                            <Button variant="outline" onClick={() => onRetry(index)}>
                                Retry
                            </Button>
                        )}
                    </div>
                ))}
            </CardContent>
        </Card>
    )
}


