//@ts-nocheck

"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
    AlertCircle,
    CheckCircle,
    Loader2,
    Info,
    Building2,
    MapPin,
    Phone,
    Mail,
    FileText,
    CreditCard,
    ArrowRight,
    ArrowLeft,
    RefreshCw,
} from "lucide-react"
import PhoneInput, { isValidPhoneNumber } from "react-phone-number-input"
import "react-phone-number-input/style.css"
import { CountrySelector } from "@/components/country-select"
import axiosInstance from "@/utils/axiosInstance"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {useAppSelector} from "@/store/hooks";

type Address = {
    street: string
    buildingNumber: string
    apartmentNumber: string
    city: string
    zipCode: string
    country: string
}

type FormData = {
    companyName: string
    clientCode: string
    phoneNumber: string
    authPhoneNumber: string
    email: string
    address: Address
    nip: string
    regon: string
    embossedName: string
    company: boolean
    customer: boolean
    selectedCompany?: string
}

// Define validation errors type
type ValidationErrors = {
    [key in keyof FormData]?: string
} & {
    address?: {
        [key in keyof Address]?: string
    }
}

// Function to generate unique 15-character client code
const generateClientCode = (): string => {
    const prefix = "RYVL-"
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    const timestamp = Date.now().toString().slice(-6) // Last 6 digits of timestamp

    // Generate 4 random characters
    let randomPart = ""
    for (let i = 0; i < 4; i++) {
        randomPart += chars.charAt(Math.floor(Math.random() * chars.length))
    }

    // Combine: RYVL- (5) + timestamp (6) + random (4) = 15 characters
    return prefix + timestamp + randomPart
}

const initialFormData: FormData = {
    companyName: "",
    clientCode: "",
    phoneNumber: "",
    authPhoneNumber: "",
    email: "",
    address: {
        street: "",
        buildingNumber: "",
        apartmentNumber: "",
        city: "",
        zipCode: "",
        country: "",
    },
    nip: "",
    regon: "",
    embossedName: "",
    company: true,
    customer: false,
}

export default function B2BAccountForm() {
    const [formData, setFormData] = useState<FormData>(initialFormData)
    const [alert, setAlert] = useState<{ type: "success" | "error"; message: string } | null>(null)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [isSubmitted, setIsSubmitted] = useState(false)
    const [phoneErrors, setPhoneErrors] = useState({
        phoneNumber: false,
        authPhoneNumber: false,
    })
    const [isPreviewMode, setIsPreviewMode] = useState(false)
    const [errors, setErrors] = useState<ValidationErrors>({})
    const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set())
    const [companies, setCompanies] = useState<{ _id: string; company_name: string }[]>([])
    const [selectedCompany, setSelectedCompany] = useState<string>("")
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [activeTab, setActiveTab] = useState("company-info")
    const [formProgress, setFormProgress] = useState(0)
    const user = useAppSelector((state) => state.user.user)


    // Auto-generate client code on component mount
    useEffect(() => {
        const autoGeneratedCode = generateClientCode()
        setFormData((prev) => ({
            ...prev,
            clientCode: autoGeneratedCode,
        }))
    }, [])

    useEffect(() => {
        fetchCompanies()
    }, [])

    useEffect(() => {
        // Calculate form completion percentage
        const requiredFields = [
            "companyName",
            "clientCode",
            "phoneNumber",
            "authPhoneNumber",
            "email",
            "nip",
            "regon",
            "embossedName",
        ]

        const requiredAddressFields = [
            "address.street",
            "address.buildingNumber",
            "address.city",
            "address.zipCode",
            "address.country",
        ]

        const allRequiredFields = [...requiredFields, ...requiredAddressFields]

        let filledFields = 0

        requiredFields.forEach((field) => {
            if (formData[field as keyof FormData] && !errors[field as keyof FormData]) {
                filledFields++
            }
        })

        requiredAddressFields.forEach((field) => {
            const addressField = field.split(".")[1] as keyof Address
            if (formData.address[addressField] && (!errors.address || !errors.address[addressField])) {
                filledFields++
            }
        })

        const progress = Math.round((filledFields / allRequiredFields.length) * 100)
        setFormProgress(progress)
    }, [formData, errors])

    const fetchCompanies = async () => {
        setIsLoading(true)
        try {
            const response = await axiosInstance.get("companies")
            setCompanies(response.data.data)
        } catch (error) {
            console.error("Error fetching companies", error)
            setAlert({ type: "error", message: "Failed to load companies. Please try again." })
        } finally {
            setIsLoading(false)
        }
    }

    // Function to regenerate client code
    const regenerateClientCode = () => {
        const newCode = generateClientCode()
        setFormData((prev) => ({
            ...prev,
            clientCode: newCode,
        }))

        // Clear any existing error for client code
        setErrors((prev) => ({
            ...prev,
            clientCode: "",
        }))

        // Show success message
        setAlert({
            type: "success",
            message: `New client code generated: ${newCode}`,
        })

        // Clear alert after 3 seconds
        setTimeout(() => setAlert(null), 3000)
    }

    // Validate a single field
    const validateField = (name: string, value: any): string => {
        // Email validation
        if (name === "email") {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            if (!value) return "Email is required"
            if (!emailRegex.test(value)) return "Please enter a valid email address"
        }

        // Required fields validation
        const requiredFields = ["companyName", "clientCode", "email", "nip", "regon", "embossedName"]

        if (requiredFields.includes(name) && !value) {
            return `${name.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase())} is required`
        }

        // Client code validation
        if (name === "clientCode") {
            if (value && value.length !== 15) {
                return "Client code must be exactly 15 characters"
            }
        }

        // NIP validation (Polish tax ID - 10 digits)
        if (name === "nip") {
            const nipRegex = /^\d{10}$/
            if (value && !nipRegex.test(value)) {
                return "NIP must be 10 digits"
            }
        }

        // REGON validation (Polish statistical number - 9 or 14 digits)
        if (name === "regon") {
            const regonRegex = /^(\d{9}|\d{14})$/
            if (value && !regonRegex.test(value)) {
                return "REGON must be 9 or 14 digits"
            }
        }

        // Address validation
        if (name.includes("address.")) {
            const addressField = name.split(".")[1]
            const requiredAddressFields = ["street", "city", "zipCode", "country"]

            if (requiredAddressFields.includes(addressField) && !value) {
                return `${addressField.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase())} is required`
            }

            // ZIP code validation
            if (addressField === "zipCode" && value) {
                // Simple ZIP code validation (can be enhanced for specific country formats)
                const zipRegex = /^[0-9a-zA-Z\s-]{4,10}$/
                if (!zipRegex.test(value)) {
                    return "Please enter a valid ZIP/postal code"
                }
            }
        }

        return ""
    }

    // Validate all form fields
    const validateForm = (): ValidationErrors => {
        const newErrors: ValidationErrors = {}

        // Validate top-level fields
        Object.entries(formData).forEach(([key, value]) => {
            if (key !== "address" && key !== "company" && key !== "customer") {
                const error = validateField(key, value)
                if (error) {
                    newErrors[key as keyof FormData] = error
                }
            }
        })

        // Validate address
        newErrors.address = {}
        Object.entries(formData.address).forEach(([key, value]) => {
            const error = validateField(`address.${key}`, value)
            if (error) {
                if (!newErrors.address) newErrors.address = {}
                newErrors.address[key as keyof Address] = error
            }
        })

        // Phone validation
        if (!formData.phoneNumber || !isValidPhoneNumber(formData.phoneNumber)) {
            newErrors.phoneNumber = "Please enter a valid phone number"
        }

        if (!formData.authPhoneNumber || !isValidPhoneNumber(formData.authPhoneNumber)) {
            newErrors.authPhoneNumber = "Please enter a valid authorization phone number"
        }

        return newErrors
    }

    // Check if there are any errors
    const hasErrors = (errors: ValidationErrors): boolean => {
        if (
            Object.keys(errors)
                .filter((key) => key !== "address")
                .some((key) => errors[key as keyof ValidationErrors])
        ) {
            return true
        }

        if (errors.address && Object.keys(errors.address).length > 0) {
            return true
        }

        return false
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        // Mark all fields as touched
        const allFields = new Set<string>()
        Object.keys(formData).forEach((key) => {
            if (key !== "address" && key !== "company" && key !== "customer") {
                allFields.add(key)
            }
        })

        Object.keys(formData.address).forEach((key) => {
            allFields.add(`address.${key}`)
        })

        setTouchedFields(allFields)

        // Validate all fields
        const validationErrors = validateForm()
        setErrors(validationErrors)

        if (hasErrors(validationErrors)) {
            setAlert({ type: "error", message: "Please correct the errors in the form before continuing." })
            // Scroll to the first error
            const firstErrorElement = document.querySelector(".error-message")
            if (firstErrorElement) {
                firstErrorElement.scrollIntoView({ behavior: "smooth", block: "center" })
            }
            return
        }

        if (!isPreviewMode) {
            // If not in preview mode, switch to preview instead of submitting
            setIsPreviewMode(true)
            window.scrollTo(0, 0) // Scroll to top to see the preview
            return
        }

        setIsSubmitting(true)
        try {
            // Prepare data for submission
            const dataToSubmit = {
                ...formData,
                parentCompany: user.recordId, // Add the selected company
            }

            // Submit data using axios instance
            const response = await axiosInstance.post("/b2b", dataToSubmit)

            // Check if the request was successful
            if (response.status >= 200 && response.status < 300) {
                setAlert({ type: "success", message: "B2B account created successfully!" })
                setIsSubmitted(true)
            } else {
                throw new Error(`Request failed with status ${response.status}`)
            }
        } catch (error) {
            console.error("Error submitting form:", error)
            setAlert({
                type: "error",
                message: error.response?.data?.message || "Error submitting form. Please try again.",
            })
        } finally {
            setIsSubmitting(false)
        }
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData((prevData) => ({ ...prevData, [name]: value }))

        // Mark field as touched
        setTouchedFields((prev) => new Set(prev).add(name))

        // Validate field on change
        const error = validateField(name, value)
        setErrors((prev) => ({
            ...prev,
            [name]: error,
        }))
    }

    const handleCheckboxChange = (name: string, checked: boolean) => {
        setFormData((prevData) => ({ ...prevData, [name]: checked }))
    }

    const handleCountryChange = (value: string) => {
        setFormData((prevData) => ({
            ...prevData,
            address: {
                ...prevData.address,
                country: value,
            },
        }))

        // Mark field as touched
        setTouchedFields((prev) => new Set(prev).add("address.country"))

        // Validate field on change
        const error = validateField("address.country", value)
        setErrors((prev) => ({
            ...prev,
            address: {
                ...prev.address,
                country: error,
            },
        }))
    }

    const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target

        // Mark field as touched
        setTouchedFields((prev) => new Set(prev).add(`address.${name}`))

        // Validate field
        const error = validateField(`address.${name}`, value)
        setErrors((prev) => ({
            ...prev,
            address: {
                ...prev.address,
                [name]: error,
            },
        }))

        // Update address field
        setFormData((prev) => ({
            ...prev,
            address: {
                ...prev.address,
                [name]: value,
            },
        }))
    }

    const handlePhoneChange = (value: string | undefined, field: "phoneNumber" | "authPhoneNumber") => {
        setFormData((prevData) => ({ ...prevData, [field]: value || "" }))

        // Mark field as touched
        setTouchedFields((prev) => new Set(prev).add(field))

        // Validate phone
        const isValid = value ? isValidPhoneNumber(value) : false
        setPhoneErrors((prev) => ({ ...prev, [field]: !isValid }))

        setErrors((prev) => ({
            ...prev,
            [field]: !isValid ? "Please enter a valid phone number" : "",
        }))
    }

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        const { name, value } = e.target

        // Mark field as touched
        setTouchedFields((prev) => new Set(prev).add(name))

        // Validate field on blur
        const error = validateField(name, value)
        setErrors((prev) => ({
            ...prev,
            [name]: error,
        }))
    }

    const handleAddressBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        const { name, value } = e.target

        // Mark field as touched
        setTouchedFields((prev) => new Set(prev).add(`address.${name}`))

        // Validate field
        const error = validateField(`address.${name}`, value)
        setErrors((prev) => ({
            ...prev,
            address: {
                ...prev.address,
                [name]: error,
            },
        }))
    }

    const handleCountryBlur = () => {
        // Mark field as touched
        setTouchedFields((prev) => new Set(prev).add("address.country"))

        // Validate field
        const error = validateField("address.country", formData.address.country)
        setErrors((prev) => ({
            ...prev,
            address: {
                ...prev.address,
                country: error,
            },
        }))
    }

    if (isSubmitted) {
        return (
            <div className="flex items-center justify-center h-[100vh] min-h-[100vh] bg-gradient-to-b from-green-50 to-white">
                <Card className="w-full max-w-md border-green-200 shadow-lg">
                    <CardHeader className="text-center pb-2">
                        <div className="mx-auto bg-green-100 p-3 rounded-full w-16 h-16 flex items-center justify-center mb-4">
                            <CheckCircle className="w-8 h-8 text-green-600" />
                        </div>
                        <CardTitle className="text-2xl font-bold text-green-700">B2B Account Created!</CardTitle>
                        <CardDescription className="text-green-600">Your account has been successfully registered</CardDescription>
                    </CardHeader>
                    <CardContent className="text-center">
                        <p className="text-gray-600 mb-6">
                            Thank you for submitting your B2B account information. Your account has been created successfully.
                        </p>
                    </CardContent>
                    <CardFooter className="flex flex-col gap-3">
                        <Button onClick={() => window.location.reload()} className="w-full  ">
                            Create Another B2B Account
                        </Button>
                        <Button variant="outline" className="w-full border-primary-200 text-primary-700 hover:bg-primary-50">
                            Go to Dashboard
                        </Button>
                    </CardFooter>
                </Card>
            </div>
        )
    }

    if (isPreviewMode) {
        return (
            <div className="space-y-8 max-w-4xl mx-auto p-4">
                {alert && (
                    <Alert variant={alert.type === "success" ? "default" : "destructive"} className="animate-fadeIn">
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>{alert.type === "success" ? "Success" : "Error"}</AlertTitle>
                        <AlertDescription>{alert.message}</AlertDescription>
                    </Alert>
                )}

                <Card className="shadow-lg border-t-4 border-t-primary overflow-hidden">
                    <CardHeader className="bg-gradient-to-r from-primary/10 to-primary/5 border-b">
                        <div className="flex items-center space-x-3">
                            <div className="p-2 bg-primary/20 rounded-full">
                                <CheckCircle className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                                <CardTitle className="text-2xl font-bold text-primary">Review B2B Account Information</CardTitle>
                                <CardDescription>Please review your information before final submission.</CardDescription>
                            </div>
                        </div>
                    </CardHeader>

                    <CardContent className="space-y-8 pt-6">
                        <FormPreviewComponent formData={formData} companies={companies} selectedCompany={selectedCompany} />
                    </CardContent>

                    <CardFooter className="flex flex-col sm:flex-row gap-4 border-t pt-6 bg-gray-50">
                        <Button
                            type="button"
                            variant="outline"
                            className="w-full sm:w-1/2 border-gray-300 hover:bg-gray-100"
                            onClick={() => setIsPreviewMode(false)}
                        >
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Edit
                        </Button>
                        <Button
                            type="button"
                            className="w-full sm:w-1/2 bg-primary hover:bg-primary/90"
                            onClick={handleSubmit}
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Creating Account...
                                </>
                            ) : (
                                <>
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Confirm & Create Account
                                </>
                            )}
                        </Button>
                    </CardFooter>
                </Card>
            </div>
        )
    }

    return (
        <form onSubmit={handleSubmit} className="space-y-8 max-w-4xl mx-auto p-4">
            {alert && (
                <Alert variant={alert.type === "success" ? "default" : "destructive"} className="animate-fadeIn">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>{alert.type === "success" ? "Success" : "Error"}</AlertTitle>
                    <AlertDescription>{alert.message}</AlertDescription>
                </Alert>
            )}

            <Card className="shadow-lg border-t-4 border-t-primary overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-primary/10 to-primary/5 border-b">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                        <div>
                            <CardTitle className="text-2xl font-bold text-primary">B2B Account Creation</CardTitle>
                            <CardDescription>Please fill out all the required information to create a B2B account.</CardDescription>
                        </div>
                        <div className="flex flex-col items-center bg-white rounded-lg p-3 border shadow-sm">
                            <div className="text-xs text-gray-500 mb-1">Form Completion</div>
                            <Progress value={formProgress} className="h-2 w-32" />
                            <div className="text-xs font-medium mt-1">{formProgress}%</div>
                        </div>
                    </div>

                    <div className="mt-2 flex items-start space-x-2 text-sm text-amber-600 bg-amber-50 p-3 rounded-md">
                        <Info className="h-4 w-4 mt-0.5 flex-shrink-0" />
                        <p>Fields marked with an asterisk (*) are required.</p>
                    </div>
                </CardHeader>

                {/* Company Selection Section */}
                <div className="p-6 border-b border-gray-200 bg-gray-50">
                    <div className="flex flex-col md:flex-row md:items-center gap-4">
                        <div className="flex-shrink-0">
                            <div className="bg-primary/20 p-2 rounded-full">
                                <Building2 className="h-5 w-5 text-primary" />
                            </div>
                        </div>
                        <div className="flex-grow">
                            <h3 className="text-lg font-semibold text-primary mb-2">Select Programme Manager</h3>
                            <div className="max-w-md">
                                <Label htmlFor="company" className="text-sm text-gray-600 mb-1.5 block">
                                    Choose Company
                                </Label>
                                <Select disabled value={user.recordId} onValueChange={(value) => setSelectedCompany(value)}>
                                    <SelectTrigger
                                        id="company"
                                        className={`${selectedCompany ? "border-green-500 bg-green-50" : ""} transition-all`}
                                    >
                                        <SelectValue placeholder="Select a company" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {isLoading ? (
                                            <div className="flex items-center justify-center p-2">
                                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                                <span>Loading companies...</span>
                                            </div>
                                        ) : companies.length > 0 ? (
                                            companies.map((company) => (
                                                <SelectItem key={company._id} value={company._id}>
                                                    {company.company_name}
                                                </SelectItem>
                                            ))
                                        ) : (
                                            <div className="p-2 text-sm text-gray-500">No companies available</div>
                                        )}
                                    </SelectContent>
                                </Select>
                                {selectedCompany && (
                                    <div className="mt-2 text-xs text-green-600 flex items-center">
                                        <CheckCircle className="h-3 w-3 mr-1" />
                                        Company selected
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <div className="px-6 pt-4 border-b">
                        <TabsList className="grid grid-cols-2 w-full max-w-md">
                            <TabsTrigger value="company-info" className="flex items-center gap-2">
                                <FileText className="h-4 w-4" />
                                <span>Company Info</span>
                            </TabsTrigger>
                            <TabsTrigger value="address" className="flex items-center gap-2">
                                <MapPin className="h-4 w-4" />
                                <span>Address</span>
                            </TabsTrigger>
                        </TabsList>
                    </div>

                    <CardContent className="p-0">
                        <TabsContent value="company-info" className="p-6 space-y-6 mt-0">
                            <div className="space-y-6">
                                <div className="flex items-center gap-2">
                                    <FileText className="h-5 w-5 text-primary" />
                                    <h3 className="text-lg font-semibold text-primary">Company Information</h3>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <ValidatedInput
                                        id="companyName"
                                        label="Company Name"
                                        value={formData.companyName}
                                        onChange={handleInputChange}
                                        onBlur={handleBlur}
                                        placeholder="Enter company name"
                                        error={errors.companyName}
                                        touched={touchedFields.has("companyName")}
                                        required
                                        icon={<Building2 className="h-4 w-4 text-gray-500" />}
                                    />

                                    {/* Enhanced Client Code Field with Auto-Generation */}
                                    <div className="space-y-2">
                                        <Label htmlFor="clientCode" className="flex text-sm font-medium">
                                            Client Code <span className="text-red-500 ml-1">*</span>
                                        </Label>
                                        <div className="flex gap-2">
                                            <div className="relative flex-1">
                                                <div className="absolute left-3 top-3">
                                                    <FileText className="h-4 w-4 text-gray-500" />
                                                </div>
                                                <Input
                                                    id="clientCode"
                                                    name="clientCode"
                                                    value={formData.clientCode}
                                                    onChange={handleInputChange}
                                                    onBlur={handleBlur} readOnly
                                                    placeholder="Auto-generated client code"
                                                    className={`pl-10 transition-all ${
                                                        touchedFields.has("clientCode") && errors.clientCode ? "border-red-500 bg-red-50" : ""
                                                    } ${formData.clientCode && !errors.clientCode ? "border-green-500 bg-green-50" : ""}`}
                                                    required
                                                />
                                                {formData.clientCode && !errors.clientCode && (
                                                    <div className="absolute right-10 top-3 text-green-500">
                                                        <CheckCircle className="h-4 w-4" />
                                                    </div>
                                                )}
                                            </div>

                                        </div>
                                        {touchedFields.has("clientCode") && errors.clientCode && (
                                            <p className="text-red-500 text-sm mt-1 error-message">{errors.clientCode}</p>
                                        )}
                                        <p className="text-xs text-gray-500">
                                            Auto-generated unique 15-character code. Click refresh to generate a new one.
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="phoneNumber" className="flex text-sm font-medium">
                                            Phone Number <span className="text-red-500 ml-1">*</span>
                                        </Label>
                                        <div className="relative">
                                            <div className="absolute left-3 top-3 text-gray-500">
                                                <Phone className="h-4 w-4" />
                                            </div>
                                            <PhoneInput
                                                international
                                                countryCallingCodeEditable={true}
                                                defaultCountry="PL"
                                                value={formData.phoneNumber}
                                                onChange={(value) => handlePhoneChange(value, "phoneNumber")}
                                                required
                                                className={`w-full p-2 pl-10 border rounded-md ${
                                                    touchedFields.has("phoneNumber") && errors.phoneNumber
                                                        ? "border-red-500 bg-red-50"
                                                        : "border-gray-300"
                                                } ${formData.phoneNumber && !errors.phoneNumber ? "border-green-500 bg-green-50" : ""}`}
                                            />
                                        </div>
                                        {touchedFields.has("phoneNumber") && errors.phoneNumber && (
                                            <p className="text-red-500 text-sm mt-1 error-message">{errors.phoneNumber}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="authPhoneNumber" className="flex text-sm font-medium">
                                            Authorization Phone Number <span className="text-red-500 ml-1">*</span>
                                        </Label>
                                        <div className="relative">
                                            <div className="absolute left-3 top-3 text-gray-500">
                                                <Phone className="h-4 w-4" />
                                            </div>
                                            <PhoneInput
                                                international
                                                countryCallingCodeEditable={true}
                                                defaultCountry="PL"
                                                value={formData.authPhoneNumber}
                                                onChange={(value) => handlePhoneChange(value, "authPhoneNumber")}
                                                required
                                                className={`w-full p-2 pl-10 border rounded-md ${
                                                    touchedFields.has("authPhoneNumber") && errors.authPhoneNumber
                                                        ? "border-red-500 bg-red-50"
                                                        : "border-gray-300"
                                                } ${formData.authPhoneNumber && !errors.authPhoneNumber ? "border-green-500 bg-green-50" : ""}`}
                                            />
                                        </div>
                                        {touchedFields.has("authPhoneNumber") && errors.authPhoneNumber && (
                                            <p className="text-red-500 text-sm mt-1 error-message">{errors.authPhoneNumber}</p>
                                        )}
                                    </div>

                                    <ValidatedInput
                                        id="email"
                                        label="Email"
                                        value={formData.email}
                                        onChange={handleInputChange}
                                        onBlur={handleBlur}
                                        type="email"
                                        placeholder="<EMAIL>"
                                        error={errors.email}
                                        touched={touchedFields.has("email")}
                                        required
                                        icon={<Mail className="h-4 w-4 text-gray-500" />}
                                    />

                                    <ValidatedInput
                                        id="nip"
                                        label="NIP (Tax ID)"
                                        value={formData.nip}
                                        onChange={handleInputChange}
                                        onBlur={handleBlur}
                                        placeholder="10-digit tax identification number"
                                        error={errors.nip}
                                        touched={touchedFields.has("nip")}
                                        required
                                        icon={<FileText className="h-4 w-4 text-gray-500" />}
                                    />

                                    <ValidatedInput
                                        id="regon"
                                        label="REGON"
                                        value={formData.regon}
                                        onChange={handleInputChange}
                                        onBlur={handleBlur}
                                        placeholder="9 or 14-digit statistical number"
                                        error={errors.regon}
                                        touched={touchedFields.has("regon")}
                                        required
                                        icon={<FileText className="h-4 w-4 text-gray-500" />}
                                    />

                                    <ValidatedInput
                                        id="embossedName"
                                        label="Embossed Name"
                                        value={formData.embossedName}
                                        onChange={handleInputChange}
                                        onBlur={handleBlur}
                                        placeholder="Name to be embossed on cards"
                                        error={errors.embossedName}
                                        touched={touchedFields.has("embossedName")}
                                        required
                                        icon={<CreditCard className="h-4 w-4 text-gray-500" />}
                                    />
                                </div>

                                <div className="flex justify-end pt-4">
                                    <Button type="button" onClick={() => setActiveTab("address")} className="flex items-center gap-2">
                                        Continue to Address
                                        <ArrowRight className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </TabsContent>

                        <TabsContent value="address" className="p-6 space-y-6 mt-0">
                            <div className="space-y-6">
                                <div className="flex items-center gap-2">
                                    <MapPin className="h-5 w-5 text-primary" />
                                    <h3 className="text-lg font-semibold text-primary">Company Address</h3>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <ValidatedInput
                                        id="street"
                                        name="street"
                                        label="Street"
                                        value={formData.address.street}
                                        onChange={handleAddressChange}
                                        onBlur={handleAddressBlur}
                                        placeholder="Enter street name"
                                        error={errors.address?.street}
                                        touched={touchedFields.has("address.street")}
                                        required
                                        icon={<MapPin className="h-4 w-4 text-gray-500" />}
                                    />

                                    <div className="grid grid-cols-2 gap-4">
                                        <ValidatedInput
                                            id="buildingNumber"
                                            name="buildingNumber"
                                            label="Building Number"
                                            value={formData.address.buildingNumber}
                                            onChange={handleAddressChange}
                                            onBlur={handleAddressBlur}
                                            placeholder="Building #"
                                            error={errors.address?.buildingNumber}
                                            touched={touchedFields.has("address.buildingNumber")}
                                            required
                                        />

                                        <ValidatedInput
                                            id="apartmentNumber"
                                            name="apartmentNumber"
                                            label="Apartment Number"
                                            value={formData.address.apartmentNumber}
                                            onChange={handleAddressChange}
                                            onBlur={handleAddressBlur}
                                            placeholder="Apt #"
                                            error={errors.address?.apartmentNumber}
                                            touched={touchedFields.has("address.apartmentNumber")}
                                        />
                                    </div>

                                    <ValidatedInput
                                        id="city"
                                        name="city"
                                        label="City"
                                        value={formData.address.city}
                                        onChange={handleAddressChange}
                                        onBlur={handleAddressBlur}
                                        placeholder="Enter city"
                                        error={errors.address?.city}
                                        touched={touchedFields.has("address.city")}
                                        required
                                        icon={<Building2 className="h-4 w-4 text-gray-500" />}
                                    />

                                    <ValidatedInput
                                        id="zipCode"
                                        name="zipCode"
                                        label="ZIP Code"
                                        value={formData.address.zipCode}
                                        onChange={handleAddressChange}
                                        onBlur={handleAddressBlur}
                                        placeholder="Enter ZIP code"
                                        error={errors.address?.zipCode}
                                        touched={touchedFields.has("address.zipCode")}
                                        required
                                    />

                                    <div className="md:col-span-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="country" className="flex text-sm font-medium">
                                                Country <span className="text-red-500 ml-1">*</span>
                                            </Label>
                                            <CountrySelector
                                                value={formData.address.country}
                                                onChange={handleCountryChange}
                                                onBlur={handleCountryBlur}
                                                error={errors.address?.country}
                                                touched={touchedFields.has("address.country")}
                                                required
                                                placeholder="Select country"
                                                className={`${
                                                    touchedFields.has("address.country") && errors.address?.country ? "border-red-500" : ""
                                                } ${formData.address.country && !errors.address?.country ? "border-green-500" : ""}`}
                                            />
                                            {touchedFields.has("address.country") && errors.address?.country && (
                                                <p className="text-red-500 text-sm mt-1 error-message">{errors.address.country}</p>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                <div className="flex justify-between pt-4">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => setActiveTab("company-info")}
                                        className="flex items-center gap-2"
                                    >
                                        <ArrowLeft className="h-4 w-4" />
                                        Back to Company Info
                                    </Button>

                                    <Button type="submit" className="flex items-center gap-2" disabled={isSubmitting}>
                                        {isSubmitting ? (
                                            <>
                                                <Loader2 className="h-4 w-4 animate-spin" />
                                                Submitting...
                                            </>
                                        ) : (
                                            <>
                                                Review Information
                                                <ArrowRight className="h-4 w-4" />
                                            </>
                                        )}
                                    </Button>
                                </div>
                            </div>
                        </TabsContent>
                    </CardContent>
                </Tabs>
            </Card>
        </form>
    )
}

interface ValidatedInputProps {
    id: string
    name?: string
    label: string
    value: string
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
    onBlur: (e: React.FocusEvent<HTMLInputElement>) => void
    placeholder?: string
    type?: string
    error?: string
    touched: boolean
    required?: boolean
    disabled?: boolean
    icon?: React.ReactNode
}

function ValidatedInput({
                            id,
                            name,
                            label,
                            value,
                            onChange,
                            onBlur,
                            placeholder,
                            type = "text",
                            error,
                            touched,
                            required = false,
                            disabled = false,
                            icon,
                        }: ValidatedInputProps) {
    return (
        <div className="space-y-2">
            <Label htmlFor={id} className="flex text-sm font-medium">
                {label} {required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <div className="relative">
                {icon && <div className="absolute left-3 top-3">{icon}</div>}
                <Input
                    id={id}
                    name={name || id}
                    value={value}
                    onChange={onChange}
                    onBlur={onBlur}
                    type={type}
                    required={required}
                    className={`w-full transition-all ${icon ? "pl-10" : ""} ${
                        touched && error ? "border-red-500 bg-red-50" : ""
                    } ${value && !error ? "border-green-500 bg-green-50" : ""}`}
                    placeholder={placeholder}
                    disabled={disabled}
                />
                {value && !error && (
                    <div className="absolute right-3 top-3 text-green-500">
                        <CheckCircle className="h-4 w-4" />
                    </div>
                )}
            </div>
            {touched && error && <p className="text-red-500 text-sm mt-1 error-message">{error}</p>}
        </div>
    )
}

function FormPreviewComponent({
                                  formData,
                                  companies,
                                  selectedCompany,
                              }: { formData: FormData; companies: { _id: string; company_name: string }[]; selectedCompany: string }) {
    // Helper function to check if an address is empty
    const isAddressEmpty = (address: Address) => {
        return !Object.values(address).some((value) => value.trim() !== "")
    }

    // Helper function to format address
    const formatAddress = (address: Address) => {
        if (isAddressEmpty(address)) return "Not provided"

        const parts = []
        if (address.street) parts.push(address.street)
        if (address.buildingNumber) {
            let buildingPart = address.buildingNumber
            if (address.apartmentNumber) buildingPart += `/${address.apartmentNumber}`
            parts.push(buildingPart)
        }

        const cityLine = []
        if (address.city) cityLine.push(address.city)
        if (address.zipCode) cityLine.push(address.zipCode)
        if (cityLine.length) parts.push(cityLine.join(", "))

        if (address.country) parts.push(address.country)

        return parts.join(", ")
    }

    return (
        <div className="space-y-8">
            {/* Company Information Section */}
            <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-primary/10 to-primary/5 px-4 py-3 border-b flex items-center gap-2">
                    <FileText className="h-5 w-5 text-primary" />
                    <h3 className="font-semibold text-lg text-primary">Company Information</h3>
                </div>
                <div className="p-4">
                    <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                        <PreviewItem label="Company Name" value={formData.companyName} icon={<Building2 className="h-4 w-4" />} />
                        <PreviewItem label="Client Code" value={formData.clientCode} icon={<FileText className="h-4 w-4" />} />
                        <PreviewItem label="Phone Number" value={formData.phoneNumber} icon={<Phone className="h-4 w-4" />} />
                        <PreviewItem
                            label="Authorization Phone Number"
                            value={formData.authPhoneNumber}
                            icon={<Phone className="h-4 w-4" />}
                        />
                        <PreviewItem label="Email" value={formData.email} icon={<Mail className="h-4 w-4" />} />
                        <PreviewItem label="NIP (Tax ID)" value={formData.nip} icon={<FileText className="h-4 w-4" />} />
                        <PreviewItem label="REGON" value={formData.regon} icon={<FileText className="h-4 w-4" />} />
                        <PreviewItem
                            label="Embossed Name"
                            value={formData.embossedName}
                            icon={<CreditCard className="h-4 w-4" />}
                        />

                        <div className="md:col-span-2 flex flex-wrap gap-3 mt-2">
                            {formData.company && (
                                <Badge
                                    variant="outline"
                                    className="bg-primary/10 text-primary border-primary/20 flex items-center gap-1.5"
                                >
                                    <Building2 className="h-3.5 w-3.5" />
                                    Company Account
                                </Badge>
                            )}
                            {formData.customer && (
                                <Badge
                                    variant="outline"
                                    className="bg-primary/10 text-primary border-primary/20 flex items-center gap-1.5"
                                >
                                    <CreditCard className="h-3.5 w-3.5" />
                                    Customer Account
                                </Badge>
                            )}
                            {selectedCompany && (
                                <Badge
                                    variant="outline"
                                    className="bg-green-100 text-green-700 border-green-200 flex items-center gap-1.5"
                                >
                                    <Building2 className="h-3.5 w-3.5" />
                                    {companies.find((c) => c._id === selectedCompany)?.company_name || "Selected Company"}
                                </Badge>
                            )}
                        </div>
                    </dl>
                </div>
            </div>

            {/* Company Address Section */}
            <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-primary/10 to-primary/5 px-4 py-3 border-b flex items-center gap-2">
                    <MapPin className="h-5 w-5 text-primary" />
                    <h3 className="font-semibold text-lg text-primary">Company Address</h3>
                </div>
                <div className="p-4">
                    <dl className="grid grid-cols-1 gap-x-6 gap-y-4">
                        <div>
                            <dt className="text-sm font-medium text-gray-500 mb-1 flex items-center gap-1.5">
                                <MapPin className="h-4 w-4" />
                                Address
                            </dt>
                            <dd className="text-sm text-gray-900 bg-gray-50 p-4 rounded border">{formatAddress(formData.address)}</dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    )
}

// Add this helper component at the end of the file
function PreviewItem({
                         label,
                         value,
                         icon,
                     }: { label: string; value: string | number | boolean; icon?: React.ReactNode }) {
    return (
        <div>
            <dt className="text-sm font-medium text-gray-500 mb-1 flex items-center gap-1.5">
                {icon}
                {label}
            </dt>
            <dd className="text-sm text-gray-900 bg-gray-50 p-3 rounded border">{value?.toString() || "Not provided"}</dd>
        </div>
    )
}
