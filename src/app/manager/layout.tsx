// @ts-nocheck
"use client"

import type * as React from "react"
import { useEffect, useState } from "react"
import { usePathname, useRouter } from "next/navigation"
import Image from "next/image"
import { ChevronRight, ChevronsUpDown, LogOut, UsersIcon } from "lucide-react"
import { PiCardholder } from "react-icons/pi"
import { Cog6ToothIcon } from "@heroicons/react/24/outline"

import { Avatar, AvatarImage } from "@/components/ui/avatar"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarHeader,
    SidebarInset,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
    SidebarProvider,
    SidebarRail,
    SidebarTrigger,
} from "@/components/ui/sidebar"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"

import axiosInstance from "@/utils/axiosInstance"
import { useAppDispatch, useAppSelector } from "@/store/hooks"
import { setUser } from "@/store/userSlice"

import { TeamSwitcher } from "@/app/lite/admin/DashboardSwitcher"
import { AutoBreadcrumb } from "@/components/auto-breadcrumb"
import { GlobalLoadingIndicator } from "@/components/global-loading-indicator"
import { Link } from "@/components/ui/link"
import { PageTransition } from "@/components/page-transition"
import { AutoLogoutProvider } from "@/components/auto-logout-provider"

// Keep PM Settings and menu structure exactly as is
type MenuItem = {
    title: string
    url: string
    icon?: React.ComponentType<any>
    isActive?: boolean
    items?: Array<{ title: string; url: string }>
}

const menuData: MenuItem[] = [
    { title: "Home", url: "/manager", icon: PiCardholder },
    {
        title: "Customers",
        url: "/manager/customers",
        icon: UsersIcon,
        isActive: false,
        items: [
            { title: "Individual", url: "/manager/customers/individual" },
            { title: "B2B", url: "/manager/customers/b2b" },
        ],
    },
    { title: "Create B2B Client", url: "/manager/b2b", icon: UsersIcon },
    // Settings stays the same (URL and labeling preserved)
    { title: "Settings", url: "/manager/settings", icon: Cog6ToothIcon },
]

const dashboards = [
    {
        name: "Programme Manager",
        logo: Cog6ToothIcon,
        plan: "Free",
    },
]

// Apply main dashboard logic patterns to PM layout [Sidebar composition pattern from shadcn/ui sidebar docs ^1]
export default function PMDashboardLayout({ children }: { children: React.ReactNode }) {
    const pathname = usePathname()
    const router = useRouter()
    const dispatch = useAppDispatch()

    const user = useAppSelector((state) => state.user.user)

    const [isAuthenticated, setIsAuthenticated] = useState(false)

    // New: permissions and permissionAudit from users/me
    const [permissions, setPermissions] = useState<string[]>([])
    const [permissionAudit, setPermissionAudit] = useState<{ key: string; enabled: boolean }[]>([])

    const [isLogoutDialogOpen, setIsLogoutDialogOpen] = useState(false)

    // Auth: Ported from MainDashboardLayout (adjust as needed to match your business rules)
    useEffect(() => {
        const checkAuth = async () => {
            try {
                const response = await axiosInstance.get("users/me", { withCredentials: true })
                const u = response?.data

                // Programme Manager access rule:
                // Allow if dashboard === "programmeManager" and recordId !== null
                if (u && u.dashboard === "programmeManager" && u.recordId !== null) {
                    // Support both u.pm.level and root-level permissions
                    const perms = (u?.pm?.permissions ?? u?.permissions ?? []) as string[]
                    const audit = (u?.pm?.permissionAudit ?? u?.permissionAudit ?? []) as { key: string; enabled: boolean }[]

                    setPermissions(perms)
                    setPermissionAudit(audit)

                    localStorage.setItem("user", u._id)
                    dispatch(setUser(u))
                    setIsAuthenticated(true)
                } else {
                    router.push("/login")
                }
            } catch (error) {
                console.error("Authentication error:", error)
                router.push("/login")
            }
        }

        checkAuth()
    }, [router, dispatch])

    const handleLogout = async () => {
        try {
            localStorage.removeItem("accessToken")
            setIsAuthenticated(false)
            setIsLogoutDialogOpen(false)
            window.location.replace("/login")
        } catch (error) {
            console.error("Error during logout:", error)
        }
    }

    const isActive = (href: string) => pathname === href

    // Permission helpers
    const normalize = (s?: string) => (s ?? "").trim().toLowerCase()
    const hasPermissionData = (permissions?.length ?? 0) > 0 || (permissionAudit?.length ?? 0) > 0

    const hasPermissionKey = (key?: string | null) => {
        if (!key) return true // if no key mapping, allow
        if (!hasPermissionData) return true // permissive fallback if no data present
        const nk = normalize(key)
        const inPermissions = permissions?.some((p) => normalize(p) === nk)
        if (inPermissions) return true
        const inAuditEnabled = permissionAudit?.some((a) => a?.enabled && normalize(a?.key) === nk)
        return !!inAuditEnabled
    }

    const subKey = (title: string) => `${title}_View` // default for sub-items (e.g., "Individual" -> "Individual_View")

    // Render a menu item with permission checks derived from users/me permissions & audit
    const renderMenuItem = (item: MenuItem) => {
        // Group with sub-items (Customers)
        if (item.items && item.items.length > 0) {
            // Only include sub-items that the user can view
            const allowedSubs = item.items.filter((sub) => hasPermissionKey(subKey(sub.title)))

            // If we have permission data and none of the subs are allowed, hide the whole group
            if (hasPermissionData && allowedSubs.length === 0) return null

            const anyActive = item.items.some((sub) => isActive(sub.url))

            return (
                <Collapsible key={item.title} asChild defaultOpen={item.isActive} className="group/collapsible">
                    <SidebarMenuItem>
                        <CollapsibleTrigger asChild>
                            <SidebarMenuButton
                                tooltip={item.title}
                                className={`font-bold transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground ${
                                    anyActive ? "text-primary bg-primary/10 border-l-2 border-primary" : "text-primary"
                                }`}
                            >
                                {item.icon && <item.icon />}
                                <span>{item.title}</span>
                                <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                            </SidebarMenuButton>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                            <SidebarMenuSub>
                                {(hasPermissionData ? allowedSubs : item.items).map((sub) => {
                                    const active = isActive(sub.url)
                                    return (
                                        <SidebarMenuSubItem
                                            key={sub.title}
                                            className={`transition-all duration-200 ${
                                                active
                                                    ? "bg-primary shadow-sm rounded-md border-l-2 border-primary-foreground ml-2"
                                                    : "hover:bg-sidebar-accent/50 hover:ml-1"
                                            }`}
                                        >
                                            <SidebarMenuSubButton
                                                className={`font-normal transition-all duration-200 ${
                                                    active ? "text-white font-medium" : "text-muted-foreground hover:text-foreground"
                                                }`}
                                                asChild
                                            >
                                                <Link href={sub.url} className="cursor-pointer">
                                                    <span>{sub.title}</span>
                                                </Link>
                                            </SidebarMenuSubButton>
                                        </SidebarMenuSubItem>
                                    )
                                })}
                            </SidebarMenuSub>
                        </CollapsibleContent>
                    </SidebarMenuItem>
                </Collapsible>
            )
        }

        // Single-level items
        let allowed = true
        if (item.title === "Home") {
            allowed = true // no permission required
        } else if (item.title === "Create B2B Client") {
            allowed = hasPermissionKey("Create B2B Client_Create")
        } else if (item.title === "Settings") {
            // show Settings if user has either view or modify
            allowed = hasPermissionKey("Settings_View") || hasPermissionKey("Settings_Modify")
        } else {
            // Fallback: try title_View pattern for any other single-level items
            allowed = hasPermissionKey(`${item.title}_View`)
        }

        if (!allowed) return null

        const active = isActive(item.url)

        return (
            <SidebarMenuItem
                key={item.title}
                className={`transition-all duration-200 ${
                    active
                        ? "bg-primary text-white rounded-md shadow-sm border-l-4 border-primary-foreground"
                        : "hover:bg-sidebar-accent/50"
                }`}
            >
                <SidebarMenuButton
                    tooltip={item.title}
                    className={`font-bold transition-all duration-200 ${
                        active ? "text-white hover:text-white" : "text-primary hover:text-primary hover:bg-transparent"
                    }`}
                    asChild
                >
                    <Link href={item.url} className="cursor-pointer">
                        {item.icon && <item.icon />}
                        <span>{item.title}</span>
                    </Link>
                </SidebarMenuButton>
            </SidebarMenuItem>
        )
    }

    if (!isAuthenticated) return null

    return (
        <SidebarProvider>
            {/* Global loader as in MainDashboardLayout */}
            <GlobalLoadingIndicator />

            {/* Keep PM look but adopt main's logic; sidebar composition per shadcn/ui Sidebar docs [^1] */}
            <Sidebar collapsible="icon" className="bg-white border-r border-gray-200">
                <SidebarHeader className="bg-white">
                    <TeamSwitcher teams={dashboards} />
                    <SidebarMenu>
                        <SidebarMenuItem>
                            <SidebarMenuButton size="lg" className="data-[state=open]:bg-gray-50 data-[state=open]:text-gray-900">
                                <Image
                                    src="/images/logo/icon-dark.svg"
                                    width={40}
                                    height={40}
                                    alt="ryvyl.eu"
                                    className="overflow-hidden size-8"
                                />
                                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">
                    <Image src="/logo.jpeg" width={150} height={150} alt="ryvyl.eu" className="overflow-hidden" />
                  </span>
                                </div>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    </SidebarMenu>
                </SidebarHeader>

                <SidebarContent className="bg-white">
                    <SidebarGroup>
                        <SidebarMenu>{menuData.map(renderMenuItem)}</SidebarMenu>
                    </SidebarGroup>
                </SidebarContent>

                <SidebarFooter className="bg-white">
                    <SidebarMenu>
                        <SidebarMenuItem>
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <SidebarMenuButton size="lg" className="data-[state=open]:bg-gray-50 data-[state=open]:text-gray-900">
                                        <Avatar className="h-8 w-8 rounded-lg">
                                            <AvatarImage src="/images/logo/icon-dark.svg" alt={user?.name || "User"} />
                                        </Avatar>
                                        <div className="grid flex-1 text-left text-sm leading-tight">
                                            <span className="truncate font-semibold">{user?.name || "User"}</span>
                                            <span className="truncate text-xs">{user?.email || "<EMAIL>"}</span>
                                        </div>
                                        <ChevronsUpDown className="ml-auto size-4" />
                                    </SidebarMenuButton>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent
                                    className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg bg-white"
                                    side="bottom"
                                    align="end"
                                    sideOffset={4}
                                >
                                    <DropdownMenuLabel className="p-0 font-normal">
                                        <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                                            <Avatar className="h-8 w-8 rounded-lg">
                                                <AvatarImage src="/images/logo/icon-dark.svg" alt={user?.name || "User"} />
                                            </Avatar>
                                            <div className="grid flex-1 text-left text-sm leading-tight">
                                                <span className="truncate font-semibold">{user?.name || "User"}</span>
                                                <span className="truncate text-xs">{user?.email || "<EMAIL>"}</span>
                                            </div>
                                        </div>
                                    </DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    <Dialog open={isLogoutDialogOpen} onOpenChange={setIsLogoutDialogOpen}>
                                        <DialogTrigger asChild>
                                            <DropdownMenuItem onSelect={(event) => event.preventDefault()}>
                                                <LogOut className="mr-2 h-4 w-4" />
                                                <span>Log out</span>
                                            </DropdownMenuItem>
                                        </DialogTrigger>
                                        <DialogContent>
                                            <DialogHeader>
                                                <DialogTitle>Are you sure you want to log out?</DialogTitle>
                                                <DialogDescription>You will be redirected to the login page.</DialogDescription>
                                            </DialogHeader>
                                            <DialogFooter>
                                                <Button variant="outline" onClick={() => setIsLogoutDialogOpen(false)}>
                                                    Cancel
                                                </Button>
                                                <Button variant="destructive" onClick={handleLogout}>
                                                    Log out
                                                </Button>
                                            </DialogFooter>
                                        </DialogContent>
                                    </Dialog>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </SidebarMenuItem>
                    </SidebarMenu>
                </SidebarFooter>

                <SidebarRail />
            </Sidebar>

            <SidebarInset>
                {/* Port main header UX: trigger + back button + breadcrumb */}
                <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12 bg-white border-b border-gray-200">
                    <div className="flex items-center gap-2 px-4 w-full">
                        <SidebarTrigger className="-ml-1" />
                        <Button
                            variant="ghost"
                            size="icon"
                            className="rounded-full mr-1"
                            onClick={() => router.back()}
                            aria-label="Go back"
                        >
                            <span className="sr-only">Go back</span>
                            {"<"}
                        </Button>
                        <Separator orientation="vertical" className="mr-2 h-4" />
                        <AutoBreadcrumb />
                    </div>
                </header>

                {/* Keep PM content background but add PageTransition like main */}
                <div className="flex flex-1 flex-col gap-4 p-4 pt-0 bg-gray-50 min-h-screen">
                    <PageTransition>
                        <AutoLogoutProvider
                            timeoutMinutes={10}
                            warningMinutes={1}
                            onLogout={() => {
                                console.log("User logged out due to inactivity")
                            }}
                        >
                            {children}
                        </AutoLogoutProvider>
                    </PageTransition>
                </div>
            </SidebarInset>
        </SidebarProvider>
    )
}
