import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import axiosInstance from "@/utils/axiosInstance"
import { Loader2 } from "lucide-react"

interface CancelResignCardDialogProps {
    cardId: string
    expDate: string
    onApiSuccess: () => void
}

export function CancelResignCardDialog({ cardId,expDate, onApiSuccess }: CancelResignCardDialogProps) {
    const [loading, setLoading] = useState(false)

    const handleCancelResignCard = async () => {
        try {
            setLoading(true)
            const data = {
                expDate
            }
            const response = await axiosInstance.post(`cards/${cardId}/cancel-resign`,data)

            if (typeof onApiSuccess === "function") {
                onApiSuccess()
            }
        } catch (error) {
            console.error("Failed to cancel card resignation", error)
            alert("Failed to cancel card resignation. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant="outline">Cancel Resignation</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Cancel Card Resignation</DialogTitle>
                    <DialogDescription>Are you sure you want to cancel the resignation of this card?</DialogDescription>
                </DialogHeader>

                <DialogFooter>
                    <Button type="submit" onClick={handleCancelResignCard} disabled={loading}>
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Canceling...
                            </>
                        ) : (
                            "Cancel Resignation"
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

