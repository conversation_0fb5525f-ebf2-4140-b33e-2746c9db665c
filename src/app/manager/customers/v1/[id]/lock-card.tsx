import React, {useState} from "react"
import {But<PERSON>} from "@/components/ui/button"
import axiosInstance from "@/utils/axiosInstance";
import {Loader2, Snowflake} from "lucide-react";

interface LockCardDialogProps {
    expDate: string;
    cardId: string;
    onApiSuccess:  () => {}
}

export function LockCardDialog({expDate, cardId, onApiSuccess}: LockCardDialogProps) {
    const [loading, setLoading] = useState(false)


    const handleLockCard = async () => {
        try {
            setLoading(true)

            const data = {
                expDate
            }
            const response = await axiosInstance.post(`cards/${cardId}/lock`, data)
            if (typeof onApiSuccess === 'function') {
                onApiSuccess();
            }

        } catch (error) {
            console.error('Card Locked successfully', error)
            alert('Failed. Please try again.')
        } finally {

            setLoading(false)
        }
    }


    return (
        <Button variant="ghost" onClick={handleLockCard} disabled={loading}
                className="flex flex-col items-center gap-2 h-auto py-3 hover:bg-green-50">
            <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <Snowflake className="h-6 w-6 text-green-600"/>
            </div>
            <span className="text-sm">{loading ? (
                <>
                    <Loader2
                        className="mr-2 h-4 w-4 animate-spin"/>
                    Freezing Card...
                </>
            ) : (
                'Freeze Card'
            )}</span>
        </Button>

    )
}



