import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import axiosInstance from "@/utils/axiosInstance"
import {ChevronRight, Loader2, LockKeyhole, Pencil} from "lucide-react"

interface ForcePinLockDialogProps {
    cardId: string
    expDate: string
    onApiSuccess: () => void
}

export function ForcePinLockDialog({ cardId,expDate, onApiSuccess }: ForcePinLockDialogProps) {
    const [loading, setLoading] = useState(false)
    const [isOpen, setIsOpen] = useState(false)

    const handleForcePinLock = async () => {
        try {
            setLoading(true)
            const response = await axiosInstance.post(`cards/${cardId}/forcePinLock`,{expDate})

            if (typeof onApiSuccess === "function") {
                onApiSuccess()
            }
            setIsOpen(false)
        } catch (error) {
            console.error("Failed to force PIN lock on card", error)
            alert("Failed to force PIN lock. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <LockKeyhole className="h-5 w-5 text-gray-600"/>
                        </div>
                        <span className="font-normal">Force Pin Lock</span>
                    </div>
                    <ChevronRight/>
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Force PIN Lock</DialogTitle>
                    <DialogDescription>
                        Are you sure you want to force a PIN lock on this card? This action will require the user to enter their PIN
                        for the next transaction.
                    </DialogDescription>
                </DialogHeader>

                <DialogFooter>
                    <Button type="submit" onClick={handleForcePinLock} disabled={loading}>
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />
                                <span>Locking...</span>
                            </>
                        ) : (
                            "Force PIN Lock"
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

