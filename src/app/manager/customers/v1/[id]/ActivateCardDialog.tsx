import {useState} from "react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    <PERSON>alogHeader,
    <PERSON>alogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import axiosInstance from "@/utils/axiosInstance"
import {ChevronRight, Loader2, Pencil} from "lucide-react"
import {CheckBadgeIcon} from "@heroicons/react/16/solid";

interface ActivateCardDialogProps {
    cardId: string
    expDate: string
    onApiSuccess: () => void
}

export function ActivateCardDialog({ cardId,expDate, onApiSuccess }: ActivateCardDialogProps) {
    const [loading, setLoading] = useState(false)

    const handleActivateCard = async () => {
        try {

            setLoading(true)
            const data = {
                expDate
            }
            const response = await axiosInstance.post(`cards/${cardId}/activate`, data)

            if (typeof onApiSuccess === "function") {
                onApiSuccess()
            }
        } catch (error) {
            console.error("Failed to activate card", error)
            alert("Failed to activate card. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <CheckBadgeIcon className="h-5 w-5 text-gray-600"/>
                        </div>
                        <span className="font-normal">Activate Card</span>
                    </div>
                    <ChevronRight/>
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Activate Card</DialogTitle>
                    <DialogDescription>Are you sure you want to activate this card?</DialogDescription>
                </DialogHeader>

                <DialogFooter>
                    <Button type="submit" onClick={handleActivateCard} disabled={loading}>
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Activating...
                            </>
                        ) : (
                            "Activate"
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

