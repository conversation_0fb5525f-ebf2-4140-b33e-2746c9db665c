"use client"

import {useState} from "react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {ChevronRight, CreditCard} from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"

interface SetCardLimitsDialogProps {
    cardId: string
    onApiSuccess: () => void
}

type LimitType = "daily" | "monthly" | "yearly"

export function SetCardLimitsDialog({cardId, onApiSuccess}: SetCardLimitsDialogProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [loading, setLoading] = useState(false)
    const [limitValue, setLimitValue] = useState("")
    const [limitType, setLimitType] = useState<LimitType>("daily")

    const handleSetLimit = async () => {
        try {
            setLoading(true)
            const data = {limitType, limitValue: Number.parseFloat(limitValue)}
            await axiosInstance.post(`cards/${cardId}/set-limit`, data)
            onApiSuccess()
            setIsOpen(false)
            setLimitValue("")
            setLimitType("daily")
        } catch (error) {
            console.error("Failed to set card limit", error)
            alert("Failed to set card limit. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <CreditCard className="h-5 w-5 text-gray-600"/>
                        </div>
                        <span className="font-normal">Set Card Limits</span>
                    </div>
                    <ChevronRight/>
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Set Card Limits</DialogTitle>
                    <DialogDescription>
                        Set spending limits for your card. Choose a limit type and enter the amount.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <label htmlFor="limitType" className="text-right">
                            Limit Type
                        </label>
                        <Select value={limitType} onValueChange={(value: LimitType) => setLimitType(value)}>
                            <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Select limit type"/>
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="POS_PUR_LMT">POS_PUR_LMT</SelectItem>
                                <SelectItem value="MONTH_POS_PUR_LMT">MONTH_POS_PUR_LMT</SelectItem>
                                <SelectItem value="ATM_CCA_LMT">ATM_CCA_LMT</SelectItem>
                                <SelectItem value="MONTH_ATM_CCA_LMT">MONTH_ATM_CCA_LMT</SelectItem>
                                <SelectItem value="CNP_AMT_LMT">CNP_AMT_LMT</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <label htmlFor="limitValue" className="text-right">
                            Limit Amount
                        </label>
                        <Input
                            id="limitValue"
                            type="number"
                            value={limitValue}
                            onChange={(e) => setLimitValue(e.target.value)}
                            placeholder="Enter limit amount"
                            className="col-span-3"
                        />
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Cancel
                    </Button>
                    <Button onClick={handleSetLimit}
                            disabled={loading || !limitValue || Number.parseFloat(limitValue) <= 0}>
                        {loading ? "Setting Limit..." : "Set Limit"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

