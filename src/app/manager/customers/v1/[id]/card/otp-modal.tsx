import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {Shield, ChevronRight, Building} from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"
import {SlCalender} from "react-icons/sl";
import {BiMoney} from "react-icons/bi";
import {Label} from "@/components/ui/label";

interface OTPDialogProps {
    expDate: string
    cardId: string
    onApiSuccess: () => void
}

export function OTPDialog({ expDate, cardId, onApiSuccess }: OTPDialogProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [loading, setLoading] = useState(false)
    const [newAnswer, setNewAnswer] = useState("")

    const handleChange3DSecure = async () => {
        try {
            setLoading(true)
            const data = { expDate, newAnswer }
            console.dir(data)
            await axiosInstance.post(`cards/${cardId}/change-3d-ans`, data)
            onApiSuccess()
            setIsOpen(false)
            setNewAnswer("")
        } catch (error) {
            console.error("Failed to change 3D Secure answer", error)
            alert("Failed to change 3D Secure answer. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <span className="font-normal">Show 3DS OTP</span>
                    </div>
                    <ChevronRight />
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>3DS OTP for recent Transaction</DialogTitle>
                    <DialogDescription>
                      Your recent transaction for EUR 99.00 3DS OTP is here.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="flex items-center gap-2">
                        <Shield className="h-5 w-5 text-gray-500" />
                        <span>994433</span>
                    </div>

                    <div className="flex items-center gap-2">
                        <Building className="h-5 w-5 text-gray-500" />
                        <span>Merchant Name: 994433</span>
                    </div>
                    <div className="flex items-center gap-2">
                        <BiMoney className="h-5 w-5 text-gray-500" />
                        <span>Amount: EUR 99.00</span>
                    </div>

                    <div className="flex items-center gap-2">
                        <SlCalender className="h-5 w-5 text-gray-500"/>
                        <span>Date: 12 Dec 2024</span>
                    </div>

                    <Label>OTP</Label>
                    <Input

                        value="994433"

                    />
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Done
                    </Button>

                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

