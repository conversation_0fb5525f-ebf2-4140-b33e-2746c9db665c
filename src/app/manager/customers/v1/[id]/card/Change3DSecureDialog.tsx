import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Shield, ChevronRight } from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"

interface Change3DSecureDialogProps {
    expDate: string
    cardId: string
    onApiSuccess: () => void
}

export function Change3DSecureDialog({ expDate, cardId, onApiSuccess }: Change3DSecureDialogProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [loading, setLoading] = useState(false)
    const [newAnswer, setNewAnswer] = useState("")

    const handleChange3DSecure = async () => {
        try {
            setLoading(true)
            const data = { expDate, newAnswer }
            console.dir(data)
            await axiosInstance.post(`cards/${cardId}/change-3d-ans`, data)
            onApiSuccess()
            setIsOpen(false)
            setNewAnswer("")
        } catch (error) {
            console.error("Failed to change 3D Secure answer", error)
            alert("Failed to change 3D Secure answer. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <Shield className="h-5 w-5 text-gray-600" />
                        </div>
                        <span className="font-normal">Change 3D Secure Answer</span>
                    </div>
                    <ChevronRight />
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Change 3D Secure Answer</DialogTitle>
                    <DialogDescription>
                        Enter your new 3D Secure answer below. This will be used to verify your identity for online transactions.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <Input
                        id="new3DSecureAnswer"
                        placeholder="New 3D Secure Answer"
                        value={newAnswer}
                        onChange={(e) => setNewAnswer(e.target.value)}
                    />
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Cancel
                    </Button>
                    <Button onClick={handleChange3DSecure} disabled={loading || !newAnswer.trim()}>
                        {loading ? "Changing..." : "Change Answer"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

