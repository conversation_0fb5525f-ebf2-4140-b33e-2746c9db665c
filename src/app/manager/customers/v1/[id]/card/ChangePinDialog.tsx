"use client"

import {useState} from "react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {AlertCircle, ChevronRight, Key} from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"
import {<PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip"
import {Label} from "@/components/ui/label";


interface ChangePinDialogProps {
    expDate: string
    cardId: string
    onApiSuccess: () => void
}

export function ChangePinDialog({ expDate, cardId, onApiSuccess }: ChangePinDialogProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [loading, setLoading] = useState(false)
    const [newPin, setNewPin] = useState("")
    const [error, setError] = useState("")
    const handleChangePin = async () => {
        try {
            setLoading(true)
            const data = { expDate, newPin }
            console.dir(data)
            await axiosInstance.post(`pin/${cardId}/change-pin`, data)
            onApiSuccess()
            setIsOpen(false)
            setNewPin("")
        } catch (error) {
            console.error("Failed to change PIN", error)
            alert("Failed to change PIN. Please try again.")
        } finally {
            setLoading(false)
        }
    }



    const handlePinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value
        setNewPin(value)

        // Validation
        if (value.length > 0 && !/^\d+$/.test(value)) {
            setError("PIN must contain only numbers")
        } else if (value.length > 4) {
            setError("PIN must be exactly 4 digits")
        } else {
            setError("")
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <Key className="h-5 w-5 text-gray-600" />
                        </div>
                        <span className="font-normal">Change PIN</span>
                    </div>
                    <ChevronRight />
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Change PIN</DialogTitle>
                    <DialogDescription>
                        Enter your new PIN below. This will be used for ATM withdrawals and in-store purchases.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <Label
                                    htmlFor="newPin"
                                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                    Enter your new PIN below
                                </Label>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>PIN must be 4 numbers only</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                    <div className="relative">
                        <Input
                            id="newPin"
                            type="password"
                            placeholder="New PIN"
                            value={newPin}
                            onChange={handlePinChange}
                            maxLength={4}
                            className={error ? "border-red-500" : ""}
                        />
                        {error && (
                            <div className="absolute right-2 top-1/2 -translate-y-1/2 text-red-500">
                                <AlertCircle size={18}/>
                            </div>
                        )}
                    </div>
                    {error && <p className="text-sm text-red-500">{error}</p>}
                </div>

                <DialogFooter>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Cancel
                    </Button>
                    <Button onClick={handleChangePin} disabled={loading || newPin.length !== 4}>
                        {loading ? "Changing..." : "Change PIN"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

