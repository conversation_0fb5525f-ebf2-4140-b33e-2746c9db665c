"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { ChevronRight, Eye, Key, ShieldCheck } from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"
import {ChangePinDialog} from "@/app/lite/admin/individual/v1/[id]/card/ChangePinDialog";
import {VerifyPinDialog} from "@/app/lite/admin/individual/v1/[id]/card/VerifyPinDialog";
import {ResetPinTriesDialog} from "@/app/cardholder/cards/ResetPinTriesDialog";

interface PinManagementDialogProps {
    cardId: string
    expDate: string
    ApiSuccess: () => void
}

export function PinManagementDialog({ cardId,expDate, ApiSuccess }: PinManagementDialogProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [loading, setLoading] = useState(false)

    const pinOptions = [
        { id: "showPin", label: "Show PIN", icon: Eye },
        { id: "changePin", label: "Change PIN", icon: Key },
        { id: "verifyPin", label: "Verify PIN", icon: ShieldCheck },
    ]



    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <Key className="h-5 w-5 text-gray-600" />
                        </div>
                        <span className="font-normal">PIN Management</span>
                    </div>
                    <ChevronRight />
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>PIN Management</DialogTitle>
                    <DialogDescription>Choose a PIN management action.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    {/*{pinOptions.map((option) => (*/}
                    {/*    <Button*/}
                    {/*        disabled={loading}*/}
                    {/*        key={option.id}*/}
                    {/*        onClick={() => handlePinAction(option.id)}*/}
                    {/*        variant="ghost"*/}
                    {/*        className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50"*/}
                    {/*    >*/}
                    {/*        <div className="flex items-center gap-3">*/}
                    {/*            <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">*/}
                    {/*                <option.icon className="h-5 w-5 text-gray-600" />*/}
                    {/*            </div>*/}
                    {/*            <span className="font-normal">{option.label}</span>*/}
                    {/*        </div>*/}
                    {/*        <ChevronRight />*/}
                    {/*    </Button>*/}
                    {/*))}*/}

                    <ChangePinDialog
                        cardId={cardId}  expDate={expDate}
                        onApiSuccess={() =>  {console.dir("Pin Verified")} }
                    />

                    <VerifyPinDialog   cardId={cardId}   />
                    <ResetPinTriesDialog
                        cardId={cardId}
                        onApiSuccess={() => {

                        }}
                    />
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Cancel
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

