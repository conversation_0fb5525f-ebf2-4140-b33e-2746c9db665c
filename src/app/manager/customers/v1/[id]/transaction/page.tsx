//@ts-nocheck
"use client"

import { useState, useEffect } from "react"
import { useSearchP<PERSON><PERSON>, useRout<PERSON> } from "next/navigation"
import { ChevronLeft, ChevronRight, Download, Filter, RefreshCw, Search } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { DateRangePicker } from "@/components/date-range-picker"
import { Skeleton } from "@/components/ui/skeleton"
import axiosInstance from "@/utils/axiosInstance";
import {countries, country_currency} from "@/utils/data";

interface Transaction {
    transactionTypeCode: string
    transactionAmount: number
    billingAmount: number
    transactionDatetime: string
    conversionRate: string
    traceNumber: string
    cardExpiryDate: string
    retrievalReference: string
    terminalId: string
    merchantId: string
    merchantLocation: string
    currencyTransaction: string
    currencyBilling: string
    accountIdentification: string
    customFields: {
        transactionInfo: string
        paypalReference: string
    }
    extendedData: {
        authorizationLevel: string
        authorizationMethod: string
        productCode: string
        deviceId: string
        channelId: string
        riskScore: string
        retryCount: string
        trackingId: string
    }
    externalToken: string
}

export default function TransactionsPage() {
    const router = useRouter()
    const searchParams = useSearchParams()

    // Pagination state
    const [page, setPage] = useState(Number.parseInt(searchParams.get("page") || "1"))
    const [limit, setLimit] = useState(Number.parseInt(searchParams.get("limit") || "10"))
    const [totalPages, setTotalPages] = useState(1)
    const [totalTransactions, setTotalTransactions] = useState(0)

    // Filter state
    const [transactionType, setTransactionType] = useState(searchParams.get("type") || "all")
    const [terminalId, setTerminalId] = useState(searchParams.get("terminal") || "all")
    const [search, setSearch] = useState(searchParams.get("search") || "")
    const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({
        from: searchParams.get("from") ? new Date(searchParams.get("from") as string) : undefined,
        to: searchParams.get("to") ? new Date(searchParams.get("to") as string) : undefined,
    })

    // Data state
    const [transactions, setTransactions] = useState<Transaction[]>([])
    const [loading, setLoading] = useState(true)
    const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null)

    // Fetch transactions with current filters and pagination
    const fetchTransactions = async () => {
        setLoading(true)

        // Build query params
        const params = new URLSearchParams()
        params.set("page", page.toString())
        params.set("limit", limit.toString())

        if (transactionType && transactionType !== "all") params.set("type", transactionType)
        if (terminalId && terminalId !== "all") params.set("terminal", terminalId)
        if (search) params.set("search", search)
        if (dateRange.from) params.set("from", dateRange.from.toISOString())
        if (dateRange.to) params.set("to", dateRange.to.toISOString())

        try {
            // For demo purposes, we'll simulate the API call with mock data
            // In production, you would use: const response = await axiosInstance.get(`/transactions?${params.toString()}`)

            // Mock response data
            const mockResponse = {
                data: {
                    success: true,
                    transactions: [
                        {
                            transactionTypeCode: "380000",
                            transactionAmount: 0,
                            billingAmount: 0,
                            transactionDatetime: "0415062456",
                            conversionRate: "",
                            traceNumber: "578388",
                            cardExpiryDate: "2804",
                            retrievalReference: "************",
                            terminalId: "TERM0001",
                            merchantId: "PORTAL1        ",
                            merchantLocation: "CMS\\ADDRESS\\CITY\\    00-123AAAPOL",
                            currencyTransaction: "248",
                            currencyBilling: "",
                            accountIdentification: "**********************",
                            customFields: {
                                transactionInfo: "",
                                paypalReference: ""
                            },
                            extendedData: {
                                authorizationLevel: "",
                                authorizationMethod: "",
                                productCode: "",
                                deviceId: "",
                                channelId: "",
                                riskScore: "",
                                retryCount: "",
                                trackingId: ""
                            },
                            externalToken: ""
                        }
                    ],
                    pagination: {
                        total: 1,
                        page: 1,
                        limit: 10,
                        totalPages: 1
                    }
                }
            };

            const data = mockResponse.data;

            if (data.success) {
                setTransactions(data.transactions)
                setTotalPages(data.pagination.totalPages)
                setTotalTransactions(data.pagination.total)
            } else {
                console.error("Failed to fetch transactions:", data.message)
            }
        } catch (error) {
            console.error("Error fetching transactions:", error)
        } finally {
            setLoading(false)
        }
    }

    // Update URL with current filters and pagination
    const updateUrl = () => {
        const params = new URLSearchParams()

        params.set("page", page.toString())
        params.set("limit", limit.toString())

        if (transactionType && transactionType !== "all") params.set("type", transactionType)
        if (terminalId && terminalId !== "all") params.set("terminal", terminalId)
        if (search) params.set("search", search)
        if (dateRange.from) params.set("from", dateRange.from.toISOString())
        if (dateRange.to) params.set("to", dateRange.to.toISOString())

        router.push(`transaction?${params.toString()}`)
    }

    // Apply filters
    const applyFilters = () => {
        setPage(1) // Reset to first page when filters change
        updateUrl()
        fetchTransactions()
    }

    // Reset filters
    const resetFilters = () => {
        setTransactionType("all")
        setTerminalId("all")
        setSearch("")
        setDateRange({ from: undefined, to: undefined })
        setPage(1)
    }

    // Handle pagination
    const goToPage = (newPage: number) => {
        if (newPage < 1 || newPage > totalPages) return
        setPage(newPage)
    }

    // Effect to fetch transactions when page or limit changes
    useEffect(() => {
        updateUrl()
        fetchTransactions()
    }, [page, limit])

    // Format date for display
    const formatDate = (dateString: string) => {
        // Example format: "0415062456" -> "2024-04-15 06:24:56"
        if (!dateString || dateString.length < 10) return 'N/A';

        const year = "20" + dateString.substr(0, 2);
        const month = dateString.substr(2, 2);
        const day = dateString.substr(4, 2);
        const hour = dateString.substr(6, 2);
        const minute = dateString.substr(8, 2);
        let second = "00";
        if (dateString.length >= 12) {
            second = dateString.substr(10, 2);
        }

        return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    }

    // Get transaction type badge color
    const getTransactionTypeColor = (typeCode: string) => {
        const code = typeCode.substring(0, 2);
        switch (code) {
            case "38": // Authorization
                return "bg-blue-500"
            case "20": // Purchase
                return "bg-green-500"
            case "22": // Refund
                return "bg-yellow-500"
            case "24": // Reversal
                return "bg-red-500"
            default:
                return "bg-gray-500"
        }
    }

    // Get transaction type description
    const getTransactionTypeDescription = (typeCode: string) => {
        const code = typeCode.substring(0, 2);
        switch (code) {
            case "38": return "Authorization"
            case "20": return "Purchase"
            case "22": return "Refund"
            case "24": return "Reversal"
            default: return "Other"
        }
    }

    return (
        <div className="container mx-auto py-6 space-y-6">
            <div className="flex justify-between items-center">
                <h1 className="text-3xl font-bold">Transactions</h1>
                <div className="flex items-center gap-2">
                    <Button variant="outline" size="icon" onClick={fetchTransactions} title="Refresh transactions">
                        <RefreshCw className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" title="Export transactions">
                        <Download className="h-4 w-4" />
                    </Button>
                </div>
            </div>

            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle>Filters</CardTitle>
                    <CardDescription>Filter transactions by various criteria</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Search</label>
                            <div className="relative">
                                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    type="text"
                                    placeholder="Search Reference, Account..."
                                    className="pl-8"
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <label className="text-sm font-medium">Transaction Type</label>
                            <Select value={transactionType} onValueChange={setTransactionType}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Types" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Types</SelectItem>
                                    <SelectItem value="38">Authorization</SelectItem>
                                    <SelectItem value="20">Purchase</SelectItem>
                                    <SelectItem value="22">Refund</SelectItem>
                                    <SelectItem value="24">Reversal</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <label className="text-sm font-medium">Terminal ID</label>
                            <Select value={terminalId} onValueChange={setTerminalId}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Terminals" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Terminals</SelectItem>
                                    <SelectItem value="TERM0001">TERM0001</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <label className="text-sm font-medium">Date Range</label>
                            <DateRangePicker date={dateRange} onDateChange={setDateRange} />
                        </div>
                    </div>

                    <div className="flex justify-end gap-2 mt-4">
                        <Button variant="outline" onClick={resetFilters}>
                            Reset
                        </Button>
                        <Button onClick={applyFilters}>Apply Filters</Button>
                    </div>
                </CardContent>
            </Card>

            {/* Transactions Table */}
            <Card>
                <CardHeader>
                    <div className="flex justify-between items-center">
                        <CardTitle>Transactions</CardTitle>
                        <div className="text-sm text-muted-foreground">
                            Showing {transactions.length} of {totalTransactions} transactions
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="rounded-md border overflow-hidden">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-[180px]">Date/Time</TableHead>
                                    <TableHead className="w-[120px]">Type</TableHead>
                                    <TableHead className="w-[120px]">Transaction Amount</TableHead>
                                    <TableHead className="w-[120px]">Billing Amount</TableHead>
                                    <TableHead className="w-[140px]">Reference</TableHead>
                                    <TableHead className="w-[120px]">Terminal ID</TableHead>
                                    <TableHead>Account</TableHead>
                                    <TableHead className="w-[80px]">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {loading ? (
                                    Array.from({ length: limit }).map((_, i) => (
                                        <TableRow key={i}>
                                            <TableCell>
                                                <Skeleton className="h-6 w-full" />
                                            </TableCell>
                                            <TableCell>
                                                <Skeleton className="h-6 w-16" />
                                            </TableCell>
                                            <TableCell>
                                                <Skeleton className="h-6 w-16" />
                                            </TableCell>
                                            <TableCell>
                                                <Skeleton className="h-6 w-full" />
                                            </TableCell>
                                            <TableCell>
                                                <Skeleton className="h-6 w-24" />
                                            </TableCell>
                                            <TableCell>
                                                <Skeleton className="h-6 w-full" />
                                            </TableCell>
                                            <TableCell>
                                                <Skeleton className="h-6 w-8" />
                                            </TableCell>
                                        </TableRow>
                                    ))
                                ) : transactions.length > 0 ? (
                                    transactions.map((transaction, index) => (
                                        <TableRow key={index}>
                                            <TableCell className="font-mono text-xs">
                                                {formatDate(transaction.transactionDatetime)}
                                            </TableCell>
                                            <TableCell>
                                                <Badge className={`${getTransactionTypeColor(transaction.transactionTypeCode)} text-white`}>
                                                    {getTransactionTypeDescription(transaction.transactionTypeCode)}
                                                </Badge>
                                            </TableCell>
                                            <TableCell className="font-mono">
                                                {  Intl.NumberFormat('en-US', {
                                                    style: 'currency',
                                                    currency: country_currency.find(r=>r.numericCode===transaction.currencyTransaction)?.currencyCode || 'EUR'
                                                }).format(transaction.transactionAmount)}
                                            </TableCell>
                                            <TableCell className="font-mono">
                                                {  Intl.NumberFormat('en-US', {
                                                    style: 'currency',
                                                    currency: country_currency.find(r=>r.numericCode===transaction.currencyBilling)?.currencyCode || 'EUR'
                                                }).format(transaction.billingAmount)}
                                            </TableCell>
                                            <TableCell className="font-mono text-xs">{transaction.retrievalReference}</TableCell>
                                            <TableCell className="font-mono text-xs">{transaction.terminalId}</TableCell>
                                            <TableCell className="max-w-[200px] truncate font-mono text-xs">
                                                {transaction.accountIdentification}
                                            </TableCell>
                                            <TableCell>
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="icon" className="h-8 w-8">
                                                            <Filter className="h-4 w-4" />
                                                            <span className="sr-only">Open menu</span>
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem onClick={() => setSelectedTransaction(transaction)}>
                                                            View Details
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => setTerminalId(transaction.terminalId)}>
                                                            Filter by Terminal
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => setTransactionType(transaction.transactionTypeCode.substring(0, 2))}>
                                                            Filter by Type
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => setSearch(transaction.retrievalReference)}>
                                                            Filter by Reference
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={7} className="h-24 text-center">
                                            No transactions found matching your criteria
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </div>

                    {/* Pagination */}
                    <div className="flex items-center justify-between mt-4">
                        <div className="text-sm text-muted-foreground">
                            Page {page} of {totalPages} ({totalTransactions} total transactions)
                        </div>
                        <div className="flex items-center gap-2">
                            <Select
                                value={limit.toString()}
                                onValueChange={(value) => {
                                    setLimit(Number.parseInt(value))
                                    setPage(1)
                                }}
                            >
                                <SelectTrigger className="w-[100px]">
                                    <SelectValue placeholder="10 per page" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="10">10 per page</SelectItem>
                                    <SelectItem value="25">25 per page</SelectItem>
                                    <SelectItem value="50">50 per page</SelectItem>
                                    <SelectItem value="100">100 per page</SelectItem>
                                </SelectContent>
                            </Select>

                            <div className="flex items-center gap-1">
                                <Button variant="outline" size="icon" onClick={() => goToPage(1)} disabled={page === 1}>
                                    <ChevronLeft className="h-4 w-4" />
                                    <ChevronLeft className="h-4 w-4 -ml-2" />
                                </Button>
                                <Button variant="outline" size="icon" onClick={() => goToPage(page - 1)} disabled={page === 1}>
                                    <ChevronLeft className="h-4 w-4" />
                                </Button>

                                <div className="flex items-center gap-1 mx-2">
                                    {(() => {
                                        // Calculate which page numbers to show
                                        let startPage = Math.max(1, page - 2)
                                        const endPage = Math.min(totalPages, startPage + 4)

                                        // Adjust start page if we're near the end
                                        if (endPage - startPage < 4 && startPage > 1) {
                                            startPage = Math.max(1, endPage - 4)
                                        }

                                        const pageNumbers = []
                                        for (let i = startPage; i <= endPage; i++) {
                                            pageNumbers.push(i)
                                        }

                                        return pageNumbers.map((pageNum) => (
                                            <Button
                                                key={pageNum}
                                                variant={pageNum === page ? "default" : "outline"}
                                                size="icon"
                                                className="w-8 h-8"
                                                onClick={() => goToPage(pageNum)}
                                            >
                                                {pageNum}
                                            </Button>
                                        ))
                                    })()}
                                </div>

                                <Button variant="outline" size="icon" onClick={() => goToPage(page + 1)} disabled={page === totalPages}>
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => goToPage(totalPages)}
                                    disabled={page === totalPages}
                                >
                                    <ChevronRight className="h-4 w-4" />
                                    <ChevronRight className="h-4 w-4 -ml-2" />
                                </Button>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Transaction Details Modal */}
            {selectedTransaction && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
                    <div className="bg-background rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
                        <div className="flex justify-between items-center border-b p-6">
                            <h2 className="text-2xl font-bold">Transaction Details</h2>
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => setSelectedTransaction(null)}
                                className="rounded-full"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    className="lucide lucide-x h-5 w-5"
                                >
                                    <path d="M18 6 6 18" />
                                    <path d="m6 6 12 12" />
                                </svg>
                                <span className="sr-only">Close</span>
                            </Button>
                        </div>

                        <div className="overflow-auto p-6 space-y-8">
                            {/* Basic Information Section */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-primary">Overview</h3>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Transaction Date/Time</p>
                                        <p className="font-mono text-sm">
                                            {formatDate(selectedTransaction.transactionDatetime)}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Transaction Type</p>
                                        <Badge className={`${getTransactionTypeColor(selectedTransaction.transactionTypeCode)} text-white`}>
                                            {getTransactionTypeDescription(selectedTransaction.transactionTypeCode)} ({selectedTransaction.transactionTypeCode})
                                        </Badge>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Transaction Amount</p>
                                        <p className="font-mono text-sm">
                                            {  Intl.NumberFormat('en-US', {
                                                style: 'currency',
                                                currency: country_currency.find(r=>r.numericCode===selectedTransaction.currencyBilling)?.currencyCode || 'EUR'
                                            }).format(selectedTransaction.billingAmount)}                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Billing Amount</p>
                                        <p className="font-mono text-sm">

                                            {  Intl.NumberFormat('en-US', {
                                                style: 'currency',
                                                currency: country_currency.find(r=>r.numericCode===selectedTransaction.currencyTransaction)?.currencyCode || 'EUR'
                                            }).format(selectedTransaction.transactionAmount)}

                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Card Expiry</p>
                                        <p className="font-mono text-sm">
                                            {selectedTransaction.cardExpiryDate ?
                                                `${selectedTransaction.cardExpiryDate.substr(0, 2)}/${selectedTransaction.cardExpiryDate.substr(2, 2)}` :
                                                'N/A'
                                            }
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Merchant & Terminal Section */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-primary">Merchant Information</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Terminal ID</p>
                                        <p className="font-mono text-sm break-all">
                                            {selectedTransaction.terminalId}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Merchant ID</p>
                                        <p className="font-mono text-sm">
                                            {selectedTransaction.merchantId.trim()}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Merchant Location</p>
                                        <p className="font-mono text-sm">
                                            {selectedTransaction.merchantLocation}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Account Identification</p>
                                        <p className="font-mono text-sm">
                                            {selectedTransaction.accountIdentification}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Reference Numbers Section */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-primary">Reference Information</h3>
                                <div className="space-y-4">
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Retrieval Reference</p>
                                        <p className="font-mono text-sm break-all bg-muted p-3 rounded-md">
                                            {selectedTransaction.retrievalReference}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Trace Number</p>
                                        <p className="font-mono text-sm break-all bg-muted p-3 rounded-md">
                                            {selectedTransaction.traceNumber || 'N/A'}
                                        </p>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}