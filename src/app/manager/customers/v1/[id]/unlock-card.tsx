import {useState} from "react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import axiosInstance from "@/utils/axiosInstance"
import {Loader2, Snowflake} from "lucide-react"

interface UnLockCardDialogProps {
    expDate: string
    cardId: string
    onApiSuccess: () => void
}

export function UnLockCardDialog({ expDate, cardId, onApiSuccess }: UnLockCardDialogProps) {
    const [loading, setLoading] = useState(false)
    const [isOpen, setIsOpen] = useState(false)

    const handleLockCard = async () => {
        try {
            setLoading(true)

            const data = {
                expDate,
            }
            await axiosInstance.post(`cards/${cardId}/unlock`, data)

            onApiSuccess()
            setIsOpen(false) // Close the dialog on success
        } catch (error) {
            console.error("Failed to unlock card", error)
            alert("Failed to unlock card. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Button
            variant="ghost" onClick={handleLockCard} disabled={loading}
            className="flex flex-col items-center gap-2 h-auto py-3 hover:bg-green-50"

        >
            <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
                <Snowflake className="h-6 w-6 text-gray-600"/>
            </div>

            <span className="text-sm"> {loading ? (
                <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin"/>
                    Unfreezing card...
                </>
            ) : (
                "Unfreeze card"
            )}</span>

        </Button>
    )
}

