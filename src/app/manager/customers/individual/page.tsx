//@ts-nocheck
"use client"

import { useEffect, useState, useMemo } from "react"
import { AlertTriangle, Search, Filter, X, Calendar } from "lucide-react"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Separator } from "@/components/ui/separator"
import axiosInstance from "@/utils/axiosInstance"
import { useAppSelector } from "@/store/hooks"
import DataExporter from "@/components/DataExporter"
import { DataTable } from "@/components/data-table"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { countryNameByCode } from "@/utils/data"
import { useRouter } from "next/navigation"
import { formatDate } from "@/utils/helpers"
import PhoneNumberDisplay from "@/components/PhoneDispaly"

// Types
interface PersonalInfo {
    firstName: string
    middleName?: string
    lastName: string
    dateOfBirth: string
    email: string
    phone: string
    birthCountry?: string
}

interface Address {
    streetAddress: string
    building?: string
    apartment?: string
    city: string
    stateProvince: string
    postalCode: string
    country: string
}

interface IndividualCustomer {
    _id: string
    clientID: string
    personalInfo: PersonalInfo
    address: Address
    citizenship?: string
    riskLevel?: string
    applicationStatus?: string
    createdAt: string
    updatedAt?: string
}

interface OnboardingData extends Array<IndividualCustomer> {}

interface FilterState {
    search: string
    firstName: string
    lastName: string
    email: string
    phoneNumber: string
    clientID: string
    country: string
    city: string
    citizenship: string
    riskLevel: string
    applicationStatus: string
    dateFrom: Date | null
    dateTo: Date | null
    birthCountry: string
}

const initialFilterState: FilterState = {
    search: "",
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    clientID: "",
    country: "",
    city: "",
    citizenship: "",
    riskLevel: "",
    applicationStatus: "",
    dateFrom: null,
    dateTo: null,
    birthCountry: "",
}

export default function Dashboard() {
    const [onboarding, setOnboarding] = useState<OnboardingData | null>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [filters, setFilters] = useState<FilterState>(initialFilterState)
    const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
    const router = useRouter()
    const user = useAppSelector((state) => state.user.user)

    useEffect(() => {
        const fetchOnboardingDetails = async () => {
            try {
                const response = await axiosInstance.get(`company/${user.recordId}/bankingClients`)
                console.dir(response.data.users)
                setOnboarding(response.data.users)
            } catch (error) {
                console.error("Error fetching onboarding details:", error)
                setError("Failed to fetch onboarding details")
            } finally {
                setLoading(false)
            }
        }

        fetchOnboardingDetails()
    }, [])

    // Advanced filtering logic
    const filteredData = useMemo(() => {
        if (!onboarding) return []

        return onboarding.filter((customer) => {
            // Global search filter
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase()
                const searchableFields = [
                    customer.personalInfo.firstName,
                    customer.personalInfo.lastName,
                    customer.personalInfo.email,
                    customer.personalInfo.phone,
                    customer.clientID,
                    customer.address.city,
                    customer.address.country,
                    customer.address.streetAddress,
                    customer.citizenship || "",
                    customer.riskLevel || "",
                    customer.applicationStatus || "",
                    customer.personalInfo.birthCountry || "",
                ]
                    .join(" ")
                    .toLowerCase()

                if (!searchableFields.includes(searchTerm)) return false
            }

            // First name filter
            if (
                filters.firstName &&
                !customer.personalInfo.firstName.toLowerCase().includes(filters.firstName.toLowerCase())
            ) {
                return false
            }

            // Last name filter
            if (filters.lastName && !customer.personalInfo.lastName.toLowerCase().includes(filters.lastName.toLowerCase())) {
                return false
            }

            // Email filter
            if (filters.email && !customer.personalInfo.email.toLowerCase().includes(filters.email.toLowerCase())) {
                return false
            }

            // Phone number filter
            if (filters.phoneNumber) {
                const phoneDigits = filters.phoneNumber.replace(/\D/g, "")
                const customerPhoneDigits = customer.personalInfo.phone.replace(/\D/g, "")
                if (!customerPhoneDigits.includes(phoneDigits)) return false
            }

            // Client ID filter
            if (filters.clientID && !customer.clientID.toLowerCase().includes(filters.clientID.toLowerCase())) {
                return false
            }

            // Country filter
            if (filters.country && customer.address.country !== filters.country) {
                return false
            }

            // City filter
            if (filters.city && !customer.address.city.toLowerCase().includes(filters.city.toLowerCase())) {
                return false
            }

            // Citizenship filter
            if (filters.citizenship && customer.citizenship !== filters.citizenship) {
                return false
            }

            // Risk level filter
            if (filters.riskLevel && customer.riskLevel !== filters.riskLevel) {
                return false
            }

            // Application status filter
            if (filters.applicationStatus && customer.applicationStatus !== filters.applicationStatus) {
                return false
            }

            // Birth country filter
            if (filters.birthCountry && customer.personalInfo.birthCountry !== filters.birthCountry) {
                return false
            }

            // Date range filter
            if (filters.dateFrom || filters.dateTo) {
                const customerDate = new Date(customer.createdAt)
                if (filters.dateFrom && customerDate < filters.dateFrom) return false
                if (filters.dateTo && customerDate > filters.dateTo) return false
            }

            return true
        })
    }, [onboarding, filters])

    // Get unique values for dropdown filters
    const uniqueCountries = useMemo(() => {
        if (!onboarding) return []
        return [...new Set(onboarding.map((customer) => customer.address.country))]
    }, [onboarding])

    const uniqueCitizenships = useMemo(() => {
        if (!onboarding) return []
        return [...new Set(onboarding.map((customer) => customer.citizenship).filter(Boolean))]
    }, [onboarding])

    const uniqueRiskLevels = useMemo(() => {
        if (!onboarding) return []
        return [...new Set(onboarding.map((customer) => customer.riskLevel).filter(Boolean))]
    }, [onboarding])

    const uniqueApplicationStatuses = useMemo(() => {
        if (!onboarding) return []
        return [...new Set(onboarding.map((customer) => customer.applicationStatus).filter(Boolean))]
    }, [onboarding])

    const uniqueBirthCountries = useMemo(() => {
        if (!onboarding) return []
        return [...new Set(onboarding.map((customer) => customer.personalInfo.birthCountry).filter(Boolean))]
    }, [onboarding])

    // Clear all filters
    const clearAllFilters = () => {
        setFilters(initialFilterState)
    }

    // Get active filter count
    const activeFilterCount = useMemo(() => {
        return Object.entries(filters).filter(([key, value]) => {
            if (key === "dateFrom" || key === "dateTo") return value !== null
            return value !== ""
        }).length
    }, [filters])

    if (loading) {
        return <LoadingOverlay />
    }

    const columns = [
        {
            header: "Date Created",
            accessorKey: "createdAt" as const,
            cell: (row) => formatDate(row.createdAt),
        },
        {
            header: "Customer ID",
            accessorKey: "clientID" as const,
            cell: (row) => row.clientID,
        },
        {
            header: "Name",
            accessorKey: "name" as const,
            cell: (row) => `${row.personalInfo.firstName} ${row.personalInfo.lastName}`,
        },
        {
            header: "Email",
            accessorKey: "email" as const,
            cell: (row) => row.personalInfo.email,
        },
        {
            header: "Mobile",
            accessorKey: "mobile" as const,
            cell: (row) => <PhoneNumberDisplay phoneNumber={row.personalInfo.phone} />,
        },
        {
            header: "Country",
            accessorKey: "country" as const,
            cell: (row) => countryNameByCode(row.address.country),
        },
    ]

    if (error || !onboarding) {
        return (
            <Card className="w-full max-w-md mx-auto mt-8">
                <CardContent className="py-10">
                    <AlertTriangle className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
                    <p className="text-center text-lg font-medium text-muted-foreground">
                        {error || "No Onboarding Details found."}
                    </p>
                </CardContent>
            </Card>
        )
    }

    // Export data
    const exportData = filteredData.map((row, index) => ({
        id: index + 1,
        customer_id: row.clientID,
        first_name: row.personalInfo.firstName,
        last_name: row.personalInfo.lastName,
        email: row.personalInfo.email,
        phone: row.personalInfo.phone,
        citizenship: row.citizenship || "",
        birth_country: row.personalInfo.birthCountry || "",
        risk_level: row.riskLevel || "",
        application_status: row.applicationStatus || "",
        address: `${row.address.streetAddress}, ${row.address.city}, ${row.address.country}`,
        created_at: formatDate(row.createdAt),
    }))

    return (
        <div className="container mx-auto px-4 py-8 space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle className="text-lg font-bold mb-2">Banking Customers</CardTitle>
                </CardHeader>
                <CardContent>
                    <DataExporter
                        data={exportData}
                        filename="individual_banking_customers"
                        title="Individual Banking Customers Report"
                    />

                    <div className="mt-6 space-y-4">
                        {/* Main Search Bar */}
                        <div className="flex items-center gap-2">
                            <div className="relative flex-1">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                <Input
                                    placeholder="Search across all fields..."
                                    value={filters.search}
                                    onChange={(e) => setFilters((prev) => ({ ...prev, search: e.target.value }))}
                                    className="pl-10"
                                />
                            </div>
                            <Button
                                variant="outline"
                                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                                className="flex items-center gap-2"
                            >
                                <Filter className="h-4 w-4" />
                                Advanced Filters
                                {activeFilterCount > 0 && (
                                    <Badge variant="secondary" className="ml-1">
                                        {activeFilterCount}
                                    </Badge>
                                )}
                            </Button>
                            {activeFilterCount > 0 && (
                                <Button variant="ghost" onClick={clearAllFilters} className="flex items-center gap-2">
                                    <X className="h-4 w-4" />
                                    Clear All
                                </Button>
                            )}
                        </div>

                        {/* Advanced Filters Panel */}
                        {showAdvancedFilters && (
                            <Card className="p-4 bg-gray-50">
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {/* First Name Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">First Name</label>
                                        <Input
                                            placeholder="Filter by first name..."
                                            value={filters.firstName}
                                            onChange={(e) => setFilters((prev) => ({ ...prev, firstName: e.target.value }))}
                                        />
                                    </div>

                                    {/* Last Name Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Last Name</label>
                                        <Input
                                            placeholder="Filter by last name..."
                                            value={filters.lastName}
                                            onChange={(e) => setFilters((prev) => ({ ...prev, lastName: e.target.value }))}
                                        />
                                    </div>

                                    {/* Email Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Email</label>
                                        <Input
                                            placeholder="Filter by email..."
                                            value={filters.email}
                                            onChange={(e) => setFilters((prev) => ({ ...prev, email: e.target.value }))}
                                        />
                                    </div>

                                    {/* Phone Number Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Phone Number</label>
                                        <Input
                                            placeholder="Filter by phone number..."
                                            value={filters.phoneNumber}
                                            onChange={(e) => setFilters((prev) => ({ ...prev, phoneNumber: e.target.value }))}
                                        />
                                    </div>

                                    {/* Customer ID Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Customer ID</label>
                                        <Input
                                            placeholder="Filter by customer ID..."
                                            value={filters.clientID}
                                            onChange={(e) => setFilters((prev) => ({ ...prev, clientID: e.target.value }))}
                                        />
                                    </div>

                                    {/* Country Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Country</label>
                                        <Select
                                            value={filters.country}
                                            onValueChange={(value) =>
                                                setFilters((prev) => ({ ...prev, country: value === "all" ? "" : value }))
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select country..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Countries</SelectItem>
                                                {uniqueCountries.map((country) => (
                                                    <SelectItem key={country} value={country}>
                                                        {countryNameByCode(country)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* City Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">City</label>
                                        <Input
                                            placeholder="Filter by city..."
                                            value={filters.city}
                                            onChange={(e) => setFilters((prev) => ({ ...prev, city: e.target.value }))}
                                        />
                                    </div>

                                    {/* Citizenship Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Citizenship</label>
                                        <Select
                                            value={filters.citizenship}
                                            onValueChange={(value) =>
                                                setFilters((prev) => ({ ...prev, citizenship: value === "all" ? "" : value }))
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select citizenship..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Citizenships</SelectItem>
                                                {uniqueCitizenships.map((citizenship) => (
                                                    <SelectItem key={citizenship} value={citizenship}>
                                                        {citizenship}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* Risk Level Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Risk Level</label>
                                        <Select
                                            value={filters.riskLevel}
                                            onValueChange={(value) =>
                                                setFilters((prev) => ({ ...prev, riskLevel: value === "all" ? "" : value }))
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select risk level..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Risk Levels</SelectItem>
                                                {uniqueRiskLevels.map((riskLevel) => (
                                                    <SelectItem key={riskLevel} value={riskLevel}>
                                                        {riskLevel.charAt(0).toUpperCase() + riskLevel.slice(1)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* Application Status Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Application Status</label>
                                        <Select
                                            value={filters.applicationStatus}
                                            onValueChange={(value) =>
                                                setFilters((prev) => ({ ...prev, applicationStatus: value === "all" ? "" : value }))
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select status..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Statuses</SelectItem>
                                                {uniqueApplicationStatuses.map((status) => (
                                                    <SelectItem key={status} value={status}>
                                                        {status.charAt(0).toUpperCase() + status.slice(1)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* Birth Country Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Birth Country</label>
                                        <Select
                                            value={filters.birthCountry}
                                            onValueChange={(value) =>
                                                setFilters((prev) => ({ ...prev, birthCountry: value === "all" ? "" : value }))
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select birth country..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Birth Countries</SelectItem>
                                                {uniqueBirthCountries.map((country) => (
                                                    <SelectItem key={country} value={country}>
                                                        {country}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* Date Range Filters */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Date From</label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button variant="outline" className="w-full justify-start text-left font-normal">
                                                    <Calendar className="mr-2 h-4 w-4" />
                                                    {filters.dateFrom ? formatDate(filters.dateFrom.toISOString()) : "Select date..."}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <CalendarComponent
                                                    mode="single"
                                                    selected={filters.dateFrom}
                                                    onSelect={(date) => setFilters((prev) => ({ ...prev, dateFrom: date }))}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                    </div>

                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Date To</label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button variant="outline" className="w-full justify-start text-left font-normal">
                                                    <Calendar className="mr-2 h-4 w-4" />
                                                    {filters.dateTo ? formatDate(filters.dateTo.toISOString()) : "Select date..."}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <CalendarComponent
                                                    mode="single"
                                                    selected={filters.dateTo}
                                                    onSelect={(date) => setFilters((prev) => ({ ...prev, dateTo: date }))}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                    </div>
                                </div>
                            </Card>
                        )}

                        {/* Active Filters Display */}
                        {activeFilterCount > 0 && (
                            <div className="flex flex-wrap gap-2">
                                {Object.entries(filters).map(([key, value]) => {
                                    if (!value || (typeof value === "string" && value === "")) return null

                                    const displayValue =
                                        key === "dateFrom" || key === "dateTo"
                                            ? formatDate((value as Date).toISOString())
                                            : (value as string)

                                    return (
                                        <Badge key={key} variant="secondary" className="flex items-center gap-1">
                                            {key.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase())}: {displayValue}
                                            <X
                                                className="h-3 w-3 cursor-pointer"
                                                onClick={() =>
                                                    setFilters((prev) => ({
                                                        ...prev,
                                                        [key]: key === "dateFrom" || key === "dateTo" ? null : "",
                                                    }))
                                                }
                                            />
                                        </Badge>
                                    )
                                })}
                            </div>
                        )}
                    </div>

                    <Separator className="my-6" />

                    <DataTable
                        data={filteredData}
                        columns={columns}
                        title="Individual Banking Customers"
                        description={`Showing ${filteredData.length} of ${onboarding?.length || 0} customers`}
                        loading={loading}
                        error={error}
                        getRowId={(c) => c._id}
                        onRowClick={(r) => router.push(`/manager/customers/v1/${r._id}`)}
                    />
                </CardContent>
            </Card>
        </div>
    )
}
