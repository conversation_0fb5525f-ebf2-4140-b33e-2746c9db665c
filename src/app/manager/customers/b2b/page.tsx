//@ts-nocheck
"use client"

import { useEffect, useState, useMemo } from "react"
import { AlertTriangle, Search, Filter, X, Calendar } from "lucide-react"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Separator } from "@/components/ui/separator"
import axiosInstance from "@/utils/axiosInstance"
import { useAppSelector } from "@/store/hooks"
import DataExporter from "@/components/DataExporter"
import { DataTable } from "@/components/data-table"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { useRouter } from "next/navigation"
import { formatDate } from "@/utils/helpers"
import PhoneNumberDisplay from "@/components/PhoneDispaly"
import {countryNameByCode} from "@/utils/data";

// Types
interface PersonalInfo {
    firstName: string
    middleName?: string
    lastName: string
    dateOfBirth: string
    email: string
    phone: string
}

interface Address {
    street: string
    buildingNumber?: string
    apartmentNumber?: string
    city: string
    zipCode: string
    country: string
    _id: string
}

interface B2BClient {
    _id: string
    companyName: string
    clientCode: string
    phoneNumber: string
    authPhoneNumber: string
    email: string
    address: Address
    nip: string
    regon: string
    embossedName: string
    company: boolean
    customer: boolean
    parentCompany: string
    status: string
    createdAt: string
    updatedAt: string
    __v: number
}

interface OnboardingData {
    personalInfo: PersonalInfo
    address: Address
}

interface FilterState {
    search: string
    companyName: string
    email: string
    phoneNumber: string
    clientCode: string
    status: string
    country: string
    dateFrom: Date | null
    dateTo: Date | null
    nip: string
    regon: string
}

const initialFilterState: FilterState = {
    search: "",
    companyName: "",
    email: "",
    phoneNumber: "",
    clientCode: "",
    status: "",
    country: "",
    dateFrom: null,
    dateTo: null,
    nip: "",
    regon: "",
}

export default function Dashboard() {
    const [onboarding, setOnboarding] = useState<OnboardingData | null>(null)
    const [b2bClients, setB2bClients] = useState<B2BClient[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [filters, setFilters] = useState<FilterState>(initialFilterState)
    const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
    const router = useRouter()
    const user = useAppSelector((state) => state.user.user)

    useEffect(() => {
        const fetchOnboardingDetails = async () => {
            try {
                const response = await axiosInstance.get(`company/${user.recordId}/bankingClients`)
                console.dir(response.data.users)
                setOnboarding(response.data.b2b)


                setB2bClients(response.data.b2b)
            } catch (error) {
                console.error("Error fetching onboarding details:", error)
                setError("Failed to fetch onboarding details")
            } finally {
                setLoading(false)
            }
        }

        fetchOnboardingDetails()
    }, [])

    // Advanced filtering logic
    const filteredB2bData = useMemo(() => {
        if (!b2bClients) return []

        return b2bClients.filter((client) => {
            // Global search filter
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase()
                const searchableFields = [
                    client.companyName,
                    client.clientCode,
                    client.email,
                    client.phoneNumber,
                    client.authPhoneNumber,

                    client.nip,
                    client.regon,
                    client.embossedName,
                ]
                    .join(" ")
                    .toLowerCase()

                if (!searchableFields.includes(searchTerm)) return false
            }

            // Company name filter
            if (filters.companyName && !client.companyName.toLowerCase().includes(filters.companyName.toLowerCase())) {
                return false
            }

            // Email filter
            if (filters.email && !client.email.toLowerCase().includes(filters.email.toLowerCase())) {
                return false
            }

            // Phone number filter
            if (filters.phoneNumber) {
                const phoneDigits = filters.phoneNumber.replace(/\D/g, "")
                const clientPhoneDigits = client.phoneNumber.replace(/\D/g, "")
                if (!clientPhoneDigits.includes(phoneDigits)) return false
            }

            // Client code filter
            if (filters.clientCode && !client.clientCode.toLowerCase().includes(filters.clientCode.toLowerCase())) {
                return false
            }

            // Status filter
            if (filters.status && client.status !== filters.status) {
                return false
            }

            // Country filter
            if (filters.country && client.address.country !== filters.country) {
                return false
            }

            // NIP filter
            if (filters.nip && !client.nip.includes(filters.nip)) {
                return false
            }

            // REGON filter
            if (filters.regon && !client.regon.includes(filters.regon)) {
                return false
            }

            // Date range filter
            if (filters.dateFrom || filters.dateTo) {
                const clientDate = new Date(client.createdAt)
                if (filters.dateFrom && clientDate < filters.dateFrom) return false
                if (filters.dateTo && clientDate > filters.dateTo) return false
            }

            return true
        })
    }, [b2bClients, filters])

    // Get unique values for dropdown filters
    const uniqueStatuses = useMemo(() => {
        return [...new Set(b2bClients.map((client) => client.status))]
    }, [b2bClients])

    const uniqueCountries = useMemo(() => {
        return [...new Set(b2bClients.map((client) => client.addresses[0].country))]
    }, [b2bClients])

    // Clear all filters
    const clearAllFilters = () => {
        setFilters(initialFilterState)
    }

    // Get active filter count
    const activeFilterCount = useMemo(() => {
        return Object.entries(filters).filter(([key, value]) => {
            if (key === "dateFrom" || key === "dateTo") return value !== null
            return value !== ""
        }).length
    }, [filters])

    if (loading) {
        return <LoadingOverlay />
    }

    // B2B clients columns
    const b2bColumns = [
        {
            header: "Date Created",
            accessorKey: "createdAt" as const,
            cell: (row) => formatDate(row.createdAt),
        },
        {
            header: "Client Code",
            accessorKey: "clientCode" as const,
            cell: (row) => row.clientCode,
        },
        {
            header: "Company Name",
            accessorKey: "companyName" as const,
            cell: (row) => row.companyName,
        },
        {
            header: "Email",
            accessorKey: "email" as const,
            cell: (row) => row.email,
        },
        {
            header: "Phone Number",
            accessorKey: "phoneNumber" as const,
            cell: (row) => <PhoneNumberDisplay phoneNumber={row.phoneNumber} />,
        },
        {
            header: "Country",
            accessorKey: "country" as const,
            cell: (row) =>countryNameByCode( row.addresses[0].country),
        },
        {
            header: "Status",
            accessorKey: "status" as const,
            cell: (row) => (
                <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                        row.status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                    }`}
                >
          {row.status}
        </span>
            ),
        },
    ]

    if (error || !onboarding) {
        return (
            <Card className="w-full max-w-md mx-auto mt-8">
                <CardContent className="py-10">
                    <AlertTriangle className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
                    <p className="text-center text-lg font-medium text-muted-foreground">
                        {error || "No Onboarding Details found."}
                    </p>
                </CardContent>
            </Card>
        )
    }

    // B2B export data
    const b2bExportData = filteredB2bData.map((row, index) => ({
        id: index + 1,
        client_code: row.clientCode,
        company_name: row.companyName,
        email: row.email,
        phone_number: row.phoneNumber,
        auth_phone_number: row.authPhoneNumber,
        address: `${row.addresses[0].street} ${row.addresses[0].buildingNumber}, ${row.addresses[0].city}, ${row.addresses[0].country}`,
        nip: row.nip,
        regon: row.regon,
        embossed_name: row.embossedName,
        status: row.status,
        created_at: formatDate(row.createdAt),
    }))

    return (
        <div className="container mx-auto px-4 py-8 space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle className="text-lg font-bold mb-2">Corporate(B2B) Customers</CardTitle>
                </CardHeader>
                <CardContent>
                    <DataExporter data={b2bExportData} filename="b2b_banking_customers" title="B2B Banking Customers Report" />

                    <div className="mt-6 space-y-4">
                        {/* Main Search Bar */}
                        <div className="flex items-center gap-2">
                            <div className="relative flex-1">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                <Input
                                    placeholder="Search across all fields..."
                                    value={filters.search}
                                    onChange={(e) => setFilters((prev) => ({ ...prev, search: e.target.value }))}
                                    className="pl-10"
                                />
                            </div>
                            <Button
                                variant="outline"
                                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                                className="flex items-center gap-2"
                            >
                                <Filter className="h-4 w-4" />
                                Advanced Filters
                                {activeFilterCount > 0 && (
                                    <Badge variant="secondary" className="ml-1">
                                        {activeFilterCount}
                                    </Badge>
                                )}
                            </Button>
                            {activeFilterCount > 0 && (
                                <Button variant="ghost" onClick={clearAllFilters} className="flex items-center gap-2">
                                    <X className="h-4 w-4" />
                                    Clear All
                                </Button>
                            )}
                        </div>

                        {/* Advanced Filters Panel */}
                        {showAdvancedFilters && (
                            <Card className="p-4 bg-gray-50">
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {/* Company Name Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Company Name</label>
                                        <Input
                                            placeholder="Filter by company name..."
                                            value={filters.companyName}
                                            onChange={(e) => setFilters((prev) => ({ ...prev, companyName: e.target.value }))}
                                        />
                                    </div>

                                    {/* Email Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Email</label>
                                        <Input
                                            placeholder="Filter by email..."
                                            value={filters.email}
                                            onChange={(e) => setFilters((prev) => ({ ...prev, email: e.target.value }))}
                                        />
                                    </div>

                                    {/* Phone Number Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Phone Number</label>
                                        <Input
                                            placeholder="Filter by phone number..."
                                            value={filters.phoneNumber}
                                            onChange={(e) => setFilters((prev) => ({ ...prev, phoneNumber: e.target.value }))}
                                        />
                                    </div>

                                    {/* Client Code Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Client Code</label>
                                        <Input
                                            placeholder="Filter by client code..."
                                            value={filters.clientCode}
                                            onChange={(e) => setFilters((prev) => ({ ...prev, clientCode: e.target.value }))}
                                        />
                                    </div>

                                    {/* Status Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Status</label>
                                        <Select
                                            value={filters.status}
                                            onValueChange={(value) => setFilters((prev) => ({ ...prev, status: value }))}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select status..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Statuses</SelectItem>
                                                {uniqueStatuses.map((status) => (
                                                    <SelectItem key={status} value={status}>
                                                        {status.charAt(0).toUpperCase() + status.slice(1)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* Country Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Country</label>
                                        <Select
                                            value={filters.country}
                                            onValueChange={(value) => setFilters((prev) => ({ ...prev, country: value }))}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select country..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Countries</SelectItem>
                                                {uniqueCountries.map((country) => (
                                                    <SelectItem key={country} value={country}>
                                                        {country}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* NIP Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">NIP</label>
                                        <Input
                                            placeholder="Filter by NIP..."
                                            value={filters.nip}
                                            onChange={(e) => setFilters((prev) => ({ ...prev, nip: e.target.value }))}
                                        />
                                    </div>

                                    {/* REGON Filter */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">REGON</label>
                                        <Input
                                            placeholder="Filter by REGON..."
                                            value={filters.regon}
                                            onChange={(e) => setFilters((prev) => ({ ...prev, regon: e.target.value }))}
                                        />
                                    </div>

                                    {/* Date Range Filters */}
                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Date From</label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button variant="outline" className="w-full justify-start text-left font-normal">
                                                    <Calendar className="mr-2 h-4 w-4" />
                                                    {filters.dateFrom ? formatDate(filters.dateFrom.toISOString()) : "Select date..."}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <CalendarComponent
                                                    mode="single"
                                                    selected={filters.dateFrom}
                                                    onSelect={(date) => setFilters((prev) => ({ ...prev, dateFrom: date }))}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                    </div>

                                    <div>
                                        <label className="text-sm font-medium mb-1 block">Date To</label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button variant="outline" className="w-full justify-start text-left font-normal">
                                                    <Calendar className="mr-2 h-4 w-4" />
                                                    {filters.dateTo ? formatDate(filters.dateTo.toISOString()) : "Select date..."}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <CalendarComponent
                                                    mode="single"
                                                    selected={filters.dateTo}
                                                    onSelect={(date) => setFilters((prev) => ({ ...prev, dateTo: date }))}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                    </div>
                                </div>
                            </Card>
                        )}

                        {/* Active Filters Display */}
                        {activeFilterCount > 0 && (
                            <div className="flex flex-wrap gap-2">
                                {Object.entries(filters).map(([key, value]) => {
                                    if (!value || (typeof value === "string" && value === "")) return null

                                    const displayValue =
                                        key === "dateFrom" || key === "dateTo"
                                            ? formatDate((value as Date).toISOString())
                                            : (value as string)

                                    return (
                                        <Badge key={key} variant="secondary" className="flex items-center gap-1">
                                            {key.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase())}: {displayValue}
                                            <X
                                                className="h-3 w-3 cursor-pointer"
                                                onClick={() =>
                                                    setFilters((prev) => ({
                                                        ...prev,
                                                        [key]: key === "dateFrom" || key === "dateTo" ? null : "",
                                                    }))
                                                }
                                            />
                                        </Badge>
                                    )
                                })}
                            </div>
                        )}
                    </div>

                    <Separator className="my-6" />

                    <DataTable
                        data={filteredB2bData}
                        columns={b2bColumns}
                        title="B2B Banking Customers"
                        description={`Showing ${filteredB2bData.length} of ${b2bClients.length} customers`}
                        loading={loading}
                        error={error}
                        getRowId={(c) => c._id}
                        onRowClick={(r) => router.push(`b2b/${r._id}`)}
                    />
                </CardContent>
            </Card>
        </div>
    )
}
