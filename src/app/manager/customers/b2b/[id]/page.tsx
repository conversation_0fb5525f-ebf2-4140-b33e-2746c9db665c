//@ts-nocheck
"use client"

import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
    AlertTriangle,
    Building,
    Calendar,
    CheckCircle2,
    ChevronDown,
    Clock,
    Copy,
    Download,
    Edit,
    FileText,
    Mail,
    MapPin,
    Package,
    Paperclip,
    Phone,
    Plus,
    RefreshCw,
    Trash2,
    Users,
    X,
} from "lucide-react"
import type React from "react"
import { memo, useCallback, useEffect, useState } from "react"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Skeleton } from "@/components/ui/skeleton"
import { Ava<PERSON>, AvatarFallback } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"

import { ContactForm } from "@/components/add-contact-modal"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { alertHelper } from "@/utils/alertHelper"
import axiosInstance from "@/utils/axiosInstance"
import Swal from "sweetalert2"
import PhoneNumberDisplay from "@/components/PhoneDispaly"
import { useAppSelector } from "@/store/hooks"

// Helper function to format dates
const formatDateLocal = (dateString: string) => {
    if (!dateString) return "N/A"
    return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
    })
}

interface Document {
    name: string
    type: string
    size: string
}

interface Event {
    name: string
    type: string
    size: string
    title: string
}

const documents: Document[] = [
    { name: "nameofdocument", type: "doctype", size: "2.1MB" },
    { name: "nameofdocument", type: "doctype", size: "2.1MB" },
    { name: "nameofdocument", type: "doctype", size: "2.1MB" },
]

interface ReviewStatus {
    icon: React.ReactNode
    label: string
    actionRequired?: boolean
}

const statuses = ["Initiated", "Pending", "Pre-checked", "Queued", "On Hold"]

const reviewStatuses: ReviewStatus[] = [
    {
        icon: <div className="w-4 h-4 rounded-full border" />,
        label: "John Smitherson",
    },
    {
        icon: (
            <div className="w-4 h-4 rounded-full bg-orange-500 text-white flex items-center justify-center text-xs">!</div>
        ),
        label: "John Smitherson",
        actionRequired: true,
    },
    {
        icon: <div className="w-4 h-4 rounded-full bg-red-500 text-white flex items-center justify-center text-xs">×</div>,
        label: "John Smitherson",
    },
    {
        icon: (
            <div className="w-4 h-4 rounded-full bg-green-500 text-white flex items-center justify-center text-xs">✓</div>
        ),
        label: "John Smitherson",
    },
]

interface Activity {
    timestamp: string
    description: string
}

const activities: Activity[] = [
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
]

const MOCK_EVENTS_DATA = [
    {
        title: "Quarterly Review Meeting",
        name: "quarterly_review",
        type: "meeting",
        size: "2.1MB",
    },
    {
        title: "Contract Renewal",
        name: "contract_renewal",
        type: "document",
        size: "1.5MB",
    },
]

const InfoItem = ({ label, value, icon }: { label: string; value: React.ReactNode; icon?: React.ReactNode }) => (
    <div className="flex items-start gap-3">
        {icon && <div className="mt-0.5 text-muted-foreground">{icon}</div>}
        <div>
            <p className="text-sm font-medium text-muted-foreground">{label}</p>
            <div className="mt-1 font-medium">{value}</div>
        </div>
    </div>
)

const AddressDetails = memo(({ address, title }: { address: any; title: string }) => {
    if (!address) return null

    return (
        <div className="space-y-4">
            <h3 className="text-lg font-semibold">{title}</h3>
            <div className="grid gap-4 md:grid-cols-2">
                <InfoItem label="Street Address" value={address.street} icon={<MapPin size={16} />} />
                {address.buildingNumber && (
                    <InfoItem label="Building Number" value={address.buildingNumber} icon={<Building size={16} />} />
                )}
                {address.apartmentNumber && (
                    <InfoItem label="Apartment Number" value={address.apartmentNumber} icon={<Building size={16} />} />
                )}
                <InfoItem label="City" value={address.city} icon={<MapPin size={16} />} />
                {address.state && <InfoItem label="State/Province" value={address.state} icon={<MapPin size={16} />} />}
                <InfoItem label="Postal Code" value={address.zipCode} icon={<MapPin size={16} />} />
                <InfoItem label="Country" value={address.country} icon={<MapPin size={16} />} />
            </div>
        </div>
    )
})
AddressDetails.displayName = "AddressDetails"

const DocumentItem = ({ name, type, size }: { name: string; type: string; size: string }) => (
    <div className="flex items-center justify-between p-3 rounded-lg border group hover:bg-muted/50 transition-colors">
        <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-md text-primary">
                <FileText size={18} />
            </div>
            <div>
                <p className="font-medium">{name}</p>
                <p className="text-xs text-muted-foreground">
                    {type} • {size}
                </p>
            </div>
        </div>
        <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity">
            <Download size={16} />
        </Button>
    </div>
)

const StatusBadge = ({ status }: { status: string }) => {
    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case "approved":
            case "active":
                return "bg-green-100 text-green-700 border-green-200"
            case "pending":
                return "bg-yellow-100 text-yellow-700 border-yellow-200"
            case "rejected":
                return "bg-red-100 text-red-700 border-red-200"
            case "medium":
                return "bg-yellow-100 text-yellow-700 border-yellow-200"
            default:
                return "bg-gray-100 text-gray-700 border-gray-200"
        }
    }

    return (
        <Badge variant="outline" className={`w-fit ${getStatusColor(status)}`}>
            {status}
        </Badge>
    )
}

const LoadingState = () => (
    <div className="w-full space-y-6">
        <Skeleton className="h-8 w-64" />
        <div className="grid lg:grid-cols-[2fr,1fr] gap-6">
            <div className="space-y-6">
                <Card>
                    <CardHeader>
                        <Skeleton className="h-6 w-48" />
                        <Skeleton className="h-4 w-32" />
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {Array(6)
                                .fill(0)
                                .map((_, i) => (
                                    <div key={i} className="flex justify-between">
                                        <Skeleton className="h-4 w-32" />
                                        <Skeleton className="h-4 w-48" />
                                    </div>
                                ))}
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader>
                        <Skeleton className="h-6 w-32" />
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {Array(4)
                                .fill(0)
                                .map((_, i) => (
                                    <Skeleton key={i} className="h-16 w-full" />
                                ))}
                        </div>
                    </CardContent>
                </Card>
            </div>
            <div className="space-y-6">
                {Array(3)
                    .fill(0)
                    .map((_, i) => (
                        <Card key={i}>
                            <CardHeader>
                                <Skeleton className="h-6 w-40" />
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {Array(3)
                                        .fill(0)
                                        .map((_, j) => (
                                            <Skeleton key={j} className="h-4 w-full" />
                                        ))}
                                </div>
                            </CardContent>
                        </Card>
                    ))}
            </div>
        </div>
    </div>
)

const ErrorState = ({ error, retry }: { error: string; retry: () => void }) => (
    <div className="w-full flex flex-col items-center justify-center p-12">
        <div className="p-4 rounded-full bg-red-100 text-red-600 mb-4">
            <AlertTriangle size={32} />
        </div>
        <h2 className="text-xl font-semibold mb-2">Error Loading Company Data</h2>
        <p className="text-muted-foreground mb-6 text-center max-w-md">{error}</p>
        <Button onClick={retry} className="flex items-center gap-2">
            <RefreshCw size={16} />
            Retry
        </Button>
    </div>
)

export default function B2BCompany({ params }: { params: { id: string } }) {
    const [b2bCompanyData, setB2bCompanyData] = useState<null | any>(null)
    const [cip, setCip] = useState<null | any>(null)
    const [contacts, setContacts] = useState<any[]>([])
    const [events, setEvents] = useState<Event[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [contactFormOpen, setContactFormOpen] = useState(false)
    const [selectedContact, setSelectedContact] = useState<any>(null)
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
    const [contactToDelete, setContactToDelete] = useState<string | null>(null)
    const [assignedProducts, setAssignedProducts] = useState<any[]>([])
    const [isLoadingProducts, setIsLoadingProducts] = useState(false)
    const [isProductModalOpen, setIsProductModalOpen] = useState(false)
    const [selectedProducts, setSelectedProducts] = useState<string[]>([])
    const [productSearchTerm, setProductSearchTerm] = useState("")
    const [companyData, setCompanyData] = useState<null | any>(null)

    const router = useRouter()
    const user = useAppSelector((state) => state.user.user)

    const fetchB2BCompanyDetails = useCallback(async () => {
        try {
            setLoading(true)
            setError(null)

            const response = await axiosInstance.get(`/b2b/${params.id}`)
            if (response.data) {
                setB2bCompanyData(response.data.data)
                setEvents(MOCK_EVENTS_DATA)

                // Fetch parent company data for products
                if (response.data.data.parentCompany?._id) {
                    const parentResponse = await axiosInstance.get(`/company/${response.data.data.parentCompany._id}`)
                    if (parentResponse.data) {
                        setCompanyData(parentResponse.data.company)
                        setCip(parentResponse.data.cip)
                    }
                }
            }
        } catch (error: any) {
            console.error("Error in fetchB2BCompanyDetails:", error)
            setError("Failed to load company data. Please try again.")
        } finally {
            setLoading(false)
        }
    }, [params.id])

    const fetchAssignedProducts = async () => {
        try {
            const response = await axiosInstance.get(`/b2b/${params.id}/products`)
            if (response.data) {
                setAssignedProducts(response.data.products || [])
            }
        } catch (error) {
            console.error("Error fetching assigned products:", error)
        }
    }

    useEffect(() => {
        fetchB2BCompanyDetails()
        fetchAssignedProducts()
    }, [fetchB2BCompanyDetails])

    const handleRowClick = (program: any) => {
        router.push(`/lite/admin/programmes/b2b/${params.id}/BIN-view`)
    }

    const handleAddContact = () => {
        setSelectedContact(null)
        setContactFormOpen(true)
    }

    const handleEditContact = (contact: any) => {
        setSelectedContact(contact)
        setContactFormOpen(true)
    }

    const handleDeleteContact = (contactId: string) => {
        setContactToDelete(contactId)
        setDeleteDialogOpen(true)
    }

    const confirmDeleteContact = async () => {
        if (!contactToDelete) return

        try {
            setLoading(true)
            const response = await axiosInstance.delete(`contacts/${contactToDelete}`)
            if (response.status === 200) {
                alertHelper.showToast("The contact has been deleted successfully.", "success")
                setContacts((prevContacts) => prevContacts.filter((contact) => contact._id !== contactToDelete))
            }

            alertHelper.showToast("The contact has been deleted successfully.", "success")
        } catch (error: any) {
            console.error("Error deleting contact:", error)
            alertHelper.showToast("Failed to delete contact. Please try again.", "error")
        } finally {
            setLoading(false)
            setDeleteDialogOpen(false)
            setContactToDelete(null)
        }
    }

    const refreshContacts = async (newContactData: any) => {
        if (selectedContact) {
            setContacts((prevContacts) =>
                prevContacts.map((contact) =>
                    contact._id === selectedContact._id ? { ...contact, ...newContactData } : contact,
                ),
            )
            alertHelper.showToast("The contact has been updated successfully.", "success")
        } else {
            const newContact = {
                ...newContactData,
                _id: `contact_${Date.now()}`,
            }
            setContacts((prevContacts) => [...prevContacts, newContact])
            alertHelper.showToast("The contact has been added successfully.", "success")
        }
    }

    const handleUnassignProduct = async (productId: string) => {
        const result = await Swal.fire({
            title: "Unassign Product",
            text: "Are you sure you want to unassign this product?",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#00a8a5",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, unassign!",
        })

        if (result.isConfirmed) {
            setLoading(true)
            try {
                const response = await axiosInstance.delete(`/b2b/${params.id}/products/${productId}`)
                if (response.status === 200) {
                    setAssignedProducts((prevProducts) => prevProducts.filter((product) => product._id !== productId))
                    alertHelper.showToast("Product unassigned successfully", "success")
                }
            } catch (error: any) {
                setError(error.response?.data.message || "Failed to unassign product")
                alertHelper.showToast(
                    "Failed to unassign product: " + (error.response?.data.message || "Unknown error"),
                    "error",
                )
            } finally {
                setLoading(false)
            }
        }
    }

    const availableProductsForAssignment =
        cip?.filter((product: any) => !assignedProducts.some((assigned: any) => assigned._id === product._id)) || []

    const filteredProducts = availableProductsForAssignment.filter(
        (product: any) =>
            product.productVersionName?.[0]?.version_name?.toLowerCase().includes(productSearchTerm.toLowerCase()) ||
            product.productVersionName?.[0]?.version_code?.toLowerCase().includes(productSearchTerm.toLowerCase()),
    )

    const handleProductSelection = (productId: string, isSelected: boolean) => {
        if (isSelected) {
            setSelectedProducts((prev) => [...prev, productId])
        } else {
            setSelectedProducts((prev) => prev.filter((id) => id !== productId))
        }
    }

    const handleSaveProducts = async () => {
        if (selectedProducts.length === 0) {
            alertHelper.showToast("Please select at least one product", "warning")
            return
        }

        try {
            setIsLoadingProducts(true)
            const response = await axiosInstance.post(`/b2b/${params.id}/products/assign`, {
                productIds: selectedProducts,
            })

            if (response.data) {
                alertHelper.showToast(
                    `${selectedProducts.length} product${selectedProducts.length !== 1 ? "s" : ""} assigned successfully`,
                    "success",
                )
                setIsProductModalOpen(false)
                setSelectedProducts([])
                setProductSearchTerm("")
                fetchAssignedProducts()
            }
        } catch (error: any) {
            console.error("Error assigning products:", error)
            alertHelper.showToast(error.response?.data?.message || "Failed to assign products", "error")
        } finally {
            setIsLoadingProducts(false)
        }
    }

    if (loading && !b2bCompanyData) {
        return <LoadingState />
    }

    if (error) {
        return <ErrorState error={error} retry={fetchB2BCompanyDetails} />
    }

    return (
        <div className="w-full space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                    <h1 className="text-2xl font-bold">{b2bCompanyData?.companyName}</h1>
                    <p className="text-muted-foreground">ID: {b2bCompanyData?.clientCode}</p>
                </div>
                <div className="flex items-center gap-3">
                    <Link href={`/lite/admin/b2b/${params.id}/edit`} passHref>
                        <Button className="gap-2">
                            <Edit size={16} />
                            Edit Company
                        </Button>
                    </Link>

                    <Button className="gap-2" onClick={() => setIsProductModalOpen(true)}>
                        <Plus size={16} />
                        Add Product
                    </Button>
                </div>
            </div>

            <div className="grid lg:grid-cols-[2fr,1fr] gap-6">
                {/* Main Content */}
                <div className="space-y-6">
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle>Company Overview</CardTitle>
                            <CardDescription>View and manage company information</CardDescription>
                        </CardHeader>
                        <CardContent className="p-0">
                            <Tabs defaultValue="details" className="w-full">
                                <div className="px-6">
                                    <TabsList className="w-full justify-start h-auto p-0 bg-transparent border-b rounded-none">
                                        <TabsTrigger
                                            value="details"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Details
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="addresses"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Addresses
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="contacts"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Contacts
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="products"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Products
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="documents"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Documents
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="questionnaire"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Questionnaire
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="signers"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Signers
                                        </TabsTrigger>

                                    </TabsList>
                                </div>

                                <TabsContent value="details" className="p-6 pt-4">
                                    <div className="grid gap-6 md:grid-cols-2">
                                        <InfoItem label="Company Name" value={b2bCompanyData?.companyName} icon={<Building size={16} />} />
                                        <InfoItem
                                            label="Embossed Name"
                                            value={b2bCompanyData?.embossedName}
                                            icon={<Building size={16} />}
                                        />
                                        <InfoItem label="NIP (Tax ID)" value={b2bCompanyData?.nip} icon={<FileText size={16} />} />
                                        <InfoItem label="REGON" value={b2bCompanyData?.regon} icon={<FileText size={16} />} />
                                        <InfoItem
                                            label="Email"
                                            value={
                                                b2bCompanyData?.email ? (
                                                    <a href={`mailto:${b2bCompanyData.email}`} className="text-primary hover:underline">
                                                        {b2bCompanyData.email}
                                                    </a>
                                                ) : (
                                                    "N/A"
                                                )
                                            }
                                            icon={<Mail size={16} />}
                                        />
                                        <InfoItem
                                            label="Phone Number"
                                            value={
                                                b2bCompanyData?.phoneNumber ? (
                                                    <PhoneNumberDisplay phoneNumber={b2bCompanyData.phoneNumber} className="font-medium" />
                                                ) : (
                                                    "N/A"
                                                )
                                            }
                                            icon={<Phone size={16} />}
                                        />
                                        <InfoItem
                                            label="Auth Phone Number"
                                            value={
                                                b2bCompanyData?.authPhoneNumber ? (
                                                    <PhoneNumberDisplay phoneNumber={b2bCompanyData.authPhoneNumber} className="font-medium" />
                                                ) : (
                                                    "N/A"
                                                )
                                            }
                                            icon={<Phone size={16} />}
                                        />
                                        <InfoItem label="Client Code" value={b2bCompanyData?.clientCode} icon={<FileText size={16} />} />
                                        <InfoItem
                                            label="Created Date"
                                            value={formatDateLocal(b2bCompanyData?.createdAt)}
                                            icon={<Calendar size={16} />}
                                        />
                                    </div>
                                </TabsContent>

                                <TabsContent value="addresses" className="p-6 pt-4 space-y-8">
                                    {b2bCompanyData?.addresses && b2bCompanyData.addresses.length > 0 ? (
                                        b2bCompanyData.addresses.map((address, index) => (
                                            <div key={index}>
                                                <AddressDetails
                                                    address={address}
                                                    title={
                                                        address.type === "registration_address"
                                                            ? "Registration Address"
                                                            : address.type === "delivery_address"
                                                                ? "Delivery Address"
                                                                : "Address"
                                                    }
                                                />
                                                {index < b2bCompanyData.addresses.length - 1 && <Separator />}
                                            </div>
                                        ))
                                    ) : (
                                        <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg bg-muted/50">
                                            <MapPin size={48} className="text-muted-foreground mb-4" />
                                            <h4 className="text-lg font-medium mb-2">No Address Information</h4>
                                            <p className="text-muted-foreground">Address details have not been added yet.</p>
                                        </div>
                                    )}
                                </TabsContent>

                                <TabsContent value="contacts" className="p-6 pt-4">
                                    <div className="space-y-6">
                                        <div className="flex items-center justify-between">
                                            <h3 className="text-lg font-semibold">Company Contacts</h3>
                                            <Button size="sm" className="gap-2" onClick={handleAddContact}>
                                                <Plus size={14} />
                                                Add Contact
                                            </Button>
                                        </div>

                                        {contacts.length > 0 ? (
                                            <div className="space-y-4">
                                                {contacts.map((contact) => (
                                                    <div key={contact._id} className="flex items-start gap-4 p-4 rounded-lg border">
                                                        <Avatar className="h-12 w-12">
                                                            <AvatarFallback className="bg-teal-100 text-primary">
                                                                {contact.name?.charAt(0) || "C"}
                                                            </AvatarFallback>
                                                        </Avatar>
                                                        <div className="flex-1 space-y-1">
                                                            <div className="flex items-center justify-between">
                                                                <h4 className="font-medium">{contact.name || "N/A"}</h4>
                                                                <div className="flex items-center gap-2">
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="icon"
                                                                        className="h-8 w-8"
                                                                        onClick={() => handleEditContact(contact)}
                                                                    >
                                                                        <Edit size={16} />
                                                                    </Button>
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="icon"
                                                                        className="h-8 w-8 text-red-500 hover:text-red-600 hover:bg-red-50"
                                                                        onClick={() => handleDeleteContact(contact._id)}
                                                                    >
                                                                        <Trash2 size={16} />
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                            <p className="text-sm text-muted-foreground">{contact.role || "N/A"}</p>
                                                            <p className="text-sm text-muted-foreground">
                                                                {contact.contactType
                                                                    ? `${contact.contactType.charAt(0).toUpperCase() + contact.contactType.slice(1)} Contact`
                                                                    : "Contact"}
                                                            </p>
                                                            <div className="flex items-center gap-4 mt-2">
                                                                <div className="flex items-center gap-1 text-sm">
                                                                    <Mail size={14} className="text-muted-foreground" />
                                                                    <a href={`mailto:${contact.email}`} className="text-primary hover:underline">
                                                                        {contact.email}
                                                                    </a>
                                                                </div>
                                                                <div className="flex items-center gap-1 text-sm">
                                                                    <Phone size={14} className="text-muted-foreground" />
                                                                    <PhoneNumberDisplay phoneNumber={contact.phone} className="font-medium" />
                                                                </div>
                                                            </div>
                                                            {contact.notes && (
                                                                <p className="text-sm text-muted-foreground mt-2 italic">Note: {contact.notes}</p>
                                                            )}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg bg-muted/50">
                                                <Users size={48} className="text-muted-foreground mb-4" />
                                                <h4 className="text-lg font-medium mb-2">No Contacts Added</h4>
                                                <p className="text-muted-foreground mb-4">This company doesn't have any contacts added yet.</p>
                                                <Button size="sm" className="gap-2" onClick={handleAddContact}>
                                                    <Plus size={14} />
                                                    Add First Contact
                                                </Button>
                                            </div>
                                        )}
                                    </div>
                                </TabsContent>

                                <TabsContent value="products" className="p-6 pt-4">
                                    <div className="flex items-center justify-between mb-4">
                                        <h3 className="text-lg font-semibold">Assigned Products</h3>
                                        <Button size="sm" className="gap-2" onClick={() => setIsProductModalOpen(true)}>
                                            <Plus size={14} />
                                            Add Product
                                        </Button>
                                    </div>

                                    {assignedProducts && assignedProducts.length > 0 ? (
                                        <div className="space-y-4">
                                            {assignedProducts.map((product, index) => (
                                                <div
                                                    key={product._id || index}
                                                    className="p-4 rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer"
                                                    onClick={() => handleRowClick(product)}
                                                >
                                                    <div className="flex items-center justify-between">
                                                        <div className="space-y-1 flex-1">
                                                            <div className="flex items-start gap-2">
                                                                <div className="p-1.5 bg-primary/10 rounded text-primary mt-0.5">
                                                                    <Package size={16} />
                                                                </div>
                                                                <div className="flex-1">
                                                                    <h4 className="font-medium">{product.version_name || "Unnamed Product"}</h4>
                                                                </div>
                                                            </div>
                                                            <div className="flex items-start gap-4 text-sm text-muted-foreground ml-8">
                                                                <div className="flex items-start gap-1">
                                                                    <FileText size={14} className="mt-0.5" />
                                                                    <span>Code: {product.version_code || "N/A"}</span>
                                                                </div>
                                                                <div className="flex items-center gap-1">
                                                                    <Calendar size={14} />
                                                                    Created: {formatDateLocal(product.createdAt)}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="flex items-center gap-2">
                                                            <Button
                                                                variant="destructive"
                                                                size="sm"
                                                                onClick={(e) => {
                                                                    e.stopPropagation()
                                                                    handleUnassignProduct(product._id)
                                                                }}
                                                            >
                                                                Unassign
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg bg-muted/50">
                                            <Package size={48} className="text-muted-foreground mb-4" />
                                            <h4 className="text-lg font-medium mb-2">No Products Assigned</h4>
                                            <p className="text-muted-foreground mb-4">This company doesn't have any products assigned yet.</p>
                                            <Button size="sm" className="gap-2" onClick={() => setIsProductModalOpen(true)}>
                                                <Plus size={14} />
                                                Add First Product
                                            </Button>
                                        </div>
                                    )}
                                </TabsContent>

                                <TabsContent value="documents" className="p-6 pt-4">
                                    <div className="space-y-6">
                                        <div>
                                            <h3 className="text-lg font-semibold mb-4">Application Documents</h3>
                                            <div className="grid gap-3 md:grid-cols-2">
                                                <DocumentItem name="Articles of Incorporation" type="PDF" size="2.1MB" />
                                                <DocumentItem name="Company W-9" type="PDF" size="1.8MB" />
                                                <DocumentItem name="EIN Letter" type="PDF" size="0.9MB" />
                                                <DocumentItem name="Business License" type="PDF" size="1.2MB" />
                                            </div>
                                        </div>

                                        <div>
                                            <div className="flex items-center justify-between mb-4">
                                                <h3 className="text-lg font-semibold">Additional Documents</h3>
                                                <Button variant="outline" size="sm" className="gap-2 bg-transparent">
                                                    <Plus size={14} />
                                                    Upload Document
                                                </Button>
                                            </div>
                                            <div className="grid gap-3 md:grid-cols-2">
                                                {documents.map((doc, index) => (
                                                    <DocumentItem key={index} name={doc.name} type={doc.type} size={doc.size} />
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="questionnaire" className="p-6 pt-4">
                                    <div className="space-y-6">
                                        <div className="p-4 rounded-lg border">
                                            <h4 className="font-medium mb-3">What is your business type?</h4>
                                            <div className="space-y-2 pl-4">
                                                <div>
                                                    <p className="text-muted-foreground">Manufacturing</p>
                                                    <p className="text-sm text-muted-foreground">10110 - Processing and preserving of meat</p>
                                                </div>
                                                <div>
                                                    <p className="text-muted-foreground">Manufacturing</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        10120 - Processing and preserving of poultry meat
                                                    </p>
                                                </div>
                                                <div>
                                                    <p className="text-muted-foreground">Manufacturing</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        10130 - Production of meat and poultry meat products
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="p-4 rounded-lg border">
                                            <h4 className="font-medium mb-3">Please describe the products and/or services you offer:</h4>
                                            <p className="text-muted-foreground pl-4">
                                                Answer that was submitted goes here and can break unto multiple lines if it goes really far and
                                                the application typed out a lot.
                                            </p>
                                        </div>

                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">What is your company website URL?</h4>
                                                <p className="text-muted-foreground pl-4">https://google.com/shopping/</p>
                                            </div>

                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">Choose the source of funds for your company:</h4>
                                                <p className="text-muted-foreground pl-4">
                                                    Business income, Shareholder funds, Loan, Deposits & Savings
                                                </p>
                                            </div>
                                        </div>

                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">Choose the outbound payment types that apply:</h4>
                                                <p className="text-muted-foreground pl-4">Other: "Text that user inputted"</p>
                                            </div>

                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">
                                                    Expected <span className="italic">inbound</span> monthly volume:
                                                </h4>
                                                <p className="text-muted-foreground pl-4">Less than 100,000.00 EUR</p>
                                            </div>
                                        </div>

                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">
                                                    Expected number of <span className="italic">inbound</span> monthly payments:
                                                </h4>
                                                <p className="text-muted-foreground pl-4">Less than 100</p>
                                            </div>

                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">
                                                    Expected countries of <span className="italic">inbound</span> payments:
                                                </h4>
                                                <p className="text-muted-foreground pl-4">United States, Argentina, France</p>
                                            </div>
                                        </div>

                                        <div className="p-4 rounded-lg border">
                                            <h4 className="font-medium mb-2">
                                                Expected <span className="italic">outbound</span> monthly volume:
                                            </h4>
                                            <p className="text-muted-foreground pl-4">100,000 - 500,000 EUR</p>
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="signers" className="p-6 pt-4">
                                    <div className="space-y-6">
                                        <div className="space-y-4">
                                            <h3 className="text-lg font-semibold">Beneficial Owners</h3>

                                            <Collapsible className="rounded-lg border">
                                                <CollapsibleTrigger className="flex items-center justify-between w-full p-4 hover:bg-muted/50 transition-colors">
                                                    <div className="flex items-center gap-3">
                                                        <Avatar className="h-8 w-8">
                                                            <AvatarFallback className="bg-primary/10 text-primary">JS</AvatarFallback>
                                                        </Avatar>
                                                        <div>
                                                            <h4 className="font-medium">John Smitherson</h4>
                                                            <p className="text-sm text-muted-foreground">35% Ownership</p>
                                                        </div>
                                                    </div>
                                                    <ChevronDown className="h-4 w-4 text-muted-foreground" />
                                                </CollapsibleTrigger>
                                                <CollapsibleContent className="p-4 pt-0 border-t">
                                                    <div className="grid gap-4 md:grid-cols-2 pt-4">
                                                        <InfoItem label="Full Name" value="John Smitherson" />
                                                        <InfoItem label="Ownership" value="35%" />
                                                        <InfoItem label="Email" value="<EMAIL>" />
                                                        <InfoItem label="Phone" value="******-123-4567" />
                                                        <InfoItem label="Date of Birth" value="15 Jan 1980" />
                                                        <InfoItem label="Nationality" value="United States" />
                                                    </div>
                                                </CollapsibleContent>
                                            </Collapsible>

                                            <Collapsible className="rounded-lg border">
                                                <CollapsibleTrigger className="flex items-center justify-between w-full p-4 hover:bg-muted/50 transition-colors">
                                                    <div className="flex items-center gap-3">
                                                        <Avatar className="h-8 w-8">
                                                            <AvatarFallback className="bg-primary/10 text-primary">JD</AvatarFallback>
                                                        </Avatar>
                                                        <div>
                                                            <h4 className="font-medium">Jane Doe</h4>
                                                            <p className="text-sm text-muted-foreground">25% Ownership</p>
                                                        </div>
                                                    </div>
                                                    <ChevronDown className="h-4 w-4 text-muted-foreground" />
                                                </CollapsibleTrigger>
                                                <CollapsibleContent className="p-4 pt-0 border-t">
                                                    <div className="grid gap-4 md:grid-cols-2 pt-4">
                                                        <InfoItem label="Full Name" value="Jane Doe" />
                                                        <InfoItem label="Ownership" value="25%" />
                                                        <InfoItem label="Email" value="<EMAIL>" />
                                                        <InfoItem label="Phone" value="******-987-6543" />
                                                        <InfoItem label="Date of Birth" value="22 Mar 1985" />
                                                        <InfoItem label="Nationality" value="United States" />
                                                    </div>
                                                </CollapsibleContent>
                                            </Collapsible>
                                        </div>

                                        <div className="space-y-4">
                                            <h3 className="text-lg font-semibold">Authorized Persons</h3>

                                            <Collapsible className="rounded-lg border">
                                                <CollapsibleTrigger className="flex items-center justify-between w-full p-4 hover:bg-muted/50 transition-colors">
                                                    <div className="flex items-center gap-3">
                                                        <Avatar className="h-8 w-8">
                                                            <AvatarFallback className="bg-primary/10 text-primary">JS</AvatarFallback>
                                                        </Avatar>
                                                        <div>
                                                            <h4 className="font-medium">Joseph Smith</h4>
                                                            <p className="text-sm text-muted-foreground">Authorized Person</p>
                                                        </div>
                                                    </div>
                                                    <ChevronDown className="h-4 w-4 text-muted-foreground" />
                                                </CollapsibleTrigger>
                                                <CollapsibleContent className="p-4 pt-0 border-t">
                                                    <div className="grid gap-4 md:grid-cols-2 pt-4">
                                                        <InfoItem label="Full Name" value="Joseph Smith" />
                                                        <InfoItem label="Role" value="Authorized Person" />
                                                        <InfoItem label="Email" value="<EMAIL>" />
                                                        <InfoItem label="Phone" value="******-456-7890" />
                                                        <InfoItem label="Date of Birth" value="10 Sep 1978" />
                                                        <InfoItem label="Nationality" value="United States" />
                                                    </div>
                                                </CollapsibleContent>
                                            </Collapsible>
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="pmtypes" className="p-6 pt-4">
                                    <div className="space-y-4">
                                        <h3 className="text-lg font-semibold">Programme Manager Types</h3>

                                        {assignedProducts && assignedProducts.length > 0 ? (
                                            <div className="space-y-3">
                                                {assignedProducts.map((product) => (
                                                    <div key={product._id} className="p-4 rounded-lg border">
                                                        <div className="flex items-center gap-3">
                                                            <div className="p-1.5 bg-primary/10 rounded text-primary">
                                                                <Package size={16} />
                                                            </div>
                                                            <div>
                                                                <h4 className="font-medium">{product.version_name || "Unnamed Product"}</h4>
                                                                <p className="text-sm text-muted-foreground">
                                                                    Manager Type: {product.programManagerType?.manager_type || "N/A"}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg bg-muted/50">
                                                <Package size={48} className="text-muted-foreground mb-4" />
                                                <h4 className="text-lg font-medium mb-2">No Programme Manager Types</h4>
                                                <p className="text-muted-foreground mb-4">
                                                    This company doesn't have any programme manager types assigned yet.
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Activity</CardTitle>
                            <CardDescription>View company activity and risk evaluation</CardDescription>
                        </CardHeader>
                        <CardContent className="p-0">
                            <Tabs defaultValue="risk">
                                <div className="px-6">
                                    <TabsList className="w-full justify-start h-auto p-0 bg-transparent border-b rounded-none">
                                        <TabsTrigger
                                            value="risk"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Risk Evaluation
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="sumsub"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            SumSub
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="notes"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Internal Notes
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="documents"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Underwriting
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="logs"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Activity Log
                                        </TabsTrigger>
                                    </TabsList>
                                </div>

                                <TabsContent value="risk" className="p-6">
                                    <div className="flex items-center gap-6">
                                        <div className="p-4 rounded-full bg-yellow-100 text-yellow-700 text-xl font-bold">225</div>
                                        <div>
                                            <p className="text-sm text-muted-foreground mb-1">Risk Status</p>
                                            <StatusBadge status="Medium" />
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="sumsub" className="p-6">
                                    <div className="space-y-6">
                                        <div>
                                            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                                                <div className="w-4 h-4 rounded-full border" />
                                                Application review
                                            </h3>
                                            <div className="space-y-4 pl-6">
                                                <div className="flex items-start gap-x-6">
                                                    <div className="w-32 text-muted-foreground">Application ID:</div>
                                                    <div className="flex items-center gap-2">
                                                        x
                                                        <Button variant="ghost" size="icon" className="h-6 w-6">
                                                            <Copy className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </div>
                                                <div className="flex items-start gap-x-6">
                                                    <div className="w-32 text-muted-foreground">Review status:</div>
                                                    <div>
                                                        [
                                                        {statuses.map((status, i) => (
                                                            <span key={status}>
                                "{status}"{i < statuses.length - 1 ? " / " : ""}
                              </span>
                                                        ))}
                                                        ]
                                                    </div>
                                                </div>
                                                <div className="flex items-start gap-x-6">
                                                    <div className="w-32 text-muted-foreground">Review answer:</div>
                                                    <div>--</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="space-y-3">
                                            {reviewStatuses.map((status, index) => (
                                                <Collapsible key={index} className="border rounded-lg overflow-hidden">
                                                    <CollapsibleTrigger className="flex w-full items-center justify-between p-4 hover:bg-muted/50 transition-colors">
                                                        <div className="flex items-center gap-2">
                                                            {status.icon}
                                                            <span>
                                {status.label}
                                                                {status.actionRequired && (
                                                                    <span className="text-orange-500 ml-2">(Action required)</span>
                                                                )}
                              </span>
                                                        </div>
                                                        <ChevronDown className="h-4 w-4" />
                                                    </CollapsibleTrigger>
                                                    <CollapsibleContent className="p-4 border-t bg-muted/30">
                                                        <div className="text-muted-foreground">Content for {status.label}</div>
                                                    </CollapsibleContent>
                                                </Collapsible>
                                            ))}
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="notes" className="p-6">
                                    <div className="space-y-4">
                                        <Textarea placeholder="Type your notes here..." className="min-h-[150px]" />
                                        <div className="flex justify-end">
                                            <Button>Save Notes</Button>
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="documents" className="p-6">
                                    <div className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <h3 className="text-lg font-semibold">Underwriting Documents</h3>
                                            <Button variant="outline" size="sm" className="gap-2 bg-transparent">
                                                <Plus size={14} />
                                                Upload Document
                                            </Button>
                                        </div>

                                        <div className="space-y-3">
                                            {documents.map((doc, index) => (
                                                <div
                                                    key={index}
                                                    className="flex items-center justify-between p-3 rounded-lg border group hover:bg-muted/50 transition-colors"
                                                >
                                                    <div className="flex items-center gap-3">
                                                        <div className="p-2 bg-primary/10 rounded-md text-primary">
                                                            <FileText size={18} />
                                                        </div>
                                                        <div>
                                                            <p className="font-medium">{doc.name}</p>
                                                            <p className="text-xs text-muted-foreground">
                                                                {doc.type} • {doc.size}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                                                        >
                                                            <Download size={16} />
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity text-red-500 hover:text-red-600 hover:bg-red-50"
                                                        >
                                                            <X size={16} />
                                                        </Button>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>

                                        <div className="text-sm text-muted-foreground mt-2">Max file size: 4MB (PNG, JPG, PDF)</div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="logs" className="p-6">
                                    <div className="space-y-4 max-h-[400px] overflow-y-auto pr-2">
                                        {activities.map((activity, index) => (
                                            <div key={index} className="relative pl-6 pb-6 last:pb-0">
                                                <div className="absolute left-0 top-1.5 w-3 h-3 rounded-full bg-primary"></div>
                                                <div className="absolute left-1.5 top-4 bottom-0 w-[1px] bg-border last:hidden"></div>
                                                <div className="space-y-1">
                                                    <div className="font-medium">{activity.timestamp}</div>
                                                    <div className="text-muted-foreground">{activity.description}</div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </CardContent>
                    </Card>
                </div>

                {/* Sidebar */}
                <div className="space-y-6">
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle>Application Details</CardTitle>
                            <CardDescription>View application status and details</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-muted-foreground">Status</span>
                                    <StatusBadge status="Approved" />
                                </div>

                                <Separator />

                                <div className="space-y-3">
                                    <InfoItem
                                        label="Application ID"
                                        value={
                                            <div className="flex items-center gap-1">
                                                <span className="truncate max-w-[180px]">{b2bCompanyData?.clientCode}</span>
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    className="h-6 w-6"
                                                    onClick={() => {
                                                        navigator.clipboard.writeText(b2bCompanyData?.clientCode)
                                                        alertHelper.showToast("ID copied to clipboard", "success")
                                                    }}
                                                >
                                                    <Copy size={14} />
                                                </Button>
                                            </div>
                                        }
                                    />

                                    <InfoItem label="Origin" value="B2B Onboarding Form" />
                                    <InfoItem label="Relation" value={b2bCompanyData.parentCompany.company_name} />

                                    <InfoItem label="Received Date" value={formatDateLocal(b2bCompanyData?.createdAt)} />

                                    <InfoItem label="Due Date" value={formatDateLocal(b2bCompanyData?.dueDate)} />

                                    <InfoItem label="Approved Date" value={formatDateLocal(b2bCompanyData?.approvedDate)} />

                                    <InfoItem label="Application Approver" value={b2bCompanyData?.approver || "N/A"} />

                                    <InfoItem label="Assigned To" value={b2bCompanyData?.assignedTo || "N/A"} />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle>Required Tasks</CardTitle>
                            <CardDescription>Track required task completion</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <CheckCircle2 size={18} className="text-green-500" />
                                        <span>Verification</span>
                                    </div>
                                    <Badge variant="outline" className="bg-green-100 text-green-700 border-green-200">
                                        Complete
                                    </Badge>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <Clock size={18} className="text-yellow-500" />
                                        <span>Document Review</span>
                                    </div>
                                    <Badge variant="outline" className="bg-yellow-100 text-yellow-700 border-yellow-200">
                                        In Progress
                                    </Badge>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <Clock size={18} className="text-gray-400" />
                                        <span>Risk Assessment</span>
                                    </div>
                                    <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-200">
                                        Pending
                                    </Badge>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <Clock size={18} className="text-gray-400" />
                                        <span>Final Approval</span>
                                    </div>
                                    <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-200">
                                        Pending
                                    </Badge>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle>Additional Events</CardTitle>
                            <CardDescription>View and manage additional events</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {events && events.length > 0 ? (
                                    events.map((event, index) => (
                                        <div key={index} className="p-3 rounded-lg border">
                                            <p className="font-medium mb-2">{event.title}</p>
                                            <div className="flex items-center gap-2 text-sm text-primary">
                                                <Paperclip size={14} />
                                                <span>
                          {event.name}.{event.type}
                        </span>
                                                <span className="text-muted-foreground">({event.size})</span>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="text-center p-4 text-muted-foreground">No events found</div>
                                )}

                                <Button variant="outline" size="sm" className="w-full gap-2 mt-2 bg-transparent">
                                    <Plus size={14} />
                                    Add Event
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>

            {/* Product Assignment Modal */}
            <Dialog open={isProductModalOpen} onOpenChange={setIsProductModalOpen}>
                <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
                    <DialogHeader>
                        <DialogTitle>Assign Products to B2B Customer</DialogTitle>
                        <DialogDescription>
                            Select products from your company's available products to assign to this B2B customer.
                        </DialogDescription>
                    </DialogHeader>

                    <div className="flex flex-col gap-4 flex-1 overflow-hidden">
                        {/* Search */}
                        <div className="relative">
                            <Input
                                placeholder="Search products by name or code..."
                                value={productSearchTerm}
                                onChange={(e) => setProductSearchTerm(e.target.value)}
                                className="pl-10"
                            />
                        </div>

                        {/* Products List */}
                        <div className="flex-1 overflow-y-auto border rounded-lg">
                            {isLoadingProducts ? (
                                <div className="flex items-center justify-center p-8">
                                    <div className="flex items-center gap-2">
                                        <RefreshCw className="animate-spin" size={16} />
                                        <span>Loading products...</span>
                                    </div>
                                </div>
                            ) : filteredProducts.length > 0 ? (
                                <div className="divide-y">
                                    {filteredProducts.map((product) => (
                                        <div key={product._id} className="p-4 hover:bg-muted/50">
                                            <div className="flex items-start gap-3">
                                                <Checkbox
                                                    id={`product-${product._id}`}
                                                    checked={selectedProducts.includes(product._id)}
                                                    onCheckedChange={(checked) => handleProductSelection(product._id, checked as boolean)}
                                                />
                                                <div className="flex-1 min-w-0">
                                                    <label htmlFor={`product-${product._id}`} className="block font-medium cursor-pointer">
                                                        {product.productVersionName?.[0]?.version_name || "Unnamed Product"}
                                                    </label>
                                                    <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                                                        <div className="flex items-center gap-1">
                                                            <FileText size={12} />
                                                            Code: {product.productVersionName?.[0]?.version_code || "N/A"}
                                                        </div>
                                                        <div className="flex items-center gap-1">
                                                            <Clock size={12} />
                                                            Created: {formatDateLocal(product.createdAt)}
                                                        </div>
                                                    </div>
                                                    {product.programManagerType?.manager_type && (
                                                        <div className="text-xs text-muted-foreground mt-1">
                                                            Manager Type: {product.programManagerType.manager_type}
                                                        </div>
                                                    )}
                                                </div>
                                                <div className="p-1.5 bg-primary/10 rounded text-primary">
                                                    <Package size={16} />
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="flex flex-col items-center justify-center p-8 text-center">
                                    <Package size={48} className="text-muted-foreground mb-4" />
                                    <h4 className="text-lg font-medium mb-2">No Products Found</h4>
                                    <p className="text-muted-foreground">
                                        {productSearchTerm ? "No products match your search criteria." : "No products available to assign."}
                                    </p>
                                    {productSearchTerm && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="mt-3 bg-transparent"
                                            onClick={() => setProductSearchTerm("")}
                                        >
                                            Clear Search
                                        </Button>
                                    )}
                                </div>
                            )}
                        </div>

                        {/* Selection Summary and Actions */}
                        <div className="flex items-center justify-between pt-4 border-t bg-muted/20 p-4 rounded-lg">
                            <div className="flex items-center gap-4">
                                <p className="text-sm font-medium">
                                    {selectedProducts.length} product{selectedProducts.length !== 1 ? "s" : ""} selected
                                </p>
                                {selectedProducts.length > 0 && (
                                    <Button variant="outline" size="sm" onClick={() => setSelectedProducts([])}>
                                        Clear Selection
                                    </Button>
                                )}
                            </div>
                            <div className="flex gap-2">
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setIsProductModalOpen(false)
                                        setSelectedProducts([])
                                        setProductSearchTerm("")
                                    }}
                                >
                                    Cancel
                                </Button>
                                <Button onClick={handleSaveProducts} disabled={selectedProducts.length === 0 || isLoadingProducts}>
                                    {isLoadingProducts ? (
                                        <>
                                            <RefreshCw className="animate-spin mr-2" size={14} />
                                            Assigning...
                                        </>
                                    ) : (
                                        `Assign ${selectedProducts.length} Product${selectedProducts.length !== 1 ? "s" : ""}`
                                    )}
                                </Button>
                            </div>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Contact Form Dialog */}
            <ContactForm
                open={contactFormOpen}
                onOpenChange={setContactFormOpen}
                companyId={params.id}
                existingContact={selectedContact}
                onSuccess={refreshContacts}
            />

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the contact from the company.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={confirmDeleteContact} className="bg-red-600 hover:bg-red-700">
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    )
}
