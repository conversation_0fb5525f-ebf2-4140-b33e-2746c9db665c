//@ts-nocheck
"use client"

import React, {useEffect, useState} from "react"
import {AlertTriangle} from "lucide-react"

import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card"
import axiosInstance from "@/utils/axiosInstance";
import {useAppSelector} from "@/store/hooks";
import DataExporter from "@/components/DataExporter";
import {DataTable} from "@/components/data-table";
import {LoadingOverlay} from "@/components/LoadingOverlay";
import {countryNameByCode} from "@/utils/data";
import { useRouter} from "next/navigation";
import {formatDate} from "@/utils/helpers";
import PhoneNumberDisplay from "@/components/PhoneDispaly";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Types
interface PersonalInfo {
    firstName: string
    middleName?: string
    lastName: string
    dateOfBirth: string
    email: string
    phone: string
}

interface Address {
    streetAddress: string
    building?: string
    apartment?: string
    city: string
    stateProvince: string
    postalCode: string
    country: string
}

interface OnboardingData {
    personalInfo: PersonalInfo
    address: Address
}



// Loading component

export default function Dashboard() {
    const [onboarding, setOnboarding] = useState<OnboardingData | null>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [phoneFilter, setPhoneFilter] = useState("")
    const [filteredData, setFilteredData] = useState([])
    const [activeTab, setActiveTab] = useState("individuals")
    const router = useRouter()
    const user = useAppSelector((state) => state.user.user)

    useEffect(() => {
        const fetchOnboardingDetails = async () => {
            try {
                const response = await axiosInstance.get(`company/${user.recordId}/bankingClients`)
                console.dir(response.data.users)
                setOnboarding(response.data.users)
                setFilteredData(response.data.users)
            } catch (error) {
                console.error("Error fetching onboarding details:", error)
                setError("Failed to fetch onboarding details")
            } finally {
                setLoading(false)
            }
        }

        fetchOnboardingDetails()
    }, [])

    // Filter data when phone filter changes
    useEffect(() => {
        if (!onboarding) return;

        if (!phoneFilter) {
            setFilteredData(onboarding);
            return;
        }

        const filtered = onboarding.filter(client =>
            client.personalInfo.phone &&
            client.personalInfo.phone.includes(phoneFilter.replace(/\D/g, ''))
        );
        setFilteredData(filtered);
    }, [phoneFilter, onboarding]);

    if (loading) {
        return <LoadingOverlay/>
    }

    const columns = [
        {
            header: "Date Created",
            accessorKey: "createdAt" as const,
            cell: (row) => formatDate(row.createdAt),
        },{
            header: "Customer ID",
            accessorKey: "clientID" as const,
            cell: (row) => row.clientID,
        },
        {
            header: "Name",
            accessorKey: "name" as const,
            cell: (row) => `${row.personalInfo.firstName} ${row.personalInfo.lastName}`,
        },
        {
            header: "Email",
            accessorKey: "email" as const,
            cell: (row) => row.personalInfo.email,
        },{
            header: "Mobile",
            accessorKey: "mobile" as const,
            cell: (row) => <PhoneNumberDisplay phoneNumber={row.personalInfo.phone} />,
        },
        {
            header: "Country",
            accessorKey: "country" as const,
            cell: (row) => countryNameByCode(row.address.country,),
        },
    ]


    if (error || !onboarding) {
        return (
            <Card className="w-full max-w-md mx-auto mt-8">
                <CardContent className="py-10">
                    <AlertTriangle className="h-16 w-16 text-yellow-500 mx-auto mb-4"/>
                    <p className="text-center text-lg font-medium text-muted-foreground">
                        {error || "No Onboarding Details found."}
                    </p>
                </CardContent>
            </Card>
        )
    }

    let data = [];
    if(onboarding && onboarding.length>0) {
        data = onboarding.map((row, index) => ({
            id: index + 1,
            name: `${row.personalInfo.firstName} ${row.personalInfo.lastName}`,
            email: row.personalInfo.email,
            phone: row.personalInfo.phone,
            citizenship: row.citizenship,
            birth_country: row.personalInfo.birthCountry,
            risk_level: row.riskLevel,
            application_status: row.applicationStatus,
            created_at: formatDate(row.createdAt),

        }));
    }
    return (
        <div className="container mx-auto px-4 py-8 space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle className="text-lg font-bold mb-2 ">Banking Customers</CardTitle>
                </CardHeader>
                <CardContent>
                    <Tabs defaultValue="individuals" onValueChange={setActiveTab} className="mb-6">
                        <TabsList className="mb-4">
                            <TabsTrigger value="individuals">Individuals</TabsTrigger>
                            <TabsTrigger value="corporates">Corporates</TabsTrigger>
                        </TabsList>

                        <TabsContent value="individuals">
                            <DataExporter
                                data={data}
                                filename="individual_banking_customers"
                                title="Individual Banking Customers Report"
                            />
                            <br/>

                            <div className="flex items-center mb-4">
                                <div className="w-full max-w-sm">
                                    <label htmlFor="phone-filter" className="text-sm font-medium mb-1 block">
                                        Filter by Phone Number
                                    </label>
                                    <Input
                                        id="phone-filter"
                                        placeholder="Enter phone number to filter..."
                                        value={phoneFilter}
                                        onChange={(e) => setPhoneFilter(e.target.value)}
                                        className="w-full"
                                    />
                                </div>
                            </div>

                            <DataTable
                                data={filteredData}
                                columns={columns}
                                title="Individual Banking Customers"
                                description={`Total Individual Customers: ${filteredData.length}`}
                                loading={loading}
                                error={error}
                                getRowId={(c) => c._id}
                                onRowClick={r => router.push(`customers/v1/${r._id}`)}
                            />
                        </TabsContent>

                        <TabsContent value="corporates">
                            <div className="flex flex-col items-center justify-center py-12 text-center">
                                <div className="rounded-full bg-muted p-6 mb-4">
                                    <AlertTriangle className="h-10 w-10 text-muted-foreground" />
                                </div>
                                <h3 className="text-xl font-semibold mb-2">Corporate Customers</h3>
                                <p className="text-muted-foreground max-w-md">
                                    Corporate customer management is coming soon. This feature is currently under development.
                                </p>
                            </div>
                        </TabsContent>
                    </Tabs>
                </CardContent>
            </Card>
        </div>
    )
}