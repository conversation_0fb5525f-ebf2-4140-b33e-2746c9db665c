//@ts-nocheck
"use client"

import React, {useEffect, useState} from "react"
import {useRouter} from "next/navigation"
import {useAppSelector} from "@/store/hooks"
import axiosInstance from "@/utils/axiosInstance"
import {formatDate} from "@/utils/helpers"
import {BarChart3, CreditCard, Users} from "lucide-react"

import {LoadingOverlay} from "@/components/LoadingOverlay"
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card"
import Company from "@/components/company-profile";

// Mock data for charts
const mockRiskData = [
    { name: "Low", value: 30 },
    { name: "Medium", value: 45 },
    { name: "High", value: 25 },
]

const mockApplicationsOverTime = [
    { month: "Jan", applications: 12 },
    { month: "Feb", applications: 19 },
    { month: "Mar", applications: 15 },
    { month: "Apr", applications: 25 },
    { month: "May", applications: 32 },
    { month: "Jun", applications: 28 },
]

const mockDocumentStatus = [
    { name: "Approved", value: 65 },
    { name: "Pending", value: 25 },
    { name: "Rejected", value: 10 },
]

const mockProductDistribution = [
    { name: "Banking", value: 40 },
    { name: "Investments", value: 30 },
    { name: "Insurance", value: 20 },
    { name: "Loans", value: 10 },
]

export default function ProgrammeManagerDashboard() {
    const [companyData, setCompanyData] = useState<null | any>(null)
    const [cip, setCip] = useState<null | any>(null)
    const [customers, setCustomers] = useState<null | any>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [activeTab, setActiveTab] = useState("overview")
    const user = useAppSelector((state) => state.user.user)
    const router = useRouter()

    useEffect(() => {
        async function fetchCompanyDetails() {
            try {
                const response = await axiosInstance.get(`/company/${user.recordId}`)
                const c_response = await axiosInstance.get(`company/${user.recordId}/bankingClients`)
                setCompanyData(response.data.company)
                setCip(response.data.cip)
                setCustomers(c_response.data.users)
            } catch (error: any) {
                setError(error.response?.data.message || "Failed to fetch company data")
            } finally {
                setLoading(false)
            }
        }

        fetchCompanyDetails()
    }, [user.recordId])

    const handleRowClick = () => {
        router.push(`/lite/admin/programmes/company/${user.recordId}/BIN-view`)
    }

    if (loading) {
        return <LoadingOverlay />
    }

    // Calculate KPIs
    const totalProducts = cip?.length || 0
    const totalCustomers = customers?.length || 0
    const riskScore = 225
    const riskLevel = riskScore > 200 ? "Medium" : riskScore > 100 ? "Low" : "High"
    const applicationStatus = "Approved"
    const completionPercentage = 85
    const daysToApproval = 14

    return (
        <div className="container mx-auto space-y-4 px-4 py-8">
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold">Programme Manager Dashboard</h1>
                    <p className="text-muted-foreground">Company {companyData.ryvyl_id}</p>
                </div>
                <div className="flex gap-2">
                    {/*<Button variant="outline" size="sm">*/}
                    {/*    Export Data*/}
                    {/*</Button>*/}
                    {/*<Button size="sm">Actions</Button>*/}
                </div>
            </div>
            <div className="grid gap-4 md:grid-cols-2   lg:grid-cols-4">
                <Card>
                    <CardContent className="pt-6">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-muted-foreground text-sm">Cardholders</p>
                                <h3 className="text-2xl font-bold mt-1">{totalCustomers}</h3>
                            </div>
                            <div className="p-2 rounded-full bg-blue-100">
                                <Users className="h-5 w-5 text-blue-600" />
                            </div>
                        </div>
                        <div className="mt-4 text-sm">
                            <p className="text-muted-foreground">
                                Last added: {customers && customers.length > 0 ? formatDate(customers[0].createdAt) : "N/A"}
                            </p>
                        </div>
                    </CardContent>
                </Card>




                <Card>
                    <CardContent className="pt-6">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-muted-foreground text-sm">Products</p>
                                <h3 className="text-2xl font-bold mt-1">{totalProducts}</h3>
                            </div>
                            <div className="p-2 rounded-full bg-blue-100">
                                <BarChart3 className="h-5 w-5 text-blue-600" />
                            </div>
                        </div>
                        <div className="mt-4 text-sm">
                            <p className="text-muted-foreground">
                                Last added: {cip && cip.length > 0 ? formatDate(cip[0].createdAt) : "N/A"}
                            </p>
                        </div>
                    </CardContent>
                </Card>

            </div>




<Company  params={{ id: user.recordId }}/>

        </div>
    )
}

