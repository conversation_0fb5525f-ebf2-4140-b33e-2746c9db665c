//@ts-nocheck
"use client"

import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
    ChevronDown,
    Copy,
    Paperclip,
    Plus,
    X,
    Building,
    MapPin,
    Phone,
    Mail,
    Globe,
    FileText,
    Package,
    Calendar,
    AlertTriangle,
    CheckCircle2,
    Clock,
    RefreshCw,
    Download,
    Edit,
    Trash2,
    Users,
} from "lucide-react"
import type React from "react"
import { useEffect, useState, memo, useCallback } from "react"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Skeleton } from "@/components/ui/skeleton"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"

import { ContactForm } from "@/components/add-contact-modal"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {alertHelper} from "@/utils/alertHelper";
import axiosInstance from "@/utils/axiosInstance";
import Swal from "sweetalert2";
import {countryNameByCode} from "@/utils/data";


const MOCK_EVENTS_DATA = [
    {
        title: "Quarterly Review Meeting",
        name: "quarterly_review",
        type: "meeting",
        size: "2.1MB",
    },
    {
        title: "Contract Renewal",
        name: "contract_renewal",
        type: "document",
        size: "1.5MB",
    },
]

// Helper function to format dates
const formatDate = (dateString: string) => {
    if (!dateString) return "N/A"
    return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
    })
}

interface Document {
    name: string
    type: string
    size: string
}

interface Event {
    name: string
    type: string
    size: string
    title: string
}

const documents: Document[] = [
    { name: "nameofdocument", type: "doctype", size: "2.1MB" },
    { name: "nameofdocument", type: "doctype", size: "2.1MB" },
    { name: "nameofdocument", type: "doctype", size: "2.1MB" },
]

interface ReviewStatus {
    icon: React.ReactNode
    label: string
    actionRequired?: boolean
}

const statuses = ["Initiated", "Pending", "Pre-checked", "Queued", "On Hold"]

const reviewStatuses: ReviewStatus[] = [
    {
        icon: <div className="w-4 h-4 rounded-full border" />,
        label: "John Smitherson",
    },
    {
        icon: (
            <div className="w-4 h-4 rounded-full bg-orange-500 text-white flex items-center justify-center text-xs">!</div>
        ),
        label: "John Smitherson",
        actionRequired: true,
    },
    {
        icon: <div className="w-4 h-4 rounded-full bg-red-500 text-white flex items-center justify-center text-xs">×</div>,
        label: "John Smitherson",
    },
    {
        icon: (
            <div className="w-4 h-4 rounded-full bg-green-500 text-white flex items-center justify-center text-xs">✓</div>
        ),
        label: "John Smitherson",
    },
]

interface Activity {
    timestamp: string
    description: string
}

const activities: Activity[] = [
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
]

const InfoItem = ({ label, value, icon }: { label: string; value: React.ReactNode; icon?: React.ReactNode }) => (
    <div className="flex items-start gap-3">
        {icon && <div className="mt-0.5 text-muted-foreground">{icon}</div>}
        <div>
            <p className="text-sm font-medium text-muted-foreground">{label}</p>
            <div className="mt-1 font-medium">{value}</div>
        </div>
    </div>
)

const AddressDetails = memo(({ address, title }: { address: any; title: string }) => {
    if (!address) return null

    return (
        <div className="space-y-4">
            <h3 className="text-lg font-semibold">{title}</h3>
            <div className="grid gap-4 md:grid-cols-2">
                <InfoItem label="Street Address" value={address.street} icon={<MapPin size={16} />} />
                {address.building_number && (
                    <InfoItem label="Building Number" value={address.building_number} icon={<Building size={16} />} />
                )}
                {address.apartment_number && (
                    <InfoItem label="Apartment Number" value={address.apartment_number} icon={<Building size={16} />} />
                )}
                <InfoItem label="City" value={address.city} icon={<MapPin size={16} />} />
                {address.state && <InfoItem label="State/Province" value={address.state} icon={<MapPin size={16} />} />}
                <InfoItem label="Postal Code" value={address.postal_code} icon={<MapPin size={16} />} />
                <InfoItem label="Country" value={address.country} icon={<MapPin size={16} />} />
            </div>
        </div>
    )
})
AddressDetails.displayName = "AddressDetails"

const DocumentItem = ({ name, type, size }: { name: string; type: string; size: string }) => (
    <div className="flex items-center justify-between p-3 rounded-lg border group hover:bg-muted/50 transition-colors">
        <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-md text-primary">
                <FileText size={18} />
            </div>
            <div>
                <p className="font-medium">{name}</p>
                <p className="text-xs text-muted-foreground">
                    {type} • {size}
                </p>
            </div>
        </div>
        <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity">
            <Download size={16} />
        </Button>
    </div>
)

const StatusBadge = ({ status }: { status: string }) => {
    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case "approved":
                return "bg-green-100 text-green-700 border-green-200"
            case "pending":
                return "bg-yellow-100 text-yellow-700 border-yellow-200"
            case "rejected":
                return "bg-red-100 text-red-700 border-red-200"
            case "medium":
                return "bg-yellow-100 text-yellow-700 border-yellow-200"
            default:
                return "bg-gray-100 text-gray-700 border-gray-200"
        }
    }

    return (
        <Badge variant="outline" className={`w-fit ${getStatusColor(status)}`}>
            {status}
        </Badge>
    )
}
const handleUnassignProduct = async (productId: string) => {
    const result = await Swal.fire({
        title: "Unassign Product",
        text: "Are you sure you want to unassign this product?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#00a8a5",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, unassign!",
    })

    if (result.isConfirmed) {
        setLoading(true)
        try {
            const response = await axiosInstance.delete(`/cip/${productId}`)
            if (response.status === 200) {
                setCip((prevCip) => prevCip.filter((product) => product._id !== productId))
                alertHelper.showToast("Product unassigned successfully", "success")
            }
        } catch (error: any) {
            setError(error.response?.data.message || "Failed to unassign product")
            alertHelper.showToast(
                "Failed to unassign product: " + (error.response?.data.message || "Unknown error"),
                "error",
            )
        } finally {
            setLoading(false)
        }
    }
}

const LoadingState = () => (
    <div className="w-full space-y-6">
        <Skeleton className="h-8 w-64" />
        <div className="grid lg:grid-cols-[2fr,1fr] gap-6">
            <div className="space-y-6">
                <Card>
                    <CardHeader>
                        <Skeleton className="h-6 w-48" />
                        <Skeleton className="h-4 w-32" />
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {Array(6)
                                .fill(0)
                                .map((_, i) => (
                                    <div key={i} className="flex justify-between">
                                        <Skeleton className="h-4 w-32" />
                                        <Skeleton className="h-4 w-48" />
                                    </div>
                                ))}
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader>
                        <Skeleton className="h-6 w-32" />
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {Array(4)
                                .fill(0)
                                .map((_, i) => (
                                    <Skeleton key={i} className="h-16 w-full" />
                                ))}
                        </div>
                    </CardContent>
                </Card>
            </div>
            <div className="space-y-6">
                {Array(3)
                    .fill(0)
                    .map((_, i) => (
                        <Card key={i}>
                            <CardHeader>
                                <Skeleton className="h-6 w-40" />
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {Array(3)
                                        .fill(0)
                                        .map((_, j) => (
                                            <Skeleton key={j} className="h-4 w-full" />
                                        ))}
                                </div>
                            </CardContent>
                        </Card>
                    ))}
            </div>
        </div>
    </div>
)

const ErrorState = ({ error, retry }: { error: string; retry: () => void }) => (
    <div className="w-full flex flex-col items-center justify-center p-12">
        <div className="p-4 rounded-full bg-red-100 text-red-600 mb-4">
            <AlertTriangle size={32} />
        </div>
        <h2 className="text-xl font-semibold mb-2">Error Loading Company Data</h2>
        <p className="text-muted-foreground mb-6 text-center max-w-md">{error}</p>
        <Button onClick={retry} className="flex items-center gap-2">
            <RefreshCw size={16} />
            Retry
        </Button>
    </div>
)

export default function Company({ params }: { params: { id: string } }) {
    const [companyData, setCompanyData] = useState<null | any>(null)
    const [cip, setCip] = useState<null | any>(null)
    const [contacts, setContacts] = useState<any[]>([])
    const [events, setEvents] = useState<Event[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [contactFormOpen, setContactFormOpen] = useState(false)
    const [selectedContact, setSelectedContact] = useState<any>(null)
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
    const [contactToDelete, setContactToDelete] = useState<string | null>(null)
    const [useMockData] = useState(true) // Always use mock data for now

    const router = useRouter()

    const fetchCompanyDetails = useCallback(async () => {
        try {
            setLoading(true)
            setError(null)

            const response = await axiosInstance.get(`/company/${params.id}`)
            setCompanyData(response.data.company)
            setCip(response.data.cip)
            setContacts(response.data.contacts)
            setEvents(MOCK_EVENTS_DATA)



        } catch (error: any) {
            console.error("Error in fetchCompanyDetails:", error)
            setError("Failed to load company data. Please try again.")
        } finally {
            setLoading(false)
        }
    }, [])

    useEffect(() => {
        fetchCompanyDetails()
    }, [fetchCompanyDetails, params.id])

    const handleRowClick = (program: any) => {
        router.push(`/lite/admin/programmes/company/${params.id}/BIN-view`)
    }


    const handleAddContact = () => {
        setSelectedContact(null)
        setContactFormOpen(true)
    }

    const handleEditContact = (contact: any) => {
        setSelectedContact(contact)
        setContactFormOpen(true)
    }

    const handleDeleteContact = (contactId: string) => {
        setContactToDelete(contactId)
        setDeleteDialogOpen(true)
    }

    const confirmDeleteContact = async () => {
        if (!contactToDelete) return

        try {
            setLoading(true)
            const response = await axiosInstance.delete(`contacts/${contactToDelete}`)
            if (response.status === 200) {
                alertHelper.showToast("The contact has been deleted successfully.", "success")
                setContacts((prevContacts) => prevContacts.filter((contact) => contact._id !== contactToDelete))

            }


            alertHelper.showToast("The contact has been deleted successfully.", "success")
        } catch (error: any) {
            console.error("Error deleting contact:", error)
            alertHelper.showToast("Failed to delete contact. Please try again.", "error")
        } finally {
            setLoading(false)
            setDeleteDialogOpen(false)
            setContactToDelete(null)
        }
    }
    // Find the refreshContacts function and replace it with this improved version
    // that properly handles both adding new contacts and updating existing ones

    const refreshContacts = async (newContactData: any) => {
        // For mock data, just update the state
        if (selectedContact) {
            // Edit existing contact - update the contact with matching ID
            setContacts((prevContacts) =>
                prevContacts.map((contact) =>
                    contact._id === selectedContact._id ? { ...contact, ...newContactData } : contact,
                ),
            )

            alertHelper.showToast("The contact has been updated successfully.", "success")
        } else {
            // Add new contact - create a new ID and add to the list
            const newContact = {
                ...newContactData,
                _id: `contact_${Date.now()}`, // Generate a unique ID
            }
            setContacts((prevContacts) => [...prevContacts, newContact])

            alertHelper.showToast("The contact has been added successfully.", "success")
        }
    }
    if (loading && !companyData) {
        return <LoadingState />
    }

    if (error) {
        return <ErrorState error={error} retry={fetchCompanyDetails} />
    }

    return (
        <div className="w-full space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                    <h1 className="text-2xl font-bold">{companyData?.company_name}</h1>
                    <p className="text-muted-foreground">ID: {companyData?.ryvyl_id}</p>
                </div>
                <div className="flex items-center gap-3">
                    {/*<StatusBadge status={companyData?.status || "Pending"} />*/}

                    {/*<Link href={`${params.id}/cip/create`} passHref>*/}
                    {/*    <Button className="gap-2">*/}
                    {/*        <Plus size={16} />*/}
                    {/*        Add Product*/}
                    {/*    </Button>*/}
                    {/*</Link>*/}
                </div>
            </div>

            <div className="grid lg:grid-cols-[2fr,1fr] gap-6">
                {/* Main Content */}
                <div className="space-y-6">
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle>Company Overview</CardTitle>
                            <CardDescription>View and manage company information</CardDescription>
                        </CardHeader>
                        <CardContent className="p-0">
                            <Tabs defaultValue="details" className="w-full">
                                <div className="px-6">
                                    <TabsList className="w-full justify-start h-auto p-0 bg-transparent border-b rounded-none">
                                        <TabsTrigger
                                            value="details"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Details
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="addresses"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Addresses
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="contacts"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Contacts
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="products"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Products
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="documents"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Documents
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="questionnaire"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Questionnaire
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="signers"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Signers
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="pmtypes"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            PM Types
                                        </TabsTrigger>
                                    </TabsList>
                                </div>

                                <TabsContent value="details" className="p-6 pt-4">
                                    <div className="grid gap-6 md:grid-cols-2">
                                        <InfoItem label="Company Name" value={companyData?.company_name} icon={<Building size={16} />} />
                                        <InfoItem
                                            label="Country of Incorporation"
                                            value={countryNameByCode(companyData?.country_of_incorporation)}
                                            icon={<MapPin size={16} />}
                                        />
                                        <InfoItem
                                            label="Registration Number"
                                            value={companyData?.company_number}
                                            icon={<FileText size={16} />}
                                        />
                                        <InfoItem
                                            label="Registration Date"
                                            value={formatDate(companyData?.registration_date)}
                                            icon={<Calendar size={16} />}
                                        />
                                        <InfoItem label="Industry" value={companyData?.company_industry} icon={<Building size={16} />} />
                                        <InfoItem
                                            label="Type of Business"
                                            value={companyData?.type_of_business}
                                            icon={<Building size={16} />}
                                        />
                                        <InfoItem
                                            label="Website"
                                            value={
                                                <a
                                                    href={companyData?.company_website}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-primary hover:underline"
                                                >
                                                    {companyData?.company_website}
                                                </a>
                                            }
                                            icon={<Globe size={16} />}
                                        />
                                        <InfoItem label="Client ID" value={companyData?.ryvyl_id} icon={<FileText size={16} />} />
                                        <InfoItem
                                            label="Company Email"
                                            value={
                                                <a href={`mailto:${companyData?.company_email}`} className="text-primary hover:underline">
                                                    {companyData?.company_email}
                                                </a>
                                            }
                                            icon={<Mail size={16} />}
                                        />
                                        <InfoItem label="Company Phone" value={companyData?.company_phone} icon={<Phone size={16} />} />
                                    </div>
                                </TabsContent>

                                <TabsContent value="addresses" className="p-6 pt-4 space-y-8">
                                    <AddressDetails address={companyData?.registered_address} title="Registered Address" />

                                    {companyData?.operational_address && (
                                        <>
                                            <Separator />
                                            <AddressDetails address={companyData?.operational_address} title="Operational Address" />
                                        </>
                                    )}
                                </TabsContent>

                                <TabsContent value="contacts" className="p-6 pt-4">
                                    <div className="space-y-6">
                                        <div className="flex items-center justify-between">
                                            <h3 className="text-lg font-semibold">Company Contacts</h3>
                                            <Button   size="sm" className="gap-2" onClick={handleAddContact}>
                                                <Plus size={14} />
                                                Add Contact
                                            </Button>
                                        </div>

                                        {contacts.length > 0 ? (
                                            <div className="space-y-4">
                                                {contacts.map((contact) => (
                                                    <div key={contact._id} className="flex items-start gap-4 p-4 rounded-lg border">
                                                        <Avatar className="h-12 w-12">
                                                            <AvatarFallback className="bg-teal-100 text-primary">
                                                                {contact.name?.charAt(0) || "C"}
                                                            </AvatarFallback>
                                                        </Avatar>
                                                        <div className="flex-1 space-y-1">
                                                            <div className="flex items-center justify-between">
                                                                <h4 className="font-medium">{contact.name || "N/A"}</h4>
                                                                <div className="flex items-center gap-2">
                                                                    {/*<Button*/}
                                                                    {/*    variant="ghost"*/}
                                                                    {/*    size="icon"*/}
                                                                    {/*    className="h-8 w-8"*/}
                                                                    {/*    onClick={() => handleEditContact(contact)}*/}
                                                                    {/*>*/}
                                                                    {/*    <Edit size={16} />*/}
                                                                    {/*</Button>*/}
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="icon"
                                                                        className="h-8 w-8 text-red-500 hover:text-red-600 hover:bg-red-50"
                                                                        onClick={() => handleDeleteContact(contact._id)}
                                                                    >
                                                                        <Trash2 size={16} />
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                            <p className="text-sm text-muted-foreground">{contact.role || "N/A"}</p>
                                                            <p className="text-sm text-muted-foreground">
                                                                {contact.contactType
                                                                    ? `${contact.contactType.charAt(0).toUpperCase() + contact.contactType.slice(1)} Contact`
                                                                    : "Contact"}
                                                            </p>
                                                            <div className="flex items-center gap-4 mt-2">
                                                                <div className="flex items-center gap-1 text-sm">
                                                                    <Mail size={14} className="text-muted-foreground" />
                                                                    <a href={`mailto:${contact.email}`} className="text-primary hover:underline">
                                                                        {contact.email}
                                                                    </a>
                                                                </div>
                                                                <div className="flex items-center gap-1 text-sm">
                                                                    <Phone size={14} className="text-muted-foreground" />
                                                                    {contact.phone}
                                                                </div>
                                                            </div>
                                                            {contact.notes && (
                                                                <p className="text-sm text-muted-foreground mt-2 italic">Note: {contact.notes}</p>
                                                            )}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg bg-muted/50">
                                                <Users size={48} className="text-muted-foreground mb-4" />
                                                <h4 className="text-lg font-medium mb-2">No Contacts Added</h4>
                                                <p className="text-muted-foreground mb-4">This company doesn't have any contacts added yet.</p>
                                                <Button size="sm" className="gap-2" onClick={handleAddContact}>
                                                    <Plus size={14} />
                                                    Add First Contact
                                                </Button>
                                            </div>
                                        )}
                                    </div>
                                </TabsContent>

                                <TabsContent value="products" className="p-6 pt-4">
                                    <div className="flex items-center justify-between mb-4">
                                        <h3 className="text-lg font-semibold">Assigned Products</h3>
                                        {/*<Link href={`${companyData?._id}/cip/create`} passHref>*/}
                                        {/*    <Button size="sm" className="gap-2">*/}
                                        {/*        <Plus size={14} />*/}
                                        {/*        Add Product*/}
                                        {/*    </Button>*/}
                                        {/*</Link>*/}
                                    </div>

                                    {cip && cip.length > 0 ? (
                                        <div className="space-y-4">
                                            {cip.map((program) => (
                                                <div
                                                    key={program._id}
                                                    className="p-4 rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer"
                                                >
                                                    <div className="flex items-center justify-between">
                                                        <div className="space-y-1">
                                                            <div className="flex items-center gap-2">
                                                                <div className="p-1.5 bg-primary/10 rounded text-primary">
                                                                    <Package size={16} />
                                                                </div>
                                                                <h4 className="font-medium">
                                                                    {program.productVersionName?.[0]?.version_name || "Unnamed Product"}
                                                                </h4>
                                                            </div>
                                                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                                                <div className="flex items-center gap-1">
                                                                    <FileText size={14} />
                                                                    Code: {program.productVersionName?.[0]?.version_code || "N/A"}
                                                                </div>
                                                                <div className="flex items-center gap-1">
                                                                    <Calendar size={14} />
                                                                    Created: {formatDate(program.createdAt)}
                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg bg-muted/50">
                                            <Package size={48} className="text-muted-foreground mb-4" />
                                            <h4 className="text-lg font-medium mb-2">No Products Assigned</h4>
                                            <p className="text-muted-foreground mb-4">This company doesn't have any products assigned yet.</p>
                                            {/*<Link href={`${companyData?._id}/cip/create`} passHref>*/}
                                            {/*    <Button size="sm" className="gap-2">*/}
                                            {/*        <Plus size={14} />*/}
                                            {/*        Add First Product*/}
                                            {/*    </Button>*/}
                                            {/*</Link>*/}
                                        </div>
                                    )}
                                </TabsContent>

                                <TabsContent value="documents" className="p-6 pt-4">
                                    <div className="space-y-6">
                                        <div>
                                            <h3 className="text-lg font-semibold mb-4">Application Documents</h3>
                                            <div className="grid gap-3 md:grid-cols-2">
                                                <DocumentItem name="Articles of Incorporation" type="PDF" size="2.1MB" />
                                                <DocumentItem name="Company W-9" type="PDF" size="1.8MB" />
                                                <DocumentItem name="EIN Letter" type="PDF" size="0.9MB" />
                                                <DocumentItem name="Business License" type="PDF" size="1.2MB" />
                                            </div>
                                        </div>

                                        <div>
                                            <div className="flex items-center justify-between mb-4">
                                                <h3 className="text-lg font-semibold">Additional Documents</h3>
                                                <Button variant="outline" size="sm" className="gap-2">
                                                    <Plus size={14} />
                                                    Upload Document
                                                </Button>
                                            </div>
                                            <div className="grid gap-3 md:grid-cols-2">
                                                {documents.map((doc, index) => (
                                                    <DocumentItem key={index} name={doc.name} type={doc.type} size={doc.size} />
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="questionnaire" className="p-6 pt-4">
                                    <div className="space-y-6">
                                        <div className="p-4 rounded-lg border">
                                            <h4 className="font-medium mb-3">What is your business type?</h4>
                                            <div className="space-y-2 pl-4">
                                                <div>
                                                    <p className="text-muted-foreground">Manufacturing</p>
                                                    <p className="text-sm text-muted-foreground">10110 - Processing and preserving of meat</p>
                                                </div>
                                                <div>
                                                    <p className="text-muted-foreground">Manufacturing</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        10120 - Processing and preserving of poultry meat
                                                    </p>
                                                </div>
                                                <div>
                                                    <p className="text-muted-foreground">Manufacturing</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        10130 - Production of meat and poultry meat products
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="p-4 rounded-lg border">
                                            <h4 className="font-medium mb-3">Please describe the products and/or services you offer:</h4>
                                            <p className="text-muted-foreground pl-4">
                                                Answer that was submitted goes here and can break unto multiple lines if it goes really far and
                                                the application typed out a lot.
                                            </p>
                                        </div>

                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">What is your company website URL?</h4>
                                                <p className="text-muted-foreground pl-4">https://google.com/shopping/</p>
                                            </div>

                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">Choose the source of funds for your company:</h4>
                                                <p className="text-muted-foreground pl-4">
                                                    Business income, Shareholder funds, Loan, Deposits & Savings
                                                </p>
                                            </div>
                                        </div>

                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">Choose the outbound payment types that apply:</h4>
                                                <p className="text-muted-foreground pl-4">Other: "Text that user inputted"</p>
                                            </div>

                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">
                                                    Expected <span className="italic">inbound</span> monthly volume:
                                                </h4>
                                                <p className="text-muted-foreground pl-4">Less than 100,000.00 EUR</p>
                                            </div>
                                        </div>

                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">
                                                    Expected number of <span className="italic">inbound</span> monthly payments:
                                                </h4>
                                                <p className="text-muted-foreground pl-4">Less than 100</p>
                                            </div>

                                            <div className="p-4 rounded-lg border">
                                                <h4 className="font-medium mb-2">
                                                    Expected countries of <span className="italic">inbound</span> payments:
                                                </h4>
                                                <p className="text-muted-foreground pl-4">United States, Argentina, France</p>
                                            </div>
                                        </div>

                                        <div className="p-4 rounded-lg border">
                                            <h4 className="font-medium mb-2">
                                                Expected <span className="italic">outbound</span> monthly volume:
                                            </h4>
                                            <p className="text-muted-foreground pl-4">100,000 - 500,000 EUR</p>
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="signers" className="p-6 pt-4">
                                    <div className="space-y-6">
                                        <div className="space-y-4">
                                            <h3 className="text-lg font-semibold">Beneficial Owners</h3>

                                            <Collapsible className="rounded-lg border">
                                                <CollapsibleTrigger className="flex items-center justify-between w-full p-4 hover:bg-muted/50 transition-colors">
                                                    <div className="flex items-center gap-3">
                                                        <Avatar className="h-8 w-8">
                                                            <AvatarFallback className="bg-primary/10 text-primary">JS</AvatarFallback>
                                                        </Avatar>
                                                        <div>
                                                            <h4 className="font-medium">John Smitherson</h4>
                                                            <p className="text-sm text-muted-foreground">35% Ownership</p>
                                                        </div>
                                                    </div>
                                                    <ChevronDown className="h-4 w-4 text-muted-foreground" />
                                                </CollapsibleTrigger>
                                                <CollapsibleContent className="p-4 pt-0 border-t">
                                                    <div className="grid gap-4 md:grid-cols-2 pt-4">
                                                        <InfoItem label="Full Name" value="John Smitherson" />
                                                        <InfoItem label="Ownership" value="35%" />
                                                        <InfoItem label="Email" value="<EMAIL>" />
                                                        <InfoItem label="Phone" value="******-123-4567" />
                                                        <InfoItem label="Date of Birth" value="15 Jan 1980" />
                                                        <InfoItem label="Nationality" value="United States" />
                                                    </div>
                                                </CollapsibleContent>
                                            </Collapsible>

                                            <Collapsible className="rounded-lg border">
                                                <CollapsibleTrigger className="flex items-center justify-between w-full p-4 hover:bg-muted/50 transition-colors">
                                                    <div className="flex items-center gap-3">
                                                        <Avatar className="h-8 w-8">
                                                            <AvatarFallback className="bg-primary/10 text-primary">JD</AvatarFallback>
                                                        </Avatar>
                                                        <div>
                                                            <h4 className="font-medium">Jane Doe</h4>
                                                            <p className="text-sm text-muted-foreground">25% Ownership</p>
                                                        </div>
                                                    </div>
                                                    <ChevronDown className="h-4 w-4 text-muted-foreground" />
                                                </CollapsibleTrigger>
                                                <CollapsibleContent className="p-4 pt-0 border-t">
                                                    <div className="grid gap-4 md:grid-cols-2 pt-4">
                                                        <InfoItem label="Full Name" value="Jane Doe" />
                                                        <InfoItem label="Ownership" value="25%" />
                                                        <InfoItem label="Email" value="<EMAIL>" />
                                                        <InfoItem label="Phone" value="******-987-6543" />
                                                        <InfoItem label="Date of Birth" value="22 Mar 1985" />
                                                        <InfoItem label="Nationality" value="United States" />
                                                    </div>
                                                </CollapsibleContent>
                                            </Collapsible>
                                        </div>

                                        <div className="space-y-4">
                                            <h3 className="text-lg font-semibold">Authorized Persons</h3>

                                            <Collapsible className="rounded-lg border">
                                                <CollapsibleTrigger className="flex items-center justify-between w-full p-4 hover:bg-muted/50 transition-colors">
                                                    <div className="flex items-center gap-3">
                                                        <Avatar className="h-8 w-8">
                                                            <AvatarFallback className="bg-primary/10 text-primary">JS</AvatarFallback>
                                                        </Avatar>
                                                        <div>
                                                            <h4 className="font-medium">Joseph Smith</h4>
                                                            <p className="text-sm text-muted-foreground">Authorized Person</p>
                                                        </div>
                                                    </div>
                                                    <ChevronDown className="h-4 w-4 text-muted-foreground" />
                                                </CollapsibleTrigger>
                                                <CollapsibleContent className="p-4 pt-0 border-t">
                                                    <div className="grid gap-4 md:grid-cols-2 pt-4">
                                                        <InfoItem label="Full Name" value="Joseph Smith" />
                                                        <InfoItem label="Role" value="Authorized Person" />
                                                        <InfoItem label="Email" value="<EMAIL>" />
                                                        <InfoItem label="Phone" value="******-456-7890" />
                                                        <InfoItem label="Date of Birth" value="10 Sep 1978" />
                                                        <InfoItem label="Nationality" value="United States" />
                                                    </div>
                                                </CollapsibleContent>
                                            </Collapsible>
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="pmtypes" className="p-6 pt-4">
                                    <div className="space-y-4">
                                        <h3 className="text-lg font-semibold">Programme Manager Types</h3>

                                        {cip && cip.length > 0 ? (
                                            <div className="space-y-3">
                                                {cip.map((program) => (
                                                    <div key={program._id} className="p-4 rounded-lg border">
                                                        <div className="flex items-center gap-3">
                                                            <div className="p-1.5 bg-primary/10 rounded text-primary">
                                                                <Package size={16} />
                                                            </div>
                                                            <div>
                                                                <h4 className="font-medium">
                                                                    {program.productVersionName?.[0]?.version_name || "Unnamed Product"}
                                                                </h4>
                                                                <p className="text-sm text-muted-foreground">
                                                                    Manager Type: {program.programManagerType?.manager_type || "N/A"}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg bg-muted/50">
                                                <Package size={48} className="text-muted-foreground mb-4" />
                                                <h4 className="text-lg font-medium mb-2">No Programme Manager Types</h4>
                                                <p className="text-muted-foreground mb-4">
                                                    This company doesn't have any programme manager types assigned yet.
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Activity</CardTitle>
                            <CardDescription>View company activity and risk evaluation</CardDescription>
                        </CardHeader>
                        <CardContent className="p-0">
                            <Tabs defaultValue="risk">
                                <div className="px-6">
                                    <TabsList className="w-full justify-start h-auto p-0 bg-transparent border-b rounded-none">
                                        <TabsTrigger
                                            value="risk"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Risk Evaluation
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="sumsub"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            SumSub
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="notes"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Internal Notes
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="documents"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Underwriting
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="logs"
                                            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                        >
                                            Activity Log
                                        </TabsTrigger>
                                    </TabsList>
                                </div>

                                <TabsContent value="risk" className="p-6">
                                    <div className="flex items-center gap-6">
                                        <div className="p-4 rounded-full bg-yellow-100 text-yellow-700 text-xl font-bold">225</div>
                                        <div>
                                            <p className="text-sm text-muted-foreground mb-1">Risk Status</p>
                                            <StatusBadge status="Medium" />
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="sumsub" className="p-6">
                                    <div className="space-y-6">
                                        <div>
                                            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                                                <div className="w-4 h-4 rounded-full border" />
                                                Application review
                                            </h3>
                                            <div className="space-y-4 pl-6">
                                                <div className="flex items-start gap-x-6">
                                                    <div className="w-32 text-muted-foreground">Application ID:</div>
                                                    <div className="flex items-center gap-2">
                                                        x
                                                        <Button variant="ghost" size="icon" className="h-6 w-6">
                                                            <Copy className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </div>
                                                <div className="flex items-start gap-x-6">
                                                    <div className="w-32 text-muted-foreground">Review status:</div>
                                                    <div>
                                                        [
                                                        {statuses.map((status, i) => (
                                                            <span key={status}>
                                "{status}"{i < statuses.length - 1 ? " / " : ""}
                              </span>
                                                        ))}
                                                        ]
                                                    </div>
                                                </div>
                                                <div className="flex items-start gap-x-6">
                                                    <div className="w-32 text-muted-foreground">Review answer:</div>
                                                    <div>--</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="space-y-3">
                                            {reviewStatuses.map((status, index) => (
                                                <Collapsible key={index} className="border rounded-lg overflow-hidden">
                                                    <CollapsibleTrigger className="flex w-full items-center justify-between p-4 hover:bg-muted/50 transition-colors">
                                                        <div className="flex items-center gap-2">
                                                            {status.icon}
                                                            <span>
                                {status.label}
                                                                {status.actionRequired && (
                                                                    <span className="text-orange-500 ml-2">(Action required)</span>
                                                                )}
                              </span>
                                                        </div>
                                                        <ChevronDown className="h-4 w-4" />
                                                    </CollapsibleTrigger>
                                                    <CollapsibleContent className="p-4 border-t bg-muted/30">
                                                        <div className="text-muted-foreground">Content for {status.label}</div>
                                                    </CollapsibleContent>
                                                </Collapsible>
                                            ))}
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="notes" className="p-6">
                                    <div className="space-y-4">
                                        <Textarea placeholder="Type your notes here..." className="min-h-[150px]" />
                                        <div className="flex justify-end">
                                            <Button>Save Notes</Button>
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="documents" className="p-6">
                                    <div className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <h3 className="text-lg font-semibold">Underwriting Documents</h3>
                                            <Button variant="outline" size="sm" className="gap-2">
                                                <Plus size={14} />
                                                Upload Document
                                            </Button>
                                        </div>

                                        <div className="space-y-3">
                                            {documents.map((doc, index) => (
                                                <div
                                                    key={index}
                                                    className="flex items-center justify-between p-3 rounded-lg border group hover:bg-muted/50 transition-colors"
                                                >
                                                    <div className="flex items-center gap-3">
                                                        <div className="p-2 bg-primary/10 rounded-md text-primary">
                                                            <FileText size={18} />
                                                        </div>
                                                        <div>
                                                            <p className="font-medium">{doc.name}</p>
                                                            <p className="text-xs text-muted-foreground">
                                                                {doc.type} • {doc.size}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                                                        >
                                                            <Download size={16} />
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity text-red-500 hover:text-red-600 hover:bg-red-50"
                                                        >
                                                            <X size={16} />
                                                        </Button>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>

                                        <div className="text-sm text-muted-foreground mt-2">Max file size: 4MB (PNG, JPG, PDF)</div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="logs" className="p-6">
                                    <div className="space-y-4 max-h-[400px] overflow-y-auto pr-2">
                                        {activities.map((activity, index) => (
                                            <div key={index} className="relative pl-6 pb-6 last:pb-0">
                                                <div className="absolute left-0 top-1.5 w-3 h-3 rounded-full bg-primary"></div>
                                                <div className="absolute left-1.5 top-4 bottom-0 w-[1px] bg-border last:hidden"></div>
                                                <div className="space-y-1">
                                                    <div className="font-medium">{activity.timestamp}</div>
                                                    <div className="text-muted-foreground">{activity.description}</div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </CardContent>
                    </Card>
                </div>

                {/* Sidebar */}
                <div className="space-y-6">
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle>Application Details</CardTitle>
                            <CardDescription>View application status and details</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-muted-foreground">Status</span>
                                    <StatusBadge status={companyData?.status || "Pending"} />
                                </div>

                                <Separator />

                                <div className="space-y-3">
                                    <InfoItem
                                        label="Application ID"
                                        value={
                                            <div className="flex items-center gap-1">
                                                <span className="truncate max-w-[180px]">{companyData?.ryvyl_id}</span>
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    className="h-6 w-6"
                                                    onClick={() => {
                                                        navigator.clipboard.writeText(companyData?.ryvyl_id)
                                                        alertHelper.showToast(

                                                            "ID copied to clipboard",
                                                            "success"
                                                        )
                                                    }}
                                                >
                                                    <Copy size={14} />
                                                </Button>
                                            </div>
                                        }
                                    />

                                    <InfoItem label="Origin" value={companyData?.origin || "N/A"} />

                                    <InfoItem label="Received Date" value={formatDate(companyData?.receivedDate)} />

                                    <InfoItem label="Due Date" value={formatDate(companyData?.dueDate)} />

                                    <InfoItem label="Approved Date" value={formatDate(companyData?.approvedDate)} />

                                    <InfoItem label="Application Approver" value={companyData?.approver || "N/A"} />

                                    <InfoItem label="Assigned To" value={companyData?.assignedTo || "N/A"} />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle>Required Tasks</CardTitle>
                            <CardDescription>Track required task completion</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <CheckCircle2 size={18} className="text-green-500" />
                                        <span>Verification</span>
                                    </div>
                                    <Badge variant="outline" className="bg-green-100 text-green-700 border-green-200">
                                        Complete
                                    </Badge>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <Clock size={18} className="text-yellow-500" />
                                        <span>Document Review</span>
                                    </div>
                                    <Badge variant="outline" className="bg-yellow-100 text-yellow-700 border-yellow-200">
                                        In Progress
                                    </Badge>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <Clock size={18} className="text-gray-400" />
                                        <span>Risk Assessment</span>
                                    </div>
                                    <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-200">
                                        Pending
                                    </Badge>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <Clock size={18} className="text-gray-400" />
                                        <span>Final Approval</span>
                                    </div>
                                    <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-200">
                                        Pending
                                    </Badge>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle>Additional Events</CardTitle>
                            <CardDescription>View and manage additional events</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {events && events.length > 0 ? (
                                    events.map((event, index) => (
                                        <div key={index} className="p-3 rounded-lg border">
                                            <p className="font-medium mb-2">{event.title}</p>
                                            <div className="flex items-center gap-2 text-sm text-primary">
                                                <Paperclip size={14} />
                                                <span>
                          {event.name}.{event.type}
                        </span>
                                                <span className="text-muted-foreground">({event.size})</span>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="text-center p-4 text-muted-foreground">No events found</div>
                                )}

                                <Button variant="outline" size="sm" className="w-full gap-2 mt-2">
                                    <Plus size={14} />
                                    Add Event
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>

            {/* Contact Form Dialog */}
            <ContactForm
                open={contactFormOpen}
                onOpenChange={setContactFormOpen}
                companyId={params.id}
                existingContact={selectedContact}
                onSuccess={refreshContacts}
            />

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the contact from the company.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={confirmDeleteContact} className="bg-red-600 hover:bg-red-700">
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    )
}
