//@ts-nocheck

"use client"
import type React from "react"
import { use<PERSON><PERSON>back, useEffect, useState } from "react"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import {
    AlertCircle,
    CreditCard,
    MapPin,
    Truck,
    Building2,
    Phone,
    Mail,
    Plus,
    Check,
    Zap,
    PlaneTakeoff,
    FileText,
    User2Icon,
} from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useRouter } from "next/navigation"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"

import axios from "@/utils/axiosInstance"
import axiosInstance from "@/utils/axiosInstance"
import { country_currency as ListOfCountries, countryNameByCode, formatPrice } from "@/utils/data"
import { alertHelper } from "@/utils/alertHelper"
import type { CardImage } from "@types/types"
import { useAppSelector } from "@/store/hooks"

// Types
interface B2BAddress {
    street: string
    buildingNumber: string
    apartmentNumber?: string
    city: string
    zipCode: string
    country: string
    _id: string
}

interface ParentCompany {
    _id: string
    company_name: string
}

interface B2BClient {
    _id: string
    companyName: string
    clientCode: string
    phoneNumber: string
    authPhoneNumber: string
    email: string
    address: B2BAddress
    nip: string
    regon: string
    embossedName: string
    company: boolean
    customer: boolean
    parentCompany: ParentCompany
    status: string
    createdAt: string
    updatedAt: string
    products?: any[]
}

export interface DeliveryMethod {
    _id: string
    zoneId: string
    zoneName?: string
    country: string
    methodId: string
    standardWeight: number
    bulkWeight: number
    deliveryTime: string
    price: number
    isActive: boolean
    source?: "regular" | "pp"
    weightRates?: { weight: number; rate: number }[]
}

interface ValidationErrors {
    productType?: string
    deliveryAddress?: string
    deliveryType?: string
}

interface Country {
    _id: string
    country_code: string
    country_name: string
    status: string
    is_active: boolean
}

const DEFAULT_COMPANY_ID = "6785126247b8a6a67fbf7cad"
const asset = "https://stagingapi.ryvyl.eu"

export default function CreateB2BPhysicalCard({ params }: { params: { id: string } }) {
    const router = useRouter()
    const authUser = useAppSelector((state) => state.user.user)

    // Loading states
    const [isLoading, setIsLoading] = useState(true)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [isAddingAddress, setIsAddingAddress] = useState(false)
    const [cardImagesLoading, setCardImagesLoading] = useState(false)

    // Data states
    const [b2bClient, setB2BClient] = useState<B2BClient | null>(null)
    const [onboarding, setOnboarding] = useState<any>(null)
    const [address, setAddress] = useState<any>(null)
    const [cip, setCip] = useState<any[]>([])
    const [savedCountries, setSavedCountries] = useState<Country[]>([])
    const [combinedDeliveryMethods, setCombinedDeliveryMethods] = useState<DeliveryMethod[]>([])
    const [balance, setBalance] = useState<number | null>(null)

    // Card image states
    const [cardImages, setCardImages] = useState<CardImage[]>([])
    const [currentCardImage, setCurrentCardImage] = useState<CardImage | null>(null)

    // Form states
    const [formData, setFormData] = useState({
        productType: "",
        embossName1: "",
        embossName2: "",
        nickname: "",
        deliveryAddress: "",
        deliveryType: "",
    })

    const [selectedAddress, setSelectedAddress] = useState("")
    const [newAddress, setNewAddress] = useState({
        building: "",
        street: "",
        apartmentNo: "",
        city: "",
        state: "",
        postalCode: "",
        country: "",
    })

    // UI states
    const [touched, setTouched] = useState({
        productType: false,
        deliveryAddress: false,
        deliveryType: false,
    })
    const [errors, setErrors] = useState<ValidationErrors>({})
    const [formSubmitted, setFormSubmitted] = useState(false)
    const [isDialogOpen, setIsDialogOpen] = useState(false)
    const [showConfirmation, setShowConfirmation] = useState(false)

    // Fetch card images
    const fetchCardImages = useCallback(async () => {
        try {
            setCardImagesLoading(true)
            const response = await axios.get<CardImage[]>("/images")
            setCardImages(response.data)
        } catch (error) {
            console.error("Error fetching card images:", error)
        } finally {
            setCardImagesLoading(false)
        }
    }, [])

    // Process card image based on selected product type
    const processCardImage = useCallback(
        (productCode: string, companyId?: string) => {
            if (!cardImages.length || !productCode) {
                setCurrentCardImage(null)
                return
            }

            const matchingImages = cardImages.filter((img) => img.product_version.version_code === productCode)
            if (matchingImages.length === 0) {
                setCurrentCardImage(null)
                return
            }

            let selectedImage = matchingImages.find((img) => img.company._id === companyId)
            if (!selectedImage) {
                selectedImage = matchingImages.find((img) => img.company._id === DEFAULT_COMPANY_ID)
            }
            if (!selectedImage) {
                selectedImage = matchingImages[0]
            }

            setCurrentCardImage(selectedImage)
        },
        [cardImages],
    )

    // Get card image URL
    const getCardImageUrl = useCallback(() => {
        if (currentCardImage) {
            return `${asset}${currentCardImage.front_side}`
        }
        return "/placeholder.svg?height=380&width=240"
    }, [currentCardImage])

    // Process delivery methods
    const processCombinedDeliveryMethods = (dhlMethods: any[], ppMethods: any[]) => {
        const dhlFormattedMethods = dhlMethods.map((method) => ({
            _id: `dhl-${method._id}`,
            zoneId: method.DHL_ZONE_NAME,
            zoneName: method.DHL_ZONE_NAME,
            country: method.Country,
            methodId: "DHL Express",
            standardWeight: 50,
            bulkWeight: 0,
            deliveryTime: method["DHL_EXPRESS  DELIVERY TIMES"],
            price: method["DHL_EXPRESS 100g"],
            isActive: true,
            source: "regular" as const,
            weightRates: [
                { weight: 50, rate: method["DHL_EXPRESS 50g"] },
                { weight: 100, rate: method["DHL_EXPRESS 100g"] },
            ],
        }))

        if (dhlMethods.length > 0 && dhlMethods[0]["DHL_Air 100g"]) {
            dhlFormattedMethods.push({
                _id: `dhl-air-${dhlMethods[0]._id}`,
                zoneId: dhlMethods[0].DHL_ZONE_NAME,
                zoneName: dhlMethods[0].DHL_ZONE_NAME,
                country: dhlMethods[0].Country,
                methodId: "DHL Air",
                standardWeight: 50,
                bulkWeight: 0,
                deliveryTime: dhlMethods[0]["DHL_AIR  DELIVERY TIMES"],
                price: dhlMethods[0]["DHL_Air 100g"],
                isActive: true,
                source: "regular" as const,
                weightRates: [
                    { weight: 50, rate: dhlMethods[0]["DHL_Air 50g"] },
                    { weight: 100, rate: dhlMethods[0]["DHL_Air 100g"] },
                ],
            })
        }

        const ppMethodsFormatted = ppMethods.map((method) => ({
            _id: `pp-${method._id}`,
            zoneId: method.Poczta_Post_Category,
            zoneName: method.Poczta_Post_Category,
            country: method.Country,
            methodId: "Polish Post",
            standardWeight: 50,
            bulkWeight: 0,
            deliveryTime: method.PPDeliveryTime,
            price: method["Poczta_Post 100g"],
            isActive: true,
            source: "pp" as const,
            weightRates: [
                { weight: 50, rate: method["Poczta_Post 50g"] },
                { weight: 100, rate: method["Poczta_Post 100g"] },
                { weight: 350, rate: method["Poczta_Post 350g"] },
                { weight: 500, rate: method["Poczta_Post 500g"] },
            ],
        }))

        const combined = [...ppMethodsFormatted, ...dhlFormattedMethods]
        setCombinedDeliveryMethods(combined)
    }

    // Get available products
    const availableProducts = cip.flatMap(
        (program) =>
            program.productVersionName?.filter(
                (r: any) => r.status === "active" && r.version_name.toLowerCase().includes("phy"),
            ) || [],
    )

    const isBalanceSufficient = () => {
        if (balance === null || !formData.deliveryType) return false
        const selectedMethod = combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)
        return selectedMethod ? balance >= selectedMethod.price : false
    }

    const getSelectedDeliveryPrice = () => {
        if (!formData.deliveryType) return 0
        const selectedMethod = combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)
        return selectedMethod?.price || 0
    }

    // Validation
    const validateForm = useCallback(() => {
        const newErrors: ValidationErrors = {}

        if (!formData.productType) {
            newErrors.productType = "Product type is required"
        }

        if (!formData.deliveryType) {
            newErrors.deliveryType = "Delivery type is required"
        } else {
            const selectedMethod = combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)
            if (selectedMethod && balance !== null) {
                if (balance < selectedMethod.price) {
                    newErrors.deliveryType = `Insufficient balance. Required: ${formatPrice(selectedMethod.price)}, Available: ${formatPrice(balance)}`
                }
            }
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }, [formData, balance, combinedDeliveryMethods])

    const handleBlur = (field: keyof typeof touched) => {
        setTouched((prev) => ({ ...prev, [field]: true }))
    }

    // Fetch initial data
    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true)
            try {
                const response = await axiosInstance.get(`onboarding/personal/${params.id}`)
                setOnboarding(response.data.data)
                setAddress(response.data.data.address)
                setBalance(response.data.balance)
                setSelectedAddress("default")
                setFormData((prev) => ({
                    ...prev,
                    deliveryAddress: "default",
                }))
            } catch (error) {
                console.error("Error fetching data:", error)
                alertHelper.showToast("Failed to load data", "error")
            } finally {
                setIsLoading(false)
            }
        }
        fetchData()
    }, [params.id])

    // Fetch countries
    useEffect(() => {
        const fetchCountries = async () => {
            try {
                const response = await axiosInstance.get("companies/country")
                setSavedCountries(response.data)
            } catch (error) {
                console.error("Error fetching countries", error)
                alertHelper.showToast("Failed to load countries data", "error")
            }
        }
        fetchCountries()
    }, [])

    // Fetch B2B data
    useEffect(() => {
        const fetchB2BData = async () => {
            if (!authUser.recordId) return

            setIsLoading(true)
            try {
                await fetchCardImages()

                const b2bResponse = await axiosInstance.get(`/b2b/${authUser.recordId}`)
                if (b2bResponse.data && b2bResponse.data.data) {
                    const b2bClientData = b2bResponse.data.data
                    setB2BClient(b2bClientData)

                    setFormData((prev) => ({
                        ...prev,
                        embossName1: b2bClientData.embossedName,
                    }))

                    if (b2bClientData.parentCompany?._id) {
                        await fetchParentCompanyData(b2bClientData.parentCompany._id)
                    }

                    const countryName = b2bClientData?.address?.country || "ALBANIA"
                    await fetchCountryDeliveryMethods(countryName)
                }
            } catch (error) {
                console.error("Error fetching B2B data:", error)
                alertHelper.showToast("Failed to load B2B client data", "error")
            } finally {
                setIsLoading(false)
            }
        }

        fetchB2BData()
    }, [authUser.recordId, fetchCardImages])

    const fetchParentCompanyData = async (companyId: string) => {
        try {
            const response = await axiosInstance.get(`/company/${companyId}`)
            setCip(response.data.cip || [])
        } catch (error) {
            console.error("Failed to fetch parent company data", error)
            alertHelper.showToast("Failed to load parent company data", "error")
        }
    }

    const fetchCountryDeliveryMethods = async (countryName: string) => {
        try {
            const response = await fetch(`/api/get-country-delivery-methods?country=${countryName}`)
            const data = await response.json()

            if (data.success) {
                const ppMethods = data.polishPost?.records || []
                const dhlMethods = data.dhl?.records || []
                processCombinedDeliveryMethods(dhlMethods, ppMethods)
            } else {
                alertHelper.showToast(`Failed to load delivery methods for ${countryName}`, "error")
            }
        } catch (error) {
            console.error("Error fetching country delivery methods:", error)
            alertHelper.showToast("Failed to load delivery methods", "error")
        }
    }

    // Auto-select single options
    useEffect(() => {
        if (availableProducts.length === 1 && !formData.productType) {
            setFormData((prev) => ({
                ...prev,
                productType: availableProducts[0].version_code,
            }))
            setErrors((prev) => ({ ...prev, productType: undefined }))
        }
    }, [availableProducts, formData.productType])

    useEffect(() => {
        if (
            combinedDeliveryMethods.length === 1 &&
            (!formData.deliveryType || formData.deliveryType !== combinedDeliveryMethods[0]._id)
        ) {
            setFormData((prev) => ({ ...prev, deliveryType: combinedDeliveryMethods[0]._id }))
            setErrors((prev) => ({ ...prev, deliveryType: undefined }))
        }
    }, [combinedDeliveryMethods, formData.deliveryType])

    // Update card image when product type changes
    useEffect(() => {
        if (formData.productType && cardImages.length > 0) {
            processCardImage(formData.productType, b2bClient?.parentCompany?._id)
        }
    }, [formData.productType, cardImages, b2bClient?.parentCompany?._id, processCardImage])

    // Validate form when values change
    useEffect(() => {
        if (formSubmitted) {
            validateForm()
        }
    }, [formData, selectedAddress, formSubmitted, validateForm])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFormData({ ...formData, [e.target.name]: e.target.value })
    }

    const handleNewAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setNewAddress({ ...newAddress, [e.target.name]: e.target.value })
    }

    const handleCountryChange = (value: string) => {
        const country = ListOfCountries.find((c) => c.iso3.toLowerCase() === value.toLowerCase())?.numericCode
        setNewAddress({ ...newAddress, country: country || "" })
    }

    const handleAddNewAddress = async () => {
        if (
            !newAddress.building ||
            !newAddress.street ||
            !newAddress.city ||
            !newAddress.state ||
            !newAddress.postalCode ||
            !newAddress.country
        ) {
            alertHelper.showToast("Please fill in all required address fields", "error")
            return
        }

        setIsAddingAddress(true)

        const data = {
            onboardingId: params.id,
            building: newAddress.building,
            street: newAddress.street,
            apartment: newAddress.apartmentNo || "",
            city: newAddress.city,
            stateProvince: newAddress.state,
            postalCode: newAddress.postalCode,
            country: newAddress.country,
        }

        try {
            await axiosInstance.post(`b2b/updateAddress`, data)
            alertHelper.showToast("Address added successfully", "success")

            setSelectedAddress("new")
            const fullAddress = `${newAddress.building}, ${newAddress.street}, ${newAddress.apartmentNo || ""}, ${newAddress.city}, ${newAddress.state}, ${newAddress.postalCode}, ${countryNameByCode(newAddress.country)}`
            setFormData((prev) => ({
                ...prev,
                deliveryAddress: fullAddress,
            }))

            setIsDialogOpen(false)
            setErrors((prev) => ({ ...prev, deliveryAddress: undefined }))
        } catch (error) {
            console.error("Failed to add address:", error)
            alertHelper.showToast("An error occurred. Please try again.", "error")
        } finally {
            setIsAddingAddress(false)
        }
    }

    const handleSubmitStep1 = async (e: React.FormEvent) => {
        e.preventDefault()
        setFormSubmitted(true)

        setTouched({
            productType: true,
            deliveryAddress: true,
            deliveryType: true,
        })

        const isValid = validateForm()

        if (!isValid) {
            const firstError = document.querySelector(".error-message")
            if (firstError) {
                firstError.scrollIntoView({ behavior: "smooth", block: "center" })
            }
            return
        }

        setShowConfirmation(true)
    }

    const handleConfirmOrder = async () => {
        setIsSubmitting(true)

        try {
            const selectedMethod = combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)
            const isPPMethod = selectedMethod?.source === "pp"

            const data = {
                b2bClientId: b2bClient?._id,
                userId: params.id,
                companyName: b2bClient?.companyName,
                clientCode: b2bClient?.clientCode,
                productCode: formData.productType,
                embossName1: formData.embossName1 || b2bClient?.embossedName,
                embossName2: formData.embossName2 || "",
                nickname: formData.nickname || "",
                currencyCode: 978,
                phoneNumber: b2bClient?.authPhoneNumber,
                nip: b2bClient?.nip,
                regon: b2bClient?.regon,
                parentCompanyId: b2bClient?.parentCompany?._id,
                deliveryMethod: {
                    id: formData.deliveryType,
                    source: isPPMethod ? "latter" : "courier",
                    methodId: selectedMethod?.methodId,
                    price: Number.parseFloat(selectedMethod?.price.toFixed(2) || "0"),
                },
            }

            await axiosInstance.post(`b2b/createCard/physical`, data)
            alertHelper.showToast("Congratulations, your B2B card is now created.", "success")
            router.back()
        } catch (error) {
            console.error("Error submitting form:", error)
            alertHelper.showToast("An error occurred. Please try again.", "error")
        } finally {
            setIsSubmitting(false)
            setShowConfirmation(false)
        }
    }

    const handleDeliveryMethodChange = (value: string) => {
        setFormData((prev) => ({ ...prev, deliveryType: value }))

        const selectedMethod = combinedDeliveryMethods.find((m) => m._id === value)
        if (selectedMethod && balance !== null) {
            if (balance < selectedMethod.price) {
                setErrors((prev) => ({
                    ...prev,
                    deliveryType: `Insufficient balance. Required: ${formatPrice(selectedMethod.price)}, Available: ${formatPrice(balance)}`,
                }))
            } else {
                setErrors((prev) => ({ ...prev, deliveryType: undefined }))
            }
        } else {
            setErrors((prev) => ({ ...prev, deliveryType: undefined }))
        }

        handleBlur("deliveryType")
    }

    const getDeliveryMethodStyling = (method: DeliveryMethod, isSelected: boolean) => {
        const isDHLExpress = method.methodId === "DHL Express"

        if (isDHLExpress) {
            return {
                containerClass: isSelected ? "border-primary bg-primary/5" : "border-border hover:border-primary/50",
                iconClass: "text-orange-600",
                priceClass: "text-orange-600 font-bold",
                badgeClass: "bg-orange-100 text-orange-800 border-orange-300",
            }
        }

        return {
            containerClass: isSelected ? "border-primary bg-primary/5" : "border-border hover:border-primary/50",
            iconClass: "text-muted-foreground",
            priceClass: "text-primary",
            badgeClass: "bg-secondary text-secondary-foreground",
        }
    }

    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
        )
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-background to-muted/30 p-4 md:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
          <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2">Create B2B Physical Card</h1>
          <p className="text-muted-foreground text-lg">Order your new business physical card with secure delivery</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Information Sidebar */}
            <div className="lg:col-span-1">
            <div className="space-y-6">
              {/* Company Information */}
                <Card className="shadow-lg border-0 bg-card/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-foreground">
                    <Building2 className="h-5 w-5 text-primary" />
                    Company Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {b2bClient && (
                      <>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-start gap-3">
                          <Building2 className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">{b2bClient.companyName}</p>
                            <p className="text-muted-foreground">Company Name</p>
                          </div>
                        </div>

                        <div className="flex items-start gap-3">
                          <FileText className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">{b2bClient.clientCode}</p>
                            <p className="text-muted-foreground">Client Code</p>
                          </div>
                        </div>

                        <div className="flex items-start gap-3">
                          <Mail className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">{b2bClient.email}</p>
                            <p className="text-muted-foreground">Company Email</p>
                          </div>
                        </div>

                        <div className="flex items-start gap-3">
                          <Phone className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">{b2bClient.phoneNumber}</p>
                            <p className="text-muted-foreground">Company Phone</p>
                          </div>
                        </div>

                        <div className="flex items-start gap-3">
                          <FileText className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">{b2bClient.nip}</p>
                            <p className="text-muted-foreground">NIP</p>
                          </div>
                        </div>

                        <div className="flex items-start gap-3">
                          <FileText className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">{b2bClient.regon}</p>
                            <p className="text-muted-foreground">REGON</p>
                          </div>
                        </div>
                      </div>

                          {/* Parent Company */}
                          {b2bClient.parentCompany && (
                              <>
                          <Separator />
                          <div className="space-y-3">
                            <h3 className="font-semibold text-foreground border-b pb-2">Parent Company</h3>
                            <div className="flex items-start gap-3 text-sm">
                              <Building2 className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                              <div>
                                <p className="font-medium text-foreground">{b2bClient.parentCompany.company_name}</p>
                                <p className="text-muted-foreground">Parent Company Name</p>
                              </div>
                            </div>
                          </div>
                        </>
                          )}
                    </>
                  )}
                </CardContent>
              </Card>

                {/* Cardholder Information */}
                <Card className="shadow-lg border-0 bg-card/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-foreground">
                    <User2Icon className="h-5 w-5 text-primary" />
                    Cardholder Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {onboarding && b2bClient && (
                      <>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-start gap-3">
                          <User2Icon className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">
                              {onboarding?.personalInfo?.firstName} {onboarding?.personalInfo?.lastName}
                            </p>
                            <p className="text-muted-foreground">Cardholder Name</p>
                          </div>
                        </div>

                        <div className="flex items-start gap-3">
                          <Phone className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-primary">{b2bClient.authPhoneNumber}</p>
                            <p className="text-muted-foreground">3DS Authorised Mobile</p>
                          </div>
                        </div>

                        <div className="flex items-start gap-3">
                          <CreditCard className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground">{b2bClient.embossedName}</p>
                            <p className="text-muted-foreground">Default Embossed Name</p>
                          </div>
                        </div>
                      </div>

                      <Separator />

                          {/* Address */}
                          <div className="space-y-3">
                        <h3 className="font-semibold text-foreground border-b pb-2">Cardholder Address</h3>
                        <div className="flex items-start gap-3 text-sm">
                          <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <address className="not-italic font-medium text-foreground">
                              {address?.buildingNumber} {address?.street}
                                {address?.apartmentNumber ? `, ${address?.apartmentNumber}` : ""}
                                <br />
                                {address?.city} {address?.zipCode}
                                <br />
                                {countryNameByCode(address?.country)}
                            </address>
                            <p className="text-muted-foreground">Registered Address</p>
                          </div>
                        </div>
                      </div>

                      <Separator />

                          {/* Account Balance */}
                          <div className="space-y-3">
                        <h3 className="font-semibold text-foreground border-b pb-2">Account Balance</h3>
                        <div className="flex items-start gap-3 text-sm">
                          <CreditCard className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-foreground text-lg">
                              {balance !== null ? formatPrice(balance) : "N/A"}
                            </p>
                            <p className="text-muted-foreground">Available Balance</p>
                              {formData.deliveryType && (
                                  <div className="mt-2 p-2 rounded-md bg-muted/50">
                                <p className="text-xs text-muted-foreground">
                                  Delivery Cost: {formatPrice(getSelectedDeliveryPrice())}
                                </p>
                                <p
                                    className={`text-xs font-medium ${isBalanceSufficient() ? "text-green-600" : "text-destructive"}`}
                                >
                                  {isBalanceSufficient() ? "✓ Sufficient funds" : "✗ Insufficient funds"}
                                </p>
                              </div>
                              )}
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>

            {/* Main Form */}
            <div className="lg:col-span-2">
            <Card className="shadow-lg border-0 bg-card/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-foreground">
                  <CreditCard className="h-5 w-5 text-primary" />
                  B2B Card Configuration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmitStep1} className="space-y-8">
                  {/* Form validation summary */}
                    {formSubmitted && Object.keys(errors).length > 0 && (
                        <Alert variant="destructive" className="border-destructive/50 bg-destructive/10">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>Please correct the errors below before continuing.</AlertDescription>
                    </Alert>
                    )}

                    {/* Balance warning */}
                    {balance !== null && formData.deliveryType && !isBalanceSufficient() && (
                        <Alert variant="destructive" className="border-destructive/50 bg-destructive/10">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Insufficient balance for selected delivery method. Required:{" "}
                          {formatPrice(getSelectedDeliveryPrice())}, Available: {formatPrice(balance)}
                      </AlertDescription>
                    </Alert>
                    )}

                    {/* Product Type */}
                    <div className="space-y-3">
                    <Label className="text-base font-semibold text-foreground">
                      Product Type <span className="text-destructive">*</span>
                    </Label>
                    <Select
                        value={formData.productType}
                        onValueChange={(value) => {
                            setFormData({ ...formData, productType: value })
                            setErrors((prev) => ({ ...prev, productType: undefined }))
                        }}
                        onOpenChange={() => handleBlur("productType")}
                        disabled={availableProducts.length === 1}
                    >
                      <SelectTrigger
                          className={`h-12 ${touched.productType && errors.productType ? "border-destructive" : "border-input"}`}
                      >
                        <SelectValue placeholder="Select Product Type" />
                      </SelectTrigger>
                      <SelectContent>
                        {b2bClient?.products
                            ?.filter((r) => r.version_name.toLowerCase().includes("phy"))
                            .map((product) => (
                                <SelectItem key={product._id} value={product.version_code}>
                              <div className="flex items-center gap-2">
                                <CreditCard className="h-4 w-4" />
                                  {product.version_name} ({product.version_code})
                              </div>
                            </SelectItem>
                            ))}
                      </SelectContent>
                    </Select>
                        {touched.productType && errors.productType && (
                            <p className="text-destructive text-sm flex items-center gap-1 error-message">
                        <AlertCircle className="h-3 w-3" />
                                {errors.productType}
                      </p>
                        )}
                  </div>

                    {/* Emboss Names */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <Label className="text-base font-semibold text-foreground">Emboss Name 1</Label>
                      <Input
                          name="embossName1"
                          placeholder="Enter emboss name 1"
                          value={formData.embossName1}
                          onChange={handleChange}
                          className="h-12 border-input"
                      />
                      <p className="text-xs text-muted-foreground">
                        Default: {b2bClient?.embossedName || "Company name"}
                      </p>
                    </div>
                    <div className="space-y-3">
                      <Label className="text-base font-semibold text-foreground">Emboss Name 2</Label>
                      <Input
                          name="embossName2"
                          placeholder="Enter emboss name 2 (optional)"
                          value={formData.embossName2}
                          onChange={handleChange}
                          className="h-12 border-input"
                      />
                      <p className="text-xs text-muted-foreground">Optional second line</p>
                    </div>
                  </div>

                    {/* Delivery Address */}
                    <div className="space-y-4">
                    <Label className="text-base font-semibold text-foreground">
                      Delivery Address <span className="text-destructive">*</span>
                    </Label>

                    <RadioGroup
                        value={selectedAddress}
                        onValueChange={(value) => {
                            setSelectedAddress(value)
                            if (value === "default" && address) {
                                setFormData((prev) => ({
                                    ...prev,
                                    deliveryAddress: `${address.street}, ${address.buildingNumber}, ${address.city}, ${address.zipCode}, ${countryNameByCode(address.country)}`,
                                }))
                            }
                            setErrors((prev) => ({ ...prev, deliveryAddress: undefined }))
                            handleBlur("deliveryAddress")
                        }}
                        className="space-y-3"
                    >
                      {/* Default Address */}
                        {address && (
                            <div
                                className={`flex items-start space-x-3 p-4 border-2 rounded-xl transition-all cursor-pointer ${
                                    selectedAddress === "default"
                                        ? "border-primary bg-primary/5"
                                        : "border-border hover:border-primary/50"
                                }`}
                            >
                          <RadioGroupItem value="default" id="default-address" className="mt-1" />
                          <div className="flex-1">
                            <Label htmlFor="default-address" className="cursor-pointer">
                              <div className="flex items-center gap-2 mb-1">
                                <MapPin className="h-4 w-4 text-muted-foreground" />
                                <span className="font-medium">Registered Address</span>
                                <Badge variant="secondary" className="text-xs">
                                  Default
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">
                                {address.buildingNumber} {address.street}{" "}
                                  {address.apartmentNumber && `${address.apartmentNumber}, `}
                                  {address.city}, {address.zipCode}, {countryNameByCode(address.country)}
                              </p>
                            </Label>
                          </div>
                        </div>
                        )}

                        {/* Add New Address */}
                        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                        <DialogTrigger asChild>
                          <div className="flex items-center space-x-3 p-4 border-2 border-dashed border-border rounded-xl cursor-pointer hover:border-primary hover:bg-primary/5 transition-all">
                            <Plus className="h-5 w-5 text-muted-foreground" />
                            <span className="font-medium text-foreground">Add Different Delivery Address</span>
                          </div>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-md">
                          <DialogHeader>
                            <DialogTitle>Add New Delivery Address</DialogTitle>
                          </DialogHeader>
                          <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="building">Building *</Label>
                                <Input
                                    id="building"
                                    name="building"
                                    placeholder="Building number"
                                    onChange={handleNewAddressChange}
                                />
                              </div>
                              <div>
                                <Label htmlFor="street">Street *</Label>
                                <Input
                                    id="street"
                                    name="street"
                                    placeholder="Street name"
                                    onChange={handleNewAddressChange}
                                />
                              </div>
                            </div>
                            <div>
                              <Label htmlFor="apartmentNo">Apartment No</Label>
                              <Input
                                  id="apartmentNo"
                                  name="apartmentNo"
                                  placeholder="Apartment number (optional)"
                                  onChange={handleNewAddressChange}
                              />
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="city">City *</Label>
                                <Input id="city" name="city" placeholder="City" onChange={handleNewAddressChange} />
                              </div>
                              <div>
                                <Label htmlFor="state">State *</Label>
                                <Input id="state" name="state" placeholder="State" onChange={handleNewAddressChange} />
                              </div>
                            </div>
                            <div>
                              <Label htmlFor="postalCode">Postal Code *</Label>
                              <Input
                                  id="postalCode"
                                  name="postalCode"
                                  placeholder="Postal code"
                                  onChange={handleNewAddressChange}
                              />
                            </div>
                            <div>
                              <Label htmlFor="country">Country *</Label>
                              <Select onValueChange={(v) => handleCountryChange(v)}>
                                <SelectTrigger id="country">
                                  <SelectValue placeholder="Select Country" />
                                </SelectTrigger>
                                <SelectContent>
                                  {savedCountries
                                      .filter((c) => c.status === "active" && c.is_active)
                                      .sort((a, b) => a.country_name.localeCompare(b.country_name))
                                      .map((country) => (
                                          <SelectItem key={`x-${country._id}`} value={country.country_code}>
                                        {country.country_name} ({country.country_code})
                                      </SelectItem>
                                      ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                          <div className="flex justify-end gap-2">
                            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                              Cancel
                            </Button>
                            <Button onClick={handleAddNewAddress} disabled={isAddingAddress}>
                              {isAddingAddress ? "Saving..." : "Save Address"}
                            </Button>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </RadioGroup>

                        {touched.deliveryAddress && errors.deliveryAddress && (
                            <p className="text-destructive text-sm flex items-center gap-1 error-message">
                        <AlertCircle className="h-3 w-3" />
                                {errors.deliveryAddress}
                      </p>
                        )}
                  </div>

                    {/* Delivery Method */}
                    <div className="space-y-4">
                    <Label className="text-base font-semibold text-foreground">
                      Delivery Method <span className="text-destructive">*</span>
                    </Label>

                    <div className="max-h-[400px] overflow-y-auto">
                      <RadioGroup
                          value={formData.deliveryType}
                          onValueChange={handleDeliveryMethodChange}
                          className="space-y-3"
                      >
                        {combinedDeliveryMethods.length > 0 ? (
                            combinedDeliveryMethods.map((method) => {
                                const isSelected = formData.deliveryType === method._id
                                const styling = getDeliveryMethodStyling(method, isSelected)
                                const isDHLExpress = method.methodId.toLowerCase().includes("express")
                                const isDHLAir = method.methodId.toLowerCase().includes("air")
                                const hasInsufficientBalance = balance !== null && balance < method.price
                                const isDisabled = hasInsufficientBalance

                                return (
                                    <Label
                                        key={method._id}
                                        htmlFor={`delivery-${method._id}`}
                                        className={`flex items-start space-x-3 p-4 border-2 rounded-xl transition-all cursor-pointer ${
                                            isDisabled
                                                ? "border-primary/30 opacity-60 cursor-not-allowed"
                                                : styling.containerClass
                                        }`}
                                    >
                                <RadioGroupItem
                                    value={method._id}
                                    id={`delivery-${method._id}`}
                                    className="mt-1"
                                    disabled={isDisabled}
                                />
                                <div className="flex-1">
                                  <div className="flex justify-between items-start mb-2">
                                    <div className="flex items-center gap-2">
                                      {isDHLExpress ? (
                                          <Zap
                                              className={`h-4 w-4 ${isDisabled ? "text-muted-foreground" : styling.iconClass}`}
                                          />
                                      ) : isDHLAir ? (
                                          <PlaneTakeoff
                                              className={`h-4 w-4 ${isDisabled ? "text-muted-foreground" : styling.iconClass}`}
                                          />
                                      ) : (
                                          <Truck
                                              className={`h-4 w-4 ${isDisabled ? "text-muted-foreground" : styling.iconClass}`}
                                          />
                                      )}
                                        <span
                                            className={`font-semibold cursor-pointer ${isDisabled ? "text-muted-foreground" : ""}`}
                                        >
                                        {method.methodId}
                                      </span>
                                        {isDHLExpress && !isDisabled && (
                                            <Badge className={styling.badgeClass}>
                                          <Zap className="h-3 w-3 mr-1" />
                                          Express
                                        </Badge>
                                        )}
                                        {method.source === "pp" && (
                                            <Badge variant="outline" className="text-xs">
                                          Standard Polish Post
                                        </Badge>
                                        )}
                                        {hasInsufficientBalance && (
                                            <Badge variant="destructive" className="text-xs">
                                          <AlertCircle className="h-3 w-3 mr-1" />
                                          Insufficient Balance
                                        </Badge>
                                        )}
                                    </div>
                                    <span
                                        className={`text-lg font-bold ${isDisabled ? "text-muted-foreground" : styling.priceClass}`}
                                    >
                                      {formatPrice(method.price)}
                                    </span>
                                  </div>
                                  <p
                                      className={`text-sm mb-2 ${isDisabled ? "text-muted-foreground" : "text-muted-foreground"}`}
                                  >
                                    Estimated delivery: {method.deliveryTime}
                                  </p>
                                    {hasInsufficientBalance && (
                                        <p className="text-xs text-destructive flex items-center gap-1">
                                      <AlertCircle className="h-3 w-3" />
                                      Need {formatPrice(method.price - balance)} more to select this option
                                    </p>
                                    )}
                                </div>
                              </Label>
                                )
                            })
                        ) : (
                            <div className="text-muted-foreground p-4 border-2 border-dashed border-border rounded-xl text-center">
                            No delivery methods available for this location
                          </div>
                        )}
                      </RadioGroup>
                    </div>

                        {touched.deliveryType && errors.deliveryType && (
                            <p className="text-destructive text-sm flex items-center gap-1 error-message">
                        <AlertCircle className="h-3 w-3" />
                                {errors.deliveryType}
                      </p>
                        )}
                  </div>

                    {/* Submit Button */}
                    <div className="pt-6">
                    <Button
                        type="submit"
                        className="w-full h-12 text-lg font-semibold bg-primary hover:bg-primary/90 transition-all duration-200"
                        disabled={isSubmitting || (balance !== null && formData.deliveryType && !isBalanceSufficient())}
                    >
                      {isSubmitting
                          ? "Processing..."
                          : balance !== null && formData.deliveryType && !isBalanceSufficient()
                              ? `Insufficient Balance - Need ${formatPrice(getSelectedDeliveryPrice() - balance)} More`
                              : "Review Order"}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>

          {/* Confirmation Dialog */}
          <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
          <DialogContent className="sm:max-w-5xl p-0 overflow-hidden rounded-xl">
            <div className="grid md:grid-cols-2 h-full">
              {/* Card Image Section */}
                <div className="bg-gradient-to-br from-primary/90 to-primary p-8 flex items-center justify-center">
                <div className="w-full flex flex-col items-center">
                  <div className="aspect-[0.63/1] w-full max-w-[240px] mx-auto rounded-xl overflow-hidden shadow-xl transform rotate-[-5deg] hover:rotate-0 transition-all duration-300 border-4 border-white/30">
                    {cardImagesLoading ? (
                        <Skeleton className="w-full h-full" />
                    ) : (
                        <img
                            src={getCardImageUrl() || "/placeholder.svg"}
                            alt={`${b2bClient?.products?.find((r) => r.version_code === formData.productType)?.version_name || "Card"} preview`}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                                e.currentTarget.src = "/placeholder.svg?height=380&width=240"
                            }}
                        />
                    )}
                  </div>
                  <div className="text-center mt-6 text-primary-foreground">
                    <h3 className="text-xl font-bold">
                      {b2bClient?.products?.find((r) => r.version_code === formData.productType)?.version_name}
                    </h3>
                    <p className="text-sm opacity-90">{b2bClient?.companyName}</p>
                    <p className="text-sm opacity-90">{b2bClient?.email}</p>
                  </div>
                </div>
              </div>

                {/* Order Details Section */}
                <div className="p-8 bg-card">
                <div className="space-y-6 max-w-xl mx-auto">
                  <div>
                    <h2 className="text-2xl font-bold text-foreground">Please Confirm Your B2B Order</h2>
                    <p className="text-muted-foreground text-sm mt-1">
                      Review your business card order details before proceeding
                    </p>
                  </div>

                  <div className="space-y-6">
                    <div className="flex items-center gap-2 pb-3 border-b">
                      <CreditCard className="h-5 w-5 text-primary" />
                      <h3 className="font-semibold text-lg">Card Details</h3>
                    </div>

                    <div className="grid gap-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Company Name:</span>
                        <span className="font-medium">{b2bClient?.companyName}</span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Client Code:</span>
                        <span className="font-medium">{b2bClient?.clientCode}</span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Product Type:</span>
                        <span className="font-medium">
                          {b2bClient?.products?.find((r) => r.version_code === formData.productType)?.version_name}
                            &nbsp; (
                            {b2bClient?.products?.find((r) => r.version_code === formData.productType)?.version_code})
                        </span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Emboss Name 1:</span>
                        <span className="font-medium">{formData.embossName1 || b2bClient?.embossedName}</span>
                      </div>

                        {formData.embossName2 && (
                            <div className="flex justify-between">
                          <span className="text-muted-foreground">Emboss Name 2:</span>
                          <span className="font-medium">{formData.embossName2}</span>
                        </div>
                        )}
                    </div>

                    <div className="flex items-center gap-2 pb-3 border-b pt-4">
                      <MapPin className="h-5 w-5 text-primary" />
                      <h3 className="font-semibold text-lg">Delivery Address</h3>
                    </div>

                    <div className="bg-muted/30 p-4 rounded-lg text-sm border border-border shadow-sm">
                      {address && selectedAddress === "default" ? (
                          <address className="not-italic">
                          {address.buildingNumber} {address.street}
                              {address.apartmentNumber ? `, ${address.apartmentNumber}` : ""}
                              <br />
                              {address.city} {address.zipCode}
                              <br />
                              {countryNameByCode(address.country)}
                        </address>
                      ) : (
                          <address className="not-italic">{formData.deliveryAddress}</address>
                      )}
                    </div>

                    <div className="flex items-center gap-2 pb-3 border-b pt-4">
                      <Truck className="h-5 w-5 text-primary" />
                      <h3 className="font-semibold text-lg">Delivery Details</h3>
                    </div>

                    <div className="grid gap-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Delivery Method:</span>
                        <span className="font-medium flex items-center gap-2">
                          {combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.methodId || "N/A"}
                            {combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.source === "pp" && (
                                <Badge variant="outline" className="text-xs">
                              PP
                            </Badge>
                            )}
                            {combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.methodId ===
                                "DHL Express" && (
                                    <Badge className="text-xs bg-orange-100 text-orange-800">
                              <Zap className="h-3 w-3 mr-1" />
                              Express
                            </Badge>
                                )}
                        </span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Zone/Region:</span>
                        <span className="font-medium">
                          {combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.zoneName || "N/A"}
                        </span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Country:</span>
                        <span className="font-medium">
                          {combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.country || "N/A"}
                        </span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Delivery Price:</span>
                        <span
                            className={`font-medium ${
                                combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.methodId ===
                                "DHL Express"
                                    ? "text-orange-600"
                                    : "text-primary"
                            }`}
                        >
                          {formatPrice(
                              combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.price || 0,
                          )}
                        </span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Estimated Delivery:</span>
                        <span className="font-medium">
                          {combinedDeliveryMethods.find((m) => m._id === formData.deliveryType)?.deliveryTime || "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-4 justify-end pt-6 border-t mt-6">
                    <Button variant="outline" onClick={() => setShowConfirmation(false)} className="px-6">
                      Cancel
                    </Button>
                    <Button onClick={handleConfirmOrder} disabled={isSubmitting} className="px-8 py-2 text-white">
                      {isSubmitting ? (
                          <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          Processing...
                        </div>
                      ) : (
                          <div className="flex items-center gap-2">
                          <Check className="h-4 w-4" />
                          Order B2B Card
                        </div>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
    )
}
