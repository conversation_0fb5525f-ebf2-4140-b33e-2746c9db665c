//@ts-nocheck
"use client"

import { useEffect, useState } from "react"
import { CreditCard, Search, Filter, Plus, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import axiosInstance from "@/utils/axiosInstance"
import { useAppSelector } from "@/store/hooks"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { formatDate } from "@/utils/helpers"
import { useRouter } from "next/navigation"
import Link from "next/link"

interface Cardholder {
    _id: string
    personalInfo: {
        firstName: string
        middleName?: string
        lastName: string
        email: string
        phone: string
        authPhoneNumber: string
        dateOfBirth: string
    }
    address: {
        street: string
        buildingNumber: string
        apartmentNumber?: string
        city: string
        stateProvince: string
        zipCode: string
        country: string
    }
    idDocument: {
        customerIdType: string
        number: string
        issueDate: string
        expiryDate: string
        issuingCountry: string
        idAuthority: string
    }
    taxInfo: {
        country: string
        taxIdNumber: string
    }
    clientID: string
    applicationStatus: string
    dashboardStatus: string
    riskStatus: string
    riskLevel: number
    operationStatus: string
    applicationDate: string
    createdAt: string
    updatedAt: string
}

export default function CardholdersList() {
    const [cardholders, setCardholders] = useState<Cardholder[]>([])
    const [filteredCardholders, setFilteredCardholders] = useState<Cardholder[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [searchTerm, setSearchTerm] = useState("")
    const [statusFilter, setStatusFilter] = useState<string>("all")
    const user = useAppSelector((state) => state.user.user)
    const router = useRouter()

    const checkCardPermission = (p  ) => {
        // If dashboard is programmeManager, check permissions
        return user.corporate.permissions.some(permission =>
            permission.toLowerCase().includes(p.toLowerCase())
        );
    };
    useEffect(() => {
        const fetchCardholders = async () => {
            setIsLoading(true)
            try {
                const response = await axiosInstance.get(`b2b/${user.recordId}/cardholders/`)
                if (response.data) {
                    setCardholders(response.data.data)
                    setFilteredCardholders(response.data.data)
                }
            } catch (error) {
                console.error("Error fetching cardholders:", error)
            } finally {
                setIsLoading(false)
            }
        }

        if (user?.recordId) {
            fetchCardholders()
        }
    }, [user?.recordId])

    // Filter cardholders based on search term and status
    useEffect(() => {
        let filtered = cardholders

        // Filter by search term
        if (searchTerm) {
            filtered = filtered.filter(
                (cardholder) =>
                    cardholder.personalInfo.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    cardholder.personalInfo.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    cardholder.personalInfo.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    cardholder.clientID.includes(searchTerm),
            )
        }

        // Filter by status
        if (statusFilter !== "all") {
            filtered = filtered.filter((cardholder) => cardholder.dashboardStatus.toLowerCase() === statusFilter)
        }

        setFilteredCardholders(filtered)
    }, [searchTerm, statusFilter, cardholders])

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            ACTIVE: { color: "bg-emerald-500 hover:bg-emerald-600", text: "Active" },
            INACTIVE: { color: "bg-gray-500 hover:bg-gray-600", text: "Inactive" },
            PENDING: { color: "bg-yellow-500 hover:bg-yellow-600", text: "Pending" },
            BLOCKED: { color: "bg-red-500 hover:bg-red-600", text: "Blocked" },
            APPROVED: { color: "bg-blue-500 hover:bg-blue-600", text: "Approved" },
        }

        const config = statusConfig[status.toUpperCase()] || statusConfig.INACTIVE
        return <Badge className={config.color}>{config.text}</Badge>
    }

    const getCardTypeBadge = (cardType: string) => {
        return (
            <Badge
                variant="outline"
                className={cardType === "physical" ? "border-blue-500 text-blue-700" : "border-purple-500 text-purple-700"}
            >
        {cardType === "physical" ? "Physical" : "Virtual"}
      </Badge>
        )
    }

    // Function to get country name by code (you may need to implement this)
    const countryNameByCode = (countryCode: string) => {
        // This is a placeholder - you should implement your country code to name mapping
        return countryCode || "N/A"
    }

    // Function to activate account (you may need to implement this)
    const activateAccount = async (cardholder: Cardholder) => {
        try {
            // Implement your activation logic here
            console.log("Activating account for:", cardholder.clientID)
            // After successful activation, you might want to refresh the data
        } catch (error) {
            console.error("Error activating account:", error)
        }
    }

    if (isLoading) {
        return <LoadingOverlay />
    }

    return (
        <div className="space-y-6">
      {/* Header */}
            <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
        <div>
          <h1 className="text-2xl font-bold">Cardholders</h1>
          <p className="text-gray-500">Manage your company cardholders</p>
        </div>
                {checkCardPermission("Cardholder_create") && (
        <Button
            onClick={() => router.push("/corporate/accounts/create")}
            className="flex items-center gap-2 bg-teal-500 hover:bg-teal-600"
        >
          <Plus className="h-4 w-4" />
          Add B2B Cardholder
        </Button>)}
      </div>

            {/* Filters and Search */}
            <Card>
        <CardContent className="p-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex flex-1 items-center gap-2">
              <Search className="h-4 w-4 text-gray-400" />
              <Input
                  placeholder="Search by name, email, or card number..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="max-w-sm"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-teal-500 focus:outline-none focus:ring-1 focus:ring-teal-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="pending">Pending</option>
                <option value="blocked">Blocked</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

            {/* Stats */}
            <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CreditCard className="h-4 w-4 text-teal-500" />
              <div>
                <p className="text-sm text-gray-500">Total Cardholders</p>
                <p className="text-2xl font-bold">{cardholders.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-emerald-500" />
              <div>
                <p className="text-sm text-gray-500">Active</p>
                <p className="text-2xl font-bold">{cardholders.filter((c) => c.dashboardStatus === "ACTIVE").length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CreditCard className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-sm text-gray-500">Approved</p>
                <p className="text-2xl font-bold">
                  {cardholders.filter((c) => c.applicationStatus === "APPROVED").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CreditCard className="h-4 w-4 text-yellow-500" />
              <div>
                <p className="text-sm text-gray-500">Low Risk</p>
                <p className="text-2xl font-bold">{cardholders.filter((c) => c.riskStatus === "LOW").length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

            {/* Cardholders Table */}
            <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5 text-teal-500" />
            Cardholders ({filteredCardholders.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader className="bg-slate-50">
              <TableRow>
                <TableHead className="font-medium text-slate-600">Date Created</TableHead>
                <TableHead className="font-medium text-slate-600">Customer Name</TableHead>
                <TableHead className="font-medium text-slate-600">Customer Email</TableHead>
                <TableHead className="font-medium text-slate-600">Citizenship</TableHead>
                <TableHead className="font-medium text-slate-600">Customer ID</TableHead>
                <TableHead className="font-medium text-slate-600">Dashboard Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCardholders.length === 0 ? (
                  <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center text-slate-500">
                    No cardholders found
                  </TableCell>
                </TableRow>
              ) : (
                  filteredCardholders.map((cardholder, index) => (
                      <TableRow key={cardholder._id} className={index % 2 === 0 ? "bg-white" : "bg-slate-50/50"}>
                    <TableCell className="text-slate-600">{formatDate(cardholder.createdAt)}</TableCell>
                    <TableCell>
                      <Link
                          href={`/corporate/accounts/${cardholder._id}`}
                          className="font-semibold text-slate-800 hover:text-slate-600 hover:underline transition-colors"
                      >
                        {`${cardholder.personalInfo.firstName} ${cardholder.personalInfo.lastName}`}
                      </Link>
                    </TableCell>
                    <TableCell className="text-slate-600">{cardholder.personalInfo.email}</TableCell>
                    <TableCell className="text-slate-600">{countryNameByCode(cardholder.address.country)}</TableCell>
                    <TableCell className="text-slate-600">{cardholder.clientID}</TableCell>
                    <TableCell>
                      {cardholder?.dashboardStatus?.toUpperCase() !== "ACTIVE" ? (
                          <Button
                              onClick={() => activateAccount(cardholder)}
                              size="sm"
                              className="bg-emerald-600 hover:bg-emerald-700 text-white"
                          >
                          Activate Account
                        </Button>
                      ) : (
                          <Badge
                              variant="outline"
                              className="bg-emerald-50 text-emerald-700 border-emerald-100 font-medium"
                          >
                          Active
                        </Badge>
                      )}
                    </TableCell>
                  </TableRow>
                  ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
    )
}
