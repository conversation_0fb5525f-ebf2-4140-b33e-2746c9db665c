//@ts-nocheck
"use client"
import type { FormEvent } from "react"
import { useEffect, useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { format } from "date-fns"
import axiosInstance from "@/utils/axiosInstance"
import { type Step, StepLoader } from "./stpes"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import {useAppDispatch, useAppSelector} from "@/store/hooks"
import { useRouter } from "next/navigation"
import { Alert } from "@/components/alert"
import PhoneInput, { isValidPhoneNumber } from "react-phone-number-input"
import "react-phone-number-input/style.css"
import {
    User,
    CreditCard,
    CheckCircle,
    AlertCircle,
    Mail,
    Hash,
    ArrowRight,
    ArrowLeft,
    Home,
    Briefcase,
    Star,
    Clock,
    Check,
} from "lucide-react"

interface Country {
    _id: string
    country_name: string
    country_code: string
    currency_code: string
    created_at: string
    created_by: string
    is_active: boolean
    status: string
}

const STATIC_B2B_CLIENT_ID = "b2b-client-12345"

const formSteps = [
    {
        id: 1,
        title: "Personal Details",
        description: "Basic information about you",
        icon: User,
        color: "from-blue-500 to-cyan-500",
    },
    {
        id: 2,
        title: "Contact Information",
        description: "How we can reach you",
        icon: Mail,
        color: "from-green-500 to-emerald-500",
    },
    {
        id: 3,
        title: "Address Details",
        description: "Your residential address",
        icon: Home,
        color: "from-purple-500 to-pink-500",
    },
    {
        id: 4,
        title: "Identity Verification",
        description: "ID document information",
        icon: CreditCard,
        color: "from-orange-500 to-red-500",
    },
    {
        id: 5,
        title: "Final Details",
        description: "Tax info and additional details",
        icon: Briefcase,
        color: "from-indigo-500 to-purple-500",
    },
]

export default function IndividualOnboarding() {
    // State for form fields
    const [firstName, setFirstName] = useState<string>("")
    const [middleName, setMiddleName] = useState<string>("")
    const [lastName, setLastName] = useState<string>("")
    const [mothersMaidenName, setMothersMaidenName] = useState<string>("")
    const [email, setEmail] = useState<string>("")
    const [phone, setPhone] = useState<string>("")
    const [authPhone, setAuthPhone] = useState<string>("")
    const [dob, setDob] = useState<string>("")
    const [birthCountry, setBirthCountry] = useState<string>("")
    const [citizenship, setCitizenship] = useState<string>("")

    // Address
    const [street, setStreet] = useState<string>("")
    const [buildingNumber, setBuildingNumber] = useState<string>("")
    const [apartmentNumber, setApartmentNumber] = useState<string>("")
    const [city, setCity] = useState<string>("")
    const [stateProvince, setStateProvince] = useState<string>("")
    const [zipCode, setZipCode] = useState<string>("")
    const [country, setCountry] = useState<string>("")

    // ID Document
    const [idDocumentType, setIdDocumentType] = useState<string>("passport")
    const [idDocumentNumber, setIdDocumentNumber] = useState<string>("")
    const [idAuthority, setIdAuthority] = useState<string>("")
    const [idIssueDate, setIdIssueDate] = useState<string>("")
    const [idExpiryDate, setIdExpiryDate] = useState<string>("")
    const [idIssuingCountry, setIdIssuingCountry] = useState<string>("")

    // Tax Info
    const [taxCountry, setTaxCountry] = useState<string>("")
    const [taxIdNumber, setTaxIdNumber] = useState<string>("")

    // Additional fields
    const [legalId, setLegalId] = useState<string>("")
    const [clientCode, setClientCode] = useState<string>("")
    const [applicationId, setApplicationId] = useState<string>("")
    const [riskLevel, setRiskLevel] = useState<string>("")
    const [riskStatus, setRiskStatus] = useState<string>("LOW")
    const [applicationStatus, setApplicationStatus] = useState<string>("APPROVED")

    const dispatch = useAppDispatch()
    const [isAuthenticated, setIsAuthenticated] = useState(false)
    const router = useRouter()

    // State for form submission
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [submitError, setSubmitError] = useState<string | null>(null)
    const [submitSuccess, setSubmitSuccess] = useState<boolean>(false)

    const [clientDetails, setClientDetails] = useState(null)
    const [cips, setCips] = useState([])
    const [selectedProductVersions, setSelectedProductVersions] = useState<string[]>([])
    const [savedCountries, setSavedCountries] = useState<Country[]>([])
    const [alert, setAlert] = useState<{ message: any; type: "success" | "error" } | null>(null)
    const [error, setError] = useState<string>("")
    const [companyData, setCompanyData] = useState<any[]>([])

    // Wizard state
    const [currentStep, setCurrentStep] = useState(1)
    const [completedSteps, setCompletedSteps] = useState<number[]>([])
    const user = useAppSelector((state) => state.user.user)
    const [steps, setSteps] = useState<Step[]>([
        { name: "Person Account Creation", status: "idle" },
        { name: "User Account Creation", status: "idle" },
        { name: "Sending Welcome Email", status: "idle" },
    ])
    const [currentProcessStep, setCurrentProcessStep] = useState(0)

    useEffect(() => {
        fetchData()
    }, [])
    useEffect(() => {
        const fetchCompanyData = async () => {
            setIsLoading(true)
            try {
                const response = await axiosInstance.get(`/b2b/${user.recordId}`)
                if (response.data) {
                    setCompanyData(response.data.data)
                }
            } catch (error) {
                console.error("Error fetching company data:", error)
            } finally {
                setIsLoading(false)
            }
        }

        if (user?.recordId) {
            fetchCompanyData()
        }
    }, [user?.recordId]) // Add dependency array to prevent infinite re-renders

    const generateApplicationId = () => {
        const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        let result = ""
        for (let i = 0; i < 15; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length))
        }
        return result
    }

    useEffect(() => {
        setApplicationId(generateApplicationId())
    }, [])

    const fetchData = async () => {
        setIsLoading(true)
        try {
            const countries = await axiosInstance.get("companies/country")
            setSavedCountries(countries.data)
        } catch (error) {
            console.error("Error fetching data", error)
        } finally {
            setIsLoading(false)
        }
    }

    const extractMessages = (error: any): string[] => {
        if (typeof error === "string") {
            return [error]
        } else if (Array.isArray(error)) {
            return error.flatMap(extractMessages)
        } else if (error && typeof error === "object") {
            return Object.values(error).flatMap(extractMessages)
        } else {
            return [JSON.stringify(error)]
        }
    }

    const validateCurrentStep = () => {
        switch (currentStep) {
            case 1:
                return firstName && lastName && mothersMaidenName && dob && birthCountry && citizenship
            case 2:
                return email && phone && isValidPhoneNumber(phone)
            case 3:
                return street && city && country
            case 4:
                return idDocumentNumber && idAuthority && idIssueDate && idExpiryDate && idIssuingCountry
            case 5:
                return taxCountry && taxIdNumber
            default:
                return false
        }
    }

    const nextStep = () => {
        if (validateCurrentStep()) {
            if (!completedSteps.includes(currentStep)) {
                setCompletedSteps([...completedSteps, currentStep])
            }
            if (currentStep < formSteps.length) {
                setCurrentStep(currentStep + 1)
            }
        }
    }

    const prevStep = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1)
        }
    }

    const calculateProgress = () => {
        return Math.round((completedSteps.length / formSteps.length) * 100)
    }

    const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        setIsSubmitting(true)
        setSubmitError(null)

        const formData = {
            clientCode: clientCode || `RYVL-${Math.floor(Math.random() * 1000000)}`,
            personalInfo: {
                firstName,
                secondName: middleName,
                lastName,
                mothersMaidenName,
                birthDate: dob,
                email,
                phoneNumber: phone,
                authPhoneNumber: authPhone || phone,
                userType: "b2b",
                birthCountry,
            },
            address: {
                street,
                buildingNumber,
                apartmentNumber,
                city,
                stateProvince,
                zipCode,
                country,
            },
            idDocument: {
                customerIdType: idDocumentType,
                number: idDocumentNumber,
                issueDate: idIssueDate,
                expiryDate: idExpiryDate,
                idAuthority: idAuthority,
                issuingCountry: idIssuingCountry,
            },
            taxInfo: {
                country: taxCountry,
                taxIdNumber,
            },
            legalId,
            citizenship,
            applicationId: applicationId || `app${Math.floor(Math.random() * 1000000000)}`,
            riskLevel,
            riskStatus,
            applicationStatus,company: companyData.parentCompany._id,
            applicationDate: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
            b2bClient:user.recordId ,
            productVersions: selectedProductVersions,
        }

        try {
            setSteps((prev) => prev.map((step, index) => (index === 0 ? { ...step, status: "loading" } : step)))
            const response = await axiosInstance.post("/onboarding/personal", formData)
            setSteps((prev) => prev.map((step, index) => (index === 0 ? { ...step, status: "complete" } : step)))

            setSteps((prev) => prev.map((step, index) => (index === 1 ? { ...step, status: "loading" } : step)))
            const registrationResponse = await axiosInstance.post("companies/register", {
                name: `${firstName} ${lastName}`,
                email: email,
                status: "Active",
                dashboard: "cardholder",
                recordId: response.data.data._id,
            })

            if (registrationResponse.status === 201) {
                await axiosInstance.post("onboarding/personal/activate", { recordId: response.data.data._id })
            }
            setSteps((prev) => prev.map((step, index) => (index === 1 ? { ...step, status: "complete" } : step)))

            setSteps((prev) => prev.map((step, index) => (index === 2 ? { ...step, status: "loading" } : step)))
            setSteps((prev) => prev.map((step, index) => (index === 2 ? { ...step, status: "complete" } : step)))

            setSubmitSuccess(true)
            setTimeout(() => {
                window.location.reload()
            }, 5000)
        } catch (error: any) {
            const errorData = error.response?.data ?? error.message ?? error
            const messages = extractMessages(errorData)
            setAlert({ message: messages, type: "error" })
            setSteps((prev) =>
                prev.map((step, index) => (index === currentProcessStep ? { ...step, status: "error" } : step)),
            )
        } finally {
            setIsSubmitting(false)
        }
    }

    const retryStep = (stepIndex: number) => {
        setSteps((prev) => prev.map((step, index) => (index === stepIndex ? { ...step, status: "idle" } : step)))
        setCurrentProcessStep(stepIndex)
    }

    const MINIMUM_AGE = 18
    const [dobError, setDobError] = useState<string | null>(null)

    const handleDobChange = (value: string) => {
        setDob(value)

        const today = new Date()
        const dobDate = new Date(value)
        const age = today.getFullYear() - dobDate.getFullYear()
        const monthDifference = today.getMonth() - dobDate.getMonth()
        const dayDifference = today.getDate() - dobDate.getDate()

        if (
            age < MINIMUM_AGE ||
            (age === MINIMUM_AGE && (monthDifference < 0 || (monthDifference === 0 && dayDifference < 0)))
        ) {
            setDobError("You must be at least 18 years old.")
        } else {
            setDobError(null)
        }
    }

    if (isSubmitting || submitSuccess) {
        return (
            <div className="min-h-screen bg-white flex items-center justify-center p-4">
                <div className="max-w-2xl w-full">
                    <Card className="shadow-2xl border-0">
                        <CardContent className="p-8">
                            <StepLoader steps={steps} currentStep={currentProcessStep} onRetry={retryStep} />
                            {submitSuccess && (
                                <div className="mt-8 p-6 bg-green-50 border border-green-200 rounded-xl">
                                    <div className="flex items-center space-x-3">
                                        <div className="p-2 bg-green-500 rounded-full">
                                            <CheckCircle className="h-6 w-6 text-white" />
                                        </div>
                                        <div>
                                            <h3 className="text-xl font-bold text-green-800">Success!</h3>
                                            <p className="text-green-600">Your application has been submitted successfully.</p>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        )
    }

    if (isLoading) {
        return <LoadingOverlay />
    }

    const progress = calculateProgress()
    const currentStepData = formSteps.find((step) => step.id === currentStep)

    return (
        <div className="min-h-screen bg-white">
            {/* Header */}
            <div className="bg-white border-b border-gray-200 shadow-sm">
                <div className="max-w-6xl mx-auto px-6 py-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">Individual Onboarding</h1>
                            <p className="text-gray-600 mt-2">Complete your profile in {formSteps.length} simple steps</p>
                        </div>
                        <div className="flex items-center space-x-4">
                            <div className="text-right">
                                <p className="text-gray-900 font-semibold">{progress}% Complete</p>
                                <Progress value={progress} className="w-40 mt-2 h-2" />
                            </div>
                            <Badge variant={progress === 100 ? "default" : "secondary"} className="px-4 py-2 text-sm font-medium">
                                Step {currentStep} of {formSteps.length}
                            </Badge>
                        </div>
                    </div>
                </div>
            </div>

            <div className="max-w-6xl mx-auto p-6">
                {/* Step Navigation */}
                <div className="mb-8">
                    <div className="flex items-center justify-between">
                        {formSteps.map((step, index) => {
                            const Icon = step.icon
                            const isCompleted = completedSteps.includes(step.id)
                            const isCurrent = currentStep === step.id
                            const isAccessible = step.id <= currentStep || completedSteps.includes(step.id)

                            return (
                                <div key={step.id} className="flex items-center">
                                    <div
                                        className={`relative flex items-center justify-center w-12 h-12 rounded-full transition-all duration-300 ${
                                            isCompleted
                                                ? "bg-green-500 text-white shadow-lg shadow-green-500/30"
                                                : isCurrent
                                                    ? `bg-gradient-to-r ${step.color} text-white shadow-lg`
                                                    : isAccessible
                                                        ? "bg-gray-100 text-gray-600 border-2 border-gray-300"
                                                        : "bg-gray-50 text-gray-400"
                                        }`}
                                    >
                                        {isCompleted ? <Check className="h-6 w-6" /> : <Icon className="h-6 w-6" />}
                                    </div>
                                    {index < formSteps.length - 1 && (
                                        <div
                                            className={`w-16 h-1 mx-2 transition-all duration-300 ${
                                                completedSteps.includes(step.id) ? "bg-green-500" : "bg-gray-200"
                                            }`}
                                        />
                                    )}
                                </div>
                            )
                        })}
                    </div>
                    <div className="mt-4 text-center">
                        <h2 className="text-2xl font-bold text-gray-900">{currentStepData?.title}</h2>
                        <p className="text-gray-600 mt-1">{currentStepData?.description}</p>
                    </div>
                </div>

                {/* Form Content */}
                <Card className="shadow-xl border-0">
                    <CardContent className="p-8">
                        <form onSubmit={handleSubmit}>
                            {alert && (
                                <div className="mb-6">
                                    <Alert
                                        type={alert.type}
                                        onClose={() => setAlert(null)}
                                        message={
                                            <ul className="list-disc list-inside">
                                                {alert.message.map((msg, index) => (
                                                    <li key={index}>{msg}</li>
                                                ))}
                                            </ul>
                                        }
                                    />
                                </div>
                            )}

                            {/* Step 1: Personal Details */}
                            {currentStep === 1 && (
                                <div className="space-y-6">
                                    <div className="text-center mb-8">
                                        <div className={`inline-flex p-4 rounded-full bg-gradient-to-r ${currentStepData?.color} mb-4`}>
                                            <User className="h-8 w-8 text-white" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900 mb-2">Tell us about yourself</h3>
                                        <p className="text-gray-600">We need some basic information to get started</p>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="firstName" className="text-gray-700 font-medium">
                                                First Name *
                                            </Label>
                                            <Input
                                                id="firstName"
                                                placeholder="Enter your first name"
                                                value={firstName}
                                                onChange={(e) => setFirstName(e.target.value)}
                                                required
                                                className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="middleName" className="text-gray-700 font-medium">
                                                Middle Name
                                            </Label>
                                            <Input
                                                id="middleName"
                                                placeholder="Enter your middle name"
                                                value={middleName}
                                                onChange={(e) => setMiddleName(e.target.value)}
                                                className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="lastName" className="text-gray-700 font-medium">
                                                Last Name *
                                            </Label>
                                            <Input
                                                id="lastName"
                                                placeholder="Enter your last name"
                                                value={lastName}
                                                onChange={(e) => setLastName(e.target.value)}
                                                required
                                                className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                                            />
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="mothersMaidenName" className="text-gray-700 font-medium">
                                            Mother's Maiden Name *
                                        </Label>
                                        <Input
                                            id="mothersMaidenName"
                                            placeholder="Enter your mother's maiden name"
                                            value={mothersMaidenName}
                                            onChange={(e) => setMothersMaidenName(e.target.value)}
                                            required
                                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="dob" className="text-gray-700 font-medium">
                                            Date of Birth *
                                        </Label>
                                        <Input
                                            id="dob"
                                            type="date"
                                            value={dob}
                                            onChange={(e) => handleDobChange(e.target.value)}
                                            required
                                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                                        />
                                        {dobError && (
                                            <p className="text-red-600 text-sm flex items-center space-x-1">
                                                <AlertCircle className="h-4 w-4" />
                                                <span>{dobError}</span>
                                            </p>
                                        )}
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="birthCountry" className="text-gray-700 font-medium">
                                                Birth Country *
                                            </Label>
                                            <Select value={birthCountry} onValueChange={setBirthCountry}>
                                                <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20">
                                                    <SelectValue placeholder="Select your birth country" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {savedCountries
                                                        .filter((c) => c.status === "active" && c.is_active)
                                                        .sort((a, b) => a.country_name.localeCompare(b.country_name))
                                                        .map((country) => (
                                                            <SelectItem key={`birth-${country._id}`} value={country.country_code}>
                                                                {country.country_name} ({country.country_code})
                                                            </SelectItem>
                                                        ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="citizenship" className="text-gray-700 font-medium">
                                                Citizenship *
                                            </Label>
                                            <Select value={citizenship} onValueChange={setCitizenship}>
                                                <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20">
                                                    <SelectValue placeholder="Select your citizenship" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {savedCountries
                                                        .filter((c) => c.status === "active" && c.is_active)
                                                        .sort((a, b) => a.country_name.localeCompare(b.country_name))
                                                        .map((country) => (
                                                            <SelectItem key={`citizenship-${country._id}`} value={country.country_code}>
                                                                {country.country_name} ({country.country_code})
                                                            </SelectItem>
                                                        ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>

                                    <div className="pt-4">
                                        <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                            <div className="flex items-center space-x-3">
                                                <Hash className="h-5 w-5 text-gray-500" />
                                                <div className="flex-1">
                                                    <Label htmlFor="applicationId" className="text-gray-700 font-medium">
                                                        Application ID
                                                    </Label>
                                                    <Input
                                                        maxLength="15"
                                                        id="applicationId"
                                                        placeholder="Auto-generated ID"
                                                        value={applicationId}
                                                        onChange={(e) => setApplicationId(e.target.value)}
                                                        className="mt-2 border-gray-300 font-mono"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Step 2: Contact Information */}
                            {currentStep === 2 && (
                                <div className="space-y-6">
                                    <div className="text-center mb-8">
                                        <div className={`inline-flex p-4 rounded-full bg-gradient-to-r ${currentStepData?.color} mb-4`}>
                                            <Mail className="h-8 w-8 text-white" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900 mb-2">How can we reach you?</h3>
                                        <p className="text-gray-600">Provide your contact information for communication</p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="email" className="text-gray-700 font-medium">
                                            Email Address *
                                        </Label>
                                        <Input
                                            id="email"
                                            placeholder="Enter your email address"
                                            type="email"
                                            value={email}
                                            onChange={(e) => setEmail(e.target.value)}
                                            required
                                            className="border-gray-300 focus:border-green-500 focus:ring-green-500/20"
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="phone" className="text-gray-700 font-medium">
                                            Phone Number *
                                        </Label>
                                        <PhoneInput
                                            international
                                            countryCallingCodeEditable={true}
                                            defaultCountry="AT"
                                            id="phone"
                                            placeholder="Enter your phone number"
                                            value={phone || ""}
                                            onChange={(value) => {
                                                if (!isValidPhoneNumber(phone || "") || (value && value.length <= (phone || "").length)) {
                                                    setPhone(value || "")
                                                }
                                            }}
                                            className={`flex h-12 w-full rounded-lg border transition-all ${
                                                phone
                                                    ? isValidPhoneNumber(phone || "")
                                                        ? "border-green-500 bg-green-50"
                                                        : "border-red-500 bg-red-50"
                                                    : "border-gray-300"
                                            } px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-green-500/20 disabled:cursor-not-allowed disabled:opacity-50`}
                                            required
                                        />
                                        {phone && (
                                            <p
                                                className={`text-sm flex items-center space-x-2 ${isValidPhoneNumber(phone || "") ? "text-green-600" : "text-red-600"}`}
                                            >
                                                {isValidPhoneNumber(phone || "") ? (
                                                    <CheckCircle className="h-4 w-4" />
                                                ) : (
                                                    <AlertCircle className="h-4 w-4" />
                                                )}
                                                <span>
                          {isValidPhoneNumber(phone || "") ? "Valid phone number" : "Please enter a valid phone number"}
                        </span>
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="authPhone" className="text-gray-700 font-medium flex items-center space-x-2">
                                            <span>Authentication Phone Number</span>
                                            <Badge variant="outline" className="text-xs">
                                                Optional
                                            </Badge>
                                        </Label>
                                        <PhoneInput
                                            international
                                            countryCallingCodeEditable={true}
                                            defaultCountry="AT"
                                            id="authPhone"
                                            placeholder="Enter authentication phone (if different)"
                                            value={authPhone || ""}
                                            onChange={(value) => {
                                                if (
                                                    !isValidPhoneNumber(authPhone || "") ||
                                                    (value && value.length <= (authPhone || "").length)
                                                ) {
                                                    setAuthPhone(value || "")
                                                }
                                            }}
                                            className={`flex h-12 w-full rounded-lg border transition-all ${
                                                authPhone
                                                    ? isValidPhoneNumber(authPhone)
                                                        ? "border-green-500 bg-green-50"
                                                        : "border-red-500 bg-red-50"
                                                    : "border-gray-300"
                                            } px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-green-500/20 disabled:cursor-not-allowed disabled:opacity-50`}
                                        />
                                        {authPhone && (
                                            <p
                                                className={`text-sm flex items-center space-x-2 ${isValidPhoneNumber(authPhone) ? "text-green-600" : "text-red-600"}`}
                                            >
                                                {isValidPhoneNumber(authPhone) ? (
                                                    <CheckCircle className="h-4 w-4" />
                                                ) : (
                                                    <AlertCircle className="h-4 w-4" />
                                                )}
                                                <span>
                          {isValidPhoneNumber(authPhone) ? "Valid phone number" : "Please enter a valid phone number"}
                        </span>
                                            </p>
                                        )}
                                        {!authPhone && (
                                            <p className="text-gray-500 text-sm">
                                                Leave blank to use primary phone number for authentication
                                            </p>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Step 3: Address Details */}
                            {currentStep === 3 && (
                                <div className="space-y-6">
                                    <div className="text-center mb-8">
                                        <div className={`inline-flex p-4 rounded-full bg-gradient-to-r ${currentStepData?.color} mb-4`}>
                                            <Home className="h-8 w-8 text-white" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900 mb-2">Where do you live?</h3>
                                        <p className="text-gray-600">Provide your current residential address</p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="street" className="text-gray-700 font-medium">
                                            Street Address *
                                        </Label>
                                        <Input
                                            id="street"
                                            placeholder="Enter your street address"
                                            value={street}
                                            onChange={(e) => setStreet(e.target.value)}
                                            required
                                            className="border-gray-300 focus:border-purple-500 focus:ring-purple-500/20"
                                        />
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="buildingNumber" className="text-gray-700 font-medium">
                                                Building Number
                                            </Label>
                                            <Input
                                                id="buildingNumber"
                                                placeholder="Building number"
                                                value={buildingNumber}
                                                onChange={(e) => setBuildingNumber(e.target.value)}
                                                className="border-gray-300 focus:border-purple-500 focus:ring-purple-500/20"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="apartmentNumber" className="text-gray-700 font-medium">
                                                Apartment Number
                                            </Label>
                                            <Input
                                                id="apartmentNumber"
                                                placeholder="Apartment number"
                                                value={apartmentNumber}
                                                onChange={(e) => setApartmentNumber(e.target.value)}
                                                className="border-gray-300 focus:border-purple-500 focus:ring-purple-500/20"
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="city" className="text-gray-700 font-medium">
                                                City *
                                            </Label>
                                            <Input
                                                id="city"
                                                placeholder="Enter your city"
                                                value={city}
                                                onChange={(e) => setCity(e.target.value)}
                                                required
                                                className="border-gray-300 focus:border-purple-500 focus:ring-purple-500/20"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="stateProvince" className="text-gray-700 font-medium">
                                                State / Province
                                            </Label>
                                            <Input
                                                id="stateProvince"
                                                placeholder="State or province"
                                                value={stateProvince}
                                                onChange={(e) => setStateProvince(e.target.value)}
                                                className="border-gray-300 focus:border-purple-500 focus:ring-purple-500/20"
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="zipCode" className="text-gray-700 font-medium">
                                                Postal / ZIP Code
                                            </Label>
                                            <Input
                                                id="zipCode"
                                                placeholder="Postal or ZIP code"
                                                value={zipCode}
                                                onChange={(e) => setZipCode(e.target.value)}
                                                className="border-gray-300 focus:border-purple-500 focus:ring-purple-500/20"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="country" className="text-gray-700 font-medium">
                                                Country *
                                            </Label>
                                            <Select value={country} onValueChange={setCountry}>
                                                <SelectTrigger className="border-gray-300 focus:border-purple-500 focus:ring-purple-500/20">
                                                    <SelectValue placeholder="Select your country" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {savedCountries
                                                        .filter((c) => c.status === "active" && c.is_active)
                                                        .sort((a, b) => a.country_name.localeCompare(b.country_name))
                                                        .map((country) => (
                                                            <SelectItem key={`address-${country._id}`} value={country.country_code}>
                                                                {country.country_name} ({country.country_code})
                                                            </SelectItem>
                                                        ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Step 4: Identity Verification */}
                            {currentStep === 4 && (
                                <div className="space-y-6">
                                    <div className="text-center mb-8">
                                        <div className={`inline-flex p-4 rounded-full bg-gradient-to-r ${currentStepData?.color} mb-4`}>
                                            <CreditCard className="h-8 w-8 text-white" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900 mb-2">Verify your identity</h3>
                                        <p className="text-gray-600">Provide your ID document information for verification</p>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="idDocumentType" className="text-gray-700 font-medium">
                                                ID Document Type *
                                            </Label>
                                            <Select value={idDocumentType} onValueChange={setIdDocumentType}>
                                                <SelectTrigger className="border-gray-300 focus:border-orange-500 focus:ring-orange-500/20">
                                                    <SelectValue placeholder="Select document type" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="ID">National ID</SelectItem>
                                                    <SelectItem value="PASSPORT">Passport</SelectItem>
                                                    <SelectItem value="RESIDENCE_CARD">Residence Card</SelectItem>
                                                    <SelectItem value="DRIVING_LICENSE">Driving License</SelectItem>
                                                    <SelectItem value="MINOR_WITHOUT_ID">Minor Without ID</SelectItem>
                                                    <SelectItem value="OTHER">Other</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="idDocumentNumber" className="text-gray-700 font-medium">
                                                ID Document Number *
                                            </Label>
                                            <Input
                                                id="idDocumentNumber"
                                                placeholder="Enter document number"
                                                value={idDocumentNumber}
                                                onChange={(e) => setIdDocumentNumber(e.target.value)}
                                                required
                                                className="border-gray-300 font-mono focus:border-orange-500 focus:ring-orange-500/20"
                                            />
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="idAuthority" className="text-gray-700 font-medium">
                                            Issuing Authority *
                                        </Label>
                                        <Input
                                            id="idAuthority"
                                            placeholder="Enter issuing authority"
                                            value={idAuthority}
                                            onChange={(e) => setIdAuthority(e.target.value)}
                                            required
                                            className="border-gray-300 focus:border-orange-500 focus:ring-orange-500/20"
                                        />
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="idIssueDate" className="text-gray-700 font-medium">
                                                Issue Date *
                                            </Label>
                                            <Input
                                                id="idIssueDate"
                                                type="date"
                                                value={idIssueDate}
                                                onChange={(e) => setIdIssueDate(e.target.value)}
                                                required
                                                className="border-gray-300 focus:border-orange-500 focus:ring-orange-500/20"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="idExpiryDate" className="text-gray-700 font-medium">
                                                Expiry Date *
                                            </Label>
                                            <Input
                                                id="idExpiryDate"
                                                type="date"
                                                value={idExpiryDate}
                                                onChange={(e) => setIdExpiryDate(e.target.value)}
                                                required
                                                className="border-gray-300 focus:border-orange-500 focus:ring-orange-500/20"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="idIssuingCountry" className="text-gray-700 font-medium">
                                                Issuing Country *
                                            </Label>
                                            <Select value={idIssuingCountry} onValueChange={setIdIssuingCountry}>
                                                <SelectTrigger className="border-gray-300 focus:border-orange-500 focus:ring-orange-500/20">
                                                    <SelectValue placeholder="Select country" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {savedCountries
                                                        .filter((c) => c.status === "active" && c.is_active)
                                                        .sort((a, b) => a.country_name.localeCompare(b.country_name))
                                                        .map((country) => (
                                                            <SelectItem key={`id-${country._id}`} value={country.country_code}>
                                                                {country.country_name} ({country.country_code})
                                                            </SelectItem>
                                                        ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Step 5: Final Details */}
                            {currentStep === 5 && (
                                <div className="space-y-6">
                                    <div className="text-center mb-8">
                                        <div className={`inline-flex p-4 rounded-full bg-gradient-to-r ${currentStepData?.color} mb-4`}>
                                            <Briefcase className="h-8 w-8 text-white" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900 mb-2">Almost done!</h3>
                                        <p className="text-gray-600">Just a few more details to complete your profile</p>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="taxCountry" className="text-gray-700 font-medium">
                                                Tax Country *
                                            </Label>
                                            <Select value={taxCountry} onValueChange={setTaxCountry}>
                                                <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500/20">
                                                    <SelectValue placeholder="Select tax country" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {savedCountries
                                                        .filter((c) => c.status === "active" && c.is_active)
                                                        .sort((a, b) => a.country_name.localeCompare(b.country_name))
                                                        .map((country) => (
                                                            <SelectItem key={`tax-${country._id}`} value={country.country_code}>
                                                                {country.country_name} ({country.country_code})
                                                            </SelectItem>
                                                        ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="taxIdNumber" className="text-gray-700 font-medium">
                                                Tax ID Number *
                                            </Label>
                                            <Input
                                                id="taxIdNumber"
                                                placeholder="Enter tax ID number"
                                                value={taxIdNumber}
                                                onChange={(e) => setTaxIdNumber(e.target.value)}
                                                required
                                                className="border-gray-300 font-mono focus:border-indigo-500 focus:ring-indigo-500/20"
                                            />
                                        </div>
                                    </div>

                                    <Separator className="bg-gray-200" />

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="legalId" className="text-gray-700 font-medium">
                                                Legal ID
                                            </Label>
                                            <Input
                                                id="legalId"
                                                placeholder="Enter legal ID"
                                                value={legalId}
                                                onChange={(e) => setLegalId(e.target.value)}
                                                className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500/20"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="riskLevel" className="text-gray-700 font-medium">
                                                Risk Level
                                            </Label>
                                            <Input
                                                id="riskLevel"
                                                placeholder="e.g., 300"
                                                value={riskLevel}
                                                onChange={(e) => setRiskLevel(e.target.value)}
                                                className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500/20"
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="riskStatus" className="text-gray-700 font-medium">
                                                Risk Status
                                            </Label>
                                            <Select value={riskStatus} onValueChange={setRiskStatus}>
                                                <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500/20">
                                                    <SelectValue placeholder="Select risk status" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="LOW">
                                                        <div className="flex items-center space-x-2">
                                                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                                            <span>Low Risk</span>
                                                        </div>
                                                    </SelectItem>
                                                    <SelectItem value="MEDIUM">
                                                        <div className="flex items-center space-x-2">
                                                            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                                            <span>Medium Risk</span>
                                                        </div>
                                                    </SelectItem>
                                                    <SelectItem value="HIGH">
                                                        <div className="flex items-center space-x-2">
                                                            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                                                            <span>High Risk</span>
                                                        </div>
                                                    </SelectItem>
                                                    <SelectItem value="VERY HIGH">
                                                        <div className="flex items-center space-x-2">
                                                            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                                                            <span>Very High Risk</span>
                                                        </div>
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="applicationStatus" className="text-gray-700 font-medium">
                                                Application Status
                                            </Label>
                                            <Select value={applicationStatus} onValueChange={setApplicationStatus}>
                                                <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500/20">
                                                    <SelectValue placeholder="Select application status" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="PENDING">
                                                        <div className="flex items-center space-x-2">
                                                            <Clock className="w-4 h-4 text-yellow-500" />
                                                            <span>Pending</span>
                                                        </div>
                                                    </SelectItem>
                                                    <SelectItem value="APPROVED">
                                                        <div className="flex items-center space-x-2">
                                                            <CheckCircle className="w-4 h-4 text-green-500" />
                                                            <span>Approved</span>
                                                        </div>
                                                    </SelectItem>
                                                    <SelectItem value="REJECTED">
                                                        <div className="flex items-center space-x-2">
                                                            <AlertCircle className="w-4 h-4 text-red-500" />
                                                            <span>Rejected</span>
                                                        </div>
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Navigation Buttons */}
                            <div className="flex items-center justify-between pt-8 border-t border-gray-200">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={prevStep}
                                    disabled={currentStep === 1}
                                    className="border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                                >
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Previous
                                </Button>

                                <div className="flex items-center space-x-2">
                                    {formSteps.map((_, index) => (
                                        <div
                                            key={index}
                                            className={`w-2 h-2 rounded-full transition-all ${
                                                index + 1 <= currentStep ? "bg-gray-900" : "bg-gray-300"
                                            }`}
                                        />
                                    ))}
                                </div>

                                {currentStep < formSteps.length ? (
                                    <Button
                                        type="button"
                                        onClick={nextStep}
                                        disabled={!validateCurrentStep()}
                                        className={`bg-gradient-to-r ${currentStepData?.color} text-white hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed`}
                                    >
                                        Next
                                        <ArrowRight className="h-4 w-4 ml-2" />
                                    </Button>
                                ) : (
                                    <Button
                                        type="submit"
                                        disabled={isSubmitting || !validateCurrentStep() || dobError}
                                        className="bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed px-8"
                                    >
                                        {isSubmitting ? (
                                            <div className="flex items-center space-x-2">
                                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                                <span>Submitting...</span>
                                            </div>
                                        ) : (
                                            <div className="flex items-center space-x-2">
                                                <Star className="h-4 w-4" />
                                                <span>Submit Application</span>
                                            </div>
                                        )}
                                    </Button>
                                )}
                            </div>

                            {submitError && (
                                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                                    <div className="flex items-center space-x-2">
                                        <AlertCircle className="h-5 w-5 text-red-600" />
                                        <p className="text-red-800 font-medium">Submission Error</p>
                                    </div>
                                    <p className="text-red-600 mt-1">{submitError}</p>
                                </div>
                            )}
                        </form>
                    </CardContent>
                </Card>
            </div>
        </div>
    )
}
