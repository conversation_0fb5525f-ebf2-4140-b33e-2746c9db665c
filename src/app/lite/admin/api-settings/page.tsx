// @ts-nocheck
"use client"

import {useEffect, useState} from "react"
import {Edit, Trash, UserPlus} from "lucide-react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Badge} from "@/components/ui/badge"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import Link from "next/link"
import axiosInstance from "@/utils/axiosInstance"
import {formatDate} from "@/utils/helpers"
import {DataTable} from "@/components/data-table"
import {CardDescription, CardHeader, CardTitle} from "@/components/ui/card";
import {Sheet, SheetClose, SheetContent, SheetHeader, SheetTitle, SheetTrigger} from "@/components/ui/sheet";
import {Label} from "@/components/ui/label";
import {Input} from "@/components/ui/input";
import {useAppSelector} from "@/store/hooks";

export default function UserPage() {
    const [users, setUsers] = useState([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState(null)
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
    const [userToDelete, setUserToDelete] = useState(null)
    const user = useAppSelector((state) => state.user.user)


    const [roles, setRoles] = useState<Role[]>([])

    useEffect(() => {
        setRoles(user.roles)
    }, [])
    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions))
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }


    const userHasCreatePermission = hasPermission(roles, "External API Users_Create")
    const userHasModifyPermission = hasPermission(roles, "External API Users_Modify")

    useEffect(() => {
        const fetchUsers = async () => {
            try {
                const response = await axiosInstance.get("users")
                setUsers(response.data)
            } catch (err) {
                setError(err.message)
            } finally {
                setLoading(false)
            }
        }

        fetchUsers()
    }, [])

    const handleDeleteClick = (userId) => {
        setUserToDelete(userId)
        setDeleteDialogOpen(true)
    }

    const handleConfirmDelete = async () => {
        if (!userToDelete) return

        try {
            await axiosInstance.delete(`users/${userToDelete}`)
            setUsers(users.filter((user) => user._id !== userToDelete))
            setDeleteDialogOpen(false)
            setUserToDelete(null)
        } catch (error) {
            console.error("Error deleting user:", error)
            // Optionally, show an error message to the user
        }
    }

    // Filter users where dashboard === "infinity"
    const filteredUsers = users.filter((user) => user.dashboard === "local_api")

    // Define columns for the DataTable
    const columns = [
        {
            header: "Date Created",
            accessorKey: "createdAt",
            cell: (user) => formatDate(user.createdAt),
            enableSorting: true,
        },
        {
            header: "User",
            accessorKey: "name",
            cell: (user) => (
                <div>
                    <div className="font-medium">{user.name}</div>

                </div>
            ),
            enableSorting: true,
        },
        {
            header: "Email",
            accessorKey: "email",
            cell: (user) => (
                <div>
                    <div className="font-medium">{user.email}</div>
                </div>
            ),
            enableSorting: false,
        }, {
            header: "Company",
            accessorKey: "company",
            cell: (user) => (
                <div>
                    <div className="font-medium">{user?.company?.company_name}</div>
                </div>
            ),
            enableSorting: false,
        },
        // {
        //     header: "Status",
        //     accessorKey: "status",
        //     cell: (user) => (
        //         <Badge variant={user.status === "active" ? "success" : "secondary"}>
        //             {user.status ? user.status.charAt(0).toUpperCase() + user.status.slice(1) : "N/A"}
        //         </Badge>
        //     ),
        //     enableSorting: true,
        // },

        {
            header: "Email Status",
            accessorKey: "status",
            cell: (user) => (
                <Badge variant={user.status === "active" ? "success" : "secondary"}>
                   Sent
                </Badge>
            ),
            enableSorting: true,
        },
        {
            header: "Actions",
            accessorKey: "_id",
            cell: (user) => (
                <div className="flex ">
                    {userHasCreatePermission &&(
                        <>
                    <Link href={`api-settings/${user._id}/edit`} passHref>
                        <Button variant="outline" size="sm" className="mr-2">
                            <Edit className="mr-2 h-4 w-4"/>
                        </Button>
                    </Link>
                    <Button
                        variant="destructive"
                        size="sm"
                        onClick={(e) => {
                            e.stopPropagation()
                            handleDeleteClick(user._id)
                        }}
                    >
                        <Trash className="mr-2 h-4 w-4"/>
                    </Button>
                        </>  )}
                </div>
            ),
            enableSorting: false,
        },

    ]

    // Define status filter options
    const filterOptions = {
        key: "status",
        options: [
            {label: "All", value: null},
            {label: "Active", value: "active"},
            {label: "Inactive", value: "inactive"},
        ],
    }

    // Custom header actions
    const HeaderActions = () => (
        <Link href="users/add" passHref>
            <Button>
                <UserPlus className="mr-2 h-4 w-4"/> Add User
            </Button>
        </Link>
    )

    return (
        <>
            <CardHeader className="flex flex-row items-center justify-between">
                <div>
                    <CardTitle>3rd Party API Users</CardTitle>
                    <CardDescription>View and Manager External API Users</CardDescription>
                </div>
                <div>
                    {userHasCreatePermission &&(
                    <Link href="api-settings/add" passHref>
                        <Button>
                            <UserPlus className="mr-2 h-4 w-4"/> Add User
                        </Button>
                    </Link>
                    )}
                </div>
            </CardHeader>

            <DataTable
                data={filteredUsers}
                columns={columns}
                title="Ryvyl Users"
                description="Manage users and their permissions."
                loading={loading}
                error={error}
                filterOptions={filterOptions}
                enableSelection={false}
                getRowId={(user) => user._id}
                headerActions={<HeaderActions/>}
            />

            <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Confirm Deletion</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete this user? This action cannot be undone.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button variant="destructive" onClick={handleConfirmDelete}>
                            Delete
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    )
}

