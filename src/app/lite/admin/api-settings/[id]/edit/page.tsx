// @ts-nocheck

'use client'

import {useEffect, useState} from "react"
import {use<PERSON><PERSON><PERSON>} from "next/navigation"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Checkbox} from "@/components/ui/checkbox"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {InfoIcon, Loader2} from "lucide-react"
import {Alert, AlertDescription, AlertTitle} from "@/components/ui/alert"
import axiosInstance from "@/utils/axiosInstance"


const Tooltip = ({ children, content }) => (
    <div className="relative group">
        {children}
        <div className="absolute z-10 invisible group-hover:visible bg-gray-800 text-white text-xs rounded p-2 bottom-full left-1/2 transform -translate-x-1/2 mb-1 w-64">
            <ul className="list-disc pl-4 space-y-1">
                {content.map((item, index) => (
                    <li key={index}>{item}</li>
                ))}
            </ul>
            <svg className="absolute text-gray-800 h-2 w-full left-0 top-full" x="0px" y="0px" viewBox="0 0 255 255">
                <polygon className="fill-current" points="0,0 127.5,127.5 255,0"/>
            </svg>
        </div>
    </div>
)

export default function EditUserPage({ params }: { params: { id: string } }) {
    const router = useRouter()
    const userId = params.id// Assuming user ID is passed as a query param
    const [formData, setFormData] = useState({
        name: "",
        email: "",
        status: "active",
        permissions:  []
    })
    const [isLoading, setIsLoading] = useState(false)
    const [alertInfo, setAlertInfo] = useState<{ type: 'success' | 'error', message: string } | null>(null)

    useEffect(() => {
        console.dir(userId)
        if (userId) {
            axiosInstance.get(`users/user/${userId}`)
                .then(response => {
                    const { name, email, status } = response.data
                     setFormData({ name, email, status  })
                })
                .catch(error => console.error("Error loading user data:", error))
        }
    }, [userId])

    const handleInputChange = (e) => {
        const { name, value } = e.target
        setFormData(prev => ({ ...prev, [name]: value }))
    }

    const handleStatusChange = (value) => {
        setFormData(prev => ({ ...prev, status: value }))
    }



    const handleSubmit = async (e) => {
        e.preventDefault()

        try {
            setIsLoading(true)
            setAlertInfo(null)


            const dataToSend = {
                name: formData.name,
                email: formData.email,
                status: formData.status,
            }

            const response = await axiosInstance.put(`users/${userId}`, dataToSend)



            if (response.status === 200) {
                setAlertInfo({ type: 'success', message: 'User updated successfully!' })
                setTimeout(() => {
                    router.back()
                }, 2000)
            }
        } catch (error) {
            setAlertInfo({ type: 'error', message: error.response?.data?.message || "An unexpected error occurred" })
            console.error("Error updating user:", error)
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <Card className="max-w-2xl">
            <CardHeader>
                <CardTitle>Edit User</CardTitle>
                <CardDescription>Update the details of the user here.</CardDescription>
            </CardHeader>
            <CardContent>
                {alertInfo && (
                    <Alert variant={alertInfo.type === 'success' ? 'default' : 'destructive'} className="mb-4">
                        <AlertTitle>{alertInfo.type === 'success' ? 'Success' : 'Error'}</AlertTitle>
                        <AlertDescription>{alertInfo.message}</AlertDescription>
                    </Alert>
                )}
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-2">
                        <Label htmlFor="name">Name</Label>
                        <Input
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            required
                        />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                            id="email"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            required
                        />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="status">Status</Label>
                        <Select onValueChange={handleStatusChange} defaultValue={formData.status}>
                            <SelectTrigger>
                                <SelectValue placeholder="Select user status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="inactive">Inactive</SelectItem>
                                <SelectItem value="pending">Pending</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="flex justify-end space-x-2">
                        <Button type="button" variant="outline" onClick={() => router.push("/users")}>
                            Cancel
                        </Button>
                        <Button type="submit" disabled={isLoading}>
                            {isLoading ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Updating User...
                                </>
                            ) : (
                                'Update User'
                            )}
                        </Button>
                    </div>
                </form>
            </CardContent>
        </Card>
    )
}
