//@ts-nocheck
"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import {
    Loader2,
    Building2,
    User,
    Mail,
    Lock,
    Shield,
    ArrowLeft,
    UserPlus,
    CheckCircle,
    AlertCircle,
} from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"

const AddUserPage = () => {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [alertInfo, setAlertInfo] = useState<{ type: "info" | "error" | "success"; message: string }>({
        type: "info",
        message: "",
    })
    const [companies, setCompanies] = useState<{ _id: string; company_name: string }[]>([])
    const [selectedCompany, setSelectedCompany] = useState<string>("")
    const [isLoadingCompanies, setIsLoadingCompanies] = useState<boolean>(false)

    const [formData, setFormData] = useState({
        name: "",
        email: "",
        password: "",
        confirmPassword: "",
        status: "",
        roles: [],
        selectedCompany: "",
    })

    const fetchCompanies = async () => {
        setIsLoadingCompanies(true)
        try {
            const response = await axiosInstance.get("companies")
            setCompanies(response.data.data)
        } catch (error) {
            console.error("Error fetching companies", error)
            setAlertInfo({
                type: "error",
                message: "Failed to load companies. Please try again.",
            })
        } finally {
            setIsLoadingCompanies(false)
        }
    }

    useEffect(() => {
        fetchCompanies()
    }, [])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value,
        })
        // Clear alert when user starts typing
        if (alertInfo.message) {
            setAlertInfo({ type: "info", message: "" })
        }
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (formData.password !== formData.confirmPassword) {
            setAlertInfo({ type: "error", message: "Passwords do not match" })
            return
        }

        if (!selectedCompany) {
            setAlertInfo({ type: "error", message: "Please select a company" })
            return
        }

        setIsLoading(true)
        try {
            const response = await axiosInstance.post("users/register", {
                name: formData.name,
                email: formData.email,
                password: formData.password,
                status: formData.status,
                dashboard: "local_api",
                roles: [],
                company: selectedCompany,
            })

            if (response.status === 201) {
                setAlertInfo({
                    type: "success",
                    message: "User created successfully! Redirecting...",
                })
                setTimeout(() => {
                    router.back()
                }, 1500)
            }
        } catch (error: any) {
            console.error("There was an error creating the user!", error)
            setAlertInfo({
                type: "error",
                message: error.response?.data?.message || "Failed to create user",
            })
        } finally {
            setIsLoading(false)
        }
    }

    const handleCompanyChange = (value: string) => {
        setSelectedCompany(value)
        setFormData((prev) => ({ ...prev, selectedCompany: value }))
    }

    const getAlertIcon = () => {
        switch (alertInfo.type) {
            case "success":
                return <CheckCircle className="h-4 w-4" />
            case "error":
                return <AlertCircle className="h-4 w-4" />
            default:
                return <AlertCircle className="h-4 w-4" />
        }
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4">
      <div className="container mx-auto max-w-2xl">
        {/* Header */}
          <div className="mb-8">
          <Button
              variant="ghost"
              onClick={() => router.back()}
              className="mb-4 text-slate-600 hover:text-slate-900"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Users
          </Button>

          <div className="text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary-100">
              <UserPlus className="h-8 w-8 text-primary-600" />
            </div>
            <h1 className="text-3xl font-bold text-slate-900">Add New User</h1>
            <p className="mt-2 text-slate-600">Create a new user account with company assignment</p>
          </div>
        </div>

          {/* Alert */}
          {alertInfo.message && (
              <Alert
                  className={`mb-6 ${
                      alertInfo.type === "error"
                          ? "border-red-200 bg-red-50 text-red-800"
                          : alertInfo.type === "success"
                              ? "border-green-200 bg-green-50 text-green-800"
                              : "border-primary-200 bg-primary-50 text-primary-800"
                  }`}
              >
            {getAlertIcon()}
                  <AlertDescription className="ml-2">{alertInfo.message}</AlertDescription>
          </Alert>
          )}

          {/* Form Card */}
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-xl text-slate-800">User Information</CardTitle>
            <CardDescription>Fill in the details below to create a new user account</CardDescription>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information Section */}
                <div className="space-y-4">
                <div className="flex items-center gap-2 text-sm font-medium text-slate-700 mb-3">
                  <User className="h-4 w-4" />
                  Personal Information
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-slate-700 font-medium">
                      Full Name
                    </Label>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                          type="text"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          required
                          className="pl-10 border-slate-200 focus:border-primary-500 focus:ring-primary-500"
                          placeholder="Enter full name"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-slate-700 font-medium">
                      Email Address
                    </Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          required
                          className="pl-10 border-slate-200 focus:border-primary-500 focus:ring-primary-500"
                          placeholder="Enter email address"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

                {/* Security Section */}
                <div className="space-y-4">
                <div className="flex items-center gap-2 text-sm font-medium text-slate-700 mb-3">
                  <Lock className="h-4 w-4" />
                  Security & Access
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="password" className="text-slate-700 font-medium">
                      Password
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                          type="password"
                          id="password"
                          name="password"
                          value={formData.password}
                          onChange={handleChange}
                          required
                          className="pl-10 border-slate-200 focus:border-primary-500 focus:ring-primary-500"
                          placeholder="Enter password"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword" className="text-slate-700 font-medium">
                      Confirm Password
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                          type="password"
                          id="confirmPassword"
                          name="confirmPassword"
                          value={formData.confirmPassword}
                          onChange={handleChange}
                          required
                          className="pl-10 border-slate-200 focus:border-primary-500 focus:ring-primary-500"
                          placeholder="Confirm password"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status" className="text-slate-700 font-medium">
                    Account Status
                  </Label>
                  <div className="relative">
                    <Shield className="absolute left-3 top-3 h-4 w-4 text-slate-400 z-10" />
                    <Select
                        name="status"
                        onValueChange={(value) => setFormData((prev) => ({ ...prev, status: value }))}
                    >
                      <SelectTrigger className="pl-10 border-slate-200 focus:border-primary-500 focus:ring-primary-500">
                        <SelectValue placeholder="Select account status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">
                          <div className="flex items-center gap-2">
                            <div className="h-2 w-2 rounded-full bg-green-500"></div>
                            Active
                          </div>
                        </SelectItem>
                        <SelectItem value="inactive">
                          <div className="flex items-center gap-2">
                            <div className="h-2 w-2 rounded-full bg-red-500"></div>
                            Inactive
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <Separator />

                {/* Company Assignment Section */}
                <div className="space-y-4">
                <div className="flex items-center gap-2 text-sm font-medium text-slate-700 mb-3">
                  <Building2 className="h-4 w-4" />
                  Company Assignment
                </div>

                <div className="space-y-2">
                  <Label htmlFor="company" className="text-slate-700 font-medium">
                    Select Company
                  </Label>
                  <div className="relative">
                    <Building2 className="absolute left-3 top-3 h-4 w-4 text-slate-400 z-10" />
                    <Select value={selectedCompany} onValueChange={handleCompanyChange}>
                      <SelectTrigger className="pl-10 border-slate-200 focus:border-primary-500 focus:ring-primary-500">
                        <SelectValue placeholder="Choose a company" />
                      </SelectTrigger>
                      <SelectContent>
                        {isLoadingCompanies ? (
                            <div className="flex items-center justify-center p-4">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Loading companies...</span>
                          </div>
                        ) : companies.length > 0 ? (
                            companies.map((company) => (
                                <SelectItem key={company._id} value={company._id}>
                              <div className="flex items-center gap-2">
                                <Building2 className="h-3 w-3" />
                                  {company.company_name}
                              </div>
                            </SelectItem>
                            ))
                        ) : (
                            <div className="p-4 text-sm text-slate-500 text-center">No companies available</div>
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                    {selectedCompany && (
                        <div className="flex items-center gap-2 mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-green-700 font-medium">
                        Company selected: {companies.find((c) => c._id === selectedCompany)?.company_name}
                      </span>
                    </div>
                    )}
                </div>
              </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 pt-6">
                <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.back()}
                    className="flex-1 border-slate-200 text-slate-600 hover:bg-slate-50"
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading} className="flex-1 bg-primary-600 hover:bg-primary-700 text-white">
                  {isLoading ? (
                      <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating User...
                    </>
                  ) : (
                      <>
                      <UserPlus className="mr-2 h-4 w-4" />
                      Create User
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
    )
}

export default AddUserPage
