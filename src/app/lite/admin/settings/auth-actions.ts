"use server"

import { cookies } from "next/headers"

// Types for API responses
type ApiResponse<T> = {
    success: boolean
    data?: T
    error?: string
}

type TwoFactorSecretResponse = {
    secret: string
    qrCodeUrl: string
}

type TwoFactorStatusResponse = {
    enabled: boolean
}

/**
 * Get the authentication token from cookies for server-side API calls
 */
async function getAuthToken() {
    const cookieStore = await cookies()
    return cookieStore.get("token")?.value
}

/**
 * Generate a new 2FA secret and QR code URL
 */
export async function generateTwoFactorSecret() {
    try {
        const token = await getAuthToken()

        const response = await fetch(`${process.env.API_URL}/api/user/2fa/generate`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
        })

        if (!response.ok) {
            throw new Error(`API error: ${response.status}`)
        }

        const data: ApiResponse<TwoFactorSecretResponse> = await response.json()

        if (!data.success || !data.data) {
            return {
                success: false,
                error: data.error || "Failed to generate 2FA secret",
            }
        }

        return {
            success: true,
            secret: data.data.secret,
            qrCodeUrl: data.data.qrCodeUrl,
        }
    } catch (error) {
        console.error("Failed to generate 2FA secret:", error)
        return {
            success: false,
            error: "Failed to generate 2FA secret",
        }
    }
}

/**
 * Verify a 2FA code and enable 2FA if valid
 */
export async function verifyAndEnableTwoFactor(code: string) {
    try {
        const token = await getAuthToken()

        const response = await fetch(`${process.env.API_URL}/api/user/2fa/verify`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({ code }),
        })

        if (!response.ok) {
            throw new Error(`API error: ${response.status}`)
        }

        const data: ApiResponse<{}> = await response.json()

        if (!data.success) {
            return {
                success: false,
                error: data.error || "Verification failed",
            }
        }

        return { success: true }
    } catch (error) {
        console.error("Failed to verify 2FA code:", error)
        return {
            success: false,
            error: "Failed to verify 2FA code",
        }
    }
}

/**
 * Disable 2FA for the user
 */
export async function disableTwoFactor() {
    try {
        const token = await getAuthToken()

        const response = await fetch(`${process.env.API_URL}/api/user/2fa/disable`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
        })

        if (!response.ok) {
            throw new Error(`API error: ${response.status}`)
        }

        const data: ApiResponse<{}> = await response.json()

        if (!data.success) {
            return {
                success: false,
                error: data.error || "Failed to disable 2FA",
            }
        }

        return { success: true }
    } catch (error) {
        console.error("Failed to disable 2FA:", error)
        return {
            success: false,
            error: "Failed to disable 2FA",
        }
    }
}

/**
 * Change the user's password
 */
export async function changePassword(currentPassword: string, newPassword: string) {
    try {
        const token = await getAuthToken()

        const response = await fetch(`${process.env.API_URL}/api/user/password/change`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({ currentPassword, newPassword }),
        })

        if (!response.ok) {
            throw new Error(`API error: ${response.status}`)
        }

        const data: ApiResponse<{}> = await response.json()

        if (!data.success) {
            return {
                success: false,
                error: data.error || "Failed to change password",
            }
        }

        return { success: true }
    } catch (error) {
        console.error("Failed to change password:", error)
        return {
            success: false,
            error: "Failed to change password",
        }
    }
}

/**
 * Get the current 2FA status for the user
 */
export async function getTwoFactorStatus() {
    try {
        const token = await getAuthToken()

        const response = await fetch(`${process.env.API_URL}/api/user/2fa/status`, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        })

        if (!response.ok) {
            throw new Error(`API error: ${response.status}`)
        }

        const data: ApiResponse<TwoFactorStatusResponse> = await response.json()

        if (!data.success || !data.data) {
            return { enabled: false }
        }

        return { enabled: data.data.enabled }
    } catch (error) {
        console.error("Failed to get 2FA status:", error)
        return { enabled: false }
    }
}
