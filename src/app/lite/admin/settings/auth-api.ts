
// Types for API responses
import axiosInstance from "@/utils/axiosInstance";

type ApiResponse<T> = {
    success: boolean
    data?: T
    error?: string
}

type TwoFactorSecretResponse = {
    secret: string
    qrCodeUrl: string
}

type TwoFactorStatusResponse = {
    enabled: boolean
}

/**
 * Client-side API functions for authentication and user settings
 */

/**
 * Generate a new 2FA secret and QR code URL
 */
export async function generateTwoFactorSecret() {
    try {
        const response = await axiosInstance.post<ApiResponse<TwoFactorSecretResponse>>("/users/2fa/generate")

        if (!response.data.success || !response.data.data) {
            return {
                success: false,
                error: response.data.error || "Failed to generate 2FA secret",
            }
        }

        return {
            success: true,
            secret: response.data.data.secret,
            qrCodeUrl: response.data.data.qrCodeUrl,
        }
    } catch (error: any) {
        console.error("Failed to generate 2FA secret:", error)
        return {
            success: false,
            error: error.response?.data?.error || "Failed to generate 2FA secret",
        }
    }
}

/**
 * Verify a 2FA code and enable 2FA if valid
 */
export async function verifyAndEnableTwoFactor(code: string) {
    try {
        const response = await axiosInstance.post<ApiResponse<{}>>("/users/2fa/verify", { code })

        if (!response.data.success) {
            return {
                success: false,
                error: response.data.error || "Verification failed",
            }
        }

        return { success: true }
    } catch (error: any) {
        console.error("Failed to verify 2FA code:", error)
        return {
            success: false,
            error: error.response?.data?.error || "Failed to verify 2FA code",
        }
    }
}

/**
 * Disable 2FA for the user
 */
export async function disableTwoFactor() {
    try {
        const response = await axiosInstance.post<ApiResponse<{}>>("/users/2fa/disable")

        if (!response.data.success) {
            return {
                success: false,
                error: response.data.error || "Failed to disable 2FA",
            }
        }

        return { success: true }
    } catch (error: any) {
        console.error("Failed to disable 2FA:", error)
        return {
            success: false,
            error: error.response?.data?.error || "Failed to disable 2FA",
        }
    }
}

/**
 * Change the user's password
 */
export async function changePassword(currentPassword: string, newPassword: string) {
    try {
        const response = await axiosInstance.post<ApiResponse<{}>>("/users/password/change", {
            currentPassword,
            newPassword,
        })

        if (!response.data.success) {
            return {
                success: false,
                error: response.data.error || "Failed to change password",
            }
        }

        return { success: true }
    } catch (error: any) {
        console.error("Failed to change password:", error)
        return {
            success: false,
            error: error.response?.data?.error || "Failed to change password",
        }
    }
}

/**
 * Get the current 2FA status for the user
 */
export async function getTwoFactorStatus() {
    try {
        const response = await axiosInstance.get<ApiResponse<TwoFactorStatusResponse>>("/users/2fa/status")

        if (!response.data.success || !response.data.data) {
            return { enabled: false }
        }

        return { enabled: response.data.data.enabled }
    } catch (error: any) {
        console.error("Failed to get 2FA status:", error)
        return { enabled: false }
    }
}
