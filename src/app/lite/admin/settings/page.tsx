"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, Check, Copy, KeyRound, Lock, Shield, Loader2 } from "lucide-react"
import Image from "next/image"
import { PasswordStrengthMeter } from "@/components/password-strength-meter"
import {
    generateTwoFactorSecret,
    verifyAndEnableTwoFactor,
    disableTwoFactor,
    changePassword,
    getTwoFactorStatus,
} from "./auth-api"

export default function SettingsPage() {
    // 2FA states
    const [twoFactorEnabled, setTwoFactorEnabled] = useState(false)
    const [isLoading2FA, setIsLoading2FA] = useState(false)
    const [verificationCode, setVerificationCode] = useState("")
    const [verificationStatus, setVerificationStatus] = useState<"idle" | "success" | "error">("idle")
    const [verificationError, setVerificationError] = useState("")
    const [qrCodeUrl, setQrCodeUrl] = useState("")
    const [secretKey, setSecretKey] = useState("")

    // Password states
    const [currentPassword, setCurrentPassword] = useState("")
    const [newPassword, setNewPassword] = useState("")
    const [confirmPassword, setConfirmPassword] = useState("")
    const [passwordChangeStatus, setPasswordChangeStatus] = useState<"idle" | "success" | "error">("idle")
    const [passwordError, setPasswordError] = useState("")
    const [isLoadingPassword, setIsLoadingPassword] = useState(false)
    const [passwordsMatch, setPasswordsMatch] = useState(true)

    // Load initial 2FA status
    useEffect(() => {
        const loadTwoFactorStatus = async () => {
            try {
                const status = await getTwoFactorStatus()
                setTwoFactorEnabled(status.enabled)

                // If 2FA is not enabled, generate a new secret
                if (!status.enabled) {
                    await handleGenerateSecret()
                }
            } catch (error) {
                console.error("Error loading 2FA status:", error)
            }
        }

        loadTwoFactorStatus()
    }, [])

    // Check if passwords match whenever either password changes
    useEffect(() => {
        if (confirmPassword) {
            setPasswordsMatch(newPassword === confirmPassword)
        } else {
            setPasswordsMatch(true)
        }
    }, [newPassword, confirmPassword])

    const handleGenerateSecret = async () => {
        setIsLoading2FA(true)
        try {
            const result = await generateTwoFactorSecret()
            if (result.success) {
                setQrCodeUrl(result.qrCodeUrl || "")
                setSecretKey(result.secret || "")
            } else {
                console.error("Failed to generate 2FA secret:", result.error)
            }
        } catch (error) {
            console.error("Error generating 2FA secret:", error)
        } finally {
            setIsLoading2FA(false)
        }
    }

    const handleVerifyTwoFactor = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsLoading2FA(true)
        setVerificationStatus("idle")
        setVerificationError("")

        try {
            const result = await verifyAndEnableTwoFactor(verificationCode)
            if (result.success) {
                setTwoFactorEnabled(true)
                setVerificationStatus("success")
            } else {
                setVerificationStatus("error")
                setVerificationError(result.error || "Verification failed")
            }
        } catch (error) {
            console.error("Error verifying 2FA code:", error)
            setVerificationStatus("error")
            setVerificationError("An unexpected error occurred")
        } finally {
            setIsLoading2FA(false)
        }
    }

    const handleDisableTwoFactor = async () => {
        setIsLoading2FA(true)
        try {
            const result = await disableTwoFactor()
            if (result.success) {
                setTwoFactorEnabled(false)
                // Generate a new secret for future setup
                await handleGenerateSecret()
            } else {
                console.error("Failed to disable 2FA:", result.error)
            }
        } catch (error) {
            console.error("Error disabling 2FA:", error)
        } finally {
            setIsLoading2FA(false)
        }
    }

    const handleCopySecretKey = () => {
        navigator.clipboard.writeText(secretKey)
    }

    const handleChangePassword = async (e: React.FormEvent) => {
        e.preventDefault()

        // Client-side validation
        if (newPassword !== confirmPassword) {
            setPasswordChangeStatus("error")
            setPasswordError("Passwords do not match")
            return
        }

        if (newPassword.length < 8) {
            setPasswordChangeStatus("error")
            setPasswordError("Password must be at least 8 characters long")
            return
        }

        setIsLoadingPassword(true)
        setPasswordChangeStatus("idle")
        setPasswordError("")

        try {
            const result = await changePassword(currentPassword, newPassword)
            if (result.success) {
                setPasswordChangeStatus("success")
                setCurrentPassword("")
                setNewPassword("")
                setConfirmPassword("")
            } else {
                setPasswordChangeStatus("error")
                setPasswordError(result.error || "Failed to change password")
            }
        } catch (error) {
            console.error("Error changing password:", error)
            setPasswordChangeStatus("error")
            setPasswordError("An unexpected error occurred")
        } finally {
            setIsLoadingPassword(false)
        }
    }

    return (
        <div className="container mx-auto py-10">
            <h1 className="text-3xl font-bold mb-6">Account Settings</h1>

            <Tabs defaultValue="security" className="w-full">
                <TabsList className="mb-6">
                    <TabsTrigger value="security" className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        Security
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="security" className="space-y-6">
                    {/* Two-Factor Authentication Section */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <KeyRound className="h-5 w-5" />
                                Two-Factor Authentication
                            </CardTitle>
                            <CardDescription>
                                Add an extra layer of security to your account by requiring a verification code from your authenticator
                                app.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {!twoFactorEnabled ? (
                                <div className="space-y-6">
                                    <div className="grid md:grid-cols-2 gap-6">
                                        <div className="space-y-4">
                                            <h3 className="font-medium">1. Scan this QR code</h3>
                                            <p className="text-sm text-muted-foreground">
                                                Use an authenticator app like Google Authenticator, Authy, or Microsoft Authenticator to scan
                                                this QR code.
                                            </p>
                                            {isLoading2FA ? (
                                                <div className="flex items-center justify-center h-[200px] w-[200px] bg-muted rounded-lg">
                                                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                                                </div>
                                            ) : (
                                                <div className="bg-white p-4 rounded-lg inline-block">
                                                    <Image
                                                        unoptimized={true}
                                                        src={qrCodeUrl || "/placeholder.svg?height=200&width=200"}
                                                        alt="QR Code for 2FA"
                                                        width={200}
                                                        height={200}
                                                    />
                                                </div>
                                            )}
                                        </div>

                                        <div className="space-y-4">
                                            <h3 className="font-medium">2. Or enter this setup key</h3>
                                            <p className="text-sm text-muted-foreground">
                                                If you can't scan the QR code, you can manually enter this setup key into your authenticator
                                                app.
                                            </p>
                                            <div className="flex items-center gap-2 bg-muted p-3 rounded-md">
                                                <code className="text-sm font-mono">{secretKey || "Loading..."}</code>
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={handleCopySecretKey}
                                                    className="h-8 w-8"
                                                    disabled={!secretKey || isLoading2FA}
                                                >
                                                    <Copy className="h-4 w-4" />
                                                    <span className="sr-only">Copy secret key</span>
                                                </Button>
                                            </div>

                                            <h3 className="font-medium pt-4">3. Verify setup</h3>
                                            <p className="text-sm text-muted-foreground">
                                                Enter the 6-digit verification code from your authenticator app to complete the setup.
                                            </p>

                                            <form onSubmit={handleVerifyTwoFactor} className="space-y-4">
                                                <div className="space-y-2">
                                                    <Label htmlFor="verification-code">Verification Code</Label>
                                                    <Input
                                                        id="verification-code"
                                                        placeholder="Enter 6-digit code"
                                                        value={verificationCode}
                                                        onChange={(e) => setVerificationCode(e.target.value)}
                                                        maxLength={6}
                                                        className="w-full"
                                                        disabled={isLoading2FA}
                                                    />
                                                </div>

                                                {verificationStatus === "error" && (
                                                    <Alert variant="destructive">
                                                        <AlertCircle className="h-4 w-4" />
                                                        <AlertTitle>Error</AlertTitle>
                                                        <AlertDescription>
                                                            {verificationError || "Invalid verification code. Please try again."}
                                                        </AlertDescription>
                                                    </Alert>
                                                )}

                                                <Button type="submit" disabled={isLoading2FA}>
                                                    {isLoading2FA && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                                    Verify and Enable
                                                </Button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    <Alert className="bg-green-50 text-green-800 border-green-200">
                                        <Check className="h-4 w-4 text-green-600" />
                                        <AlertTitle>Two-factor authentication is enabled</AlertTitle>
                                        <AlertDescription>Your account is now protected with two-factor authentication.</AlertDescription>
                                    </Alert>

                                    <Button variant="destructive" onClick={handleDisableTwoFactor} disabled={isLoading2FA}>
                                        {isLoading2FA && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                        Disable Two-Factor Authentication
                                    </Button>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Change Password Section */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Lock className="h-5 w-5" />
                                Change Password
                            </CardTitle>
                            <CardDescription>Update your password to keep your account secure.</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleChangePassword} className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="current-password">Current Password</Label>
                                    <Input
                                        id="current-password"
                                        type="password"
                                        value={currentPassword}
                                        onChange={(e) => setCurrentPassword(e.target.value)}
                                        disabled={isLoadingPassword}
                                        required
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="new-password">New Password</Label>
                                    <Input
                                        id="new-password"
                                        type="password"
                                        value={newPassword}
                                        onChange={(e) => setNewPassword(e.target.value)}
                                        disabled={isLoadingPassword}
                                        required
                                        className={newPassword.length > 0 && newPassword.length < 8 ? "border-red-500" : ""}
                                    />

                                    {/* Password Strength Meter */}
                                    <PasswordStrengthMeter password={newPassword} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="confirm-password">Confirm New Password</Label>
                                    <Input
                                        id="confirm-password"
                                        type="password"
                                        value={confirmPassword}
                                        onChange={(e) => setConfirmPassword(e.target.value)}
                                        disabled={isLoadingPassword}
                                        required
                                        className={confirmPassword && !passwordsMatch ? "border-red-500" : ""}
                                    />
                                    {confirmPassword && !passwordsMatch && <p className="text-sm text-red-500">Passwords do not match</p>}
                                </div>

                                {passwordChangeStatus === "success" && (
                                    <Alert className="bg-green-50 text-green-800 border-green-200">
                                        <Check className="h-4 w-4 text-green-600" />
                                        <AlertTitle>Password updated</AlertTitle>
                                        <AlertDescription>Your password has been changed successfully.</AlertDescription>
                                    </Alert>
                                )}

                                {passwordChangeStatus === "error" && (
                                    <Alert variant="destructive">
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertTitle>Error</AlertTitle>
                                        <AlertDescription>{passwordError}</AlertDescription>
                                    </Alert>
                                )}

                                <Button
                                    type="submit"
                                    disabled={
                                        isLoadingPassword ||
                                        newPassword.length < 8 ||
                                        !passwordsMatch ||
                                        !currentPassword ||
                                        !newPassword ||
                                        !confirmPassword
                                    }
                                >
                                    {isLoadingPassword && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                    Update Password
                                </Button>
                            </form>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    )
}
