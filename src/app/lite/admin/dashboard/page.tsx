// pages/dashboard.tsx
'use client'
import {useEffect, useState} from 'react';
import {useRouter} from 'next/navigation';
import axiosInstance from "@/utils/axiosInstance";
import {Badge} from "@/components/ui/badge";
import {CompanyPipelineChart} from "@/app/lite/admin/dashboard/charts/companyChart";
import CardComparisonChart from "@/app/lite/admin/dashboard/charts/virtualVsPhysical";
import {LoadingOverlay} from "@/components/LoadingOverlay";

const Dashboard = () => {
    const router = useRouter();
    const [loading, setLoading] = useState(true);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [user, setUser] = useState<any>(null);  // To store the user details
    const [roleId, setRoleId] = useState<string | null>(null);



    useEffect(() => {
        const checkAuth = async () => {
            try {
                const response = await axiosInstance.get('users/me', {withCredentials: true});
                if (response.data) {
                    // Set user details and role ID
                    setUser(response.data);
                    // @ts-ignore
                    setRoleId(response.data.roles[0]);
                    setIsAuthenticated(true);
                } else {
                    router.push('/login'); // Redirect to login if not authenticated
                }
            } catch (error) {
                console.error(error);
                router.push('/login'); // Redirect to login on error
            } finally {
                setLoading(false);
            }
        };

        checkAuth();
    }, [router]);

    if (loading) {
        return <LoadingOverlay/>;
    }

    if (!isAuthenticated) {
        return null; // Prevent rendering until authentication is verified
    }

    // @ts-ignore
    return (
        <>
            <header className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Welcome to Your Dashboard {user.name}</h1>
                <div className="mt-2 text-gray-600 dark:text-gray-400 flex gap-2">Your roles
                    <div className="gap-2">
                        {user?.roles?.map((role, index: number) => (
                            <span key={index}>
                                <Badge variant="outline" className="text-sm mx-2">
                                    {role.name}
                                </Badge>
                            </span>
                        ))}
                    </div>
                </div>
            </header>


            <div className="grid grid-cols-3 gap-3">
                <CompanyPipelineChart/>
                <CardComparisonChart className="col-span-2"/>
                <div className="flex items-center justify-center mt-5">


                </div>
            </div>
        </>
    );
};

export default Dashboard;
