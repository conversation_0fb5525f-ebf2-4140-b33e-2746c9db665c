//@ts-nocheck
"use client"

import * as React from "react";
import { Label, Pie, PieChart, Sector } from "recharts";
import { PieSectorDataItem } from "recharts/types/polar/Pie";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
    ChartConfig,
    ChartContainer,
    ChartStyle,
    ChartTooltip,
    ChartTooltipContent,
} from "@/components/ui/chart";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import axiosInstance from "@/utils/axiosInstance"



const chartConfig = {
    pipeline: {
        label: "Pipeline",
        color: "hsl(var(--chart-1))",
    },
    cip: {
        label: "CIP",
        color: "hsl(var(--chart-2))",
    },
} satisfies ChartConfig;

export function CompanyPipelineChart() {
    const id = "pie-interactive";
    const [activeMonth, setActiveMonth] = React.useState("pipeline"); // Default to "pipeline"
    const [chartData, setChartData] = React.useState([
        { month: "pipeline", desktop: 0, fill: "hsl(var(--chart-1))" },
        { month: "cip", desktop: 0, fill: "hsl(var(--chart-2))" },
    ]);

    React.useEffect(() => {
        async function fetchData() {
            try {
                const response = await axiosInstance.get("company");
                const companies = response.data.data;
                const cardProgramme = response.data.programme;

                // Filter unique companies (those not in cardProgramme)
                const cardProgrammeCompanyIds = cardProgramme.map(program => program.company);
                const uniqueCompanies = companies.filter(
                    company => !cardProgrammeCompanyIds.includes(company._id)
                );

                // Update chart data with actual counts
                setChartData([
                    { month: "pipeline", desktop: uniqueCompanies.length, fill: "hsl(var(--chart-1))" },
                    { month: "cip", desktop: cardProgramme.length, fill: "hsl(var(--chart-2))" },
                ]);
            } catch (error) {
                console.error("Failed to fetch companies:", error);
            }
        }

        fetchData();
    }, []);

    const activeIndex = React.useMemo(
        () => chartData.findIndex((item) => item.month === activeMonth),
        [activeMonth, chartData]
    );
    const months = React.useMemo(() => chartData.map((item) => item.month), [chartData]);

    return (
        <Card data-chart={id} className="flex flex-col">
            <ChartStyle id={id} config={chartConfig} />
            <CardHeader className="flex-row items-start space-y-0 pb-0">
                <div className="grid gap-1">
                    <CardTitle>Card Program Report</CardTitle>
                    <CardDescription>Issuing Pipeline vs CIP Created</CardDescription>
                </div>
                <Select value={activeMonth} onValueChange={setActiveMonth}>
                    <SelectTrigger className="ml-auto h-7 w-[130px] rounded-lg pl-2.5" aria-label="Select a value">
                        <SelectValue placeholder="Select month" />
                    </SelectTrigger>
                    <SelectContent align="end" className="rounded-xl">
                        {months.map((key) => {
                            const config = chartConfig[key as keyof typeof chartConfig];
                            if (!config) return null;
                            return (
                                <SelectItem key={key} value={key} className="rounded-lg [&_span]:flex">
                                    <div className="flex items-center gap-2 text-xs">
                                        <span
                                            className="flex h-3 w-3 shrink-0 rounded-sm"
                                            style={{ backgroundColor: `var(--color-${key})` }}
                                        />
                                        {config?.label}
                                    </div>
                                </SelectItem>
                            );
                        })}
                    </SelectContent>
                </Select>
            </CardHeader>
            <CardContent className="flex flex-1 justify-center pb-0">
                <ChartContainer
                    id={id}
                    config={chartConfig}
                    className="mx-auto aspect-square w-full max-w-[300px]"
                >
                    <PieChart>
                        <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
                        <Pie
                            data={chartData}
                            dataKey="desktop"
                            nameKey="month"
                            innerRadius={60}
                            strokeWidth={5}
                            activeIndex={activeIndex}
                            activeShape={({ outerRadius = 0, ...props }: PieSectorDataItem) => (
                                <g>
                                    <Sector {...props} outerRadius={outerRadius + 10} />
                                    <Sector {...props} outerRadius={outerRadius + 25} innerRadius={outerRadius + 12} />
                                </g>
                            )}
                        >
                            <Label
                                content={({ viewBox }) => {
                                    if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                                        return (
                                            <text
                                                x={viewBox.cx}
                                                y={viewBox.cy}
                                                textAnchor="middle"
                                                dominantBaseline="middle"
                                            >
                                                <tspan
                                                    x={viewBox.cx}
                                                    y={viewBox.cy}
                                                    className="fill-foreground text-3xl font-bold"
                                                >
                                                    {chartData[activeIndex].desktop.toLocaleString()}
                                                </tspan>
                                                <tspan
                                                    x={viewBox.cx}
                                                    y={(viewBox.cy || 0) + 24}
                                                    className="fill-muted-foreground"
                                                >
                                                    Companies
                                                </tspan>
                                            </text>
                                        );
                                    }
                                }}
                            />
                        </Pie>
                    </PieChart>
                </ChartContainer>
            </CardContent>
        </Card>
    );
}
