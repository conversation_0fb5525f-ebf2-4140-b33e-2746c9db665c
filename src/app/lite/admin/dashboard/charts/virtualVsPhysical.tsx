//@ts-nocheck
"use client"

import * as React from "react"
import {<PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, XAxis, <PERSON><PERSON><PERSON><PERSON>} from "recharts"

import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card"
import {
    ChartConfig,
    ChartContainer,
    ChartStyle,
    ChartTooltip,
    ChartTooltipContent,
} from "@/components/ui/chart"

// Generate sample data for one month
const generateData = () => {
    const data = []
    const currentDate = new Date()
    currentDate.setDate(1) // Start from the first day of the current month

    for (let i = 0; i < 30; i++) {
        data.push({
            date: currentDate.toISOString().split('T')[0],
            virtual: Math.floor(Math.random() * 100) + 50,
            physical: Math.floor(Math.random() * 50) + 20,
        })
        currentDate.setDate(currentDate.getDate() + 1)
    }

    return data
}

const data = generateData()

const chartConfig = {
    virtual: {
        label: "Virtual Cards",
        color: "hsl(var(--chart-1))",
    },
    physical: {
        label: "Physical Cards",
        color: "hsl(var(--chart-2))",
    },
} satisfies ChartConfig

interface CardComparisonChartProps {
    className?: string
}

export default function CardComparisonChart({className}: CardComparisonChartProps) {
    const id = "card-comparison-chart"

    return (
        <Card data-chart={id} className={className}>
            <ChartStyle id={id} config={chartConfig}/>
            <CardHeader>
                <CardTitle>Card Issuance Comparison</CardTitle>
                <CardDescription>Virtual vs Physical Cards (Daily for One Month)(Dummy Report)</CardDescription>
            </CardHeader>
            <CardContent className="pb-4">
                <ChartContainer
                    id={id}
                    config={chartConfig}
                    className="h-[400px] w-full"
                >
                    <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={data}>
                            <XAxis
                                dataKey="date"
                                stroke="#888888"
                                fontSize={12}
                                tickLine={false}
                                axisLine={false}
                                tickFormatter={(value) => new Date(value).getDate().toString()}
                            />
                            <YAxis
                                stroke="#888888"
                                fontSize={12}
                                tickLine={false}
                                axisLine={false}
                                tickFormatter={(value) => `${value}`}
                            />
                            <ChartTooltip
                                cursor={false}
                                content={<ChartTooltipContent/>}
                            />
                            <Bar dataKey="virtual" fill="var(--color-virtual)" radius={[4, 4, 0, 0]}/>
                            <Bar dataKey="physical" fill="var(--color-physical)" radius={[4, 4, 0, 0]}/>
                        </BarChart>
                    </ResponsiveContainer>
                </ChartContainer>
            </CardContent>
        </Card>
    )
}