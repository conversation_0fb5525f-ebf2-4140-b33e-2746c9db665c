//@ts-nocheck
"use client"

import { useEffect, useState } from "react"
import axiosInstance from "@/utils/axiosInstance"
import Link from "next/link"
import { formatDate } from "@/utils/helpers"
import { DataTable } from "@/components/data-table"
import type { CardProgram, Company, Role } from "@types/types"
import { useAppSelector } from "@/store/hooks"
import { CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import DataExporter from "@/components/DataExporter"

// Define the Role type
export default function StripedCompanyTable() {
    const [allCompanies, setAllCompanies] = useState<Company[]>([])
    const [cardProgramme, setCardProgramme] = useState<CardProgram[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [searchTerm, setSearchTerm] = useState<string>("")
    const [activeTab, setActiveTab] = useState<string>("pending")

    useEffect(() => {
        async function fetchData() {
            try {
                const response = await axiosInstance.get("company")
                const companies = response.data.data
                const cardProgramme = response.data.programme

                setAllCompanies(companies)
                setCardProgramme(cardProgramme)
            } catch (error) {
                setError("Failed to fetch companies")
            } finally {
                setLoading(false)
            }
        }

        fetchData()
    }, [])

    // Combine all permissions
    const user = useAppSelector((state) => state.user.user)
    const [roles, setRoles] = useState<Role[]>([])

    useEffect(() => {
        setRoles(user.roles)
    }, [])

    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions)) // Remove duplicates
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        // If roles array is empty, allow all permissions
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }

    const userHasRequestPermission = hasPermission(roles, "Issuing Pipeline_Create CIP")
    const userHasViewPermission = hasPermission(roles, "Issuing Pipeline_View")

    // Filter companies based on tab
    const getFilteredCompanies = (tab: string) => {
        const cardProgrammeCompanyIds = cardProgramme.map((program) => program.company)

        switch (tab) {
            case "pending":
                // Companies not in cardProgramme (don't have CIP)
                return allCompanies.filter((company) => !cardProgrammeCompanyIds.includes(company._id) && company.status !=="disabled")
            case "active":
                // Companies that have CIP
                return allCompanies.filter((company) => cardProgrammeCompanyIds.includes(company._id))
            case "disabled":
                // Companies with disabled status
                return allCompanies.filter((company) => company.status?.toLowerCase() === "disabled")
            default:
                return []
        }
    }

    const companies = getFilteredCompanies(activeTab)

    // Define columns for the DataTable
    const columns = [
        {
            header: "Date Created",
            accessorKey: "created_at" as const,
            cell: (company: Company) => formatDate(company.created_at),
        },
        {
            header: "Company Name",
            accessorKey: "company_name" as const,
            cell: (company: Company) => (
                <Link href={`companies/${company._id}`} className="font-bold hover:underline">
          {company.company_name}
        </Link>
            ),
        },
        {
            header: "Company Number",
            accessorKey: "company_number" as const,
        },
        {
            header: "Company Email",
            accessorKey: "company_email" as const,
        },
    ]

    // Define status filter options (placeholder - adjust based on your actual data)
    const filterOptions = {
        key: "status" as keyof Company,
        options: [
            { label: "All", value: null },
            { label: "Active", value: "Active" },
            { label: "Inactive", value: "Inactive" },
            { label: "Pending", value: "Pending" },
        ],
    }

    const data = companies.map((row, index) => ({
        id: index + 1,
        name: row.company_name,
        email: row.company_email,
        company_phone: row.company_phone,
        company_website: row.company_website,
        created_at: formatDate(row.created_at),
    }))

    const filteredCompanies = companies.filter((company) => {
        if (!searchTerm.trim()) return true
        const searchLower = searchTerm.toLowerCase()
        // Search across multiple fields
        return (
            company.company_name?.toLowerCase().includes(searchLower) ||
            company.company_email?.toLowerCase().includes(searchLower) ||
            company.company_number?.toLowerCase().includes(searchLower) ||
            company.admin_name?.toLowerCase().includes(searchLower) ||
            company.admin_email?.toLowerCase().includes(searchLower) ||
            company.company_phone?.toLowerCase().includes(searchLower) ||
            company.contact_name?.toLowerCase().includes(searchLower)
        )
    })

    // Get counts for each tab
    const pendingCount = getFilteredCompanies("pending").length
    const activeCount = getFilteredCompanies("active").length
    const disabledCount = getFilteredCompanies("disabled").length

    const getTabTitle = (tab: string) => {
        switch (tab) {
            case "pending":
                return "Pending Companies"
            case "active":
                return "Active Companies"
            case "disabled":
                return "Disabled Companies"
            default:
                return "Companies"
        }
    }

    const getTabDescription = (tab: string) => {
        switch (tab) {
            case "pending":
                return `Companies without CIP: ${pendingCount}`
            case "active":
                return `Companies with CIP: ${activeCount}`
            case "disabled":
                return `Disabled companies: ${disabledCount}`
            default:
                return ""
        }
    }

    return (
        <>
      <CardHeader className="px-6 py-5 flex-row items-center justify-between bg-background">
        <div>
          <CardTitle className="text-2xl font-bold">Issuing Pipeline</CardTitle>
          <CardDescription>
            Total companies: {allCompanies.length} | Pending: {pendingCount} | Active: {activeCount} | Disabled:{" "}
              {disabledCount}
          </CardDescription>
        </div>
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 bg-white"
            />
          </div>
          <DataExporter
              data={data}
              filename={`issuing_pipeline_${activeTab}`}
              title={`${getTabTitle(activeTab)} Report`}
          />
        </div>
      </CardHeader>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 mx-6 mb-4">
          <TabsTrigger value="pending" className="flex items-center gap-2">
            Pending
            <span className="bg-orange-100 text-orange-800 text-xs font-medium px-2 py-1 rounded-full">
              {pendingCount}
            </span>
          </TabsTrigger>
          <TabsTrigger value="active" className="flex items-center gap-2">
            Approved
            <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
              {activeCount}
            </span>
          </TabsTrigger>
          <TabsTrigger value="disabled" className="flex items-center gap-2">
            Declined
            <span className="bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded-full">{disabledCount}</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="mt-0">
          <DataTable
              data={filteredCompanies}
              columns={columns}
              title={getTabTitle("pending")}
              description={getTabDescription("pending")}
              loading={loading}
              error={error}
              filterOptions={filterOptions}
              enableSelection={true}
              enableExport={true}
              getRowId={(company) => company._id}
              onRowClick={(company) => {
                  if (userHasViewPermission) {
                      window.location.href = `companies/${company._id}`
                  }
              }}
          />
        </TabsContent>

        <TabsContent value="active" className="mt-0">
          <DataTable
              data={filteredCompanies}
              columns={columns}
              title={getTabTitle("active")}
              description={getTabDescription("active")}
              loading={loading}
              error={error}
              filterOptions={filterOptions}
              enableSelection={true}
              enableExport={true}
              getRowId={(company) => company._id}
              onRowClick={(company) => {
                  if (userHasViewPermission) {
                      window.location.href = `companies/${company._id}`
                  }
              }}
          />
        </TabsContent>

        <TabsContent value="disabled" className="mt-0">
          <DataTable
              data={filteredCompanies}
              columns={columns}
              title={getTabTitle("disabled")}
              description={getTabDescription("disabled")}
              loading={loading}
              error={error}
              filterOptions={filterOptions}
              enableSelection={true}
              enableExport={true}
              getRowId={(company) => company._id}
              onRowClick={(company) => {
                  if (userHasViewPermission) {
                      window.location.href = `companies/${company._id}`
                  }
              }}
          />
        </TabsContent>
      </Tabs>
    </>
    )
}
