// @ts-nocheck
'use client'

import React, {useEffect, useState} from 'react'
import {CustomFlowbiteTheme, Flowbite, Tabs} from 'flowbite-react'
import {Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle} from '@/components/ui/card'
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from '@/components/ui/table'
import axiosInstance from '@/utils/axiosInstance'
import {ChevronDown, ChevronUp, Edit2Icon, ExternalLink, Loader2, Plus} from 'lucide-react'
import Link from "next/link"
import {Button} from "@/components/ui/button"
import {SkeletonTable} from "@/components/SkeletonTable";
import {formatDate} from "@/utils/helpers";
import {Badge} from "@/components/ui/badge";

export default function Company({params}: { params: { id: string } }) {
    const [companyData, setCompanyData] = useState<null | any>(null);
    const [cip, setCip] = useState<null | any>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [sortConfig, setSortConfig] = useState({key: null, direction: 'ascending'});

    useEffect(() => {
        async function fetchCompanyDetails() {
            try {
                const response = await axiosInstance.get(`/company/${params.id}`);
                setCompanyData(response.data.company);
                setCip(response.data.cip);
            } catch (error: any) {
                setError(error.response?.data.message || 'Failed to fetch company data');
            } finally {
                setLoading(false);
            }
        }

        fetchCompanyDetails();
    }, [params.id]);

    const customTheme: CustomFlowbiteTheme = {
        tabs: {
            base: "flex flex-col gap-2",
            tablist: {
                base: "flex text-center",
                styles: {
                    underline: "-mb-px flex-wrap border-b border-gray-200 dark:border-gray-700",
                },
                tabitem: {
                    base: "flex items-center justify-center rounded-t-lg p-4 text-sm font-medium first:ml-0 focus:outline-none disabled:cursor-not-allowed disabled:text-gray-400 disabled:dark:text-gray-500",
                    styles: {
                        underline: {
                            base: "rounded-t-lg",
                            active: {
                                on: "active rounded-t-lg border-b-2 border-primary text-primary dark:border-primary dark:text-primary",
                                off: "border-b-2 border-transparent text-gray-500 hover:border-gray-300 hover:text-gray dark:text-gray-400 dark:hover:text-gray-300"
                            }
                        },
                    },
                    icon: "mr-2 h-5 w-5"
                }
            },
            tabitemcontainer: {
                base: "",
                styles: {
                    default: "",
                    underline: "",
                    pills: "",
                    fullWidth: ""
                }
            },
            tabpanel: "py-3"
        }
    }

    if (loading) {
        return (
            <Card className="w-full max-w-md mx-auto mt-8">
                <CardContent className="flex flex-col items-center justify-center py-10">
                    <Loader2 className="h-10 w-10 animate-spin text-primary"/>
                    <p className="mt-4 text-lg font-medium text-muted-foreground">Loading Company data...</p>
                </CardContent>
            </Card>
        )
    }

    if (!companyData) {
        return (
            <Card className="w-full max-w-md mx-auto mt-8">
                <CardContent className="py-10">
                    <p className="text-center text-lg font-medium text-muted-foreground">Company Data Not Found.</p>
                </CardContent>
            </Card>
        )
    }

    const requestSort = (key) => {
        let direction = 'ascending';
        if (sortConfig.key === key && sortConfig.direction === 'ascending') {
            direction = 'descending';
        }
        setSortConfig({key, direction});
    };

    const getSortedPrograms = (programs) => {
        if (!sortConfig.key) return programs;

        return [...programs].sort((a, b) => {
            const aValue = sortConfig.key.split('.').reduce((obj, key) => obj[key], a);
            const bValue = sortConfig.key.split('.').reduce((obj, key) => obj[key], b);

            if (aValue < bValue) {
                return sortConfig.direction === 'ascending' ? -1 : 1;
            }
            if (aValue > bValue) {
                return sortConfig.direction === 'ascending' ? 1 : -1;
            }
            return 0;
        });
    };

    const sortedPrograms = getSortedPrograms(cip);

    const SortIcon = ({columnKey}) => {
        if (sortConfig.key !== columnKey) {
            return <ChevronUp className="ml-1 h-4 w-4 text-muted-foreground"/>;
        }
        return sortConfig.direction === 'ascending' ? (
            <ChevronUp className="ml-1 h-4 w-4"/>
        ) : (
            <ChevronDown className="ml-1 h-4 w-4"/>
        );
    };
    return (
        <Flowbite theme={{theme: customTheme}}>
            <div>
                <CardHeader className="px-6 py-5 flex-row items-center justify-between bg-background">
                    <div>
                        <CardTitle className="text-2xl font-bold">Company [ID#{params.id}]</CardTitle>
                    </div>
                    <div className="flex items-center justify-center align-middle space-x-2">
                        <div className="relative">
                            <Link href={`${companyData._id}/edit`} passHref>
                                <Button size="sm" className="mr-2"> <Edit2Icon/> Edit Details</Button>
                            </Link>
                        </div>
                        <Link href={`${companyData._id}/cip/create`} passHref>
                            <Button size="sm" className="mr-2">Create Program</Button>
                        </Link>

                    </div>
                </CardHeader>

                <div className="w-full mt-5">
                    <div className="flex flex-col gap-4 md:flex-row md:gap-6">
                        <div className="w-full md:w-1/3">
                            <Card>
                                <CardHeader>
                                    <div>
                                        <CardTitle>{companyData.company_name}</CardTitle>
                                        <CardDescription>Company In Issuing Pipeline Program</CardDescription>
                                    </div>
                                </CardHeader>

                                <CardContent>

                                    <Tabs aria-label="Tabs with underline" variant="underline">
                                        <Tabs.Item active title="Business Info">
                                            <Table>
                                                <TableRow className="bg-background">
                                                    <TableHead className="font-semibold text-primary cursor-pointer"
                                                               colSpan={2}>
                                                        Company Information
                                                    </TableHead>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Company
                                                        Name</TableCell>
                                                    <TableCell>{companyData.company_name}</TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Industry</TableCell>
                                                    <TableCell>{companyData.company_industry}</TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Registration
                                                        Number</TableCell>
                                                    <TableCell>{companyData.company_number}</TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Phone
                                                        Number</TableCell>
                                                    <TableCell>{companyData.company_phone}</TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Registration
                                                        Date</TableCell>
                                                    <TableCell>{new Date(companyData.registration_date).toLocaleDateString()}</TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Address</TableCell>
                                                    <TableCell>{companyData.company_address}</TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Email</TableCell>
                                                    <TableCell>{companyData.company_email}</TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Country of
                                                        Incorporation</TableCell>
                                                    <TableCell>{companyData.country_of_incorporation}</TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Website</TableCell>
                                                    <TableCell>{companyData.company_website}</TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Type of
                                                        Business</TableCell>
                                                    <TableCell>{companyData.type_of_business}</TableCell>
                                                </TableRow>
                                            </Table>
                                        </Tabs.Item>

                                        <Tabs.Item title="Company Contact">
                                            <Table>
                                                <TableHeader>
                                                    <TableRow className="bg-background">
                                                        <TableHead className="font-semibold text-primary cursor-pointer"
                                                                   colSpan={2}>
                                                            Company Contact Info
                                                        </TableHead>
                                                    </TableRow>
                                                </TableHeader>
                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Contact
                                                        Name</TableCell>
                                                    <TableCell>{companyData.contact_name}</TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Contact
                                                        Role</TableCell>
                                                    <TableCell>{companyData.contact_role}</TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Email
                                                        Address</TableCell>
                                                    <TableCell>{companyData.company_email}</TableCell>
                                                </TableRow>
                                            </Table>
                                        </Tabs.Item>
                                    </Tabs>
                                </CardContent>
                            </Card>
                            <Card className="mt-4">
                                <CardHeader>
                                    <div>
                                        <CardTitle>ADMIN INFORMATION</CardTitle>
                                        <CardDescription>Company admin details</CardDescription>
                                    </div>
                                </CardHeader>

                                <CardContent>
                                    <Table>
                                        <TableBody>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Admin Name</TableCell>
                                                <TableCell>{companyData.admin_name}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Admin Role</TableCell>
                                                <TableCell>{companyData.admin_role}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Admin Email</TableCell>
                                                <TableCell>{companyData.admin_email}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Admin Phone</TableCell>
                                                <TableCell>{companyData.admin_phone}</TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                </CardContent>
                            </Card>
                        </div>


                        <div className="w-full md:w-2/3">

                            <Card className="w-full">
                                <CardHeader className="flex flex-row items-center justify-between">
                                    <div>
                                        <CardTitle>CIP</CardTitle>
                                        <CardDescription>CIP Programme Products</CardDescription>
                                    </div>
                                    <Link href={`${companyData._id}/cip/create`} passHref>
                                        <Button size="sm">
                                            <Plus className="mr-2 h-4 w-4"/>
                                            Add Product
                                        </Button>
                                    </Link>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
                                        {sortedPrograms.map((program) => (
                                            <Card key={program._id} className="flex flex-col">
                                                <CardHeader>
                                                    <CardTitle
                                                        className="text-lg">{program.cardScheme.scheme_name}</CardTitle>
                                                    <CardDescription>{program.cardProgrammeType.programme_type}</CardDescription>
                                                </CardHeader>
                                                <CardContent className="flex-grow">
                                                    <div className="space-y-2">
                                                        <div className="flex justify-between">
                                                            <span className="text-sm  font-bold">BIN Type:</span>
                                                            <span className="text-sm">{program.binType.type}</span>
                                                        </div>

                                                        <div className="flex justify-between">
                                                            <span
                                                                className="text-sm  font-bold">Programme Manager:</span>
                                                            <span
                                                                className="text-sm">{program.programManagerType}</span>
                                                        </div>

                                                        <div className="flex justify-between">
                                                            <span className="text-sm  font-bold">BIN Code</span>
                                                            <span className="text-sm">{program.binRange}</span>
                                                        </div>

                                                        {program.productDetails.map((item) => {
                                                            return (
                                                                <>
<div className="border rounded p-2">


                                                                    <div className="flex justify-between">
                                                                        <span
                                                                            className="text-sm  font-bold">Currency Code:</span>
                                                                        <span
                                                                            className="text-sm">{item.productCcy?.currency_code}</span>
                                                                    </div>

                                                                    <div className="flex justify-between">
                                                                        <span
                                                                            className="text-sm  font-bold">Version Name:</span>
                                                                        <span
                                                                            className="text-sm">{item.productVersionName?.version_name}</span>
                                                                    </div>
</div>
                                                                </>
                                                            )
                                                        })}


                                                    </div>
                                                </CardContent>
                                                <CardFooter className="justify-end">
                                                    <Link href={`/lite/admin/programmes/${program._id}`} passHref>
                                                        <Button variant="outline" size="sm">
                                                            View Details
                                                            <ExternalLink className="ml-2 h-4 w-4"/>
                                                        </Button>
                                                    </Link>
                                                </CardFooter>
                                            </Card>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="mt-5">
                                <CardHeader>
                                    <div>
                                        <CardTitle>CARD ISSUING PROGRAM</CardTitle>
                                        <CardDescription>Company program information</CardDescription>
                                    </div>
                                </CardHeader>

                                <CardContent>

                                    <Table>
                                        <TableHeader>
                                            <TableRow className="bg-background">
                                                <TableHead className="font-semibold text-primary cursor-pointer"
                                                           colSpan={2}>
                                                    Card Program Information
                                                </TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Card Usage</TableCell>
                                                <TableCell>{companyData.card_usage}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Cardholder
                                                    Groups</TableCell>
                                                <TableCell>{companyData.cardholder_groups}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Fund Loading</TableCell>
                                                <TableCell>{companyData.fund_loading}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Business Sector</TableCell>
                                                <TableCell>{companyData.business_sector}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Regions</TableCell>
                                                <TableCell>{companyData.regions}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Countries</TableCell>
                                                <TableCell>{companyData.countries}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Business
                                                    Purpose</TableCell>
                                                <TableCell>{companyData.business_purpose}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Card User
                                                    Groups</TableCell>
                                                <TableCell>{companyData.card_user_groups}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Number of Cards</TableCell>
                                                <TableCell>{companyData.number_of_cards}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Monthly Loading
                                                    Value</TableCell>
                                                <TableCell>{companyData.monthly_loading_value}</TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                </CardContent>
                            </Card>


                        </div>
                    </div>
                </div>
            </div>
        </Flowbite>
    )
}

