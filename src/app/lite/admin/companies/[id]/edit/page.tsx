//@ts-nocheck
"use client"

import type React from "react"
import {use<PERSON><PERSON>back, useEffect, useState} from "react"
import {But<PERSON>} from "@/components/ui/button"
import {Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle} from "@/components/ui/card"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Textarea} from "@/components/ui/textarea"
import {Alert, AlertDescription, AlertTitle} from "@/components/ui/alert"
import {Skeleton} from "@/components/ui/skeleton"
import {Checkbox} from "@/components/ui/checkbox"
import {Ta<PERSON>, TabsContent, TabsList, TabsTrigger} from "@/components/ui/tabs"
import {
    AlertCircle,
    ArrowLeft,
    Building,
    FileText,
    Globe,
    Info,
    Loader2,
    Mail,
    MapPin,
    Phone,
    RefreshCw,
    Save,
    X,
} from "lucide-react"
import PhoneInput, {isValidPhoneNumber} from "react-phone-number-input"
import "react-phone-number-input/style.css"
import {use<PERSON>outer} from "next/navigation"
import {countries} from "@/utils/data";
import axiosInstance from "@/utils/axiosInstance";



type Address = {
    street: string
    building_number: string
    apartment_number: string
    city: string
    state: string
    postal_code: string
    country: string
}

type FormData = {
    company_name: string
    company_industry: string
    company_number: string
    company_phone: string
    registration_date: string
    contact_name: string
    contact_role: string
    company_email: string
    country_of_incorporation: string
    company_website: string
    type_of_business: string
    card_usage: string
    cardholder_groups: string
    fund_loading: string
    business_sector: string
    regions: string
    countries: string
    business_purpose: string
    card_user_groups: string
    number_of_cards: string
    monthly_loading_value: string
    admin_name: string
    admin_role: string
    admin_email: string
    admin_phone: string
    registered_address: Address
    operational_address: Address
}

type ValidationErrors = {
    [key in keyof FormData]?: string
} & {
    registered_address?: {
        [key in keyof Address]?: string
    }
    operational_address?: {
        [key in keyof Address]?: string
    }
}


const getCountryName = (code: string) => {
    const country = countries.find((c) => c.code === code)
    return country ? country.name : code
}

export default function EditCompanyPage({params}: { params: { id: string } }) {
    const [formData, setFormData] = useState<FormData | null>(null)
    const [originalData, setOriginalData] = useState<FormData | null>(null)
    const [loading, setLoading] = useState(true)
    const [saving, setSaving] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [alert, setAlert] = useState<{ type: "success" | "error"; message: string } | null>(null)
    const [errors, setErrors] = useState<ValidationErrors>({})
    const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set())
    const [useSameAddress, setUseSameAddress] = useState(false)
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
    const [companyData, setCompanyData] = useState<null | any>(null)
    const router = useRouter()

    const formatDateForAPI = (dateString: string): string => {
        if (!dateString) return ""
        return new Date(dateString + "T00:00:00.000Z").toISOString()
    }
    // Helper function to format date from API (convert ISO to YYYY-MM-DD)
    const formatDateFromAPI = (isoString: string): string => {
        if (!isoString) return ""
        return isoString.split("T")[0]
    }
    // Load company data
    const fetchCompanyData = useCallback(async () => {
        try {
            setLoading(true)
            setError(null)


            const response = await axiosInstance.get(`/company/${params.id}`)
            setCompanyData(response.data.company)
            const companyData = response.data.company

            // Transform data to match form structure
            const transformedData: FormData = {
                company_name: companyData.company_name || "",
                company_industry: companyData.company_industry || "",
                company_number: companyData.company_number || "",
                company_phone: companyData.company_phone || "",
                registration_date: formatDateFromAPI(companyData.registration_date) || "",
                contact_name: companyData.contact_name || "",
                contact_role: companyData.contact_role || "",
                company_email: companyData.company_email || "",
                country_of_incorporation: companyData.country_of_incorporation || "",
                company_website: companyData.company_website || "",
                type_of_business: companyData.type_of_business || "",
                card_usage: companyData.card_usage || "",
                cardholder_groups: companyData.cardholder_groups || "",
                fund_loading: companyData.fund_loading || "",
                business_sector: companyData.business_sector || "",
                regions: companyData.regions || "",
                countries: companyData.countries || "",
                business_purpose: companyData.business_purpose || "",
                card_user_groups: companyData.card_user_groups || "",
                number_of_cards: companyData.number_of_cards || "",
                monthly_loading_value: companyData.monthly_loading_value || "",
                admin_name: companyData.admin_name || "",
                admin_role: companyData.admin_role || "",
                admin_email: companyData.admin_email || "",
                admin_phone: companyData.admin_phone || "",
                registered_address: companyData.registered_address || {
                    street: "",
                    building_number: "",
                    apartment_number: "",
                    city: "",
                    state: "",
                    postal_code: "",
                    country: "",
                },
                operational_address: companyData.operational_address || {
                    street: "",
                    building_number: "",
                    apartment_number: "",
                    city: "",
                    state: "",
                    postal_code: "",
                    country: "",
                },
            }

            setFormData(transformedData)
            setOriginalData(JSON.parse(JSON.stringify(transformedData)))

            // Check if addresses are the same
            const addressesMatch =
                JSON.stringify(transformedData.registered_address) === JSON.stringify(transformedData.operational_address)
            setUseSameAddress(addressesMatch)
        } catch (error: any) {
            console.error("Error fetching company data:", error)
            setError("Failed to load company data. Please try again.")
        } finally {
            setLoading(false)
        }
    }, [params.id])

    useEffect(() => {
        fetchCompanyData().then(r => {
            console.log("company fetched")
        })
    }, [fetchCompanyData])

    // Check for unsaved changes
    useEffect(() => {
        if (formData && originalData) {
            const hasChanges = JSON.stringify(formData) !== JSON.stringify(originalData)
            setHasUnsavedChanges(hasChanges)
        }
    }, [formData, originalData])

    // Validation functions
    const validateField = (name: string, value: any): string => {
        if (name.includes("email")) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            if (!value) return "Email is required"
            if (!emailRegex.test(value)) return "Please enter a valid email address"
        }

        if (name === "company_website") {
            if (value) {
                try {
                    new URL(value)
                } catch (e) {
                    return "Please enter a valid URL (e.g., https://example.com)"
                }
            }
        }

        const requiredFields = [
            "company_name",
            "company_industry",
            "company_number",
            "registration_date",
            "contact_name",
            "contact_role",
            "company_email",
            "country_of_incorporation",
            "type_of_business",
            "business_sector",
            "business_purpose",
            "admin_name",
            "admin_role",
            "admin_email",
        ]

        if (requiredFields.includes(name) && !value) {
            return `${name.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())} is required`
        }

        const numberFields = ["number_of_cards", "monthly_loading_value", "fund_loading"]
        if (numberFields.includes(name)) {
            if (value && (isNaN(Number(value)) || Number(value) < 0)) {
                return "Please enter a valid positive number"
            }
        }

        if (name === "registration_date") {
            if (value) {
                const date = new Date(value)
                const today = new Date()
                if (date > today) {
                    return "Registration date cannot be in the future"
                }
            }
        }

        return ""
    }

    const validateForm = (): ValidationErrors => {
        if (!formData) return {}

        const newErrors: ValidationErrors = {}

        Object.entries(formData).forEach(([key, value]) => {
            if (key !== "registered_address" && key !== "operational_address") {
                const error = validateField(key, value)
                if (error) {
                    newErrors[key as keyof FormData] = error
                }
            }
        })

        // Validate addresses
        newErrors.registered_address = {}
        Object.entries(formData.registered_address).forEach(([key, value]) => {
            const requiredAddressFields = ["street", "city", "postal_code", "country"]
            if (requiredAddressFields.includes(key) && !value) {
                if (!newErrors.registered_address) newErrors.registered_address = {}
                newErrors.registered_address[key as keyof Address] =
                    `${key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())} is required`
            }
        })

        if (!useSameAddress) {
            newErrors.operational_address = {}
            Object.entries(formData.operational_address).forEach(([key, value]) => {
                const requiredAddressFields = ["street", "city", "postal_code", "country"]
                if (requiredAddressFields.includes(key) && !value) {
                    if (!newErrors.operational_address) newErrors.operational_address = {}
                    newErrors.operational_address[key as keyof Address] =
                        `${key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())} is required`
                }
            })
        }

        // Phone validation
        if (!formData.company_phone || !isValidPhoneNumber(formData.company_phone)) {
            newErrors.company_phone = "Please enter a valid phone number"
        }

        if (!formData.admin_phone || !isValidPhoneNumber(formData.admin_phone)) {
            newErrors.admin_phone = "Please enter a valid phone number"
        }

        return newErrors
    }

    const hasErrors = (errors: ValidationErrors): boolean => {
        return Object.keys(errors).some((key) => {
            if (key === "registered_address" || key === "operational_address") {
                return errors[key] && Object.keys(errors[key]!).length > 0
            }
            return errors[key as keyof ValidationErrors]
        })
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!formData) return

        const validationErrors = validateForm()
        setErrors(validationErrors)

        if (hasErrors(validationErrors)) {
            setAlert({type: "error", message: "Please correct the errors in the form before saving."})
            return
        }

        setSaving(true)
        try {
            // Simulate API call
            await new Promise((resolve) => setTimeout(resolve, 2000))

            // In real implementation:
            const response = await axiosInstance.put(`/company/${params.id}`, formData)

            setAlert({type: "success", message: "Company information updated successfully!"})
            setOriginalData(JSON.parse(JSON.stringify(formData)))
            setHasUnsavedChanges(false)
        } catch (error: any) {
            setAlert({type: "error", message: "Error updating company information. Please try again."})
            console.error("Error updating company:", error)
        } finally {
            setSaving(false)
        }
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        if (!formData) return

        const {name, value} = e.target
        setFormData((prev) => (prev ? {...prev, [name]: value} : null))
        setTouchedFields((prev) => new Set(prev).add(name))

        const error = validateField(name, value)
        setErrors((prev) => ({...prev, [name]: error}))
    }

    const handlePhoneChange = (value: string | undefined, field: "company_phone" | "admin_phone") => {
        if (!formData) return

        setFormData((prev) => (prev ? {...prev, [field]: value || ""} : null))
        setTouchedFields((prev) => new Set(prev).add(field))

        const isValid = value ? isValidPhoneNumber(value) : false
        setErrors((prev) => ({
            ...prev,
            [field]: !isValid ? "Please enter a valid phone number" : "",
        }))
    }

    const handleAddressChange = (
        e: React.ChangeEvent<HTMLInputElement>,
        addressType: "registered_address" | "operational_address",
    ) => {
        if (!formData) return

        const {name, value} = e.target
        setTouchedFields((prev) => new Set(prev).add(`${addressType}.${name}`))

        if (addressType === "registered_address" && useSameAddress) {
            setFormData((prev) =>
                prev
                    ? {
                        ...prev,
                        registered_address: {...prev.registered_address, [name]: value},
                        operational_address: {...prev.operational_address, [name]: value},
                    }
                    : null,
            )
        } else {
            setFormData((prev) =>
                prev
                    ? {
                        ...prev,
                        [addressType]: {...prev[addressType], [name]: value},
                    }
                    : null,
            )
        }
    }

    const handleCountryChange = (value: string, field: string) => {
        if (!formData) return

        if (field.includes("address")) {
            const [addressType, fieldName] = field.split(".")
            if (addressType === "registered_address" && useSameAddress) {
                setFormData((prev) =>
                    prev
                        ? {
                            ...prev,
                            registered_address: {...prev.registered_address, country: value},
                            operational_address: {...prev.operational_address, country: value},
                        }
                        : null,
                )
            } else {
                setFormData((prev) =>
                    prev
                        ? {
                            ...prev,
                            [addressType]: {...prev[addressType as keyof FormData], country: value},
                        }
                        : null,
                )
            }
        } else {
            setFormData((prev) => (prev ? {...prev, [field]: value} : null))
        }
        setTouchedFields((prev) => new Set(prev).add(field))
    }

    const handleReset = () => {
        if (originalData) {
            setFormData(JSON.parse(JSON.stringify(originalData)))
            setErrors({})
            setTouchedFields(new Set())
            setAlert(null)
            setHasUnsavedChanges(false)
        }
    }

    // Update operational address when checkbox changes
    useEffect(() => {
        if (formData && useSameAddress) {
            setFormData((prev) =>
                prev
                    ? {
                        ...prev,
                        operational_address: {...prev.registered_address},
                    }
                    : null,
            )
        }
    }, [useSameAddress, formData?.registered_address])

    if (loading) {
        return <LoadingState/>
    }

    if (error) {
        return <ErrorState error={error} retry={fetchCompanyData}/>
    }

    if (!formData) {
        return <ErrorState error="No company data found" retry={fetchCompanyData}/>
    }

    return (
        <div className="space-y-6 max-w-6xl mx-auto p-4">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div className="flex items-center gap-4">

                    <Button variant="outline" size="sm" onClick={() => router.back()}>
                        <ArrowLeft size={16} className="mr-2"/>
                        Back to Profile
                    </Button>

                    <div>
                        <h1 className="text-2xl font-bold">Edit Company Information</h1>
                        <p className="text-muted-foreground">
                            {formData.company_name} • ID: {companyData.ryvyl_id}
                        </p>
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    {hasUnsavedChanges && (
                        <span className="text-sm text-amber-600 bg-amber-50 px-2 py-1 rounded">Unsaved changes</span>
                    )}
                </div>
            </div>

            {alert && (
                <Alert variant={alert.type === "success" ? "default" : "destructive"}>
                    <AlertCircle className="h-4 w-4"/>
                    <AlertTitle>{alert.type === "success" ? "Success" : "Error"}</AlertTitle>
                    <AlertDescription>{alert.message}</AlertDescription>
                </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
                <Card className="shadow-lg border-t-4 border-t-primary">
                    <CardHeader className="bg-gray-50 border-b">
                        <CardTitle className="text-xl font-bold text-primary">Company Information</CardTitle>
                        <CardDescription>Update your company details and business information.</CardDescription>
                        <div className="mt-2 flex items-start space-x-2 text-sm text-amber-600 bg-amber-50 p-2 rounded">
                            <Info className="h-4 w-4 mt-0.5 flex-shrink-0"/>
                            <p>Fields marked with an asterisk (*) are required.</p>
                        </div>
                    </CardHeader>

                    <CardContent className="p-0">
                        <Tabs defaultValue="company" className="w-full">
                            <div className="px-6 pt-6">
                                <TabsList
                                    className="w-full justify-start h-auto p-0 bg-transparent border-b rounded-none">
                                    <TabsTrigger
                                        value="company"
                                        className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                    >
                                        Company Details
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="business"
                                        className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                    >
                                        Business Information
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="addresses"
                                        className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                    >
                                        Addresses
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="admin"
                                        className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent"
                                    >
                                        Administrator
                                    </TabsTrigger>
                                </TabsList>
                            </div>

                            <TabsContent value="company" className="p-6 pt-4">
                                <div className="space-y-6">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <ValidatedInput
                                            id="company_name"
                                            label="Company Name"
                                            value={formData.company_name}
                                            onChange={handleInputChange}
                                            placeholder="Enter company name"
                                            error={errors.company_name}
                                            touched={touchedFields.has("company_name")}
                                            required
                                            icon={<Building size={16}/>}
                                        />
                                        <ValidatedInput
                                            id="company_industry"
                                            label="Company Industry"
                                            value={formData.company_industry}
                                            onChange={handleInputChange}
                                            placeholder="e.g. Technology, Finance"
                                            error={errors.company_industry}
                                            touched={touchedFields.has("company_industry")}
                                            required
                                            icon={<Building size={16}/>}
                                        />
                                        <ValidatedInput
                                            id="company_number"
                                            label="Company Registration Number"
                                            value={formData.company_number}
                                            onChange={handleInputChange}
                                            placeholder="Enter registration number"
                                            error={errors.company_number}
                                            touched={touchedFields.has("company_number")}
                                            required
                                            icon={<FileText size={16}/>}
                                        />
                                        <div className="space-y-2">
                                            <Label htmlFor="company_phone" className="flex items-center gap-2">
                                                <Phone size={16}/>
                                                Company Telephone <span className="text-red-500 ml-1">*</span>
                                            </Label>
                                            <PhoneInput
                                                international
                                                countryCallingCodeEditable={true}
                                                defaultCountry="US"
                                                value={formData.company_phone}
                                                onChange={(value) => handlePhoneChange(value, "company_phone")}
                                                className={`w-full p-2 border rounded-md ${
                                                    touchedFields.has("company_phone") && errors.company_phone
                                                        ? "border-red-500"
                                                        : "border-gray-300"
                                                }`}
                                            />
                                            {touchedFields.has("company_phone") && errors.company_phone && (
                                                <p className="text-red-500 text-sm mt-1">{errors.company_phone}</p>
                                            )}
                                        </div>
                                        <ValidatedInput
                                            id="registration_date"
                                            label="Date of Registration"
                                            value={formData.registration_date}
                                            onChange={handleInputChange}
                                            type="date"
                                            error={errors.registration_date}
                                            touched={touchedFields.has("registration_date")}
                                            required
                                        />
                                        <ValidatedInput
                                            id="contact_name"
                                            label="Contact Name"
                                            value={formData.contact_name}
                                            onChange={handleInputChange}
                                            placeholder="Full name"
                                            error={errors.contact_name}
                                            touched={touchedFields.has("contact_name")}
                                            required
                                        />
                                        <ValidatedInput
                                            id="contact_role"
                                            label="Role of Contact"
                                            value={formData.contact_role}
                                            onChange={handleInputChange}
                                            placeholder="e.g. CEO, CFO"
                                            error={errors.contact_role}
                                            touched={touchedFields.has("contact_role")}
                                            required
                                        />
                                        <ValidatedInput
                                            id="company_email"
                                            label="Company Email"
                                            value={formData.company_email}
                                            onChange={handleInputChange}
                                            type="email"
                                            placeholder="<EMAIL>"
                                            error={errors.company_email}
                                            touched={touchedFields.has("company_email")}
                                            required
                                            icon={<Mail size={16}/>}
                                        />
                                        <div className="space-y-2">
                                            <Label htmlFor="country_of_incorporation"
                                                   className="flex items-center gap-2">
                                                <MapPin size={16}/>
                                                Country of Incorporation <span className="text-red-500 ml-1">*</span>
                                            </Label>
                                            <select
                                                id="country_of_incorporation"
                                                value={formData.country_of_incorporation}
                                                onChange={(e) => handleCountryChange(e.target.value, "country_of_incorporation")}
                                                className={`w-full p-2 border rounded-md ${
                                                    touchedFields.has("country_of_incorporation") && errors.country_of_incorporation
                                                        ? "border-red-500"
                                                        : "border-gray-300"
                                                }`}
                                                required
                                            >
                                                <option value="">Select country</option>
                                                {countries.map((country) => (
                                                    <option key={country.code} value={country.name}>
                                                        {country.name}
                                                    </option>
                                                ))}
                                            </select>
                                            {touchedFields.has("country_of_incorporation") && errors.country_of_incorporation && (
                                                <p className="text-red-500 text-sm mt-1">{errors.country_of_incorporation}</p>
                                            )}
                                        </div>
                                        <ValidatedInput
                                            id="company_website"
                                            label="Company Website"
                                            value={formData.company_website}
                                            onChange={handleInputChange}
                                            type="url"
                                            placeholder="https://www.example.com"
                                            error={errors.company_website}
                                            touched={touchedFields.has("company_website")}
                                            icon={<Globe size={16}/>}
                                        />
                                    </div>
                                </div>
                            </TabsContent>

                            <TabsContent value="business" className="p-6 pt-4">
                                <div className="space-y-6">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <ValidatedInput
                                            id="type_of_business"
                                            label="Type of Business"
                                            value={formData.type_of_business}
                                            onChange={handleInputChange}
                                            placeholder="e.g. Corporation, LLC"
                                            error={errors.type_of_business}
                                            touched={touchedFields.has("type_of_business")}
                                            required
                                        />
                                        <ValidatedInput
                                            id="business_sector"
                                            label="Business Sector"
                                            value={formData.business_sector}
                                            onChange={handleInputChange}
                                            placeholder="e.g. Retail, Manufacturing"
                                            error={errors.business_sector}
                                            touched={touchedFields.has("business_sector")}
                                            required
                                        />
                                        <ValidatedInput
                                            id="card_usage"
                                            label="Card Usage"
                                            value={formData.card_usage}
                                            onChange={handleInputChange}
                                            placeholder="Describe card usage"
                                            error={errors.card_usage}
                                            touched={touchedFields.has("card_usage")}
                                        />
                                        <ValidatedInput
                                            id="cardholder_groups"
                                            label="Cardholder Groups"
                                            value={formData.cardholder_groups}
                                            onChange={handleInputChange}
                                            placeholder="e.g. Employees, Contractors"
                                            error={errors.cardholder_groups}
                                            touched={touchedFields.has("cardholder_groups")}
                                        />
                                        <ValidatedInput
                                            id="fund_loading"
                                            label="Fund Loading Per Calendar Month (€)"
                                            value={formData.fund_loading}
                                            onChange={handleInputChange}
                                            type="number"
                                            placeholder="Enter amount"
                                            error={errors.fund_loading}
                                            touched={touchedFields.has("fund_loading")}
                                        />
                                        <ValidatedInput
                                            id="regions"
                                            label="Regions of Operation"
                                            value={formData.regions}
                                            onChange={handleInputChange}
                                            placeholder="e.g. North America, Europe"
                                            error={errors.regions}
                                            touched={touchedFields.has("regions")}
                                        />
                                        <ValidatedInput
                                            id="countries"
                                            label="Countries of Operation"
                                            value={formData.countries}
                                            onChange={handleInputChange}
                                            placeholder="List countries"
                                            error={errors.countries}
                                            touched={touchedFields.has("countries")}
                                        />
                                        <ValidatedInput
                                            id="card_user_groups"
                                            label="Card User Groups (List roles)"
                                            value={formData.card_user_groups}
                                            onChange={handleInputChange}
                                            placeholder="e.g. Sales, Management"
                                            error={errors.card_user_groups}
                                            touched={touchedFields.has("card_user_groups")}
                                        />
                                        <ValidatedInput
                                            id="number_of_cards"
                                            label="How many cards required in Year 1"
                                            value={formData.number_of_cards}
                                            onChange={handleInputChange}
                                            type="number"
                                            placeholder="Enter number"
                                            error={errors.number_of_cards}
                                            touched={touchedFields.has("number_of_cards")}
                                        />
                                        <ValidatedInput
                                            id="monthly_loading_value"
                                            label="Monthly Loading Value (EUR)"
                                            value={formData.monthly_loading_value}
                                            onChange={handleInputChange}
                                            type="number"
                                            placeholder="Enter amount"
                                            error={errors.monthly_loading_value}
                                            touched={touchedFields.has("monthly_loading_value")}
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="business_purpose" className="flex">
                                            Business Purpose (For using cards) <span
                                            className="text-red-500 ml-1">*</span>
                                        </Label>
                                        <Textarea
                                            id="business_purpose"
                                            name="business_purpose"
                                            value={formData.business_purpose}
                                            onChange={handleInputChange}
                                            required
                                            className={`min-h-[100px] ${
                                                touchedFields.has("business_purpose") && errors.business_purpose ? "border-red-500" : ""
                                            }`}
                                            placeholder="Describe the purpose of using cards in your business"
                                        />
                                        {touchedFields.has("business_purpose") && errors.business_purpose && (
                                            <p className="text-red-500 text-sm mt-1">{errors.business_purpose}</p>
                                        )}
                                    </div>
                                </div>
                            </TabsContent>

                            <TabsContent value="addresses" className="p-6 pt-4">
                                <div className="space-y-8">
                                    <AddressSection
                                        title="Company Registered Address"
                                        address={formData.registered_address}
                                        onChange={(e) => handleAddressChange(e, "registered_address")}
                                        errors={errors.registered_address}
                                        touchedFields={touchedFields}
                                        addressType="registered_address"
                                        onCountryChange={(value) => handleCountryChange(value, "registered_address.country")}
                                    />

                                    <div className="flex items-center space-x-2 my-6">
                                        <Checkbox
                                            id="same-address"
                                            checked={useSameAddress}
                                            onCheckedChange={(checked) => setUseSameAddress(checked === true)}
                                        />
                                        <Label htmlFor="same-address" className="text-sm font-medium">
                                            Use registered address as operational address
                                        </Label>
                                    </div>

                                    <AddressSection
                                        title="Company Operational Address"
                                        address={formData.operational_address}
                                        onChange={(e) => handleAddressChange(e, "operational_address")}
                                        errors={errors.operational_address}
                                        touchedFields={touchedFields}
                                        addressType="operational_address"
                                        disabled={useSameAddress}
                                        onCountryChange={(value) => handleCountryChange(value, "operational_address.country")}
                                    />
                                </div>
                            </TabsContent>

                            <TabsContent value="admin" className="p-6 pt-4">
                                <div className="space-y-6">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <ValidatedInput
                                            id="admin_name"
                                            label="Administrator Name"
                                            value={formData.admin_name}
                                            onChange={handleInputChange}
                                            placeholder="Full name"
                                            error={errors.admin_name}
                                            touched={touchedFields.has("admin_name")}
                                            required
                                        />
                                        <ValidatedInput
                                            id="admin_role"
                                            label="Administrator Role"
                                            value={formData.admin_role}
                                            onChange={handleInputChange}
                                            placeholder="e.g. Finance Manager"
                                            error={errors.admin_role}
                                            touched={touchedFields.has("admin_role")}
                                            required
                                        />
                                        <ValidatedInput
                                            id="admin_email"
                                            label="Administrator Email"
                                            value={formData.admin_email}
                                            onChange={handleInputChange}
                                            type="email"
                                            placeholder="<EMAIL>"
                                            error={errors.admin_email}
                                            touched={touchedFields.has("admin_email")}
                                            required
                                            icon={<Mail size={16}/>}
                                        />
                                        <div className="space-y-2">
                                            <Label htmlFor="admin_phone" className="flex items-center gap-2">
                                                <Phone size={16}/>
                                                Administrator Mobile Telephone <span
                                                className="text-red-500 ml-1">*</span>
                                            </Label>
                                            <PhoneInput
                                                international
                                                countryCallingCodeEditable={true}
                                                defaultCountry="US"
                                                value={formData.admin_phone}
                                                onChange={(value) => handlePhoneChange(value, "admin_phone")}
                                                className={`w-full p-2 border rounded-md ${
                                                    touchedFields.has("admin_phone") && errors.admin_phone ? "border-red-500" : "border-gray-300"
                                                }`}
                                            />
                                            {touchedFields.has("admin_phone") && errors.admin_phone && (
                                                <p className="text-red-500 text-sm mt-1">{errors.admin_phone}</p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </TabsContent>
                        </Tabs>
                    </CardContent>

                    <CardFooter className="flex flex-col sm:flex-row gap-4 border-t pt-6">
                        <Button
                            type="button"
                            variant="outline"
                            className="w-full sm:w-auto"
                            onClick={handleReset}
                            disabled={saving || !hasUnsavedChanges}
                        >
                            <X className="mr-2 h-4 w-4"/>
                            Reset Changes
                        </Button>
                        <div className="flex-1"/>
                        <Button type="submit" className="w-full sm:w-auto" disabled={saving}>
                            {saving ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin"/>
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <Save className="mr-2 h-4 w-4"/>
                                    Save Changes
                                </>
                            )}
                        </Button>
                    </CardFooter>
                </Card>
            </form>
        </div>
    )
}

// Helper Components
interface ValidatedInputProps {
    id: string
    label: string
    value: string
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
    placeholder?: string
    type?: string
    error?: string
    touched: boolean
    required?: boolean
    disabled?: boolean
    icon?: React.ReactNode
}

function ValidatedInput({
                            id,
                            label,
                            value,
                            onChange,
                            placeholder,
                            type = "text",
                            error,
                            touched,
                            required = false,
                            disabled = false,
                            icon,
                        }: ValidatedInputProps) {
    return (
        <div className="space-y-2">
            <Label htmlFor={id} className="flex items-center gap-2">
                {icon}
                {label} {required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
                id={id}
                name={id}
                value={value}
                onChange={onChange}
                type={type}
                required={required}
                className={`w-full ${touched && error ? "border-red-500" : ""} ${value && !error ? "border-green-500" : ""}`}
                placeholder={placeholder}
                disabled={disabled}
            />
            {touched && error && <p className="text-red-500 text-sm mt-1">{error}</p>}
        </div>
    )
}

interface AddressSectionProps {
    title: string
    address: Address
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
    errors?: { [key in keyof Address]?: string }
    touchedFields: Set<string>
    addressType: "registered_address" | "operational_address"
    disabled?: boolean
    onCountryChange: (value: string) => void
}

function AddressSection({
                            title,
                            address,
                            onChange,
                            errors = {},
                            touchedFields,
                            addressType,
                            disabled = false,
                            onCountryChange,
                        }: AddressSectionProps) {
    return (
        <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
                <MapPin size={18}/>
                {title}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[
                    {id: "street", label: "Street", placeholder: "Enter street name", required: true},
                    {id: "building_number", label: "Building Number", placeholder: "Enter building number"},
                    {id: "apartment_number", label: "Apartment Number", placeholder: "Enter apartment number"},
                    {id: "city", label: "City", placeholder: "Enter city", required: true},
                    {id: "state", label: "State / Province", placeholder: "Enter state or province"},
                    {
                        id: "postal_code",
                        label: "Postal / ZIP Code",
                        placeholder: "Enter postal or ZIP code",
                        required: true
                    },
                ].map(({id, label, placeholder, required = false}) => (
                    <div key={id} className="space-y-2">
                        <Label htmlFor={id} className="flex">
                            {label} {required && <span className="text-red-500 ml-1">*</span>}
                        </Label>
                        <Input
                            id={id}
                            name={id}
                            value={address[id as keyof Address]}
                            onChange={onChange}
                            required={required}
                            className={`w-full ${
                                touchedFields.has(`${addressType}.${id}`) && errors[id as keyof Address] ? "border-red-500" : ""
                            } ${address[id as keyof Address] && !errors[id as keyof Address] ? "border-green-500" : ""}`}
                            placeholder={placeholder}
                            disabled={disabled}
                        />
                        {touchedFields.has(`${addressType}.${id}`) && errors[id as keyof Address] && (
                            <p className="text-red-500 text-sm mt-1">{errors[id as keyof Address]}</p>
                        )}
                    </div>
                ))}
                <div className="space-y-2">
                    <Label htmlFor={`${addressType}-country`} className="flex">
                        Country <span className="text-red-500 ml-1">*</span>
                    </Label>
                    <select
                        id={`${addressType}-country`}
                        value={address.country}
                        onChange={(e) => onCountryChange(e.target.value)}
                        className={`w-full p-2 border rounded-md ${
                            touchedFields.has(`${addressType}.country`) && errors.country ? "border-red-500" : "border-gray-300"
                        }`}
                        required
                        disabled={disabled}
                    >
                        <option value="">Select country</option>
                        {countries.map((country) => (
                            <option key={country.code} value={country.name}>
                                {country.name}
                            </option>
                        ))}
                    </select>
                    {touchedFields.has(`${addressType}.country`) && errors.country && (
                        <p className="text-red-500 text-sm mt-1">{errors.country}</p>
                    )}
                </div>
            </div>
        </div>
    )
}

const LoadingState = () => (
    <div className="w-full space-y-6 max-w-6xl mx-auto p-4">
        <div className="flex items-center gap-4">
            <Skeleton className="h-10 w-32"/>
            <div>
                <Skeleton className="h-8 w-64 mb-2"/>
                <Skeleton className="h-4 w-48"/>
            </div>
        </div>
        <Card>
            <CardHeader>
                <Skeleton className="h-6 w-48"/>
                <Skeleton className="h-4 w-96"/>
            </CardHeader>
            <CardContent>
                <div className="space-y-6">
                    {Array(8)
                        .fill(0)
                        .map((_, i) => (
                            <div key={i} className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Skeleton className="h-4 w-32"/>
                                    <Skeleton className="h-10 w-full"/>
                                </div>
                                <div className="space-y-2">
                                    <Skeleton className="h-4 w-32"/>
                                    <Skeleton className="h-10 w-full"/>
                                </div>
                            </div>
                        ))}
                </div>
            </CardContent>
        </Card>
    </div>
)

const ErrorState = ({error, retry}: { error: string; retry: () => void }) => (
    <div className="w-full flex flex-col items-center justify-center p-12 max-w-6xl mx-auto">
        <div className="p-4 rounded-full bg-red-100 text-red-600 mb-4">
            <AlertCircle size={32}/>
        </div>
        <h2 className="text-xl font-semibold mb-2">Error Loading Company Data</h2>
        <p className="text-muted-foreground mb-6 text-center max-w-md">{error}</p>
        <Button onClick={retry} className="flex items-center gap-2">
            <RefreshCw size={16}/>
            Retry
        </Button>
    </div>
)
