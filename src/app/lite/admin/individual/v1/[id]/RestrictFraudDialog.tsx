import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import axiosInstance from "@/utils/axiosInstance"
import { Loader2 } from "lucide-react"

interface RestrictFraudDialogProps {
    cardId: string
    expDate:string
    onApiSuccess: () => void
}

export function RestrictFraudDialog({ cardId,expDate, onApiSuccess }: RestrictFraudDialogProps) {
    const [loading, setLoading] = useState(false)

    const handleRestrictFraud = async () => {
        try {
            setLoading(true)
            const response = await axiosInstance.post(`cards/${cardId}/restrictFraud`,{expDate})

            if (typeof onApiSuccess === "function") {
                onApiSuccess()
            }
        } catch (error) {
            console.error("Failed to restrict fraud", error)
            alert("Failed to restrict fraud. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant="destructive">Restrict Fraud</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Restrict Fraud</DialogTitle>
                    <DialogDescription>
                        Are you sure you want to restrict this card for fraud? This action will limit the card's functionality to
                        prevent potential fraudulent activities.
                    </DialogDescription>
                </DialogHeader>

                <DialogFooter>
                    <Button type="submit" onClick={handleRestrictFraud} disabled={loading} variant="destructive">
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Restricting...
                            </>
                        ) : (
                            "Restrict Fraud"
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

