"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>lide<PERSON> } from "@/components/ui/slider"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    <PERSON>alogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Card, CardContent } from "@/components/ui/card"
import { ChevronRight, CreditCard, DollarSign, Calendar, Banknote, Shield } from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"

interface SetCardLimitsDialogProps {
    cardId: string
    onApiSuccess: () => void
}

const limitConfigs = {
    POS_PUR_LMT: {
        label: "Daily POS Purchase",
        description: "Maximum amount for daily point-of-sale purchases",
        icon: CreditCard,
        min: 0,
        max: 10000,
        step: 50,
        defaultValue: 1000,
    },
    MONTH_POS_PUR_LMT: {
        label: "Monthly POS Purchase",
        description: "Maximum amount for monthly point-of-sale purchases",
        icon: Calendar,
        min: 0,
        max: 50000,
        step: 500,
        defaultValue: 10000,
    },
    ATM_CCA_LMT: {
        label: "Daily ATM Withdrawal",
        description: "Maximum amount for daily ATM cash withdrawals",
        icon: Banknote,
        min: 0,
        max: 2000,
        step: 25,
        defaultValue: 500,
    },
    MONTH_ATM_CCA_LMT: {
        label: "Monthly ATM Withdrawal",
        description: "Maximum amount for monthly ATM cash withdrawals",
        icon: Calendar,
        min: 0,
        max: 25000,
        step: 250,
        defaultValue: 5000,
    },
    CNP_AMT_LMT: {
        label: "Daily Card Not Present",
        description: "Maximum amount for daily online/phone transactions",
        icon: Shield,
        min: 0,
        max: 10000,
        step: 100,
        defaultValue: 2000,
    },
    MONTH_AMT_CNP_LMT: {
        label: "Monthly Card Not Present",
        description: "Maximum amount for monthly online/phone transactions",
        icon: Shield,
        min: 0,
        max: 30000,
        step: 500,
        defaultValue: 5000,
    },
} as const

type LimitType = keyof typeof limitConfigs

interface ApiCardLimit {
    name: string
    value: number
    noLimit: boolean
    desc: string
    type: string
    life: string
    balanceType: string
    min: number
}

interface ApiResponse {
    success: boolean
    limits: ApiCardLimit[]
}

export function SetCardLimitsDialog({ cardId, onApiSuccess }: SetCardLimitsDialogProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [loading, setLoading] = useState(false)
    const [fetchingLimits, setFetchingLimits] = useState(true)
    const [limits, setLimits] = useState<Record<LimitType, number>>(() => {
        const initialLimits = {} as Record<LimitType, number>
        Object.entries(limitConfigs).forEach(([key, config]) => {
            initialLimits[key as LimitType] = config.defaultValue
        })
        return initialLimits
    })

    useEffect(() => {
        const getCardLimits = async () => {
            try {
                setFetchingLimits(true)
                const response = await axiosInstance.get(`cards/${cardId}/limits`)
                const apiResponse = response.data as ApiResponse

                if (apiResponse.success && apiResponse.limits) {
                    const updatedLimits = { ...limits }

                    apiResponse.limits.forEach((limit: ApiCardLimit) => {
                        if (limit.name in limitConfigs) {
                            updatedLimits[limit.name as LimitType] = limit.value
                        }
                    })

                    setLimits(updatedLimits)
                }
            } catch (error) {
                console.error("Failed to fetch card limits", error)
                // alert("Failed to fetch card limits. Please try again.")
            } finally {
                setFetchingLimits(false)
            }
        }

        if (cardId) {
            getCardLimits()
        }
    }, [cardId])

    const handleSetLimits = async () => {
        try {
            setLoading(true)
            const limitsArray = Object.entries(limits).map(([name, value]) => ({
                name,
                value: Number.parseFloat(value.toFixed(2)),
            }))

            console.log("[v0] Setting limits with single request:", limitsArray)

            await axiosInstance.post(`cards/${cardId}/patch-card-limits`, limitsArray)

            onApiSuccess()
            setIsOpen(false)
        } catch (error) {
            console.error("Failed to set card limits", error)
            alert("Failed to set card limits. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    const updateLimit = (limitType: LimitType, value: number[]) => {
        setLimits((prev) => ({
            ...prev,
            [limitType]: value[0],
        }))
    }

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "EUR",
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount)
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <CreditCard className="h-5 w-5 text-gray-600" />
                        </div>
                        <span className="font-normal">Set Card Limits</span>
                    </div>
                    <ChevronRight />
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <DollarSign className="h-5 w-5 text-primary" />
                        Set Card Limits
                    </DialogTitle>
                    <DialogDescription>
                        Configure spending limits for different transaction types. Use the sliders to set your preferred amounts.
                    </DialogDescription>
                </DialogHeader>

                {fetchingLimits ? (
                    <div className="flex items-center justify-center py-8">
                        <div className="text-sm text-muted-foreground">Loading current limits...</div>
                    </div>
                ) : (
                    <div className="space-y-4 py-4">
                        {Object.entries(limitConfigs).map(([limitType, config]) => {
                            const IconComponent = config.icon
                            const currentValue = limits[limitType as LimitType]

                            return (
                                <Card key={limitType} className="border-l-4 border-l-primary">
                                    <CardContent className="p-4">
                                        <div className="flex items-start gap-3 mb-3">
                                            <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                                                <IconComponent className="h-4 w-4 text-primary" />
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <h4 className="font-medium text-sm">{config.label}</h4>
                                                <p className="text-xs text-muted-foreground mt-1">{config.description}</p>
                                            </div>
                                            <div className="text-right">
                                                <div className="text-lg font-semibold text-primary">{formatCurrency(currentValue)}</div>
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Slider
                                                value={[currentValue]}
                                                onValueChange={(value) => updateLimit(limitType as LimitType, value)}
                                                max={config.max}
                                                min={config.min}
                                                step={config.step}
                                                className="w-full"
                                            />
                                            <div className="flex justify-between text-xs text-muted-foreground">
                                                <span>{formatCurrency(config.min)}</span>
                                                <span>{formatCurrency(config.max)}</span>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            )
                        })}
                    </div>
                )}

                <DialogFooter className="gap-2">
                    <Button variant="outline" onClick={() => setIsOpen(false)} disabled={loading || fetchingLimits}>
                        Cancel
                    </Button>
                    <Button onClick={handleSetLimits} disabled={loading || fetchingLimits}>
                        {loading ? "Setting Limits..." : "Apply All Limits"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
