"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    Di<PERSON>Header,
    <PERSON>alogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {Phone, ChevronRight, Edit2Icon} from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"
import { alertHelper } from "@/utils/alertHelper"
import PhoneInput, { isValidPhoneNumber } from 'react-phone-number-input'
import 'react-phone-number-input/style.css'

interface Change3DSecurePhoneDialogProps {
    expDate: string
    cardId: string
    onApiSuccess: () => void
}

export function Change3DSecurePhoneDialog({ expDate, cardId, onApiSuccess }: Change3DSecurePhoneDialogProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [loading, setLoading] = useState(false)
    const [newPhoneNumber, setNewPhoneNumber] = useState("")

    const handleChange3DSecurePhone = async () => {
        try {
            setLoading(true)
            const data = { expDate, newPhoneNumber }
            console.dir(data)
            await axiosInstance.post(`cards/${cardId}/change-3d-phone`, data)
            onApiSuccess()
            setIsOpen(false)
            setNewPhoneNumber("")
            alertHelper.showToast("3D Secure phone number changed successfully", "success")
        } catch (error) {
            console.error("Failed to change 3D Secure phone number", error)
            alertHelper.showToast("Failed to change 3D Secure phone number. Please try again.", "error")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2   hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <Edit2Icon className="h-5 w-5 text-gray-600" />
                        </div>

                    </div>

                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Change 3D Secure Phone Number</DialogTitle>
                    <DialogDescription>
                        Enter your new phone number for 3D Secure authentication. This number will receive verification codes for
                        your online transactions.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <PhoneInput
                        international
                        countryCallingCodeEditable={true}
                        defaultCountry="AT"
                        id="new3DSecurePhone"
                        placeholder="New Phone Number"
                        value={newPhoneNumber}
                        onChange={(value) => setNewPhoneNumber(value || "")}
                        className={`flex h-10 w-full bg-white rounded-md border ${
                            newPhoneNumber
                                ? isValidPhoneNumber(newPhoneNumber)
                                    ? "border-green-500 focus-visible:ring-green-500"
                                    : "border-red-500 focus-visible:ring-red-500"
                                : "border-input"
                        } bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50`}
                    />
                    {newPhoneNumber && (
                        <p className={`text-sm ${isValidPhoneNumber(newPhoneNumber) ? "text-green-500" : "text-red-500"}`}>
                            {isValidPhoneNumber(newPhoneNumber)
                                ? "Valid phone number"
                                : "Please enter a valid phone number"}
                        </p>
                    )}
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Cancel
                    </Button>
                    <Button onClick={handleChange3DSecurePhone} disabled={loading || !isValidPhoneNumber(newPhoneNumber)}>
                        {loading ? "Changing..." : "Change Phone Number"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
