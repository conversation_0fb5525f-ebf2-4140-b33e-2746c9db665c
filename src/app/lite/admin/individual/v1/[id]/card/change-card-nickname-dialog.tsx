"use client"

import {useState} from "react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Tag} from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"
import {alertHelper} from "@/utils/alertHelper"

interface ChangeCardNicknameDialogProps {
    cardId: string
    currentNickname?: string
    onApiSuccess: () => void
}

export function ChangeCardNicknameDialog({ cardId, currentNickname, onApiSuccess }: ChangeCardNicknameDialogProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [loading, setLoading] = useState(false)
    const [nickname, setNickname] = useState(currentNickname || "")

    const handleChangeNickname = async () => {
        try {
            setLoading(true)
            const data = { nickname: nickname.trim() }
            console.dir(data)
            await axiosInstance.post(`cards/${cardId}/change-nickname`, data)
            onApiSuccess()
            setIsOpen(false)
            alertHelper.showToast("Card nickname updated successfully", "success")
        } catch (error) {
            console.error("Failed to change card nickname", error)
            alertHelper.showToast("Failed to update card nickname. Please try again.", "error")
        } finally {
            setLoading(false)
        }
    }

    // Alphanumeric validation (letters and numbers only)
    const isAlphanumeric = (str: string) => /^[a-zA-Z0-9\s]*$/.test(str)

    const trimmedNickname = nickname.trim()
    const isValidLength = trimmedNickname.length > 0 && trimmedNickname.length <= 25
    const isValidFormat = isAlphanumeric(trimmedNickname)
    const isValidNickname = isValidLength && isValidFormat

    const getValidationMessage = () => {
        if (trimmedNickname.length === 0) return ""
        if (!isValidFormat) return "Only letters and numbers are allowed"
        if (!isValidLength) return "Nickname must be 1-25 characters"
        return "Valid nickname"
    }

    const getValidationColor = () => {
        if (trimmedNickname.length === 0) return "border-input"
        return isValidNickname
            ? "border-green-500 focus-visible:ring-green-500"
            : "border-red-500 focus-visible:ring-red-500"
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <Tag className="h-5 w-5 text-gray-600" />
                        </div>
                        <div className="text-left">
                            <div className="font-medium">Card Nickname</div>
                            <div className="text-sm text-gray-500">{currentNickname || "Add a nickname to your card"}</div>
                        </div>
                    </div>
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>{currentNickname ? "Change Card Nickname" : "Add Card Nickname"}</DialogTitle>
                    <DialogDescription>
                        Give your card a memorable nickname to easily identify it in your wallet. Use only letters and
                        numbers (max
                        25 characters). This nickname is only visible to you.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                        <Label htmlFor="nickname">Card Nickname</Label>
                        <Input
                            id="nickname"
                            placeholder="e.g., ShoppingCard, TravelCard, MainCard"
                            value={nickname}
                            onChange={(e) => setNickname(e.target.value)}
                            maxLength={25}
                            className={getValidationColor()}
                        />
                        <div className="flex justify-between text-sm">
                            {trimmedNickname.length > 0 && (
                                <span
                                    className={isValidNickname ? "text-green-500" : "text-red-500"}>{getValidationMessage()}</span>
                            )}
                            <span className="text-gray-500 ml-auto">{nickname.length}/25</span>
                        </div>
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Cancel
                    </Button>
                    <Button onClick={handleChangeNickname} disabled={loading || !isValidNickname}>
                        {loading ? "Saving..." : currentNickname ? "Update Nickname" : "Add Nickname"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
