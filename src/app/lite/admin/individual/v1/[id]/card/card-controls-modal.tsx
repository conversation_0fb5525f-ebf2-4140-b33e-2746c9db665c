"use client"

import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTit<PERSON>} from "@/components/ui/dialog"
import {Switch} from "@/components/ui/switch"
import {Building, CreditCard, CreditCardIcon as SmartCard, Globe, Smartphone, Wifi} from "lucide-react"

interface CardData {
    cardHash: string
    cardMask: string
    cardKey: string
    cardUuid: string
    expDate: string
    issueDate: string
    status: string
    statusCode: string
    terminating: boolean
    productCode: string
    productDesc: string
    holder: string
    accounts: Array<{
        accNo: string
        primary: boolean
        currencyCode: string
        currencyName: string
    }>
    embossName1: string
    source: string
    kind: string
    main: boolean
    limits: Array<{
        name: string
        value: number
        noLimit: boolean
    }>
    visual: string
    tokenized: boolean
    delivery: {
        deliveryType: string
    }
    contactless: boolean
    cardTechnologyMode: string
    creationDate: string
    autoRenewal: boolean
}


interface CardControlsModalProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    card: CardData
}

export function CardControlsModal({open, onO<PERSON><PERSON><PERSON><PERSON>, card}: CardControlsModalProps) {
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>Card controls</DialogTitle>
                    <p className="text-sm text-gray-500">Customise how you can use your Wise card.</p>
                </DialogHeader>
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                                <Globe className="h-5 w-5 text-gray-600"/>
                            </div>
                            <div>
                                <div className="font-medium">Online payments</div>
                                <div className="text-sm text-gray-500">Pay for goods and services on the internet.</div>
                            </div>
                        </div>
                        <Switch className="data-[state=checked]:bg-[#1C3020] data-[state=checked]:text-white"/>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                                <CreditCard className="h-5 w-5 text-gray-600"/>
                            </div>
                            <div>
                                <div className="font-medium">Magnetic stripe</div>
                                <div className="text-sm text-gray-500">Swipe your card with a machine to pay or
                                    withdraw.
                                </div>
                            </div>
                        </div>
                        <Switch className="data-[state=checked]:bg-[#1C3020] data-[state=checked]:text-white"/>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                                <Wifi className="h-5 w-5 text-gray-600"/>
                            </div>
                            <div>
                                <div className="font-medium">Contactless</div>
                                <div className="text-sm text-gray-500">Tap your physical card to pay or withdraw cash.
                                </div>
                            </div>
                        </div>
                        <Switch className="data-[state=checked]:bg-[#1C3020] data-[state=checked]:text-white"/>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                                <SmartCard className="h-5 w-5 text-gray-600"/>
                            </div>
                            <div>
                                <div className="font-medium">Chip transactions</div>
                                <div className="text-sm text-gray-500">Insert your card into a machine to pay or
                                    withdraw cash.
                                </div>
                            </div>
                        </div>
                        <Switch className="data-[state=checked]:bg-[#1C3020] data-[state=checked]:text-white"/>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                                <Smartphone className="h-5 w-5 text-gray-600"/>
                            </div>
                            <div>
                                <div className="font-medium">Mobile wallet</div>
                                <div className="text-sm text-gray-500">Use your mobile device to pay or withdraw cash.
                                </div>
                            </div>
                        </div>
                        <Switch className="data-[state=checked]:bg-[#1C3020] data-[state=checked]:text-white"/>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                                <Building className="h-5 w-5 text-gray-600"/>
                            </div>
                            <div>
                                <div className="font-medium">Cash withdrawals</div>
                                <div className="text-sm text-gray-500">
                                    Use contactless, chip or magnetic stripe to withdraw at ATMs.
                                </div>
                            </div>
                        </div>
                        <Switch className="data-[state=checked]:bg-[#1C3020] data-[state=checked]:text-white"/>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    )
}

