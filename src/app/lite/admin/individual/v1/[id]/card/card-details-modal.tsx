//@ts-nocheck
"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Loader2 } from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"

interface CardData {
    cardHash: string
    cardMask: string
    cardKey: string
    cardUuid: string
    expDate: string
    issueDate: string
    status: string
    statusCode: string
    terminating: boolean
    productCode: string
    productDesc: string
    holder: string
    accounts: Array<{
        accNo: string
        accNo: string
        primary: boolean
        currencyCode: string
        currencyName: string
    }>
    embossName1: string
    source: string
    kind: string
    main: boolean
    limits: Array<{
        name: string
        value: number
        noLimit: boolean
    }>
    visual: string
    tokenized: boolean
    delivery: {
        deliveryType: string
    }
    contactless: boolean
    cardTechnologyMode: string
    creationDate: string
    autoRenewal: boolean
}

interface CardDetailsModalProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    card: CardData
}

export function CardDetailsModal({ open, onOpenChange, card }: CardDetailsModalProps) {
    const [copiedField, setCopiedField] = useState<string | null>(null)
    const [cvv, setCvv] = useState<string | null>(null)
    const [cardNumber, setCardNumber] = useState<string | null>(null)
    const [isLoadingCvv, setIsLoadingCvv] = useState(false)
    const [isLoadingCardNumber, setIsLoadingCardNumber] = useState(false)
    const [sensitiveInfoVisible, setSensitiveInfoVisible] = useState(true)

    useEffect(() => {
        let timeout: NodeJS.Timeout

        if (open && (cardNumber || cvv)) {
            setSensitiveInfoVisible(true)
            timeout = setTimeout(() => {
                setSensitiveInfoVisible(false)
            }, 10000) // 10 seconds
        }

        return () => {
            if (timeout) clearTimeout(timeout)
        }
    }, [open, cardNumber, cvv])

    const revealSensitiveInfo = () => {
        setSensitiveInfoVisible(true)
        // Reset the timeout
        setTimeout(() => {
            setSensitiveInfoVisible(false)
        }, 10000)
    }

    const handleCopy = async (text: string, field: string) => {
        try {
            await navigator.clipboard.writeText(text)
            setCopiedField(field)
            setTimeout(() => setCopiedField(null), 2000)
        } catch (err) {
            console.error("Failed to copy text: ", err)
        }
    }

    const fetchCardDetails = async () => {
        if (!open) return

        setIsLoadingCvv(true)
        setIsLoadingCardNumber(true)

        try {
            const data = {
                expDate: card.expDate,
            }

            // Fetch CVV
            const cvvResponse = await axiosInstance.post(`data/${card.cardKey}/cvv`, data)
            setCvv(cvvResponse.data.decryptedCardCvv)
            // Fetch Card Number
            const cardNumberResponse = await axiosInstance.post(`data/${card.cardKey}/card-number`, data)
            setCardNumber(cardNumberResponse.data.decryptedCardNumber)
        } catch (error) {
            console.error("Error fetching card details:", error)
        } finally {
            setIsLoadingCvv(false)
            setIsLoadingCardNumber(false)
        }
    }

    useEffect(() => {
        fetchCardDetails()
    }, [open]) // Added fetchCardDetails to dependencies
    const formatCardNumber = (cardNumber: string): string => {
        return cardNumber
            .replace(/\D/g, "") // Remove non-digit characters
            .replace(/(.{4})/g, "$1     ") // Add space after every 4 digits
            .trim() // Remove trailing space
    }

    const formatExpiryDate = (expiryDate: string): string => {
        return expiryDate
            .replace(/\D/g, "") // Remove non-digit characters
            .replace(/(\d{2})\d{2}(\d{2})/, "$1/$2") // Convert YYYY to YY
    }
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>Card details</DialogTitle>
                </DialogHeader>
                <div className="space-y-6">
                    <div className="space-y-2">
                        <div className="text-sm text-gray-500">Cardholder name</div>
                        <div className="flex items-center justify-between">
                            <div className="font-medium">{card?.embossName1}</div>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 px-3 text-primary hover:bg-green-50 hover:text-primary"
                                onClick={() => handleCopy(card?.embossName1, "name")}
                            >
                                {copiedField === "name" ? "Copied" : "Copy"}
                            </Button>
                        </div>
                    </div>

                    <div className="space-y-2">
                        <div className="text-sm text-gray-500">Card number</div>
                        <div className="flex items-center justify-between">
                            {isLoadingCardNumber ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                                <div className="font-medium font-mono">
                                    {sensitiveInfoVisible
                                        ? formatCardNumber(cardNumber || card?.cardMask)
                                        : "•••• •••• •••• " + (cardNumber ? cardNumber.slice(-4) : "••••")}
                                    {!sensitiveInfoVisible && (
                                        <Button
                                            variant="link"
                                            size="sm"
                                            onClick={revealSensitiveInfo}
                                            className="ml-2 p-0 h-auto text-xs text-primary"
                                        >
                                            Show
                                        </Button>
                                    )}
                                </div>
                            )}
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 px-3 text-primary hover:bg-green-50 hover:text-primary"
                                onClick={() => handleCopy(cardNumber || card?.cardMask, "number")}
                                disabled={isLoadingCardNumber}
                            >
                                {copiedField === "number" ? "Copied" : "Copy"}
                            </Button>
                        </div>
                    </div>

                    <div className="space-y-2">
                        <div className="text-sm text-gray-500">Expiry date</div>
                        <div className="flex items-center justify-between">
                            <div className="font-medium  font-mono">{formatExpiryDate(card?.expDate)}</div>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 px-3 text-primary"
                                onClick={() => handleCopy(card?.expDate, "expiry")}
                            >
                                {copiedField === "expiry" ? "Copied" : "Copy"}
                            </Button>
                        </div>
                    </div>

                    <div className="space-y-2">
                        <div className="text-sm text-gray-500">Security code</div>
                        <div className="flex items-center justify-between">
                            {isLoadingCvv ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                                <div className="font-medium">
                                    {sensitiveInfoVisible ? cvv || "***" : "***"}
                                    {!sensitiveInfoVisible && cvv && (
                                        <Button
                                            variant="link"
                                            size="sm"
                                            onClick={revealSensitiveInfo}
                                            className="ml-2 p-0 h-auto text-xs text-primary"
                                        >
                                            Show
                                        </Button>
                                    )}
                                </div>
                            )}
                            {/*<Button*/}
                            {/*    variant="ghost"*/}
                            {/*    size="sm"*/}
                            {/*    className="h-8 px-3 text-primary hover:bg-green-50 hover:text-primary"*/}
                            {/*    onClick={() => handleCopy(cvv || "***", "security")}*/}
                            {/*    disabled={isLoadingCvv}*/}
                            {/*>*/}
                            {/*    {copiedField === "security" ? "Copied" : "Copy"}*/}
                            {/*</Button>*/}
                        </div>
                    </div>

                    <Button className="w-full " onClick={() => onOpenChange(false)}>
                        Done
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}

