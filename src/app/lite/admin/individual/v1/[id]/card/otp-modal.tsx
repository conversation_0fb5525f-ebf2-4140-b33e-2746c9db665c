"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    <PERSON>alogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Shield, ChevronRight, CreditCard, Building, Clock, DollarSign, AlertTriangle, Copy, Check } from "lucide-react"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"

interface OTPDialogProps {
    otpCode: string
    cardLastFour: string
    uniqueId: string
    merchantName: string
    dateTime: string
    currencyCode: string
    amount: string
    onApiSuccess?: () => void
}

export function SOTPDialog({
                               otpCode = "019283",
                               cardLastFour = "1234",
                               uniqueId = "TXN-2024-001234",
                               merchantName = "Amazon Web Services",
                               dateTime = "5th December 2024 at 2:30 PM",
                               currencyCode = "USD",
                               amount = "99.99",
                               onApiSuccess,
                           }: OTPDialogProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [copied, setCopied] = useState(false)

    const handleCopyOTP = async () => {
        try {
            await navigator.clipboard.writeText(otpCode)
            setCopied(true)
            setTimeout(() => setCopied(false), 2000)
        } catch (error) {
            console.error("Failed to copy OTP", error)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button
                    variant="ghost"
                    className="w-full justify-between px-4 py-3 h-auto hover:bg-gray-50 border border-gray-200 rounded-lg"
                >
                    <div className="flex items-center gap-3">
                        <div className="p-2 bg-blue-100 rounded-full">
                            <Shield className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="text-left">
                            <span className="font-medium text-gray-900 block">View OTP Code</span>
                            <span className="text-sm text-gray-500">Transaction verification required</span>
                        </div>
                    </div>
                    <ChevronRight className="h-5 w-5 text-gray-400" />
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
                <DialogHeader className="text-center pb-2">
                    <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
                        <Shield className="h-8 w-8 text-white" />
                    </div>
                    <DialogTitle className="text-2xl font-bold text-gray-900">Transaction Verification</DialogTitle>
                    <DialogDescription className="text-base text-gray-600 max-w-md mx-auto">
                        Ryvyl is sending you this OTP Code to verify your card transaction. Use this code to complete your payment.
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-8 py-6">
                    {/* OTP Code Section - More Prominent */}
                    <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
                        <CardContent className="p-8 text-center">
                            <Label className="text-lg font-semibold text-gray-800 block mb-4">Your OTP Code</Label>
                            <div className="relative">
                                <div className="text-6xl font-mono font-bold tracking-[0.5em] text-gray-900 mb-4 select-all">
                                    {otpCode}
                                </div>
                                <Button
                                    variant="outline"
                                    size="lg"
                                    onClick={handleCopyOTP}
                                    className="bg-white hover:bg-gray-50 border-2 shadow-sm"
                                >
                                    {copied ? (
                                        <>
                                            <Check className="h-4 w-4 text-green-600 mr-2" />
                                            <span className="text-green-600 font-medium">Copied!</span>
                                        </>
                                    ) : (
                                        <>
                                            <Copy className="h-4 w-4 mr-2" />
                                            <span className="font-medium">Copy Code</span>
                                        </>
                                    )}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Transaction Details - Better Layout */}
                    <Card className="border border-gray-200">
                        <CardContent className="p-6">
                            <h4 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                                <CreditCard className="h-5 w-5 text-gray-600" />
                                Transaction Details
                            </h4>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-4">
                                    <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                                        <CreditCard className="h-5 w-5 text-gray-600 mt-0.5" />
                                        <div className="flex-1 min-w-0">
                                            <span className="text-sm font-medium text-gray-600 block">Card Number</span>
                                            <p className="text-lg font-semibold text-gray-900">•••• {cardLastFour}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                                        <Shield className="h-5 w-5 text-gray-600 mt-0.5" />
                                        <div className="flex-1 min-w-0">
                                            <span className="text-sm font-medium text-gray-600 block">Transaction ID</span>
                                            <p className="text-lg font-semibold text-gray-900 break-all">{uniqueId}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                                        <Clock className="h-5 w-5 text-gray-600 mt-0.5" />
                                        <div className="flex-1 min-w-0">
                                            <span className="text-sm font-medium text-gray-600 block">Date & Time</span>
                                            <p className="text-lg font-semibold text-gray-900">{dateTime}</p>
                                        </div>
                                    </div>
                                </div>

                                <div className="space-y-4">
                                    <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                                        <Building className="h-5 w-5 text-gray-600 mt-0.5" />
                                        <div className="flex-1 min-w-0">
                                            <span className="text-sm font-medium text-gray-600 block">Merchant</span>
                                            <p className="text-lg font-semibold text-gray-900">{merchantName}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-start gap-3 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
                                        <DollarSign className="h-6 w-6 text-green-600 mt-0.5" />
                                        <div className="flex-1 min-w-0">
                                            <span className="text-sm font-medium text-green-700 block">Amount</span>
                                            <p className="text-2xl font-bold text-green-800">
                                                {currencyCode} {amount}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Security Warning - More Prominent */}
                    <Card className="border-2 border-amber-200 bg-gradient-to-r from-amber-50 to-orange-50">
                        <CardContent className="p-6">
                            <div className="flex gap-4">
                                <div className="p-2 bg-amber-100 rounded-full flex-shrink-0">
                                    <AlertTriangle className="h-6 w-6 text-amber-600" />
                                </div>
                                <div>
                                    <h5 className="font-semibold text-amber-800 text-lg mb-2">Important Security Notice</h5>
                                    <div className="space-y-2 text-amber-700">
                                        <p className="font-medium">• Never share this OTP code with anyone</p>
                                        <p className="font-medium">• Ryvyl will never ask for this code over the phone</p>
                                        <p className="font-medium">• This code expires in 10 minutes</p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <DialogFooter className="pt-6 border-t">
                    <div className="flex gap-3 w-full">
                        <Button variant="outline" onClick={() => setIsOpen(false)} className="flex-1 h-12 text-base">
                            Close
                        </Button>
                        <Button onClick={handleCopyOTP} className="flex-1 h-12 text-base bg-blue-600 hover:bg-blue-700">
                            {copied ? (
                                <>
                                    <Check className="h-4 w-4 mr-2" />
                                    Copied!
                                </>
                            ) : (
                                <>
                                    <Copy className="h-4 w-4 mr-2" />
                                    Copy OTP
                                </>
                            )}
                        </Button>
                    </div>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
