import {useState} from "react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {<PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogTrigger,} from "@/components/ui/dialog"
import {<PERSON>ertTriangle, ChevronRight, OctagonMinus} from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"
import Swal from 'sweetalert2'

interface RestrictCardDialogProps {
    expDate: string
    cardId: string
    onApiSuccess: () => void
}

export function RestrictCardDialog({expDate, cardId, onApiSuccess}: RestrictCardDialogProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [loading, setLoading] = useState(false)

    const restrictOptions = [
        // {id: "restrictFraud", label: "Fraud"},
        {id: "restrictStolen", label: "Stolen"},
        {id: "restrictLost", label: "Lost"},
        {id: "restrictMobileLost", label: "Mobile Lost"},
    ]

    const handleRestrict = async (restrictType: string) => {
        try {
            setLoading(true)
            const data = {expDate}
            await axiosInstance.post(`cards/${cardId}/${restrictType}`, data)
            onApiSuccess()
            setIsOpen(false)
            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: 'Card restricted successfully',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            })
        } catch (error) {
            console.error("Failed to restrict card", error)
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to restrict card. Please try again.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            })
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <AlertTriangle className="h-5 w-5 text-gray-600"/>
                        </div>
                        <span className="font-normal">Report card</span>
                    </div>
                    <ChevronRight/>
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Report Card</DialogTitle>
                </DialogHeader>
                <div className="grid   py-4">
                    {restrictOptions.map((option) => (
                        <Button disabled={loading} key={option.id} onClick={() => handleRestrict(option.id)} variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                                    <OctagonMinus className="h-5 w-5 text-gray-600"/>
                                </div>

                                <div className="flex flex-col ali">
                                    <span className="font-normal">{option.label}</span>
                                    <span className="font-small text-gray-500">Restrict card if {option.label}</span>
                                </div>
                            </div>
                            <ChevronRight/>
                        </Button>
                    ))}
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Cancel
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
