import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    <PERSON>alogHeader,
    <PERSON>alogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import axiosInstance from "@/utils/axiosInstance"
import {ChevronRight, Loader2, Pencil, ShieldAlert} from "lucide-react"
import {Label} from "@/components/ui/label";
import {Input} from "@/components/ui/input";
import Swal from 'sweetalert2'

interface ResignCardDialogProps {
    cardId: string
    expDate: string
    onApiSuccess: () => void
}

export function ResignCardDialog({ cardId, expDate, onApiSuccess }: ResignCardDialogProps) {
    const [loading, setLoading] = useState(false)
    const [reason, setReason] = useState("")
    const [description, setDescription] = useState("")

    const handleResignCard = async () => {
        try {
            setLoading(true)
            const data = {
                expDate,
                reason,
                description,
            }
            const response = await axiosInstance.post(`cards/${cardId}/resign`, data)

            if (typeof onApiSuccess === "function") {
                onApiSuccess()
            }

            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: 'Card resigned successfully',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            })
        } catch (error) {
            console.error("Failed to resign card", error)
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to resign card. Please try again.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            })
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <ShieldAlert className="h-5 w-5 text-gray-600"/>
                        </div>
                        <span className="font-normal">Resign Card</span>
                    </div>
                    <ChevronRight/>
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Resign Card</DialogTitle>
                    <DialogDescription>Are you sure you want to resign this card?</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="expDate" className="text-right">
                            Exp Date
                        </Label>
                        <Input
                            id="expDate"
                            value={expDate}
                            className="col-span-3"
                        />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="reason" className="text-right">
                            Reason
                        </Label>
                        <Input
                            id="reason"
                            value={reason}
                            onChange={(e) => setReason(e.target.value)}
                            className="col-span-3"
                        />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="description" className="text-right">
                            Description
                        </Label>
                        <Input
                            id="description"
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            className="col-span-3"
                        />
                    </div>
                </div>
                <DialogFooter>
                    <Button type="submit" onClick={handleResignCard} disabled={loading}>
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin"/>
                                Resigning...
                            </>
                        ) : (
                            "Resign"
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
