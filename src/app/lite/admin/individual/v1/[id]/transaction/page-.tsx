//@ts-nocheck
"use client"

import { useEffect, useState } from "react"
import { ArrowLeft, CalendarIcon, Download, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useRouter } from "next/navigation"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import axiosInstance from "@/utils/axiosInstance"
import { useAppSelector } from "@/store/hooks"
import { LoadingOverlay } from "@/components/LoadingOverlay"

interface Transaction {
    date: string
    group: string
    type: string
    merchantDetails: string
    responseCode: string
    status: string
    txnAmount: number
    txnCcy: string
    eurAmount: number
    details: string
}

interface ICard {
    id: string
    maskedNumber: string
}

interface FilterState {
    fromDate: string
    toDate: string
}

export default function CardTransactions({ params }: { params: { id: string } }) {
    const router = useRouter()
    const user = useAppSelector((state) => state.user.user)

    const [selectedCard, setSelectedCard] = useState(params.id)
    const [loading, setLoading] = useState(true)
    const [pendingTransactions, setPendingTransactions] = useState<Transaction[]>([])
    const [completedTransactions, setCompletedTransactions] = useState<Transaction[]>([])
    const [cancelledTransactions, setCancelledTransactions] = useState<Transaction[]>([])
    const [onboarding, setOnboarding] = useState<null | any>(null)
    const [account, setAccount] = useState<null | any>(null)
    const [cards, setCards] = useState<null | any>([]) // Initialize cards as an empty array
    const [error, setError] = useState<string | null>(null) // Add error state
    const [activeTab, setActiveTab] = useState("all")

    // Separate filter states for each tab
    const [allFilters, setAllFilters] = useState<FilterState>({ fromDate: "", toDate: "" })
    const [pendingFilters, setPendingFilters] = useState<FilterState>({ fromDate: "", toDate: "" })
    const [completedFilters, setCompletedFilters] = useState<FilterState>({ fromDate: "", toDate: "" })
    const [declinedFilters, setDeclinedFilters] = useState<FilterState>({ fromDate: "", toDate: "" })

    useEffect(() => {
        fetchTransactions(params.id).then((r) => console.log("transactions fetched"))
    }, [params.id])

    useEffect(() => {
        fetchOnboardingDetails()
    }, [])

    const fetchOnboardingDetails = async () => {
        setLoading(true)
        try {
            if (user.recordId !== null) {
                const response = await axiosInstance.get(`onboarding/personal/${user.recordId}`)
                setOnboarding(response.data.data)
                setAccount(response.data.account[0])
                setCards(response.data.cards || []) // Handle potential undefined cards
            }
        } catch (error: any) {
            console.error("Error fetching onboarding details:", error)
            setError(error.message) // Set error message
        } finally {
            setLoading(false)
        }
    }

    const fetchTransactions = async (cardId: string) => {
        try {
            setLoading(true)
            // const response = await axiosInstance.get(`cards/${cardId}/actionLog`)
            // console.log(response)

            // Simulate loading
            setTimeout(() => {
                setLoading(false)
            }, 1000)
        } catch (error) {
            console.error("Error fetching details:", error)
            setLoading(false)
        }
    }

    const handleGetTransactions = (tabName: string) => {
        // You would apply the specific tab's filters here
        fetchTransactions(selectedCard)
    }

    const handleCardChange = (value: string) => {
        setSelectedCard(value)
        // If you want to navigate to the new card's page
        if (value !== params.id) {
            router.push(`/cards/${value}/transactions`)
        }
    }

    const handleBack = () => {
        router.back()
    }

    // Calculate totals for all transactions
    const allTransactions = [...pendingTransactions, ...completedTransactions, ...cancelledTransactions]
    const totalMoneyIn = allTransactions.reduce((sum, t) => (t.txnAmount > 0 ? sum + t.txnAmount : sum), 0)
    const totalMoneyOut = allTransactions.reduce((sum, t) => (t.txnAmount < 0 ? sum + Math.abs(t.txnAmount) : sum), 0)

    if (loading) {
        return <LoadingOverlay />
    }

    return (
        <div className="container mx-auto py-6 px-4 max-w-[1200px]">
            <Button variant="ghost" className="mb-6 flex items-center gap-2 hover:bg-muted" onClick={handleBack}>
                <ArrowLeft className="h-4 w-4" />
                Back to Cards
            </Button>

            <Card className="w-full shadow-md">
                <CardHeader className="pb-4">
                    <CardTitle className="text-2xl font-bold">Card Transactions</CardTitle>
                    <p className="text-muted-foreground mt-1">View and filter transactions for your selected card</p>

                    <div className="mt-4">
                        <label className="block text-sm font-medium mb-2">Select Card</label>
                        <Select value={selectedCard} onValueChange={handleCardChange}>
                            <SelectTrigger className="w-full md:w-[300px]">
                                <SelectValue placeholder="Select a card" />
                            </SelectTrigger>
                            <SelectContent>
                                {cards
                                    .filter((r) => r.status.toLowerCase() === "active")
                                    .map((card) => (
                                        <SelectItem key={card.cardKey} value={card.cardKey}>
                                            {card.cardMask.slice(-4)} {card.embossName1}
                                        </SelectItem>
                                    ))}
                            </SelectContent>
                        </Select>
                    </div>
                </CardHeader>

                <CardContent className="space-y-8">
                    <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
                        <div className="border-b border-border mb-6">
                            <div className="flex space-x-8">
                                <TabsList className="bg-transparent p-0 h-auto">
                                    <TabsTrigger
                                        value="all"
                                        className="px-1 py-3 font-medium rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary text-muted-foreground"
                                    >
                                        All Transactions
                                        <Badge variant="outline" className="ml-2">
                                            {allTransactions.length || 0}
                                        </Badge>
                                    </TabsTrigger>
                                </TabsList>

                                <TabsList className="bg-transparent p-0 h-auto">
                                    <TabsTrigger
                                        value="pending"
                                        className="px-1 py-3 font-medium rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary text-muted-foreground"
                                    >
                                        Pending
                                        <Badge variant="outline" className="ml-2 bg-amber-500">
                                            {pendingTransactions.length || 0}
                                        </Badge>
                                    </TabsTrigger>
                                </TabsList>

                                <TabsList className="bg-transparent p-0 h-auto">
                                    <TabsTrigger
                                        value="completed"
                                        className="px-1 py-3 font-medium rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary text-muted-foreground"
                                    >
                                        Settled
                                        <Badge variant="primary" className="ml-2 text-white bg-primary">
                                            {completedTransactions.length || 0}
                                        </Badge>
                                    </TabsTrigger>
                                </TabsList>

                                <TabsList className="bg-transparent p-0 h-auto">
                                    <TabsTrigger
                                        value="declined"
                                        className="px-1 py-3 font-medium rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary text-muted-foreground"
                                    >
                                        Cancelled
                                        <Badge variant="destructive" className="ml-2">
                                            {cancelledTransactions.length || 0}
                                        </Badge>
                                    </TabsTrigger>
                                </TabsList>
                            </div>
                        </div>

                        {/* All Transactions Tab */}
                        <TabsContent value="all" className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <div>
                                    <label className="block text-sm font-medium mb-2">From Date</label>
                                    <div className="relative">
                                        <Input
                                            type="date"
                                            value={allFilters.fromDate}
                                            onChange={(e) => setAllFilters({ ...allFilters, fromDate: e.target.value })}
                                            className="w-full"
                                        />
                                        <CalendarIcon className="absolute right-3 top-2.5 h-4 w-4 opacity-50" />
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">To Date</label>
                                    <div className="relative">
                                        <Input
                                            type="date"
                                            value={allFilters.toDate}
                                            onChange={(e) => setAllFilters({ ...allFilters, toDate: e.target.value })}
                                            className="w-full"
                                        />
                                        <CalendarIcon className="absolute right-3 top-2.5 h-4 w-4 opacity-50" />
                                    </div>
                                </div>
                            </div>

                            <div className="flex justify-between items-center">
                                <Button
                                    className="bg-primary text-primary-foreground"
                                    onClick={() => handleGetTransactions("all")}
                                    disabled={loading}
                                >
                                    {loading ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            Loading...
                                        </>
                                    ) : (
                                        "Apply Filters"
                                    )}
                                </Button>

                                <Button variant="outline" className="gap-2">
                                    <Download className="h-4 w-4" />
                                    Download CSV
                                </Button>
                            </div>

                            <div className="grid grid-cols-2 gap-4 my-4">
                                <div className="p-4 rounded-lg bg-muted/50">
                                    <div className="text-sm text-muted-foreground">Total Money In</div>
                                    <div className="text-lg font-medium text-green-600">€{totalMoneyIn.toFixed(2)}</div>
                                </div>
                                <div className="p-4 rounded-lg bg-muted/50">
                                    <div className="text-sm text-muted-foreground">Total Money Out</div>
                                    <div className="text-lg font-medium text-red-600">€{totalMoneyOut.toFixed(2)}</div>
                                </div>
                            </div>

                            <TransactionTable transactions={allTransactions} />
                        </TabsContent>

                        {/* Authorizations Tab */}
                        <TabsContent value="pending" className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <div>
                                    <label className="block text-sm font-medium mb-2">From Date</label>
                                    <div className="relative">
                                        <Input
                                            type="date"
                                            value={pendingFilters.fromDate}
                                            onChange={(e) => setPendingFilters({ ...pendingFilters, fromDate: e.target.value })}
                                            className="w-full"
                                        />
                                        <CalendarIcon className="absolute right-3 top-2.5 h-4 w-4 opacity-50" />
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">To Date</label>
                                    <div className="relative">
                                        <Input
                                            type="date"
                                            value={pendingFilters.toDate}
                                            onChange={(e) => setPendingFilters({ ...pendingFilters, toDate: e.target.value })}
                                            className="w-full"
                                        />
                                        <CalendarIcon className="absolute right-3 top-2.5 h-4 w-4 opacity-50" />
                                    </div>
                                </div>
                            </div>

                            <div className="flex justify-between items-center">
                                <Button
                                    className="bg-primary text-primary-foreground"
                                    onClick={() => handleGetTransactions("pending")}
                                    disabled={loading}
                                >
                                    {loading ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            Loading...
                                        </>
                                    ) : (
                                        "Apply Filters"
                                    )}
                                </Button>

                                <Button variant="outline" className="gap-2">
                                    <Download className="h-4 w-4" />
                                    Download CSV
                                </Button>
                            </div>

                            <div className="grid grid-cols-2 gap-4 my-4">
                                <div className="p-4 rounded-lg bg-muted/50">
                                    <div className="text-sm text-muted-foreground">Total Money In</div>
                                    <div className="text-lg font-medium text-green-600">
                                        €{pendingTransactions.reduce((sum, t) => (t.txnAmount > 0 ? sum + t.txnAmount : sum), 0).toFixed(2)}
                                    </div>
                                </div>
                                <div className="p-4 rounded-lg bg-muted/50">
                                    <div className="text-sm text-muted-foreground">Total Money Out</div>
                                    <div className="text-lg font-medium text-red-600">
                                        €
                                        {pendingTransactions
                                            .reduce((sum, t) => (t.txnAmount < 0 ? sum + Math.abs(t.txnAmount) : sum), 0)
                                            .toFixed(2)}
                                    </div>
                                </div>
                            </div>

                            <TransactionTable transactions={pendingTransactions} />
                        </TabsContent>

                        {/* Completed Tab */}
                        <TabsContent value="completed" className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <div>
                                    <label className="block text-sm font-medium mb-2">From Date</label>
                                    <div className="relative">
                                        <Input
                                            type="date"
                                            value={completedFilters.fromDate}
                                            onChange={(e) => setCompletedFilters({ ...completedFilters, fromDate: e.target.value })}
                                            className="w-full"
                                        />
                                        <CalendarIcon className="absolute right-3 top-2.5 h-4 w-4 opacity-50" />
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">To Date</label>
                                    <div className="relative">
                                        <Input
                                            type="date"
                                            value={completedFilters.toDate}
                                            onChange={(e) => setCompletedFilters({ ...completedFilters, toDate: e.target.value })}
                                            className="w-full"
                                        />
                                        <CalendarIcon className="absolute right-3 top-2.5 h-4 w-4 opacity-50" />
                                    </div>
                                </div>
                            </div>

                            <div className="flex justify-between items-center">
                                <Button
                                    className="bg-primary text-primary-foreground"
                                    onClick={() => handleGetTransactions("completed")}
                                    disabled={loading}
                                >
                                    {loading ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            Loading...
                                        </>
                                    ) : (
                                        "Apply Filters"
                                    )}
                                </Button>

                                <Button variant="outline" className="gap-2">
                                    <Download className="h-4 w-4" />
                                    Download CSV
                                </Button>
                            </div>

                            <div className="grid grid-cols-2 gap-4 my-4">
                                <div className="p-4 rounded-lg bg-muted/50">
                                    <div className="text-sm text-muted-foreground">Total Money In</div>
                                    <div className="text-lg font-medium text-green-600">
                                        €
                                        {completedTransactions
                                            .reduce((sum, t) => (t.txnAmount > 0 ? sum + t.txnAmount : sum), 0)
                                            .toFixed(2)}
                                    </div>
                                </div>
                                <div className="p-4 rounded-lg bg-muted/50">
                                    <div className="text-sm text-muted-foreground">Total Money Out</div>
                                    <div className="text-lg font-medium text-red-600">
                                        €
                                        {completedTransactions
                                            .reduce((sum, t) => (t.txnAmount < 0 ? sum + Math.abs(t.txnAmount) : sum), 0)
                                            .toFixed(2)}
                                    </div>
                                </div>
                            </div>

                            <TransactionTable transactions={completedTransactions} />
                        </TabsContent>

                        {/* Declined Tab */}
                        <TabsContent value="declined" className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <div>
                                    <label className="block text-sm font-medium mb-2">From Date</label>
                                    <div className="relative">
                                        <Input
                                            type="date"
                                            value={declinedFilters.fromDate}
                                            onChange={(e) => setDeclinedFilters({ ...declinedFilters, fromDate: e.target.value })}
                                            className="w-full"
                                        />
                                        <CalendarIcon className="absolute right-3 top-2.5 h-4 w-4 opacity-50" />
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">To Date</label>
                                    <div className="relative">
                                        <Input
                                            type="date"
                                            value={declinedFilters.toDate}
                                            onChange={(e) => setDeclinedFilters({ ...declinedFilters, toDate: e.target.value })}
                                            className="w-full"
                                        />
                                        <CalendarIcon className="absolute right-3 top-2.5 h-4 w-4 opacity-50" />
                                    </div>
                                </div>
                            </div>

                            <div className="flex justify-between items-center">
                                <Button
                                    className="bg-primary text-primary-foreground"
                                    onClick={() => handleGetTransactions("declined")}
                                    disabled={loading}
                                >
                                    {loading ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            Loading...
                                        </>
                                    ) : (
                                        "Apply Filters"
                                    )}
                                </Button>

                                <Button variant="outline" className="gap-2">
                                    <Download className="h-4 w-4" />
                                    Download CSV
                                </Button>
                            </div>

                            <div className="grid grid-cols-2 gap-4 my-4">
                                <div className="p-4 rounded-lg bg-muted/50">
                                    <div className="text-sm text-muted-foreground">Total Money In</div>
                                    <div className="text-lg font-medium text-green-600">
                                        €
                                        {cancelledTransactions
                                            .reduce((sum, t) => (t.txnAmount > 0 ? sum + t.txnAmount : sum), 0)
                                            .toFixed(2)}
                                    </div>
                                </div>
                                <div className="p-4 rounded-lg bg-muted/50">
                                    <div className="text-sm text-muted-foreground">Total Money Out</div>
                                    <div className="text-lg font-medium text-red-600">
                                        €
                                        {cancelledTransactions
                                            .reduce((sum, t) => (t.txnAmount < 0 ? sum + Math.abs(t.txnAmount) : sum), 0)
                                            .toFixed(2)}
                                    </div>
                                </div>
                            </div>

                            <TransactionTable transactions={cancelledTransactions} />
                        </TabsContent>
                    </Tabs>
                </CardContent>
            </Card>
        </div>
    )
}

interface TransactionTableProps {
    transactions: Transaction[]
}

function TransactionTable({ transactions }: TransactionTableProps) {
    return (
        <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Date</TableHead>
                            <TableHead>Group</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead className="min-w-[200px]">Merchant Details</TableHead>
                            <TableHead>Response Code</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>TXN Amount</TableHead>
                            <TableHead>TXN Ccy</TableHead>
                            <TableHead>EUR Amount</TableHead>
                            <TableHead>Details</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {transactions.length === 0 ? (
                            <TableRow>
                                <TableCell colSpan={10} className="h-24 text-center text-muted-foreground">
                                    No transactions found
                                </TableCell>
                            </TableRow>
                        ) : (
                            transactions.map((transaction, index) => (
                                <TableRow key={index}>
                                    <TableCell>{transaction.date}</TableCell>
                                    <TableCell>{transaction.group}</TableCell>
                                    <TableCell>{transaction.type}</TableCell>
                                    <TableCell>{transaction.merchantDetails}</TableCell>
                                    <TableCell>{transaction.responseCode}</TableCell>
                                    <TableCell>{transaction.status}</TableCell>
                                    <TableCell>{transaction.txnAmount}</TableCell>
                                    <TableCell>{transaction.txnCcy}</TableCell>
                                    <TableCell>{transaction.eurAmount}</TableCell>
                                    <TableCell>{transaction.details}</TableCell>
                                </TableRow>
                            ))
                        )}
                    </TableBody>
                </Table>
            </div>
        </div>
    )
}

// Custom Badge component with success variant
declare module "@/components/ui/badge" {
    interface BadgeVariants {
        variant: "default" | "secondary" | "destructive" | "outline" | "success"
    }
}

