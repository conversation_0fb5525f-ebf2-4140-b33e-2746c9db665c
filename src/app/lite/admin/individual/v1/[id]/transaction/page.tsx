//@ts-nocheck
"use client"

import { useState, useEffect } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import {
    ChevronLeft,
    ChevronRight,
    Download,
    Filter,
    RefreshCw,
    Search,
    Calendar,
    ArrowUpDown,
    Info,
    Building,
    Terminal,
    FileText,
    Clock,
    DollarSign,
    CreditCard,
    CheckCircle2,
    XCircle,
    AlertCircle,
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { DateRangePicker } from "@/components/date-range-picker"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import axiosInstance from "@/utils/axiosInstance"
import { country_currency } from "@/utils/data"

interface TransactionResult {
    approved: boolean
    message: string
}

interface Transaction {
    transactionTypeCode: string
    transactionAmount: number
    billingAmount: number
    transactionDatetime: string
    conversionRate: string
    traceNumber: string
    cardExpiryDate: string
    retrievalReference: string
    terminalId: string
    merchantId: string
    merchantLocation: string
    currencyTransaction: string
    currencyBilling: string
    accountIdentification: string
    cardId: string
    result: TransactionResult
    settled: boolean
    customFields: {
        transactionInfo: string
        paypalReference: string
    }
    extendedData: {
        authorizationLevel: string
        authorizationMethod: string
        productCode: string
        deviceId: string
        channelId: string
        riskScore: string
        retryCount: string
        trackingId: string
    }
    externalToken: string
}

export default function TransactionsPage() {
    const router = useRouter()
    const searchParams = useSearchParams()

    // Pagination state
    const [page, setPage] = useState(Number.parseInt(searchParams.get("page") || "1"))
    const [limit, setLimit] = useState(Number.parseInt(searchParams.get("limit") || "10"))
    const [totalPages, setTotalPages] = useState(1)
    const [totalTransactions, setTotalTransactions] = useState(0)

    // Filter state
    const [transactionType, setTransactionType] = useState(searchParams.get("type") || "all")
    const [terminalId, setTerminalId] = useState(searchParams.get("terminal") || "all")
    const [cardId, setCardId] = useState(searchParams.get("cardId") || "all")
    const [search, setSearch] = useState(searchParams.get("search") || "")
    const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({
        from: searchParams.get("from") ? new Date(searchParams.get("from") as string) : undefined,
        to: searchParams.get("to") ? new Date(searchParams.get("to") as string) : undefined,
    })

    // Data state
    const [transactions, setTransactions] = useState<Transaction[]>([])
    const [cardIds, setCardIds] = useState<string[]>([])
    const [loading, setLoading] = useState(true)
    const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null)
    const [activeTab, setActiveTab] = useState("all")

    // Fetch transactions with current filters and pagination
    const fetchTransactions = async () => {
        setLoading(true)

        // Build query params
        const params = new URLSearchParams()
        params.set("page", page.toString())
        params.set("limit", limit.toString())

        if (transactionType && transactionType !== "all") params.set("type", transactionType)
        if (terminalId && terminalId !== "all") params.set("terminal", terminalId)
        if (cardId && cardId !== "all") params.set("cardId", cardId)
        if (search) params.set("search", search)
        if (dateRange.from) params.set("from", dateRange.from.toISOString())
        if (dateRange.to) params.set("to", dateRange.to.toISOString())

        try {
            const response = await axiosInstance.get(`/legacy/transactions`, {
                params: {
                    page: page,
                    limit: limit,
                    dateStart: dateRange.from?.toISOString() || "",
                    dateEnd: dateRange.to?.toISOString() || "",
                    cardId: cardId !== "all" ? cardId : "",
                },
            })

            const data = response.data
            setTransactions(data.transactions)
            setTotalPages(data.pagination.totalPages)
            setTotalTransactions(data.pagination.total)

            // Extract unique card IDs for the dropdown
            const uniqueCardIds = Array.from(new Set(data.transactions.map((t: Transaction) => t.cardId).filter(Boolean)))
            setCardIds(uniqueCardIds)
        } catch (error) {
            console.error("Error fetching transactions:", error)
        } finally {
            setLoading(false)
        }
    }

    // Update URL with current filters and pagination
    const updateUrl = () => {
        const params = new URLSearchParams()

        params.set("page", page.toString())
        params.set("limit", limit.toString())

        if (transactionType && transactionType !== "all") params.set("type", transactionType)
        if (terminalId && terminalId !== "all") params.set("terminal", terminalId)
        if (cardId && cardId !== "all") params.set("cardId", cardId)
        if (search) params.set("search", search)
        if (dateRange.from) params.set("from", dateRange.from.toISOString())
        if (dateRange.to) params.set("to", dateRange.to.toISOString())

        router.push(`transaction?${params.toString()}`)
    }

    // Apply filters
    const applyFilters = () => {
        setPage(1) // Reset to first page when filters change
        updateUrl()
        fetchTransactions()
    }

    // Reset filters
    const resetFilters = () => {
        setTransactionType("all")
        setTerminalId("all")
        setCardId("all")
        setSearch("")
        setDateRange({ from: undefined, to: undefined })
        setPage(1)
    }

    // Handle pagination
    const goToPage = (newPage: number) => {
        if (newPage < 1 || newPage > totalPages) return
        setPage(newPage)
    }

    // Effect to fetch transactions when page or limit changes
    useEffect(() => {
        updateUrl()
        fetchTransactions()
    }, [page, limit])

    // Format date for display
    const formatDate = (dateString: string) => {
        if (!dateString || dateString.length < 10) return "N/A"

        // Handle special case for the unusual format
        if (dateString.length === 12 && dateString.startsWith("000000000")) {
            return (
                new Date().toISOString().split("T")[0] + " " + dateString.substr(9, 2) + ":" + dateString.substr(11, 1) + "0:00"
            )
        }

        const year = "20" + dateString.substr(0, 2)
        const month = dateString.substr(2, 2)
        const day = dateString.substr(4, 2)
        const hour = dateString.substr(6, 2)
        const minute = dateString.substr(8, 2)
        let second = "00"
        if (dateString.length >= 12) {
            second = dateString.substr(10, 2)
        }

        return `${year}-${month}-${day} ${hour}:${minute}:${second}`
    }

    // Get transaction type badge color and style
    const getTransactionTypeInfo = (typeCode: string) => {
        const code = typeCode.substring(0, 2)
        switch (code) {
            case "38": // Authorization
                return {
                    color: "bg-blue-100 text-blue-800 border-blue-300",
                    icon: <Info className="h-3.5 w-3.5 mr-1" />,
                    description: "Authorization",
                }
            case "20": // Purchase
                return {
                    color: "bg-emerald-100 text-emerald-800 border-emerald-300",
                    icon: <DollarSign className="h-3.5 w-3.5 mr-1" />,
                    description: "Purchase",
                }
            case "22": // Refund
                return {
                    color: "bg-amber-100 text-amber-800 border-amber-300",
                    icon: <ArrowUpDown className="h-3.5 w-3.5 mr-1" />,
                    description: "Refund",
                }
            case "24": // Reversal
                return {
                    color: "bg-rose-100 text-rose-800 border-rose-300",
                    icon: <RefreshCw className="h-3.5 w-3.5 mr-1" />,
                    description: "Reversal",
                }
            case "09": // Balance Inquiry
                return {
                    color: "bg-purple-100 text-purple-800 border-purple-300",
                    icon: <AlertCircle className="h-3.5 w-3.5 mr-1" />,
                    description: "Balance Inquiry",
                }
            case "00": // Default/Other
                return {
                    color: "bg-gray-100 text-gray-800 border-gray-300",
                    icon: <CreditCard className="h-3.5 w-3.5 mr-1" />,
                    description: "Card Transaction",
                }
            default:
                return {
                    color: "bg-gray-100 text-gray-800 border-gray-300",
                    icon: <FileText className="h-3.5 w-3.5 mr-1" />,
                    description: "Other",
                }
        }
    }

    // Format currency
    const formatCurrency = (amount: number, currencyCode: string) => {
        // Handle string currency codes like "EUR" directly
        if (typeof currencyCode === "string" && currencyCode.length === 3 && isNaN(Number(currencyCode))) {
            return Intl.NumberFormat("en-US", {
                style: "currency",
                currency: currencyCode,
            }).format(amount)
        }

        // Handle numeric currency codes
        const currency = country_currency.find((r) => r.numericCode === currencyCode)?.currencyCode || "EUR"
        return Intl.NumberFormat("en-US", {
            style: "currency",
            currency: currency,
        }).format(amount)
    }

    // Get available transaction statuses based on actual data
    const getAvailableTransactionStatuses = () => {
        const statusCounts = {
            pending: 0,
            settled: 0,
            cancellations: 0,
            declined: 0,
            refunds: 0,
            notifications: 0,
        }

        transactions.forEach((transaction) => {
            // Categorize transactions based on their properties
            if (!transaction.result?.approved) {
                statusCounts.declined++
            } else if (transaction.transactionTypeCode.startsWith("22")) {
                // Refund transactions
                statusCounts.refunds++
            } else if (transaction.transactionTypeCode.startsWith("24")) {
                // Reversal/Cancellation transactions
                statusCounts.cancellations++
            } else if (transaction.transactionTypeCode.startsWith("09")) {
                // Balance Inquiry/Notifications
                statusCounts.notifications++
            } else if (transaction.settled) {
                statusCounts.settled++
            } else {
                statusCounts.pending++
            }
        })

        const statusMap = [
            { code: "pending", label: "Pending", count: statusCounts.pending },
            { code: "settled", label: "Approved", count: statusCounts.settled },
            { code: "cancellations", label: "Cancellations", count: statusCounts.cancellations },
            { code: "declined", label: "Declined", count: statusCounts.declined },
            { code: "refunds", label: "Refunds", count: statusCounts.refunds },
            { code: "notifications", label: "Notifications", count: statusCounts.notifications },
        ]

        // Only return statuses that have transactions
        return statusMap.filter((status) => status.count > 0)
    }

    // Filter transactions by status for tabs
    const getFilteredTransactions = () => {
        if (activeTab === "all") return transactions

        return transactions.filter((transaction) => {
            switch (activeTab) {
                case "pending":
                    return (
                        transaction.result?.approved &&
                        !transaction.settled &&
                        !transaction.transactionTypeCode.startsWith("22") &&
                        !transaction.transactionTypeCode.startsWith("24") &&
                        !transaction.transactionTypeCode.startsWith("09")
                    )
                case "settled":
                    return transaction.result?.approved && transaction.settled
                case "cancellations":
                    return transaction.transactionTypeCode.startsWith("24") // Reversal transactions
                case "declined":
                    return !transaction.result?.approved
                case "refunds":
                    return transaction.transactionTypeCode.startsWith("22") // Refund transactions
                case "notifications":
                    return transaction.transactionTypeCode.startsWith("09") // Balance Inquiry transactions
                default:
                    return true
            }
        })
    }

    return (
        <div className="container mx-auto py-8 space-y-8">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Transactions</h1>
                    <p className="text-muted-foreground mt-1">View and manage your transaction history</p>
                </div>
                <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" onClick={fetchTransactions} className="flex items-center gap-1">
                        <RefreshCw className="h-4 w-4" />
                        <span className="hidden sm:inline">Refresh</span>
                    </Button>
                    <Button variant="outline" size="sm" className="flex items-center gap-1">
                        <Download className="h-4 w-4" />
                        <span className="hidden sm:inline">Export</span>
                    </Button>
                </div>
            </div>

            {/* Filters */}
            <Card className="border-border/40 shadow-sm">
                <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                        <Filter className="h-4 w-4" />
                        Filters
                    </CardTitle>
                    <CardDescription>Filter transactions by various criteria</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Search</label>
                            <div className="relative">
                                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    type="text"
                                    placeholder="Reference, Account..."
                                    className="pl-8"
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <label className="text-sm font-medium">Transaction Type</label>
                            <Select value={transactionType} onValueChange={setTransactionType}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Types" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Types</SelectItem>
                                    <SelectItem value="pending">Pending</SelectItem>
                                    <SelectItem value="settled">Approved</SelectItem>
                                    <SelectItem value="cancellations">Cancellations</SelectItem>
                                    <SelectItem value="declined">Declined</SelectItem>
                                    <SelectItem value="refunds">Refunds</SelectItem>
                                    <SelectItem value="notifications">Notifications</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <label className="text-sm font-medium">Terminal ID</label>
                            <Select value={terminalId} onValueChange={setTerminalId}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Terminals" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Terminals</SelectItem>
                                    <SelectItem value="TERM0001">TERM0001</SelectItem>
                                    <SelectItem value="20371960">20371960</SelectItem>
                                    <SelectItem value="20343744">20343744</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <label className="text-sm font-medium">Card ID</label>
                            <Select value={cardId} onValueChange={setCardId}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Cards" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Cards</SelectItem>
                                    {cardIds.map((id) => (
                                        <SelectItem key={id} value={id}>
                                            {id}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <label className="text-sm font-medium flex items-center gap-1">
                                <Calendar className="h-4 w-4" />
                                Date Range
                            </label>
                            <DateRangePicker date={dateRange} onDateChange={setDateRange} />
                        </div>
                    </div>

                    <div className="flex justify-end gap-2 mt-4">
                        <Button variant="outline" size="sm" onClick={resetFilters}>
                            Reset
                        </Button>
                        <Button size="sm" onClick={applyFilters}>
                            Apply Filters
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Transactions Table */}
            <Card className="border-border/40 shadow-sm">
                <CardHeader className="pb-0">
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                        <CardTitle className="text-lg">Transaction History</CardTitle>
                        <div className="text-sm text-muted-foreground">
                            Showing {transactions.length} of {totalTransactions} transactions
                        </div>
                    </div>
                </CardHeader>

                <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mt-2">
                    <div className="px-6">
                        <div className="flex flex-wrap gap-2 mb-4">
                            {/* All Tab */}
                            <button
                                onClick={() => setActiveTab("all")}
                                className={`flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 ${
                                    activeTab === "all"
                                        ? "bg-primary text-primary-foreground shadow-md"
                                        : "bg-muted/50 text-muted-foreground hover:bg-muted hover:text-foreground"
                                }`}
                            >
                                <div className="flex items-center gap-2">
                                    <div className="w-2 h-2 rounded-full bg-current opacity-60"></div>
                                    <span>All Transactions</span>
                                    <Badge
                                        variant={activeTab === "all" ? "secondary" : "outline"}
                                        className={`ml-1 text-xs ${
                                            activeTab === "all"
                                                ? "bg-primary-foreground/20 text-primary-foreground border-primary-foreground/30"
                                                : ""
                                        }`}
                                    >
                                        {transactions.length}
                                    </Badge>
                                </div>
                            </button>

                            {/* All Status Tabs - Always Show */}
                            {[
                                {
                                    code: "pending",
                                    label: "Pending",
                                    icon: <Clock className="w-4 h-4" />,
                                    bgClass:
                                        activeTab === "pending"
                                            ? "bg-amber-500 text-white"
                                            : "bg-amber-50 text-amber-700 hover:bg-amber-100",
                                    badgeClass:
                                        activeTab === "pending"
                                            ? "bg-white/20 text-white border-white/30"
                                            : "bg-amber-100 text-amber-800 border-amber-200",
                                },
                                {
                                    code: "settled",
                                    label: "Approved",
                                    icon: <CheckCircle2 className="w-4 h-4" />,
                                    bgClass:
                                        activeTab === "settled"
                                            ? "bg-green-500 text-white"
                                            : "bg-green-50 text-green-700 hover:bg-green-100",
                                    badgeClass:
                                        activeTab === "settled"
                                            ? "bg-white/20 text-white border-white/30"
                                            : "bg-green-100 text-green-800 border-green-200",
                                },
                                {
                                    code: "cancellations",
                                    label: "Cancellations",
                                    icon: <XCircle className="w-4 h-4" />,
                                    bgClass:
                                        activeTab === "cancellations" ? "bg-red-500 text-white" : "bg-red-50 text-red-700 hover:bg-red-100",
                                    badgeClass:
                                        activeTab === "cancellations"
                                            ? "bg-white/20 text-white border-white/30"
                                            : "bg-red-100 text-red-800 border-red-200",
                                },
                                {
                                    code: "declined",
                                    label: "Declined",
                                    icon: <AlertCircle className="w-4 h-4" />,
                                    bgClass:
                                        activeTab === "declined" ? "bg-rose-500 text-white" : "bg-rose-50 text-rose-700 hover:bg-rose-100",
                                    badgeClass:
                                        activeTab === "declined"
                                            ? "bg-white/20 text-white border-white/30"
                                            : "bg-rose-100 text-rose-800 border-rose-200",
                                },
                                {
                                    code: "refunds",
                                    label: "Refunds",
                                    icon: <ArrowUpDown className="w-4 h-4" />,
                                    bgClass:
                                        activeTab === "refunds" ? "bg-blue-500 text-white" : "bg-blue-50 text-blue-700 hover:bg-blue-100",
                                    badgeClass:
                                        activeTab === "refunds"
                                            ? "bg-white/20 text-white border-white/30"
                                            : "bg-blue-100 text-blue-800 border-blue-200",
                                },
                                {
                                    code: "notifications",
                                    label: "Notifications",
                                    icon: <Info className="w-4 h-4" />,
                                    bgClass:
                                        activeTab === "notifications"
                                            ? "bg-purple-500 text-white"
                                            : "bg-purple-50 text-purple-700 hover:bg-purple-100",
                                    badgeClass:
                                        activeTab === "notifications"
                                            ? "bg-white/20 text-white border-white/30"
                                            : "bg-purple-100 text-purple-800 border-purple-200",
                                },
                            ].map((status) => {
                                // Calculate count for this status
                                let count = 0
                                transactions.forEach((transaction) => {
                                    switch (status.code) {
                                        case "pending":
                                            if (
                                                transaction.result?.approved &&
                                                !transaction.settled &&
                                                !transaction.transactionTypeCode.startsWith("22") &&
                                                !transaction.transactionTypeCode.startsWith("24") &&
                                                !transaction.transactionTypeCode.startsWith("09")
                                            ) {
                                                count++
                                            }
                                            break
                                        case "settled":
                                            if (transaction.result?.approved && transaction.settled) count++
                                            break
                                        case "cancellations":
                                            if (transaction.transactionTypeCode.startsWith("24")) count++
                                            break
                                        case "declined":
                                            if (!transaction.result?.approved) count++
                                            break
                                        case "refunds":
                                            if (transaction.transactionTypeCode.startsWith("22")) count++
                                            break
                                        case "notifications":
                                            if (transaction.transactionTypeCode.startsWith("09")) count++
                                            break
                                    }
                                })

                                return (
                                    <button
                                        key={status.code}
                                        onClick={() => setActiveTab(status.code)}
                                        className={`flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 ${status.bgClass}`}
                                    >
                                        <div className="flex items-center gap-2">
                                            {status.icon}
                                            <span>{status.label}</span>
                                            <Badge variant="outline" className={`ml-1 text-xs border ${status.badgeClass}`}>
                                                {count}
                                            </Badge>
                                        </div>
                                    </button>
                                )
                            })}
                        </div>

                        {/* Status Summary Bar */}
                        {transactions.length > 0 && (
                            <div className="mb-4 p-4 bg-muted/30 rounded-lg border">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                        <div className="flex items-center gap-2">
                                            <div className="w-3 h-3 rounded-full bg-current opacity-60"></div>
                                            <span className="text-sm font-medium">
                        {activeTab === "all"
                            ? "All Transactions"
                            : activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}
                      </span>
                                        </div>
                                        <Separator orientation="vertical" className="h-4" />
                                        <span className="text-sm text-muted-foreground">
                      Showing {getFilteredTransactions().length} of {transactions.length} transactions
                    </span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        {activeTab !== "all" && (
                                            <Button variant="ghost" size="sm" onClick={() => setActiveTab("all")} className="text-xs">
                                                View All
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    <TabsContent value={activeTab} className="mt-0 pt-0">
                        <CardContent className="p-0 pt-4">
                            {transactions.length === 0 ? (
                                /* Complete Empty State */
                                <div className="p-8 bg-gradient-to-br from-muted/30 to-muted/10 rounded-lg border border-dashed border-muted-foreground/20">
                                    <div className="text-center space-y-4">
                                        <div className="mx-auto w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center">
                                            <FileText className="h-8 w-8 text-muted-foreground/60" />
                                        </div>
                                        <div className="space-y-2">
                                            <h3 className="text-lg font-semibold text-muted-foreground">No transactions found</h3>
                                            <p className="text-sm text-muted-foreground/70 max-w-md mx-auto">
                                                There are no transactions to display at the moment. Transactions will appear here once they are
                                                processed.
                                            </p>
                                        </div>
                                        <div className="flex flex-col sm:flex-row gap-2 justify-center items-center pt-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={fetchTransactions}
                                                className="flex items-center gap-1"
                                            >
                                                <RefreshCw className="h-4 w-4" />
                                                Refresh Data
                                            </Button>
                                            <Button variant="ghost" size="sm" onClick={resetFilters} className="flex items-center gap-1">
                                                <Filter className="h-4 w-4" />
                                                Clear Filters
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            ) : getFilteredTransactions().length === 0 ? (
                                /* Status-Specific Empty State */
                                <div className="p-8 bg-gradient-to-br from-muted/20 to-muted/5 rounded-lg border border-dashed border-muted-foreground/15">
                                    <div className="text-center space-y-4">
                                        <div className="mx-auto w-16 h-16 bg-muted/40 rounded-full flex items-center justify-center">
                                            {(() => {
                                                const statusIcons = {
                                                    pending: <Clock className="h-8 w-8 text-amber-500/70" />,
                                                    settled: <CheckCircle2 className="h-8 w-8 text-green-500/70" />,
                                                    cancellations: <XCircle className="h-8 w-8 text-red-500/70" />,
                                                    declined: <AlertCircle className="h-8 w-8 text-rose-500/70" />,
                                                    refunds: <ArrowUpDown className="h-8 w-8 text-blue-500/70" />,
                                                    notifications: <Info className="h-8 w-8 text-purple-500/70" />,
                                                }
                                                return statusIcons[activeTab] || <FileText className="h-8 w-8 text-muted-foreground/60" />
                                            })()}
                                        </div>
                                        <div className="space-y-2">
                                            <h3 className="text-lg font-semibold text-muted-foreground">
                                                No {activeTab === "all" ? "transactions" : activeTab} found
                                            </h3>
                                            <p className="text-sm text-muted-foreground/70 max-w-md mx-auto">
                                                {(() => {
                                                    const messages = {
                                                        pending:
                                                            "No pending transactions at the moment. Approved transactions that haven't been settled will appear here.",
                                                        settled:
                                                            "No approved transactions found. Completed and finalized transactions will be displayed here.",
                                                        cancellations:
                                                            "No cancelled transactions found. Reversed or cancelled transactions will appear here.",
                                                        declined:
                                                            "No declined transactions found. Rejected or failed transactions will be shown here.",
                                                        refunds:
                                                            "No refund transactions found. Processed refunds and returns will be displayed here.",
                                                        notifications:
                                                            "No notification transactions found. Balance inquiries and system notifications will appear here.",
                                                    }
                                                    return messages[activeTab] || "No transactions match the current criteria."
                                                })()}
                                            </p>
                                        </div>
                                        <div className="flex flex-col sm:flex-row gap-2 justify-center items-center pt-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => setActiveTab("all")}
                                                className="flex items-center gap-1"
                                            >
                                                <FileText className="h-4 w-4" />
                                                View All Transactions
                                            </Button>
                                            <Button variant="ghost" size="sm" onClick={fetchTransactions} className="flex items-center gap-1">
                                                <RefreshCw className="h-4 w-4" />
                                                Refresh
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                /* Transaction Table */
                                <div className="rounded-md border overflow-hidden">
                                    <Table>
                                        <TableHeader>
                                            <TableRow className="bg-muted/50">
                                                <TableHead className="w-[180px]">Date/Time</TableHead>
                                                <TableHead className="w-[120px]">Type</TableHead>
                                                <TableHead className="w-[140px]">Transaction Amount</TableHead>
                                                <TableHead className="w-[140px]">Billing Amount</TableHead>
                                                <TableHead className="w-[140px]">Reference</TableHead>
                                                <TableHead>Account</TableHead>
                                                <TableHead className="w-[100px]">Status</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {loading
                                                ? Array.from({ length: limit }).map((_, i) => (
                                                    <TableRow key={i} className="hover:bg-muted/50">
                                                        <TableCell>
                                                            <Skeleton className="h-6 w-full" />
                                                        </TableCell>
                                                        <TableCell>
                                                            <Skeleton className="h-6 w-16" />
                                                        </TableCell>
                                                        <TableCell>
                                                            <Skeleton className="h-6 w-16" />
                                                        </TableCell>
                                                        <TableCell>
                                                            <Skeleton className="h-6 w-16" />
                                                        </TableCell>
                                                        <TableCell>
                                                            <Skeleton className="h-6 w-24" />
                                                        </TableCell>
                                                        <TableCell>
                                                            <Skeleton className="h-6 w-16" />
                                                        </TableCell>
                                                        <TableCell>
                                                            <Skeleton className="h-6 w-full" />
                                                        </TableCell>
                                                    </TableRow>
                                                ))
                                                : getFilteredTransactions().map((transaction, index) => {
                                                    const typeInfo = getTransactionTypeInfo(transaction.transactionTypeCode)
                                                    return (
                                                        <TableRow
                                                            onClick={() => setSelectedTransaction(transaction)}
                                                            key={index}
                                                            className="hover:bg-muted/50 cursor-pointer"
                                                        >
                                                            <TableCell className="font-medium">
                                                                {formatDate(transaction.transactionDatetime)}
                                                            </TableCell>
                                                            <TableCell>
                                                                <Badge className={`${typeInfo.color} border flex items-center`} variant="outline">
                                                                    {typeInfo.icon}
                                                                    {typeInfo.description}
                                                                </Badge>
                                                            </TableCell>
                                                            <TableCell className="font-mono">
                                                                {formatCurrency(transaction.transactionAmount, transaction.currencyTransaction)}
                                                            </TableCell>
                                                            <TableCell className="font-mono">
                                                                {formatCurrency(transaction.billingAmount, transaction.currencyBilling)}
                                                            </TableCell>
                                                            <TableCell className="font-medium text-primary">
                                                                {transaction.retrievalReference}
                                                            </TableCell>
                                                            <TableCell className="max-w-[200px] truncate">
                                                                <span className="text-muted-foreground">{transaction.accountIdentification}</span>
                                                            </TableCell>
                                                            <TableCell>
                                                                {transaction.result && (
                                                                    <Badge
                                                                        variant="outline"
                                                                        className={`flex items-center ${
                                                                            transaction.result.approved
                                                                                ? "bg-green-100 text-green-800 border-green-300"
                                                                                : "bg-red-100 text-red-800 border-red-300"
                                                                        }`}
                                                                    >
                                                                        {transaction.result.approved ? (
                                                                            <CheckCircle2 className="h-3.5 w-3.5 mr-1" />
                                                                        ) : (
                                                                            <XCircle className="h-3.5 w-3.5 mr-1" />
                                                                        )}
                                                                        {transaction.result.approved ? "Approved" : "Declined"}
                                                                    </Badge>
                                                                )}
                                                            </TableCell>
                                                        </TableRow>
                                                    )
                                                })}
                                        </TableBody>
                                    </Table>
                                </div>
                            )}
                        </CardContent>

                        {/* Pagination - only show when there are transactions */}
                        {transactions.length > 0 && getFilteredTransactions().length > 0 && (
                            <CardFooter className="flex flex-col sm:flex-row items-center justify-between gap-4 pt-2 pb-6">
                                <div className="text-sm text-muted-foreground order-2 sm:order-1">
                                    Page {page} of {totalPages} ({totalTransactions} total transactions)
                                </div>
                                <div className="flex items-center gap-2 order-1 sm:order-2">
                                    <Select
                                        value={limit.toString()}
                                        onValueChange={(value) => {
                                            setLimit(Number.parseInt(value))
                                            setPage(1)
                                        }}
                                    >
                                        <SelectTrigger className="w-[100px]">
                                            <SelectValue placeholder="10 per page" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="10">10 per page</SelectItem>
                                            <SelectItem value="25">25 per page</SelectItem>
                                            <SelectItem value="50">50 per page</SelectItem>
                                            <SelectItem value="100">100 per page</SelectItem>
                                        </SelectContent>
                                    </Select>

                                    <div className="flex items-center gap-1">
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            onClick={() => goToPage(1)}
                                            disabled={page === 1}
                                            className="h-8 w-8"
                                        >
                                            <ChevronLeft className="h-3 w-3" />
                                            <ChevronLeft className="h-3 w-3 -ml-2" />
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            onClick={() => goToPage(page - 1)}
                                            disabled={page === 1}
                                            className="h-8 w-8"
                                        >
                                            <ChevronLeft className="h-4 w-4" />
                                        </Button>

                                        <div className="flex items-center gap-1 mx-2">
                                            {(() => {
                                                let startPage = Math.max(1, page - 2)
                                                const endPage = Math.min(totalPages, startPage + 4)
                                                if (endPage - startPage < 4 && startPage > 1) {
                                                    startPage = Math.max(1, endPage - 4)
                                                }
                                                const pageNumbers = []
                                                for (let i = startPage; i <= endPage; i++) {
                                                    pageNumbers.push(i)
                                                }
                                                return pageNumbers.map((pageNum) => (
                                                    <Button
                                                        key={pageNum}
                                                        variant={pageNum === page ? "default" : "outline"}
                                                        size="icon"
                                                        className="w-8 h-8"
                                                        onClick={() => goToPage(pageNum)}
                                                    >
                                                        {pageNum}
                                                    </Button>
                                                ))
                                            })()}
                                        </div>

                                        <Button
                                            variant="outline"
                                            size="icon"
                                            onClick={() => goToPage(page + 1)}
                                            disabled={page === totalPages}
                                            className="h-8 w-8"
                                        >
                                            <ChevronRight className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            onClick={() => goToPage(totalPages)}
                                            disabled={page === totalPages}
                                            className="h-8 w-8"
                                        >
                                            <ChevronRight className="h-3 w-3" />
                                            <ChevronRight className="h-3 w-3 -ml-2" />
                                        </Button>
                                    </div>
                                </div>
                            </CardFooter>
                        )}
                    </TabsContent>
                </Tabs>
            </Card>

            {/* Transaction Details Modal */}
            <Dialog open={!!selectedTransaction} onOpenChange={(open) => !open && setSelectedTransaction(null)}>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col p-0">
                    <DialogHeader className="px-6 pt-6 pb-2 sticky top-0 bg-background z-10 border-b">
                        <DialogTitle className="text-2xl font-bold flex items-center gap-2">
                            <FileText className="h-5 w-5" />
                            Transaction Details
                        </DialogTitle>
                        <DialogDescription>
                            Detailed information about transaction {selectedTransaction?.retrievalReference}
                        </DialogDescription>
                    </DialogHeader>

                    {selectedTransaction && (
                        <div className="overflow-y-auto max-h-[calc(90vh-120px)] px-6 pb-6">
                            <div className="space-y-6 py-4">
                                {/* Status Banner */}
                                <div
                                    className={`${getTransactionTypeInfo(selectedTransaction.transactionTypeCode).color} p-4 rounded-lg flex items-center justify-between sticky top-0 z-10`}
                                >
                                    <div className="flex items-center gap-3">
                                        {getTransactionTypeInfo(selectedTransaction.transactionTypeCode).icon}
                                        <div>
                                            <h3 className="font-semibold">
                                                {getTransactionTypeInfo(selectedTransaction.transactionTypeCode).description}
                                            </h3>
                                            <p className="text-sm opacity-80">Transaction Code: {selectedTransaction.transactionTypeCode}</p>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="font-semibold">
                                            {formatCurrency(selectedTransaction.transactionAmount, selectedTransaction.currencyTransaction)}
                                        </div>
                                        <div className="text-sm opacity-80">{formatDate(selectedTransaction.transactionDatetime)}</div>
                                    </div>
                                </div>

                                {/* Transaction Result */}
                                {selectedTransaction.result && (
                                    <Alert
                                        variant="outline"
                                        className={
                                            selectedTransaction.result.approved ? "border-green-300 bg-green-50" : "border-red-300 bg-red-50"
                                        }
                                    >
                                        <div className="flex items-center gap-2">
                                            {selectedTransaction.result.approved ? (
                                                <CheckCircle2 className="h-5 w-5 text-green-600" />
                                            ) : (
                                                <XCircle className="h-5 w-5 text-red-600" />
                                            )}
                                            <AlertTitle className={selectedTransaction.result.approved ? "text-green-800" : "text-red-800"}>
                                                {selectedTransaction.result.approved ? "Transaction Approved" : "Transaction Declined"}
                                            </AlertTitle>
                                        </div>
                                        <AlertDescription className="mt-2 text-sm">{selectedTransaction.result.message}</AlertDescription>
                                    </Alert>
                                )}

                                {/* Tabs for different sections */}
                                <Tabs defaultValue="overview" className="w-full">
                                    <TabsList className="grid grid-cols-4 w-full">
                                        <TabsTrigger value="overview">Overview</TabsTrigger>
                                        <TabsTrigger value="amounts">Amounts</TabsTrigger>
                                        <TabsTrigger value="merchant">Merchant</TabsTrigger>
                                        <TabsTrigger value="references">References</TabsTrigger>
                                    </TabsList>

                                    {/* Overview Tab */}
                                    <TabsContent value="overview" className="mt-4 space-y-4">
                                        <div className="flex items-center gap-2">
                                            <Clock className="h-5 w-5 text-primary" />
                                            <h3 className="text-lg font-semibold">Overview</h3>
                                        </div>
                                        <Separator />
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                            <div className="space-y-1">
                                                <p className="text-sm text-muted-foreground">Transaction Date/Time</p>
                                                <p className="font-mono text-sm bg-muted p-2 rounded">
                                                    {formatDate(selectedTransaction.transactionDatetime)}
                                                </p>
                                            </div>
                                            <div className="space-y-1">
                                                <p className="text-sm text-muted-foreground">Transaction Type</p>
                                                <Badge
                                                    className={`${getTransactionTypeInfo(selectedTransaction.transactionTypeCode).color} border flex items-center`}
                                                    variant="outline"
                                                >
                                                    {getTransactionTypeInfo(selectedTransaction.transactionTypeCode).icon}
                                                    {getTransactionTypeInfo(selectedTransaction.transactionTypeCode).description} (
                                                    {selectedTransaction.transactionTypeCode})
                                                </Badge>
                                            </div>
                                            <div className="space-y-1">
                                                <p className="text-sm text-muted-foreground">Card Expiry</p>
                                                <p className="font-mono text-sm bg-muted p-2 rounded">
                                                    {selectedTransaction.cardExpiryDate
                                                        ? `${selectedTransaction.cardExpiryDate.substr(0, 2)}/${selectedTransaction.cardExpiryDate.substr(2, 2)}`
                                                        : "N/A"}
                                                </p>
                                            </div>
                                            <div className="space-y-1">
                                                <p className="text-sm text-muted-foreground">Settlement Status</p>
                                                <Badge
                                                    variant="outline"
                                                    className={
                                                        selectedTransaction.settled
                                                            ? "bg-green-100 text-green-800 border-green-300"
                                                            : "bg-amber-100 text-amber-800 border-amber-300"
                                                    }
                                                >
                                                    {selectedTransaction.settled ? "Approved" : "Pending Settlement"}
                                                </Badge>
                                            </div>
                                            {selectedTransaction.cardId && (
                                                <div className="space-y-1">
                                                    <p className="text-sm text-muted-foreground">Card ID</p>
                                                    <p className="font-mono text-sm bg-muted p-2 rounded">{selectedTransaction.cardId}</p>
                                                </div>
                                            )}
                                            <div className="space-y-1">
                                                <p className="text-sm text-muted-foreground">Account Identification</p>
                                                <p className="font-mono text-sm bg-muted p-2 rounded break-all">
                                                    {selectedTransaction.accountIdentification}
                                                </p>
                                            </div>
                                        </div>
                                    </TabsContent>

                                    {/* Amounts Tab */}
                                    <TabsContent value="amounts" className="mt-4 space-y-4">
                                        <div className="flex items-center gap-2">
                                            <DollarSign className="h-5 w-5 text-primary" />
                                            <h3 className="text-lg font-semibold">Amount Information</h3>
                                        </div>
                                        <Separator />
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <Card className="border-2 border-emerald-100">
                                                <CardHeader className="pb-2">
                                                    <CardTitle className="text-base">Transaction Amount</CardTitle>
                                                    <CardDescription>Original transaction currency</CardDescription>
                                                </CardHeader>
                                                <CardContent>
                                                    <div className="text-2xl font-bold">
                                                        {formatCurrency(
                                                            selectedTransaction.transactionAmount,
                                                            selectedTransaction.currencyTransaction,
                                                        )}
                                                    </div>
                                                    <div className="text-sm text-muted-foreground mt-1">
                                                        Currency Code: {selectedTransaction.currencyTransaction}
                                                    </div>
                                                </CardContent>
                                            </Card>

                                            <Card className="border-2 border-blue-100">
                                                <CardHeader className="pb-2">
                                                    <CardTitle className="text-base">Billing Amount</CardTitle>
                                                    <CardDescription>Amount in billing currency</CardDescription>
                                                </CardHeader>
                                                <CardContent>
                                                    <div className="text-2xl font-bold">
                                                        {formatCurrency(selectedTransaction.billingAmount, selectedTransaction.currencyBilling)}
                                                    </div>
                                                    <div className="text-sm text-muted-foreground mt-1">
                                                        Currency Code: {selectedTransaction.currencyBilling || "Same as transaction"}
                                                    </div>
                                                </CardContent>
                                            </Card>

                                            {selectedTransaction.conversionRate && (
                                                <div className="col-span-2">
                                                    <div className="text-sm text-muted-foreground">
                                                        <span className="font-medium">Conversion Rate:</span> {selectedTransaction.conversionRate}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </TabsContent>

                                    {/* Merchant Tab */}
                                    <TabsContent value="merchant" className="mt-4 space-y-4">
                                        <div className="flex items-center gap-2">
                                            <Building className="h-5 w-5 text-primary" />
                                            <h3 className="text-lg font-semibold">Merchant Information</h3>
                                        </div>
                                        <Separator />
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div className="space-y-1">
                                                <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                    <Terminal className="h-4 w-4" />
                                                    Terminal ID
                                                </p>
                                                <p className="font-mono text-sm bg-muted p-2 rounded">{selectedTransaction.terminalId}</p>
                                            </div>
                                            <div className="space-y-1">
                                                <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                    <Building className="h-4 w-4" />
                                                    Merchant ID
                                                </p>
                                                <p className="font-mono text-sm bg-muted p-2 rounded">
                                                    {selectedTransaction.merchantId.trim()}
                                                </p>
                                            </div>
                                            <div className="space-y-1 col-span-2">
                                                <p className="text-sm text-muted-foreground">Merchant Location</p>
                                                <p className="font-mono text-sm bg-muted p-2 rounded">{selectedTransaction.merchantLocation}</p>
                                            </div>
                                        </div>
                                    </TabsContent>

                                    {/* References Tab */}
                                    <TabsContent value="references" className="mt-4 space-y-4">
                                        <div className="flex items-center gap-2">
                                            <FileText className="h-5 w-5 text-primary" />
                                            <h3 className="text-lg font-semibold">Reference Information</h3>
                                        </div>
                                        <Separator />
                                        <div className="space-y-4">
                                            <div className="space-y-1">
                                                <p className="text-sm text-muted-foreground">Retrieval Reference</p>
                                                <p className="font-mono text-sm break-all bg-muted p-3 rounded-md">
                                                    {selectedTransaction.retrievalReference}
                                                </p>
                                            </div>
                                            <div className="space-y-1">
                                                <p className="text-sm text-muted-foreground">Trace Number</p>
                                                <p className="font-mono text-sm break-all bg-muted p-3 rounded-md">
                                                    {selectedTransaction.traceNumber || "N/A"}
                                                </p>
                                            </div>

                                            {selectedTransaction.externalToken && (
                                                <div className="space-y-1">
                                                    <p className="text-sm text-muted-foreground">External Token</p>
                                                    <p className="font-mono text-sm break-all bg-muted p-3 rounded-md">
                                                        {selectedTransaction.externalToken}
                                                    </p>
                                                </div>
                                            )}

                                            {/* Extended Data Section (if available) */}
                                            {Object.values(selectedTransaction.extendedData).some((value) => value) && (
                                                <div className="mt-6">
                                                    <p className="text-sm font-medium mb-2">Extended Data</p>
                                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 bg-muted/50 p-3 rounded-md">
                                                        {Object.entries(selectedTransaction.extendedData).map(([key, value]) =>
                                                            value ? (
                                                                <div key={key} className="text-sm">
                                                                    <span className="font-medium">{key.replace(/([A-Z])/g, " $1").trim()}:</span> {value}
                                                                </div>
                                                            ) : null,
                                                        )}
                                                    </div>
                                                </div>
                                            )}

                                            {/* Custom Fields Section (if available) */}
                                            {Object.values(selectedTransaction.customFields).some((value) => value) && (
                                                <div className="mt-4">
                                                    <p className="text-sm font-medium mb-2">Custom Fields</p>
                                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 bg-muted/50 p-3 rounded-md">
                                                        {Object.entries(selectedTransaction.customFields).map(([key, value]) =>
                                                            value ? (
                                                                <div key={key} className="text-sm">
                                                                    <span className="font-medium">{key.replace(/([A-Z])/g, " $1").trim()}:</span> {value}
                                                                </div>
                                                            ) : null,
                                                        )}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </TabsContent>
                                </Tabs>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    )
}
