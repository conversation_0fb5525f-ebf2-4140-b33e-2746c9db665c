import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import axiosInstance from "@/utils/axiosInstance"
import { Loader2 } from "lucide-react"

interface RestrictMobileLostDialogProps {
    cardId: string
    expDate:string
    onApiSuccess: () => void
}

export function RestrictMobileLostDialog({ cardId, expDate, onApiSuccess }: RestrictMobileLostDialogProps) {
    const [loading, setLoading] = useState(false)
    const [isOpen, setIsOpen] = useState(false)

    const handleRestrictMobileLost = async () => {
        try {
            setLoading(true)
            const response = await axiosInstance.post(`cards/${cardId}/restrictMobileLost`,{ expDate})

            if (typeof onApiSuccess === "function") {
                onApiSuccess()
            }
            setIsOpen(false)
        } catch (error) {
            console.error("Failed to restrict card reported lost via mobile", error)
            alert("Failed to restrict card. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="destructive">Report Lost (Mobile)</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Report Card as Lost (Mobile)</DialogTitle>
                    <DialogDescription>
                        Are you sure you want to report this card as lost via mobile? This action will immediately block all
                        transactions on this card to prevent unauthorized use.
                    </DialogDescription>
                </DialogHeader>

                <DialogFooter>
                    <Button type="submit" onClick={handleRestrictMobileLost} disabled={loading} variant="destructive">
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />
                                <span>Reporting...</span>
                            </>
                        ) : (
                            "Report Lost"
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

