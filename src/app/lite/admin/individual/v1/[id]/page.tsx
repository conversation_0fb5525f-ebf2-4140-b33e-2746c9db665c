// @ts-nocheck
"use client"

import type React from "react"
import { useCallback, useEffect, useMemo, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card"
import axiosInstance from "@/utils/axiosInstance"
import { calculateRiskLevel, formatDate, formatDob } from "@/utils/helpers"
import { country_currency, countryNameByCode } from "@/utils/data"
import { alertHelper } from "@/utils/alertHelper"
import PhoneNumberDisplay from "@/components/PhoneDispaly"
import DataExporter from "@/components/DataExporter"
import CreateCardComponent from "@/app/lite/admin/individual/v1/[id]/create-card/create-virtual-card"
import { CardDetailsSheet } from "@/app/lite/admin/individual/v1/[id]/card"
import { generateProfilePDF } from "@/utils/pdfGenerator"

// UI Components
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Icons
import {
    AlertCircle,
    Calendar,
    ChevronDown,
    Clock,
    Copy,
    CreditCard,
    Download,
    FileText,
    Home,
    Info,
    Mail,
    MapPin,
    Paperclip,
    Phone,
    Plus,
    RefreshCw,
    Search,
    Shield,
    User, Users,
    X,
} from "lucide-react"
import { useAppSelector } from "@/store/hooks"
import CardholderPermissionsDialog from "@/components/cardholder-permissions-dialog";
import B2bPermissionsDialog from "@/components/b2b-permissions-dialog";

const asset = "https://stagingapi.ryvyl.eu"

interface Document {
    name: string
    type: string
    size: string
}

interface Event {
    name: string
    type: string
    size: string
    title: string
}

interface CardImage {
    _id: string
    front_side: string
    back_side: string
    product_version: {
        version_code: string
    }
    company: {
        _id: string
    }
}

const events: Event[] = [
    {
        title: "Name of requested event goes here",
        name: "nameofevent",
        type: "eventtype",
        size: "2.1MB",
    },
    {
        title: "Name of requested event goes here",
        name: "nameofevent",
        type: "eventtype",
        size: "2.1MB",
    },
]

const documents: Document[] = [
    { name: "nameofdocument", type: "doctype", size: "2.1MB" },
    { name: "nameofdocument", type: "doctype", size: "2.1MB" },
    { name: "nameofdocument", type: "doctype", size: "2.1MB" },
]

interface ReviewStatus {
    icon: React.ReactNode
    label: string
    actionRequired?: boolean
}

const statuses = ["Initiated", "Pending", "Pre-checked", "Queued", "On Hold"]

const reviewStatuses: ReviewStatus[] = [
    {
        icon: <div className="w-4 h-4 rounded-full border" />,
        label: "John Smitherson",
    },
    {
        icon: (
            <div className="w-4 h-4 rounded-full bg-orange-500 text-white flex items-center justify-center text-xs">!</div>
        ),
        label: "John Smitherson",
        actionRequired: true,
    },
    {
        icon: <div className="w-4 h-4 rounded-full bg-red-500 text-white flex items-center justify-center text-xs">×</div>,
        label: "John Smitherson",
    },
    {
        icon: (
            <div className="w-4 h-4 rounded-full bg-green-500 text-white flex items-center justify-center text-xs">✓</div>
        ),
        label: "John Smitherson",
    },
]

interface Activity {
    timestamp: string
    description: string
}

const activities: Activity[] = [
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
    {
        timestamp: "30 March 2023, 14:00:00",
        description: "[Employee/User name] performed an action that appears in this text line.",
    },
]

const PHYSICAL_CARD_LIMIT = 5
const VIRTUAL_CARD_LIMIT = 6
const DEFAULT_COMPANY_ID = "6785126247b8a6a67fbf7cad"

export default function IndividualOnboardingPage({ params }: { params: { id: string } }) {
    const router = useRouter()
    const searchParams = useSearchParams()
    const [onboarding, setOnboarding] = useState<null | any>(null)
    const [account, setAccount] = useState<null | any>(null)
    const [cards, setCards] = useState<null | any>(null)
    const [loading, setLoading] = useState(true)
    const [accountBalance, setBalance] = useState(null)
    const [error, setError] = useState()
    const [createCardLoading, setCreateCardLoading] = useState(false)
    const [isSheetOpen, setIsSheetOpen] = useState(false)
    const [selectedCardId, setSelectedCardId] = useState<string | null>(null)
    const [cipOnboarding, setCipOnboarding] = useState<null | any>(null)
    const [ItClient, setItClient] = useState<any | null>(null)
    const [expandedCase, setExpandedCase] = useState("september")
    const [activeTab, setActiveTab] = useState("profile")
    const user = useAppSelector((state) => state.user.user)
    const [cardImages, setCardImages] = useState<CardImage[]>([])
    const [currentCardImages, setCurrentCardImages] = useState<{ [key: string]: CardImage }>({})
    const [cardImagesLoading, setCardImagesLoading] = useState(false)
    const [dataLoaded, setDataLoaded] = useState(false)

    const [dialogOpen, setDialogOpen] = useState(false)


    // Memoize card categorization to avoid recalculating on every render
    const { physicalCards, virtualCards, physicalCardLimitReached, virtualCardLimitReached } = useMemo(() => {
        console.log("Categorizing cards:", cards)

        if (!cards || !Array.isArray(cards)) {
            console.log("No cards or cards is not an array:", cards)
            return {
                physicalCards: [],
                virtualCards: [],
                physicalCardLimitReached: false,
                virtualCardLimitReached: false,
            }
        }

        console.log("Cards to categorize:", cards.length)

        // Handle both regular cards and B2B cards structure
        const physical = cards.filter((card) => {
            const isPhysical =
                card.productDesc?.includes("PHY") ||
                card.cardType?.toLowerCase().includes("physical") ||
                card.type?.toLowerCase().includes("physical") ||
                card.productType?.toLowerCase().includes("physical") ||
                card.deliveryMethod?.toLowerCase().includes("physical")
            // console.log("Card physical check:", card, "isPhysical:", isPhysical)
            return isPhysical
        })

        const virtual = cards.filter((card) => {
            const isVirtual =
                !card.productDesc?.includes("PHY") &&
                !card.cardType?.toLowerCase().includes("physical") &&
                !card.type?.toLowerCase().includes("physical") &&
                !card.productType?.toLowerCase().includes("physical") &&
                !card.deliveryMethod?.toLowerCase().includes("physical")
            // console.log("Card virtual check:", card, "isVirtual:", isVirtual)
            return isVirtual
        })

        console.log("Categorized cards - Physical:", physical.length, "Virtual:", virtual.length)

        return {
            physicalCards: physical,
            virtualCards: virtual,
            physicalCardLimitReached: physical.length >= PHYSICAL_CARD_LIMIT,
            virtualCardLimitReached: virtual.length >= VIRTUAL_CARD_LIMIT,
        }
    }, [cards])

    // Memoize card data for export
    const cardsData = useMemo(() => {
        if (!cards || cards.length === 0) return []

        return cards.map((row, index) => ({
            id: index + 1,
            emboss_name1: row.embossName1,
            card_number: row.cardMask,
            exp_date: row.expDate,
            status: row.status,
            created_at: formatDate(row.createdAt),
        }))
    }, [cards])

    // Check for card ID in URL when component mounts or URL changes
    useEffect(() => {
        const cardId = searchParams.get("card")
        if (cardId) {
            setSelectedCardId(cardId)
            setIsSheetOpen(true)
        }
    }, [searchParams])

    // Update URL when sheet is opened/closed
    useEffect(() => {
        if (isSheetOpen && selectedCardId) {
            // Update URL with card ID
            const url = new URL(window.location.href)
            url.searchParams.set("card", selectedCardId)
            window.history.pushState({}, "", url.toString())
        } else {
            // Remove card ID from URL when sheet is closed
            if (searchParams.has("card")) {
                const url = new URL(window.location.href)
                url.searchParams.delete("card")
                window.history.pushState({}, "", url.toString())
            }
        }
    }, [isSheetOpen, selectedCardId, searchParams])

    // Fetch all data in parallel for better performance
    const fetchAllData = useCallback(async () => {
        try {
            setLoading(true)

            // Fetch onboarding details, card images, and other data in parallel
            const [onboardingResponse, cardImagesResponse] = await Promise.all([
                axiosInstance.get(`onboarding/personal/${params.id}`),
                axiosInstance.get<CardImage[]>("/images"),
            ])

            const onboardingData = onboardingResponse.data.data
            const accountData = onboardingResponse.data.account[0]
            const cardsData = onboardingResponse.data.cards

            setOnboarding(onboardingData)
            setAccount(accountData)
            setCardImages(cardImagesResponse.data)

            // Process card images
            if (cardsData && cardsData.length > 0 && cardImagesResponse.data) {
                processCardImages(cardsData, cardImagesResponse.data, onboardingData?.company?._id)
            }

            // Fetch additional data
            if (accountData) {
                getAccountBalance(accountData.accountNumber)
            }

            if (onboardingData?.clientID) {
                getClient(onboardingData.clientID)
            }


            if (user?.dashboard === "corporate" || user?.dashboard === "cardholder" && user?.cardholder.userType === "b2b") {
                try {
                    console.log("Fetching B2B cards for cardholder:", params.id)
                    const b2bcards = await axiosInstance.get(`b2b/cardholder/${params.id}/cards`)
                    console.log("B2B Cards Full Response:", b2bcards)
                    console.log("B2B Cards Data:", b2bcards.data)

                    // Handle different possible response structures
                    let b2bCardsData = null
                    if (b2bcards.data) {
                        if (Array.isArray(b2bcards.data)) {
                            b2bCardsData = b2bcards.data
                        } else if (b2bcards.data.data && Array.isArray(b2bcards.data.data)) {
                            b2bCardsData = b2bcards.data.data
                        } else if (b2bcards.data.cards && Array.isArray(b2bcards.data.cards)) {
                            b2bCardsData = b2bcards.data.cards
                        } else if (b2bcards.data.result && Array.isArray(b2bcards.data.result)) {
                            b2bCardsData = b2bcards.data.result
                        }
                    }

                    console.log("Processed B2B Cards Data:", b2bCardsData)
                    console.log("B2B Cards Count:", b2bCardsData?.length || 0)

                    if (b2bCardsData && Array.isArray(b2bCardsData)) {
                        setCards(b2bCardsData)

                        // Process card images for B2B cards if they exist
                        if (b2bCardsData.length > 0 && cardImagesResponse.data) {
                            processCardImages(b2bCardsData, cardImagesResponse.data, onboardingData?.company?._id)
                        }
                    } else {
                        console.warn("No valid B2B cards data found")
                        setCards([])
                    }
                } catch (b2bError) {
                    console.error("Error fetching B2B cards:", b2bError)
                    console.error("B2B Error details:", {
                        message: b2bError.message,
                        response: b2bError.response?.data,
                        status: b2bError.response?.status,
                    })
                    // Fallback to regular cards if B2B fetch fails
                    setCards(cardsData || [])
                }
            } else {
                console.log("Using regular cards data")
                setCards(cardsData || [])
            }

            setDataLoaded(true)
        } catch (error) {
            console.error("Error fetching data:", error)
            setError(error)
        } finally {
            setLoading(false)
        }
    }, [params.id, user?.dashboard])

    // Process card images once we have both cards and images
    const processCardImages = useCallback((cards, images, companyId) => {
        setCardImagesLoading(true)
        try {
            const imageMap: { [key: string]: CardImage } = {}

            cards.forEach((card) => {
                const matchingImages = images.filter((img) => img.product_version.version_code === card.productCode)
                let companyImage = matchingImages.find((img) => img.company._id === companyId)

                if (!companyImage) {
                    companyImage = matchingImages.find((img) => img.company._id === DEFAULT_COMPANY_ID)
                }

                if (companyImage) {
                    imageMap[card.cardKey] = companyImage
                }
            })

            setCurrentCardImages(imageMap)
        } catch (error) {
            console.error("Error processing card images:", error)
        } finally {
            setCardImagesLoading(false)
        }
    }, [])

    // Initial data fetch
    useEffect(() => {
        fetchAllData()
    }, [fetchAllData])

    const getClient = async (clientID) => {
        if (!clientID) return

        try {
            // if (user.dashboard !== "corporate" || user.dashboard!== "cardholder") {
            //     const response = await axiosInstance.get(`/client/${clientID}`)
            //     setItClient(response.data.client)
            // }
        } catch (error) {
            console.error("Error fetching client data:", error)
        }
    }

    const getAccountBalance = async (account = "**********************") => {
        try {
            const response = await axiosInstance.get(`/legacy/fetch-balance/${account}`)
            setBalance(response.data.availableBalance)
        } catch (error) {
            console.error("Error fetching account balance:", error)
        }
    }

    const handleCardClick = useCallback((cardId: string) => {
        setSelectedCardId(cardId)
        setIsSheetOpen(true)
    }, [])

    const handleSheetOpenChange = useCallback((open: boolean) => {
        setIsSheetOpen(open)
        if (!open) {
            // Remove card ID from URL when sheet is closed
            const url = new URL(window.location.href)
            url.searchParams.delete("card")
            window.history.pushState({}, "", url.toString())
        }
    }, [])

    const handleRefreshData = useCallback(async () => {

        await fetchAllData()
    }, [fetchAllData])

    function calculateRiskColor(score: number): string {
        if (score < 0) {
            throw new Error("Score cannot be negative.")
        }

        if (score <= 199) {
            return "bg-green-500"
        } else if (score <= 399) {
            return "bg-yellow-500"
        } else if (score <= 599) {
            return "bg-red-500"
        } else if (score <= 899) {
            return "bg-red-800"
        } else {
            return ""
        }
    }

    function getStatusColor(status: string) {
        switch (status.toUpperCase()) {
            case "ACTIVE":
                return "bg-green-500"
            case "INACTIVE":
                return "bg-yellow-500"
            case "BLOCKED":
                return "bg-red-500"
            case "ORDERED":
                return "bg-blue-500"
            default:
                return "bg-gray-500"
        }
    }

    const copyToClipboard = useCallback((text: string) => {
        navigator.clipboard.writeText(text)
        alertHelper.showToast("Copied to clipboard", "success")
    }, [])

    // Render loading state
    if (loading) {
        return (
            <div className="container bg-white mx-auto p-6">
                <div className="flex items-center space-x-4 mb-6">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="space-y-2">
                        <Skeleton className="h-6 w-[250px]" />
                        <Skeleton className="h-4 w-[200px]" />
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="lg:col-span-2 space-y-6">
                        <Skeleton className="h-[300px] w-full rounded-lg" />
                        <Skeleton className="h-[200px] w-full rounded-lg" />
                    </div>
                    <div className="space-y-6">
                        <Skeleton className="h-[200px] w-full rounded-lg" />
                        <Skeleton className="h-[300px] w-full rounded-lg" />
                    </div>
                </div>
            </div>
        )
    }

    // Render error state if no onboarding data
    if (!onboarding) {
        return (
            <div className="container mx-auto p-6">
                <Card className="max-w-md mx-auto">
                    <CardContent className="flex flex-col items-center justify-center py-10">
                        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
                        <h2 className="text-xl font-semibold mb-2">No Data Found</h2>
                        <p className="text-muted-foreground text-center mb-6">
                            We couldn't find any onboarding details for this customer.
                        </p>
                        <Button onClick={() => router.back()}>Go Back</Button>
                    </CardContent>
                </Card>
            </div>
        )
    }

    // Render card image
    const renderCardImage = (cardKey, card_type) => {
        if (cardImagesLoading) {
            return <Skeleton className="h-[25px] w-[40px] rounded" />
        }

        const cardImage = currentCardImages[cardKey]
        const imageSrc = cardImage ? `${asset}/${cardImage.front_side}` : "/cards/pp-fd.svg"

        return (
            <Image
                src={imageSrc || "/placeholder.svg"}
                alt="Credit card"
                width={40}
                height={25}
                className={
                    card_type === "vtl" ? "object-cover h-[25px] w-[40px] rounded" : "object-cover h-[40px] w-[25px] rounded"
                }
                priority={true}
                unoptimized
            />
        )
    }

    const checkCardPermission = (card  ) => {
        console.log("Checking permission for card:", card);

        // If dashboard is NOT programmeManager, always allow
        if (user?.dashboard !== "programmeManager") {
            return true;
        }

        // If dashboard is programmeManager, check permissions
        return user.pm.permissions.some(permission =>
            permission.toLowerCase().includes(card.toLowerCase())
        );
    };

   console.log("has permission: ",  checkCardPermission("virtual"))

    return (
        <div className="container mx-auto p-6">
            {/* Header */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
                <div>
                    <div className="flex items-center gap-3 mb-2">
                        <h1 className="text-2xl font-bold">
                            {onboarding.personalInfo.firstName} {onboarding.personalInfo.lastName}
                        </h1>
                        <Badge className="bg-green-500">Active</Badge>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                        <span>Client ID: {onboarding.clientID}</span>

                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => copyToClipboard(onboarding.clientID)}
                        >
                            <Copy className="h-3.5 w-3.5" />
                        </Button>
                    </div>
                    {/*<div className="flex items-center gap-2  ">*/}
                    {/*    <span className="text-muted-foreground">Relationship: </span>*/}
                    {/*    {onboarding.company.company_name}*/}
                    {/*</div>*/}
                </div>

                <div className="flex flex-wrap gap-2">
                    {user.dashboard==="infinity" && (
                    <Button
                        className="gap-2"
                        onClick={() => setDialogOpen(true)}
                    >
                        <Users size={16}/>
                        Manage Permissions
                    </Button>
                        )}
                    <CardholderPermissionsDialog
                        open={dialogOpen}
                        onOpenChange={setDialogOpen}
                        companyName={onboarding.personalInfo.firstName +" "+ onboarding.personalInfo.lastName}
                        companyEmail={onboarding?.personalInfo.email}
                        personId={onboarding._id}
                        previousPermissions={onboarding?.permissions}
                        previousPermissionAudit={onboarding?.permissionsAudit}
                        permissionsLog={onboarding?.permissionsLog}
                    />
                    <Button
                        variant="outline"
                        className="flex items-center gap-2"
                        onClick={() => generateProfilePDF(onboarding, account, cards, accountBalance)}
                    >
                        <Download className="h-4 w-4" />
                        Export Profile
                    </Button>



                    {user.dashboard==="corporate" && (
                        <>
                        {!virtualCardLimitReached ? (
                            <CreateCardComponent
                                account={account}
                                onboarding={onboarding}
                                onApiSuccess={handleRefreshData}
                                companyId={onboarding?.company?._id}
                            />
                        ) : (
                            <HoverCard>
                                <HoverCardTrigger>
                                    {" "}
                                    <Button disabled className="flex items-center gap-2">
                                        <CreditCard className="h-4 w-4" />
                                        Create Virtual Card
                                    </Button>
                                </HoverCardTrigger>
                                <HoverCardContent>
                                    You have reached the maximum limit of {VIRTUAL_CARD_LIMIT} virtual cards.
                                </HoverCardContent>
                            </HoverCard>
                        )}

                        {!physicalCardLimitReached &&(
                            <>
                                {user.dashboard === "cardholder" ? (
                                    <Link href={`cardholder/${onboarding._id}/create-card`}>
                                        <Button className="flex items-center gap-2">
                                            <CreditCard className="h-4 w-4" />
                                            Create Physical Card
                                        </Button>
                                    </Link>
                                ) : (
                                    <Link href={`/corporate/cards/${onboarding._id}/create`}>
                                        <Button className="flex items-center gap-2">
                                            <CreditCard className="h-4 w-4" />
                                            Create Physical Card
                                        </Button>
                                    </Link>
                                )}
                            </>
                        )}

                        {physicalCardLimitReached && (
                            <>
                                <HoverCard>
                                    <HoverCardTrigger>
                                        {" "}
                                        <Button disabled className="flex items-center gap-2">
                                            <CreditCard className="h-4 w-4" />
                                            Create Physical Card
                                        </Button>
                                    </HoverCardTrigger>
                                    <HoverCardContent>
                                        You have reached the maximum limit of {PHYSICAL_CARD_LIMIT} Physical cards.
                                    </HoverCardContent>
                                </HoverCard>
                            </>
                        )}
                        </>
                    )}

                    {account && (
                        <>
                        { checkCardPermission("virtual") &&(
                            <>
                            {!virtualCardLimitReached
                                &&   checkCardPermission("virtual")? (
                                <CreateCardComponent
                                    account={account}
                                    onboarding={onboarding}
                                    onApiSuccess={handleRefreshData}
                                    companyId={onboarding?.company?._id}
                                />
                            ) : (
                                <HoverCard>
                                    <HoverCardTrigger>
                                        {" "}
                                        <Button disabled className="flex items-center gap-2">
                                            <CreditCard className="h-4 w-4" />
                                            Create Virtual Card
                                        </Button>
                                    </HoverCardTrigger>
                                    <HoverCardContent>
                                        You have reached the maximum limit of {VIRTUAL_CARD_LIMIT} virtual cards.
                                    </HoverCardContent>
                                </HoverCard>
                            )}
                                </>
                            )}


                            { checkCardPermission("physical") &&(
<>
                            {!physicalCardLimitReached &&  (
                                <>
                                    {user.dashboard === "cardholder" ? (
                                        <Link href={`cardholder/${onboarding._id}/create-card`}>
                                            <Button className="flex items-center gap-2">
                                                <CreditCard className="h-4 w-4" />
                                                Create Physical Card
                                            </Button>
                                        </Link>
                                    ) : (

                                        <Link href={`${onboarding._id}/create-card`}>
                                            <Button className="flex items-center gap-2">
                                                <CreditCard className="h-4 w-4" />
                                                Create Physical Card
                                            </Button>
                                        </Link>
                                    )}
                                </>
                            )}

                            {physicalCardLimitReached && (
                                <>
                                    <HoverCard>
                                        <HoverCardTrigger>
                                            {" "}
                                            <Button disabled className="flex items-center gap-2">
                                                <CreditCard className="h-4 w-4" />
                                                Create Physical Card
                                            </Button>
                                        </HoverCardTrigger>
                                        <HoverCardContent>
                                            You have reached the maximum limit of {PHYSICAL_CARD_LIMIT} Physical cards.
                                        </HoverCardContent>
                                    </HoverCard>
                                </>
                            )}
</>

)}


                            {/*          {(virtualCardLimitReached || physicalCardLimitReached) && (*/}
                            {/*              <div className="flex items-center gap-2 px-3 py-2 bg-amber-50 border border-amber-200 rounded-md">*/}
                            {/*                  <AlertCircle className="h-4 w-4 text-amber-600" />*/}
                            {/*                  <span className="text-sm text-amber-700">*/}
                            {/*  {virtualCardLimitReached && physicalCardLimitReached*/}
                            {/*      ? "All card limits reached"*/}
                            {/*      : virtualCardLimitReached*/}
                            {/*          ? `Virtual card limit reached (${VIRTUAL_CARD_LIMIT})`*/}
                            {/*          : `Physical card limit reached (${PHYSICAL_CARD_LIMIT})`}*/}
                            {/*</span>*/}
                            {/*              </div>*/}
                            {/*          )}*/}
                        </>
                    )}
                </div>
            </div>

            {/* Main content */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Left column - 2/3 width */}
                <div className="lg:col-span-2 space-y-6">
                    {/* Main tabs */}
                    <Card className="overflow-hidden">
                        <Tabs value={activeTab} onValueChange={setActiveTab}>
                            <div className="border-b">
                                <TabsList className="w-full justify-start rounded-none h-12 bg-muted/30 p-0">
                                    <TabsTrigger
                                        value="profile"
                                        className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"
                                    >
                                        Profile
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="accounts"
                                        className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"
                                    >
                                        Accounts
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="cards"
                                        className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"
                                    >
                                        Cards
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="kyc"
                                        className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"
                                    >
                                        KYC Details
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="activity"
                                        className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"
                                    >
                                        Activity
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="technical"
                                        className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"
                                    >
                                        Technical Information
                                    </TabsTrigger>

                                    {/*<TabsTrigger*/}
                                    {/*    onClick={() => router.push(`/onboarding/${onboarding._id}/transactions`)}*/}
                                    {/*    className="data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:bg-primary-50 data-[state=active]:text-primary-700 data-[state=active]:shadow-none rounded-none h-12 px-4"*/}
                                    {/*>*/}
                                    {/*    Transactions*/}
                                    {/*</TabsTrigger>*/}
                                </TabsList>
                            </div>

                            {/* Profile Tab */}
                            <TabsContent value="profile" className="p-6 space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                    {/* Personal Information */}
                                    <div className="space-y-4">
                                        <div className="flex items-center gap-2 mb-4">
                                            <User className="h-5 w-5 text-primary" />
                                            <h2 className="text-lg font-semibold">Personal Information</h2>
                                        </div>

                                        <div className="grid grid-cols-1 gap-3">
                                            <div className="flex flex-col">
                                                <span className="text-sm text-muted-foreground">Full Name</span>
                                                <span className="font-medium">
                          {onboarding.personalInfo.firstName} {onboarding.personalInfo.middleName}{" "}
                                                    {onboarding.personalInfo.lastName}
                        </span>
                                            </div>

                                            <div className="flex flex-col">
                                                <span className="text-sm text-muted-foreground">Date of Birth</span>
                                                <span className="font-medium">{formatDob(onboarding.personalInfo.dateOfBirth)}</span>
                                            </div>

                                            <div className="flex flex-col">
                                                <span className="text-sm text-muted-foreground">Nationality</span>
                                                <span className="font-medium">{countryNameByCode(onboarding.citizenship)}</span>
                                            </div>

                                            <div className="flex flex-col">
                                                <span className="text-sm text-muted-foreground">Email</span>
                                                <div className="flex items-center gap-2">
                                                    <Mail className="h-4 w-4 text-muted-foreground" />
                                                    <span className="font-medium">{onboarding.personalInfo.email}</span>
                                                </div>
                                            </div>

                                            <div className="flex flex-col">
                                                <span className="text-sm text-muted-foreground">Phone Number</span>
                                                <div className="flex items-center gap-2">
                                                    <Phone className="h-4 w-4 text-muted-foreground" />
                                                    <PhoneNumberDisplay phoneNumber={onboarding.personalInfo.phone} />
                                                </div>
                                            </div>

                                            <div className="flex flex-col">
                                                <span className="text-sm text-muted-foreground">Auth Phone Number</span>
                                                <div className="flex items-center gap-2">
                                                    <Shield className="h-4 w-4 text-primary" />
                                                    <PhoneNumberDisplay
                                                        phoneNumber={onboarding.personalInfo.authPhoneNumber}
                                                        className="text-primary font-medium"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Address Information */}
                                    <div className="space-y-4">
                                        <div className="flex items-center gap-2 mb-4">
                                            <Home className="h-5 w-5 text-primary" />
                                            <h2 className="text-lg font-semibold">Address Information</h2>
                                        </div>

                                        <div className="p-4 border rounded-lg bg-muted/20 mb-6">
                                            <div className="flex items-center gap-2 mb-2">
                                                <MapPin className="h-4 w-4 text-primary" />
                                                <h3 className="font-medium">Primary Address</h3>
                                            </div>

                                            <address className="not-italic space-y-1 text-muted-foreground">
                                                <p>
                                                    {onboarding.address.street} {onboarding.address.buildingNumber}
                                                </p>
                                                {onboarding.address.apartmentNumber && <p>Apt {onboarding.address.apartmentNumber}</p>}
                                                <p>
                                                    {onboarding.address.city}, {onboarding.address.stateProvince} {onboarding.address.zipCode}
                                                </p>
                                                <p>{countryNameByCode(onboarding.address.country)}</p>
                                            </address>
                                        </div>

                                        {ItClient && ItClient.correspondenceAddress && (
                                            <div className="p-4 border rounded-lg bg-muted/20">
                                                <div className="flex items-center gap-2 mb-2">
                                                    <MapPin className="h-4 w-4 text-muted-foreground" />
                                                    <h3 className="font-medium">Correspondence Address</h3>
                                                </div>

                                                <address className="not-italic space-y-1 text-muted-foreground">
                                                    <p>
                                                        {ItClient.correspondenceAddress.street} {ItClient.correspondenceAddress.buildingNumber}
                                                    </p>
                                                    {ItClient.correspondenceAddress.apartment && (
                                                        <p>Apt {ItClient.correspondenceAddress.apartment}</p>
                                                    )}
                                                    <p>
                                                        {ItClient.correspondenceAddress.city}, {ItClient.correspondenceAddress.stateProvince}{" "}
                                                        {ItClient.correspondenceAddress.zipCode}
                                                    </p>
                                                    <p>{countryNameByCode(ItClient.correspondenceAddress.country)}</p>
                                                </address>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </TabsContent>

                            {/* Accounts Tab */}
                            <TabsContent value="accounts" className="p-6">
                                <div className="space-y-6">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <CreditCard className="h-5 w-5 text-primary" />
                                            <h2 className="text-lg font-semibold">Account Details</h2>
                                        </div>

                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="flex items-center gap-2"
                                            onClick={() => getAccountBalance(account?.accountNumber)}
                                        >
                                            <RefreshCw className="h-4 w-4" />
                                            Refresh Balance
                                        </Button>
                                    </div>

                                    <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl p-6">
                                        <div className="flex flex-col md:flex-row justify-between gap-6">
                                            <div>
                                                <h3 className="text-sm text-muted-foreground mb-1">Available Balance</h3>
                                                <div className="text-3xl font-bold mb-2">
                                                    {Intl.NumberFormat("en-US", {
                                                        style: "currency",
                                                        currency:
                                                            country_currency.find((r) => r.numericCode === account?.currency)?.currencyCode || "EUR",
                                                    }).format(accountBalance)}
                                                </div>
                                                <div className="text-sm text-muted-foreground">{account?.bankName || "Ryvyl EU"}</div>
                                            </div>

                                            <div className="space-y-3">
                                                <div>
                                                    <span className="text-sm text-muted-foreground block">IBAN</span>
                                                    <div className="flex items-center gap-2">
                                                        <span className="font-mono">{account?.accountNumber}</span>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-6 w-6"
                                                            onClick={() => copyToClipboard(account?.accountNumber)}
                                                        >
                                                            <Copy className="h-3.5 w-3.5" />
                                                        </Button>
                                                    </div>
                                                </div>

                                                <div>
                                                    <span className="text-sm text-muted-foreground block">SWIFT Code</span>
                                                    <span className="font-mono">{account?.bankNumber || "Not Specified"}</span>
                                                </div>

                                                <div>
                                                    <span className="text-sm text-muted-foreground block">Account Name</span>
                                                    <span>
                            {account?.accountHolder ||
                                onboarding.personalInfo.firstName + " " + onboarding.personalInfo.lastName}
                          </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </TabsContent>

                            {/* Cards Tab */}
                            <TabsContent value="cards" className="p-6">


                                    <div className="space-y-6">


                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <CreditCard className="h-5 w-5 text-primary" />
                                                <h2 className="text-lg font-semibold">Payment Cards</h2>
                                            </div>

                                            <div className="flex gap-2">
                                                <DataExporter
                                                    data={cardsData}
                                                    filename={`${onboarding.personalInfo.firstName}-cards`}
                                                    title={`${onboarding.personalInfo.firstName} Cards Report`}
                                                />
                                            </div>
                                        </div>

                                        <Tabs defaultValue="virtual" className="w-full">
                                            <TabsList className="mb-4 bg-muted/30">
                                                <TabsTrigger
                                                    value="virtual"
                                                    className="data-[state=active]:bg-primary-500 data-[state=active]:text-white"
                                                >
                                                    Virtual Cards ({virtualCards.length}/{VIRTUAL_CARD_LIMIT})
                                                </TabsTrigger>
                                                <TabsTrigger
                                                    value="physical"
                                                    className="data-[state=active]:bg-primary-500 data-[state=active]:text-white"
                                                >
                                                    Physical Cards ({physicalCards.length}/{PHYSICAL_CARD_LIMIT})
                                                </TabsTrigger>
                                            </TabsList>

                                            <TabsContent value="virtual">
                                                {virtualCards.length > 0 ? (
                                                    <div className="space-y-4">
                                                        <div className="border rounded-lg">
                                                            <Table>
                                                                <TableHeader>
                                                                    <TableRow>
                                                                        <TableHead>Card</TableHead>
                                                                        <TableHead>Name</TableHead>
                                                                        <TableHead>Number</TableHead>
                                                                        <TableHead>Status</TableHead>
                                                                        <TableHead>Expiry</TableHead>
                                                                        <TableHead>NickName</TableHead>
                                                                    </TableRow>
                                                                </TableHeader>
                                                                <TableBody>
                                                                    {virtualCards.map((card, index) => (
                                                                        <TableRow
                                                                            onClick={() => handleCardClick(card.cardKey || card.id || card._id)}
                                                                            key={index}
                                                                            className="cursor-pointer hover:bg-muted/50"
                                                                        >
                                                                            <TableCell>{renderCardImage(card.cardKey, "vtl")}</TableCell>
                                                                            <TableCell className="font-medium">
                                                                                {card.embossName1 || card.cardholderName || card.name || "N/A"}
                                                                            </TableCell>
                                                                            <TableCell className="font-mono">
                                                                                ••••{" "}
                                                                                {(card.cardMask || card.cardNumber || card.maskedNumber || "****").slice(-4)}
                                                                            </TableCell>
                                                                            <TableCell>
                                                                                <Badge className={getStatusColor(card.status || "UNKNOWN")}>
                                                                                    {card.status || "UNKNOWN"}
                                                                                </Badge>
                                                                            </TableCell>
                                                                            <TableCell>{card.expDate || card.expiryDate || card.expiry || "N/A"}</TableCell>
                                                                            <TableCell>{card.nickName || card.nickname || card.alias || "N/A"}</TableCell>
                                                                        </TableRow>
                                                                    ))}
                                                                </TableBody>
                                                            </Table>
                                                        </div>

                                                        {virtualCardLimitReached && (
                                                            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                                                                <div className="flex items-center gap-2 text-amber-800">
                                                                    <AlertCircle className="h-4 w-4" />
                                                                    <span className="font-medium">Virtual Card Limit Reached</span>
                                                                </div>
                                                                <p className="text-sm text-amber-700 mt-1">
                                                                    You have reached the maximum limit of {VIRTUAL_CARD_LIMIT} virtual cards.
                                                                </p>
                                                            </div>
                                                        )}
                                                    </div>
                                                ) : (
                                                    <div className="bg-muted/20 p-8 rounded-lg text-center">
                                                        <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                                        <h4 className="text-lg font-medium mb-2">No Virtual Cards</h4>
                                                        <p className="text-muted-foreground max-w-md mx-auto mb-4">
                                                            There are no virtual cards associated with this account.
                                                        </p>
                                                        <div className="flex justify-center gap-2">
                                                            {!virtualCardLimitReached ? (
                                                                <CreateCardComponent
                                                                    account={account}
                                                                    onboarding={onboarding}
                                                                    onApiSuccess={handleRefreshData}
                                                                    companyId={onboarding?.company?._id}
                                                                />
                                                            ) : (
                                                                <div className="text-center">
                                                                    <p className="text-sm text-amber-600 mb-2">
                                                                        Virtual card limit reached ({VIRTUAL_CARD_LIMIT} cards maximum)
                                                                    </p>
                                                                    <p className="text-xs text-muted-foreground">
                                                                        Remove existing cards to create new ones
                                                                    </p>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                )}
                                            </TabsContent>

                                            <TabsContent value="physical">

                                                {physicalCards.length > 0 ? (
                                                    <div className="space-y-4">
                                                        <div className="border rounded-lg">
                                                            <Table>
                                                                <TableHeader>
                                                                    <TableRow>
                                                                        <TableHead>Card</TableHead>
                                                                        <TableHead>Name</TableHead>
                                                                        <TableHead>Number</TableHead>
                                                                        <TableHead>Status</TableHead>
                                                                        <TableHead>Expiry</TableHead>
                                                                        <TableHead>NickName</TableHead>
                                                                    </TableRow>
                                                                </TableHeader>
                                                                <TableBody>
                                                                    {physicalCards.map((card, index) => (
                                                                        <TableRow
                                                                            onClick={() => handleCardClick(card.cardKey || card.id || card._id)}
                                                                            key={index}
                                                                            className="cursor-pointer hover:bg-muted/50"
                                                                        >
                                                                            <TableCell>{renderCardImage(card.cardKey, "phy")}</TableCell>
                                                                            <TableCell className="font-medium">
                                                                                {card.embossName1 || card.cardholderName || card.name || "N/A"}
                                                                            </TableCell>
                                                                            <TableCell className="font-mono">
                                                                                ••••{" "}
                                                                                {(card.cardMask || card.cardNumber || card.maskedNumber || "****").slice(-4)}
                                                                            </TableCell>
                                                                            <TableCell>
                                                                                <Badge className={getStatusColor(card.status || "UNKNOWN")}>
                                                                                    {card.status || "UNKNOWN"}
                                                                                </Badge>
                                                                            </TableCell>
                                                                            <TableCell>{card.expDate || card.expiryDate || card.expiry || "N/A"}</TableCell>
                                                                            <TableCell>{card.nickName || card.nickname || card.alias || "N/A"}</TableCell>
                                                                        </TableRow>
                                                                    ))}
                                                                </TableBody>
                                                            </Table>
                                                        </div>

                                                        {physicalCardLimitReached && (
                                                            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                                                                <div className="flex items-center gap-2 text-amber-800">
                                                                    <AlertCircle className="h-4 w-4" />
                                                                    <span className="font-medium">Physical Card Limit Reached</span>
                                                                </div>
                                                                <p className="text-sm text-amber-700 mt-1">
                                                                    You have reached the maximum limit of {PHYSICAL_CARD_LIMIT} physical cards.
                                                                </p>
                                                            </div>
                                                        )}
                                                    </div>
                                                ) : (
                                                    <div className="bg-muted/20 p-8 rounded-lg text-center">
                                                        <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                                        <h4 className="text-lg font-medium mb-2">No Physical Cards</h4>
                                                        <p className="text-muted-foreground max-w-md mx-auto mb-4">
                                                            There are no physical cards associated with this account.
                                                        </p>
                                                        <div className="flex justify-center gap-2">
                                                            {!physicalCardLimitReached ? (
                                                                <>
                                                                    {user.dashboard === "cardholder" ? (
                                                                        <Link href={`cardholder/${onboarding._id}/create-card`}>
                                                                            <Button className="flex items-center gap-2">
                                                                                <CreditCard className="h-4 w-4" />
                                                                                Create Physical Card
                                                                            </Button>
                                                                        </Link>
                                                                    ) : (
                                                                        <Link href={`${onboarding._id}/create-card`}>
                                                                            <Button className="flex items-center gap-2">
                                                                                <CreditCard className="h-4 w-4" />
                                                                                Create Physical Card
                                                                            </Button>
                                                                        </Link>
                                                                    )}
                                                                </>
                                                            ) : (
                                                                <div className="text-center">
                                                                    <p className="text-sm text-amber-600 mb-2">
                                                                        Physical card limit reached ({PHYSICAL_CARD_LIMIT} cards maximum)
                                                                    </p>
                                                                    <p className="text-xs text-muted-foreground">
                                                                        Remove existing cards to create new ones
                                                                    </p>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                )}
                                            </TabsContent>
                                        </Tabs>
                                    </div>

                            </TabsContent>

                            {/* Show all cards if categorization fails */}
                            {cards && cards.length > 0 && virtualCards.length === 0 && physicalCards.length === 0 && (
                                <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <h4 className="font-medium text-blue-800 mb-2">All Cards (Uncategorized)</h4>
                                    <div className="text-sm text-blue-700 mb-4">
                                        Found {cards.length} cards but couldn't categorize them. Showing all cards below:
                                    </div>
                                    <div className="border rounded-lg">
                                        <Table>
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead>Card</TableHead>
                                                    <TableHead>Name</TableHead>
                                                    <TableHead>Number</TableHead>
                                                    <TableHead>Status</TableHead>
                                                    <TableHead>Type</TableHead>
                                                    <TableHead>Details</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {cards.map((card, index) => (
                                                    <TableRow
                                                        onClick={() => handleCardClick(card.cardKey || card.id || card._id || index.toString())}
                                                        key={index}
                                                        className="cursor-pointer hover:bg-muted/50"
                                                    >
                                                        <TableCell>{renderCardImage(card.cardKey, "vtl")}</TableCell>
                                                        <TableCell className="font-medium">
                                                            {card.embossName1 || card.cardholderName || card.name || card.holderName || "N/A"}
                                                        </TableCell>
                                                        <TableCell className="font-mono">
                                                            ••••{" "}
                                                            {(card.cardMask || card.cardNumber || card.maskedNumber || card.pan || "****").slice(-4)}
                                                        </TableCell>
                                                        <TableCell>
                                                            <Badge className={getStatusColor(card.status || "UNKNOWN")}>
                                                                {card.status || "UNKNOWN"}
                                                            </Badge>
                                                        </TableCell>
                                                        <TableCell>
                                                            {card.productDesc || card.cardType || card.type || card.productType || "Unknown"}
                                                        </TableCell>
                                                        <TableCell className="text-xs">
                              <pre className="whitespace-pre-wrap max-w-xs overflow-hidden">
                                {JSON.stringify(card, null, 1).slice(0, 100)}...
                              </pre>
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </div>
                                </div>
                            )}

                            {/* KYC Details Tab */}
                            <TabsContent value="kyc" className="p-6">
                                <div className="space-y-6">
                                    <div className="flex items-center gap-2 mb-4">
                                        <FileText className="h-5 w-5 text-primary" />
                                        <h2 className="text-lg font-semibold">Identity Verification</h2>
                                    </div>

                                    <p className="text-muted-foreground mb-4">
                                        All identity verification cases for this user are listed below.
                                    </p>

                                    <Accordion type="single" collapsible className="w-full">
                                        <AccordionItem value="september" className="border rounded-lg overflow-hidden mb-4">
                                            <AccordionTrigger className="px-4 py-3 hover:bg-muted/20">
                                                <div className="flex items-center gap-4">
                                                    <Badge className="bg-green-500">Active</Badge>
                                                    <span className="font-medium">13 September 2024</span>
                                                </div>
                                            </AccordionTrigger>
                                            <AccordionContent className="px-4 py-3 border-t">
                                                <div className="grid grid-cols-1 md:grid-cols-[200px_1fr] gap-4 py-2">
                                                    <div className="font-medium text-muted-foreground">Sumsub case number</div>
                                                    <div className="flex items-center gap-2">
                                                        <span className="font-mono">66ea2b129a750456b55952b1</span>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-6 w-6"
                                                            onClick={() => copyToClipboard("66ea2b129a750456b55952b1")}
                                                        >
                                                            <Copy className="h-3.5 w-3.5" />
                                                        </Button>
                                                    </div>

                                                    <div className="font-medium text-muted-foreground">Case date</div>
                                                    <div>13 September 2024</div>

                                                    <div className="font-medium text-muted-foreground">Sumsub case status</div>
                                                    <div className="flex items-center gap-2">
                                                        <Badge className="bg-green-500">Green</Badge>
                                                        <span className="text-muted-foreground">(completed)</span>
                                                    </div>

                                                    {onboarding.idDocument?.frontImagePath && (
                                                        <>
                                                            <div className="font-medium text-muted-foreground">ID - front:</div>
                                                            <div className="flex items-center gap-2 text-primary">
                                                                <Paperclip className="h-4 w-4" />
                                                                <Link
                                                                    target="_blank"
                                                                    href={asset + onboarding.idDocument?.frontImagePath}
                                                                    className="hover:underline"
                                                                >
                                                                    {onboarding.idDocument?.frontImagePath.split("/").pop()}
                                                                </Link>
                                                            </div>
                                                        </>
                                                    )}

                                                    {onboarding.idDocument?.backImagePath && (
                                                        <>
                                                            <div className="font-medium text-muted-foreground">ID - back:</div>
                                                            <div className="flex items-center gap-2 text-primary">
                                                                <Paperclip className="h-4 w-4" />
                                                                <Link
                                                                    target="_blank"
                                                                    href={asset + onboarding.idDocument?.backImagePath}
                                                                    className="hover:underline"
                                                                >
                                                                    {onboarding.idDocument?.backImagePath.split("/").pop()}
                                                                </Link>
                                                            </div>
                                                        </>
                                                    )}

                                                    <div className="font-medium text-muted-foreground">ID number</div>
                                                    <div className="font-mono">{onboarding.idDocument?.number}</div>

                                                    <div className="font-medium text-muted-foreground">Issued date</div>
                                                    <div>{onboarding.idDocument?.issueDate}</div>

                                                    <div className="font-medium text-muted-foreground">Expiration date</div>
                                                    <div>{onboarding.idDocument?.expiryDate}</div>

                                                    <div className="font-medium text-muted-foreground">ID Authority</div>
                                                    <div>{onboarding.idDocument?.idAuthority}</div>

                                                    <div className="font-medium text-muted-foreground">Country of citizenship</div>
                                                    <div>{countryNameByCode(onboarding?.citizenship)}</div>

                                                    <div className="font-medium text-muted-foreground">Address of residency</div>
                                                    <div className="space-y-1">
                                                        <div>
                                                            {onboarding.address?.street ||
                                                                "" + " " + onboarding.address?.building ||
                                                                "" + " " + onboarding.address?.apartment ||
                                                                ""}
                                                        </div>
                                                        <div>{onboarding.address.city}</div>
                                                        <div>{onboarding.address.stateProvince}</div>
                                                        <div>{onboarding.address.zipCode}</div>
                                                        <div>{countryNameByCode(onboarding.address.country)}</div>
                                                    </div>
                                                </div>
                                            </AccordionContent>
                                        </AccordionItem>

                                        <AccordionItem value="april" className="border rounded-lg overflow-hidden">
                                            <AccordionTrigger className="px-4 py-3 hover:bg-muted/20">
                                                <div className="flex items-center gap-4">
                                                    <Badge variant="outline">Inactive</Badge>
                                                    <span className="font-medium">13 April 2024</span>
                                                </div>
                                            </AccordionTrigger>
                                            <AccordionContent className="px-4 py-3 border-t">
                                                <div className="py-8 text-center text-muted-foreground">
                                                    This verification case is no longer active.
                                                </div>
                                            </AccordionContent>
                                        </AccordionItem>
                                    </Accordion>

                                    <div className="mt-8">
                                        <div className="flex items-center gap-2 mb-4">
                                            <Paperclip className="h-5 w-5 text-primary" />
                                            <h2 className="text-lg font-semibold">Documents</h2>
                                        </div>

                                        <div className="space-y-4 border rounded-lg p-4">
                                            {documents.map((doc, index) => (
                                                <div
                                                    key={index}
                                                    className="flex items-center justify-between group p-2 hover:bg-muted/20 rounded-md"
                                                >
                                                    <div className="flex items-center gap-2">
                                                        <Paperclip className="h-5 w-5 text-muted-foreground" />
                                                        <span className="text-primary">
                              [{doc.name}].[{doc.type}]
                            </span>
                                                        <span className="text-muted-foreground">({doc.size})</span>
                                                    </div>
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                                                    >
                                                        <X className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            ))}

                                            <div className="pt-4">
                                                <Button variant="outline" className="flex items-center gap-2">
                                                    <Plus className="h-4 w-4" />
                                                    Upload documents
                                                </Button>
                                                <p className="text-sm text-muted-foreground mt-2">Max file size: 4MB (PNG, JPG, PDF)</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </TabsContent>

                            {/* Activity Tab */}
                            <TabsContent value="activity" className="p-6">
                                <div className="space-y-6">
                                    <div className="flex items-center gap-2 mb-4">
                                        <Clock className="h-5 w-5 text-primary" />
                                        <h2 className="text-lg font-semibold">Activity Log</h2>
                                    </div>

                                    <div className="border rounded-lg p-4">
                                        <div className="flex items-center justify-between mb-4">
                                            <h3 className="font-medium">Recent Activity</h3>
                                            <Button variant="outline" size="sm" className="flex items-center gap-2">
                                                <Search className="h-3.5 w-3.5" />
                                                Filter
                                            </Button>
                                        </div>

                                        <ScrollArea className="h-[400px] pr-4">
                                            <div className="space-y-4">
                                                {activities.map((activity, index) => (
                                                    <div key={index} className="flex items-start gap-3 pb-4 border-b last:border-0">
                                                        <div className="w-2 h-2 rounded-full bg-primary mt-2.5" />
                                                        <div className="space-y-1">
                                                            <div className="font-medium">{activity.timestamp}</div>
                                                            <div className="text-muted-foreground">{activity.description}</div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </ScrollArea>
                                    </div>

                                    <div className="mt-8">
                                        <div className="flex items-center gap-2 mb-4">
                                            <FileText className="h-5 w-5 text-primary" />
                                            <h2 className="text-lg font-semibold">Internal Notes</h2>
                                        </div>

                                        <div className="border rounded-lg p-4">
                                            <Textarea
                                                placeholder="Add internal notes about this customer here..."
                                                className="min-h-[150px]"
                                            />
                                            <div className="flex justify-end mt-4">
                                                <Button>Save Notes</Button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </TabsContent>
                            {/* Technical Information Tab */}
                            <TabsContent value="technical" className="p-6">
                                <div className="space-y-6">
                                    <div className="flex  items-center gap-2 mb-4">
                                        <Info className="h-5 w-5 text-primary" />
                                        <h2 className="text-lg font-semibold">Technical Information</h2>
                                    </div>

                                    <div className=" flex flex-col gap-4">
                                        <div className="p-4 border rounded-lg bg-muted/20">
                                            <span className="text-sm text-muted-foreground block mb-1">OraSys ID</span>
                                            <span className="font-medium">924427</span>
                                        </div>

                                        <div className="p-4 border rounded-lg bg-muted/20">
                                            <span className="text-sm text-muted-foreground block mb-1">CIOL ID</span>
                                            <span className="font-mono text-sm">dfjhlur4jr.fghp8t94</span>
                                        </div>

                                        <div className="p-4 border rounded-lg bg-muted/20">
                                            <span className="text-sm text-muted-foreground block mb-1">External ID</span>
                                            <span className="text-muted-foreground italic text-sm">Not available</span>
                                        </div>

                                        <div className="p-4 border rounded-lg bg-muted/20">
                                            <span className="text-sm text-muted-foreground block mb-1">SumSub ID</span>
                                            <span className="text-muted-foreground italic text-sm">Not available</span>
                                        </div>
                                    </div>
                                </div>
                            </TabsContent>
                        </Tabs>
                    </Card>

                    {/* Risk Evaluation Card */}
                    <Card>
                        <CardHeader className="pb-3">
                            <div className="flex items-center gap-2">
                                <Shield className="h-5 w-5 text-primary" />
                                <CardTitle>Risk Evaluation</CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <div className="text-sm text-muted-foreground mb-2">Risk Score</div>
                                    <div className="text-3xl font-bold">{onboarding.riskLevel || "0"}</div>
                                    <Progress
                                        value={Math.min((onboarding.riskLevel / 900) * 100, 100)}
                                        className="h-2 mt-2"
                                        indicatorClassName={calculateRiskColor(onboarding.riskLevel)}
                                    />
                                </div>

                                <div>
                                    <div className="text-sm text-muted-foreground mb-2">Risk Status</div>
                                    <Badge className={`${calculateRiskColor(onboarding.riskLevel)} text-lg px-3 py-1`}>
                                        {calculateRiskLevel(onboarding.riskLevel)}
                                    </Badge>
                                    <p className="text-sm text-muted-foreground mt-2">
                                        Last evaluated on {formatDate(onboarding.updatedAt || new Date())}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Right column - 1/3 width */}
                <div className="space-y-6">
                    {/* Application Details Card */}
                    <Card>
                        <CardHeader className="pb-3">
                            <div className="flex items-center gap-2">
                                <FileText className="h-5 w-5 text-primary" />
                                <CardTitle>Application Details</CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex justify-between items-center">
                                <span className="text-muted-foreground">Status</span>
                                <Badge className="bg-green-500">Approved</Badge>
                            </div>
                            <Separator />
                            <div className="flex justify-between items-center">
                                <span className="text-muted-foreground">Application ID</span>
                                <div className="flex items-center gap-1">
                                    <span className="font-medium">{onboarding.clientID}</span>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-6 w-6"
                                        onClick={() => copyToClipboard(onboarding.clientID)}
                                    >
                                        <Copy className="h-3.5 w-3.5" />
                                    </Button>
                                </div>
                            </div>
                            <Separator />
                            <div className="flex justify-between items-center">

                                {user.dashboard==="corporate"?
                                    (<span className="text-muted-foreground">B2B Name</span>):
                                    (<span className="text-muted-foreground">Programme Manager Name</span>)
                                }

                                <span>{onboarding.company.company_name}</span>
                            </div>
                            <Separator />{" "}
                            <div className="flex justify-between items-center">
                                <span className="text-muted-foreground">Origin</span>
                                <span>{onboarding.origin || "Web Application"}</span>
                            </div>
                            <Separator />
                            <div className="flex justify-between items-center">
                                <span className="text-muted-foreground">Registration Date</span>
                                <span>{formatDate(onboarding.createdAt) || "N/A"}</span>
                            </div>

                            <Separator />
                            <div className="flex justify-between items-center">
                                <span className="text-muted-foreground">Last Update</span>
                                <span>{formatDate(onboarding.updatedAt) || "N/A"}</span>
                            </div>

                        </CardContent>
                    </Card>


                </div>
            </div>

            {/* Card Details Sheet */}
            <CardDetailsSheet
                isOpen={isSheetOpen}
                companyId={onboarding?.company?._id}
                onOpenChange={handleSheetOpenChange}
                cardId={selectedCardId || ""}
            />
        </div>
    )
}
