//@ts-nocheck
"use client"

import { useEffect, useState, useRef } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from "@/components/ui/sheet"
import axiosInstance from "@/utils/axiosInstance"
import { Badge } from "@/components/ui/badge"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { CardManagement } from "@/app/lite/admin/individual/v1/[id]/card/card-design"
import { CheckCircle, XCircle } from "lucide-react"

interface CardData {
    cardHash: string
    cardMask: string
    cardKey: string
    cardUuid: string
    expDate: string
    issueDate: string
    status: string
    statusCode: string
    terminating: boolean
    productCode: string
    productDesc: string
    holder: string
    accounts: Array<{
        accNo: string
        primary: boolean
        currencyCode: string
        currencyName: string
    }>
    embossName1: string
    source: string
    kind: string
    main: boolean
    limits: Array<{
        name: string
        value: number
        noLimit: boolean
    }>
    visual: string
    tokenized: boolean
    delivery: {
        deliveryType: string
    }
    contactless: boolean
    cardTechnologyMode: string
    creationDate: string
    autoRenewal: boolean
}

interface CardDetailsSheetProps {
    isOpen: boolean
    onOpenChange: (open: boolean) => void
    cardId: string
    companyId: string
}

export function CardDetailsSheet({ isOpen, onOpenChange, cardId, companyId }: CardDetailsSheetProps) {
    const router = useRouter()
    const searchParams = useSearchParams()
    const [loading, setLoading] = useState(true)
    const [card, setCard] = useState<CardData | null>(null)
    const [dbCard, setDbCard] = useState<  null>(null)
    const [error, setError] = useState<string | null>(null)
    const isClosing = useRef(false)

    // Example progress steps - replace with actual data from your API
    const progressSteps = [
        { name: "Card Created", completed: true },
        { name: "Card Activated", completed: card?.status === "ACTIVE" },
        { name: "Set 4-digit PIN", completed: dbCard?.set_pin === true }
    ]

    useEffect(() => {
        const urlCardId = searchParams.get("card")
        if (urlCardId && !isOpen && !isClosing.current) {
            onOpenChange(true)
        }
    }, [searchParams, isOpen, onOpenChange])

    useEffect(() => {
        if (isOpen && cardId) {
            const url = new URL(window.location.href)
            url.searchParams.set("card", cardId)
            window.history.pushState({}, "", url.toString())
        } else {
            if (searchParams.has("card")) {
                const url = new URL(window.location.href)
                url.searchParams.delete("card")
                window.history.pushState({}, "", url.toString())
            }
        }
    }, [isOpen, cardId, searchParams])

    useEffect(() => {
        if (isOpen && cardId) {
            fetchCardDetails(cardId)
        }
    }, [isOpen, cardId])

    const handleSheetOpenChange = (open: boolean) => {
        if (!open) {
            isClosing.current = true
            const url = new URL(window.location.href)
            url.searchParams.delete("card")
            window.history.pushState({}, "", url.toString())

            setTimeout(() => {
                isClosing.current = false
            }, 100)
        }
        onOpenChange(open)
    }

    const fetchCardDetails = async (cardId) => {
        try {
            const response = await axiosInstance.get<CardData>(`client/card/${cardId}`)
            setCard(response.data.card)
            setDbCard(response.data.dbCard)
        } catch (error) {
            console.error("Error fetching card details:", error)
            setError("Failed to load card details. Please try again later.")
        } finally {
            setLoading(false)
        }
    }

    function getStatusColor(status: string) {
        switch (status.toUpperCase()) {
            case "ACTIVE":
                return "bg-green-500"
            case "INACTIVE":
                return "bg-yellow-500"
            case "BLOCKED":
                return "bg-red-500"
            case "ORDERED":
                return "bg-blue-500"
            default:
                return "bg-gray-500"
        }
    }

    return (
        <Sheet open={isOpen} onOpenChange={handleSheetOpenChange}>
            <SheetContent side="right" className="w-[55%] bg-white sm:max-w-none max-h-screen overflow-y-auto">
                <SheetHeader>
                    <SheetTitle className="text-xl font-semibold text-primary">
                        **** {card?.cardMask.slice(-4, card?.cardMask.length)} - {card?.embossName1}
                    </SheetTitle>
                    <SheetDescription>
                        Nickname: {dbCard?.nickName} &nbsp;
                        {card?.status && <Badge className={getStatusColor(card?.status)}>{card?.status}</Badge>}
                    </SheetDescription>
                </SheetHeader>

                {/* Progress Stepper */}
                <div className="mt-6 mb-8">
                    <div className="relative flex flex-col sm:flex-row justify-between items-start">
                        {/* Connecting line */}
                        <div className="absolute top-6 left-6 right-6 h-0.5 bg-gray-200 hidden sm:block" />

                        {progressSteps.map((step, index) => (
                            <div key={index} className="relative z-10 flex flex-col items-center w-full sm:w-auto mb-4 sm:mb-0">
                                <div
                                    className={`flex items-center justify-center w-12 h-12 rounded-full border-2 ${
                                        step.completed ? "bg-green-50 border-green-500" : "bg-red-50 border-red-500"
                                    }`}
                                >
                                    {step.completed ? (
                                        <CheckCircle className="h-6 w-6 text-green-500" />
                                    ) : (
                                        <XCircle className="h-6 w-6 text-red-500" />
                                    )}
                                </div>
                                <div className="mt-2 text-center">
                  <span className={`font-medium ${step.completed ? "text-green-700" : "text-red-700"}`}>
                    {step.name}
                  </span>
                                </div>
                                {index < progressSteps.length - 1 && <div className="w-full h-0.5 bg-gray-200 my-4 block sm:hidden" />}
                            </div>
                        ))}
                    </div>
                </div>

                {card ? (
                    <CardManagement companyId={companyId} dbCard={dbCard} card={card} onApiSuccess={fetchCardDetails} />
                ) : (
                    <LoadingOverlay />
                )}
            </SheetContent>
        </Sheet>
    )
}

