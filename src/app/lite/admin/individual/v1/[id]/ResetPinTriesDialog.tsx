import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import axiosInstance from "@/utils/axiosInstance"
import { ChevronRight, Loader2 } from "lucide-react"
import { IoIosKeypad } from "react-icons/io"
import Swal from 'sweetalert2'

interface ResetPinTriesDialogProps {
    cardId: string
    onApiSuccess: () => void
}

export function ResetPinTriesDialog({ cardId, onApiSuccess }: ResetPinTriesDialogProps) {
    const [loading, setLoading] = useState(false)

    const handleResetPinTries = async () => {
        try {
            setLoading(true)
            const response = await axiosInstance.get(`cards/${cardId}/resetPinTries`)

            if (typeof onApiSuccess === "function") {
                onApiSuccess()
            }

            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: '<PERSON><PERSON> tries reset successfully',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            })
        } catch (error) {
            console.error("Failed to reset PIN tries", error)
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to reset PIN tries. Please try again.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            })
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <IoIosKeypad className="h-5 w-5 text-gray-600"/>
                        </div>
                        <span className="font-normal">Reset Pin Tries</span>
                    </div>
                    <ChevronRight/>
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Reset PIN Tries</DialogTitle>
                    <DialogDescription>Are you sure you want to reset the PIN tries for this card?</DialogDescription>
                </DialogHeader>

                <DialogFooter>
                    <Button type="submit" onClick={handleResetPinTries} disabled={loading}>
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Resetting...
                            </>
                        ) : (
                            "Reset PIN Tries"
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}