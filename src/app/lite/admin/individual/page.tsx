//@ts-nocheck
'use client'

import React, {useEffect, useState} from 'react';
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {Input} from "@/components/ui/input";
import {ChevronDown, ChevronUp, Loader2, Search} from 'lucide-react';
import axiosInstance from "@/utils/axiosInstance";
import {calculateRiskLevel, formatDate} from "@/utils/helpers";
import Link from "next/link";

interface IndividualOnboarding {
    _id: string;
    product: 'consumer-debit' | 'consumer-prepaid';
    personalInfo: {
        firstName: string;
        middleName?: string;
        lastName: string;
        dateOfBirth: string;
    };
    address: {
        streetAddress: string;
        streetAddress2?: string;
        city: string;
        stateProvince?: string;
        postalCode?: string;
        country: string;
    };
    idDocument: {
        type: 'passport' | 'national-id' | 'drivers-license';
        number: string;
        issuingCountry: string;
    };
    taxInfo: {
        country: string;
        taxIdNumber: string;
    };
    riskScore: number;
    cardTypes: ('virtual' | 'physical')[];

    createdAt: string;
}


export default function IndividualOnboardingTable() {
    const [onboardings, setOnboardings] = useState<IndividualOnboarding[]>([]);
    const [filteredOnboardings, setFilteredOnboardings] = useState<IndividualOnboarding[]>([]);

    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [sortConfig, setSortConfig] = useState<{
        key: keyof IndividualOnboarding | null,
        direction: 'ascending' | 'descending'
    }>({key: null, direction: 'ascending'});

    useEffect(() => {
        fetchData();
    }, []);

    async function fetchData() {
        try {
            // Replace with your actual API endpoint
            const response = await axiosInstance.get('onboarding/personal');
            if (Array.isArray(response.data.data)) {
                setOnboardings(response.data.data);
                setFilteredOnboardings(response.data.data);
            } else {
                throw new Error('Unexpected API response format');
            }
        } catch (error) {
            setError('Failed to fetch onboardings');
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        const filtered = onboardings.filter(onboarding =>
            onboarding.personalInfo.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            onboarding.personalInfo.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            onboarding.idDocument.number.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setFilteredOnboardings(filtered);
    }, [searchTerm, onboardings]);

    const requestSort = (key: keyof IndividualOnboarding) => {
        let direction: 'ascending' | 'descending' = 'ascending';
        if (sortConfig.key === key && sortConfig.direction === 'ascending') {
            direction = 'descending';
        }
        setSortConfig({key, direction});
    };

    function formatString(input) {
        return input
            .split('-') // Split the string by the hyphen
            .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter of each word
            .join(' '); // Join the words back with a space
    }

    const sortedOnboardings = React.useMemo(() => {
        let sortableOnboardings = [...filteredOnboardings];
        if (sortConfig.key !== null) {
            sortableOnboardings.sort((a, b) => {
                if (a[sortConfig.key!] < b[sortConfig.key!]) {
                    return sortConfig.direction === 'ascending' ? -1 : 1;
                }
                if (a[sortConfig.key!] > b[sortConfig.key!]) {
                    return sortConfig.direction === 'ascending' ? 1 : -1;
                }
                return 0;
            });
        }
        return sortableOnboardings;
    }, [filteredOnboardings, sortConfig]);

    const SortIcon = ({columnKey}: { columnKey: keyof IndividualOnboarding }) => {
        if (sortConfig.key !== columnKey) {
            return <ChevronUp className="ml-1 h-4 w-4 text-muted-foreground"/>;
        }
        return sortConfig.direction === 'ascending' ? (
            <ChevronUp className="ml-1 h-4 w-4"/>
        ) : (
            <ChevronDown className="ml-1 h-4 w-4"/>
        );
    };

    return (
        <Card className="w-full">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-2xl font-bold">Individual Customers</CardTitle>
                <div className="flex items-center space-x-2">
                    <div className="relative">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"/>
                        <Input
                            placeholder="Search onboardings..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-8"
                        />
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead className="font-semibold cursor-pointer"
                                           onClick={() => requestSort('createdAt')}>
                                    Date Created
                                    <SortIcon columnKey="createdAt"/>
                                </TableHead>
                                <TableHead className="font-semibold cursor-pointer"
                                           onClick={() => requestSort('personalInfo')}>
                                    Customer Name
                                    <SortIcon columnKey="personalInfo"/>
                                </TableHead>
                                <TableHead className="font-semibold cursor-pointer"
                                           onClick={() => requestSort('product')}>
                                    Product
                                    <SortIcon columnKey="product"/>
                                </TableHead>

                                <TableHead className="font-semibold cursor-pointer"
                                           onClick={() => requestSort('address')}>
                                    Customer ID
                                    <SortIcon columnKey="address"/>
                                </TableHead>
                                <TableHead className="font-semibold cursor-pointer"
                                           onClick={() => requestSort('idDocument')}>
                                    ID Document <small className="text-gray-400">[Country <span className='text-primary'>|</span> Type <span
                                    className='text-primary'>|</span> Number]</small>
                                    <SortIcon columnKey="idDocument"/>
                                </TableHead>


                                <TableHead className="font-semibold cursor-pointer"
                                           onClick={() => requestSort('riskScore')}>
                                    Risk Category
                                    <SortIcon columnKey="taxInfo"/>
                                </TableHead>

                                <TableHead className="font-semibold cursor-pointer">
                                    Status
                                </TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {loading ? (
                                <TableRow>
                                    <TableCell colSpan={6} className="h-24 text-center">
                                        <Loader2 className="mx-auto h-6 w-6 animate-spin"/>
                                        <p className="mt-2 text-sm text-muted-foreground">Loading onboardings...</p>
                                    </TableCell>
                                </TableRow>
                            ) : error ? (
                                <TableRow>
                                    <TableCell colSpan={6} className="h-24 text-center text-red-500">
                                        {error}
                                    </TableCell>
                                </TableRow>
                            ) : sortedOnboardings.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={6} className="h-24 text-center">
                                        No onboardings found.
                                    </TableCell>
                                </TableRow>
                            ) : (
                                sortedOnboardings.map((onboarding, index) => (
                                    <TableRow key={onboarding._id} className={index % 2 === 0 ? 'bg-muted/50' : ''}>
                                        <TableCell>{formatDate(onboarding.createdAt)}</TableCell>
                                        <TableCell className="font-bold"> <Link href={`individual/v1/${onboarding._id}/`}
                                                                                passHref>{`${onboarding.personalInfo.firstName} ${onboarding.personalInfo.lastName}`}</Link></TableCell>
                                        <TableCell>{formatString(onboarding.product)} ({onboarding.cardCurrency.map((c) => c.currency_code).join(",")})</TableCell>
                                        <TableCell>{`${onboarding.clientID} `}</TableCell>
                                        <TableCell> {formatString(onboarding.idDocument.issuingCountry)} <span className='text-primary'>|</span> {formatString(onboarding.idDocument.type)} <span className='text-primary'>|</span>  {onboarding.idDocument.number} </TableCell>
                                        <TableCell>{calculateRiskLevel(onboarding.riskScore)}</TableCell>
                                        <TableCell>N/A</TableCell>

                                        {/*<TableCell>*/}
                                        {/*    <Link href={`individual/${onboarding._id}/`}  passHref>*/}
                                        {/*        <Button size="sm" className="mr-2">View Details</Button>*/}
                                        {/*    </Link>*/}
                                        {/*</TableCell>*/}
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </div>
            </CardContent>
        </Card>
    );
}
