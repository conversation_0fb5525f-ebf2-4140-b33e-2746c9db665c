// @ts-nocheck
"use client"

import * as React from "react"
import { useEffect, useState } from "react"
import { usePathname, useRouter } from "next/navigation"
import Image from "next/image"
import {
    ArrowLeft,
    Building2,
    ChevronRight,
    ChevronsUpDown,
    CogIcon,
    CreditCardIcon,
    Home,
    List,
    ListTodo,
    LogOut,
    LogInIcon as LogsIcon,
    UsersIcon, UserCog,
} from "lucide-react"

import { Avatar, AvatarImage } from "@/components/ui/avatar"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarHeader,
    SidebarInset,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
    SidebarProvider,
    SidebarRail,
    SidebarTrigger,
} from "@/components/ui/sidebar"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import axiosInstance from "@/utils/axiosInstance"
import { useAppDispatch, useAppSelector } from "@/store/hooks"
import { setUser } from "@/store/userSlice"
import { TeamSwitcher } from "@/app/lite/admin/DashboardSwitcher"
import { BuildingStorefrontIcon, Cog6ToothIcon } from "@heroicons/react/24/outline"
import { FaDonate } from "react-icons/fa"
import { AutoBreadcrumb } from "@/components/auto-breadcrumb"

import { GlobalLoadingIndicator } from "@/components/global-loading-indicator"
import { Link } from "@/components/ui/link"
import { PageTransition } from "@/components/page-transition"
import {AutoLogoutProvider} from "@/components/auto-logout-provider";
import {MdInventory, MdOutlineInventory2} from "react-icons/md";

// Unified menu data structure
const menuData = [
    {
        title: "BIN Settings",
        url: "#",
        icon: CogIcon,
        isActive: true,
        items: [
            { title: "Card Scheme", url: "/lite/admin/configurations/card-scheme" },
            { title: "Programme Type", url: "/lite/admin/configurations/programme-type" },
            { title: "BIN Category", url: "/lite/admin/configurations/bin-category" },
            { title: "BIN Variants", url: "/lite/admin/configurations/bin-variants" },

            { title: "BIN Type", url: "/lite/admin/configurations/bin-type" },
            // { title: "BIN Range", url: "/lite/admin/configurations/bin-range" },
            { title: "BIN Usage", url: "/lite/admin/configurations/bin-usage" },
            { title: "Card Programme Type", url: "/lite/admin/configurations/card-programme-type" },
            { title: "Programme Manager Type", url: "/lite/admin/configurations/programme-manager-type" },
        ],
    },
    {
        title: "Product Settings",
        url: "#",
        icon: CogIcon,
        isActive: true,
        items: [

            { title: "Product Versions", url: "/lite/admin/configurations/product-versions" },
            { title: "Product Code Request", url: "/lite/admin/configurations/product-code-request" },
            { title: "Product Currency", url: "/lite/admin/configurations/currency" },
            { title: "Card Library", url: "/lite/admin/configurations/card-images" },

        ],
    },
    {
        title: "Global Settings",
        url: "#",
        icon: CogIcon,
        isActive: false,
        items: [
            // { title: "DHL Zones", url: "/lite/admin/configurations/delivery-zone" },
            // { title: "Polish Post Regions", url: "/lite/admin/configurations/delivery-regions" },
            // { title: "Delivery Methods", url: "/lite/admin/configurations/delivery" },
            { title: "DHL Delivery", url: "/lite/admin/configurations/delivery/bulk/dhl" },
            { title: "Polish Post Delivery", url: "/lite/admin/configurations/delivery/bulk/pp" },

            { title: "Countries", url: "/lite/admin/configurations/country" },



        ],
    },
    // {
    //     title: "Ryvyl Employees",
    //     url: "#",
    //     icon: UsersIcon,
    //     isActive: false,
    //     items: [
    //         { title: "Employees", url: "/lite/admin/users" },
    //         { title: "Permission Roles", url: "/lite/admin/roles" },
    //     ],
    // },

    { title: "Analytics", url: "/lite/admin/dashboard", icon: Home },
    { title: "Programme Pipeline", url: "/lite/admin/companies", icon: Building2 },
    { title: "CIP", url: "/lite/admin/programmes", icon: List },
    {
        title: "Customers",
        url: "#",
        icon: UsersIcon,
        isActive: false,
        items: [
            { title: "Individual", url: "/lite/admin/customers/individual" },
            { title: "B2B", url: "/lite/admin/customers/b2b" },
        ],
    },
    { title: "Transactions", url: "/lite/admin/transactions", icon: List,
        isActive: false,

        items: [
            { title: "All", url: "/lite/admin/transactions" },
            { title: "Pending", url: "#" },
            { title: "Settled", url: "#" },
            { title: "Cancelled", url: "#" },
            { title: "Refunds / Reversals", url: "#" },
        ],},
    { title: "Inventory Management",icon: MdOutlineInventory2 , url: "/lite/admin/inventory" },
    {
        title: "Logs",
        url: "#",
        icon: LogsIcon,
        isActive: false,
        items: [
            { title: "Server Logs", url: "/lite/admin/logs" },
            { title: "Webhook Logs", url: "/lite/admin/logs/webhook" },
        ],
    },

    { title: "External API Users", url: "/lite/admin/api-settings", icon: UsersIcon },

    { title: "Roles", url: "/lite/admin/roles" , icon:UserCog},
    { title: "Users", url: "/lite/admin/users", icon: UsersIcon },
    { title: "Events", url: "/lite/admin/events", icon: ListTodo },
    { title: "Settings", url: "/lite/admin/settings", icon: Cog6ToothIcon },
    {
        title: "Coming Soon",
        url: "#",
        icon: CogIcon,
        isActive: false,
        items: [
            { title: "Fees", url: "/lite/admin/coming-soon" },
            { title: "Limits", url: "/lite/admin/coming-soon" },
            // {title: "Card Image Library", url: "/lite/admin/coming-soon"},
            // { title: "Delivery Methods", url: "/lite/admin/coming-soon" },
            // { title: "Inventory Management", url: "/lite/admin/coming-soon" },
            // { title: "Bank Ac - IBAN process", url: "/lite/admin/coming-soon" },
            { title: "Fraud Management", url: "/lite/admin/coming-soon" },
        ],
    },
]

const dashboards = [
    {
        name: "Issuing",
        logo: CreditCardIcon,
        plan: "Free",
    },
    {
        name: "Acquiring",
        logo: BuildingStorefrontIcon,
        plan: "",
    },
    {
        name: "Payments",
        logo: FaDonate,
        plan: "",
    },

    {
        name: "Business Settings",
        logo: CogIcon,
        plan: "",
    },
]

export default function MainDashboardLayout({ children }: { children: React.ReactNode }) {
    const pathname = usePathname()
    const dispatch = useAppDispatch()
    const [isAuthenticated, setIsAuthenticated] = useState(false)
    const router = useRouter()
    const user = useAppSelector((state) => state.user.user)
    const [roles, setRoles] = useState<any>(null)
    const [isLogoutDialogOpen, setIsLogoutDialogOpen] = React.useState(false)

    useEffect(() => {
        const checkAuth = async () => {
            try {
                const response = await axiosInstance.get("users/me", { withCredentials: true })
                if (response.data && response.data.dashboard !== "cardholder" && response.data.recordId == null) {
                    // setUser(response.data)

                    setRoles(response.data.roles)
                    localStorage.setItem("user", response.data._id)
                    dispatch(setUser(response.data))
                    setIsAuthenticated(true)
                } else {
                    router.push("/login")
                }
            } catch (error) {
                console.error(error)
                router.push("/login")
            }
        }

        checkAuth()
    }, [router, dispatch])

    const handleLogout = async () => {
        try {
            localStorage.removeItem("accessToken")
            setIsAuthenticated(false)
            setIsLogoutDialogOpen(false)
            // router.push('/login')
            window.location.replace("/login")
        } catch (error) {
            console.error("Error during logout:", error)
        }
    }

    const link = (url: string) => {
        router.push(url)
    }

    if (!isAuthenticated) {
        return null
    }

    const isActive = (href: string) => {
        return pathname === href
    }

    const renderMenuItem = (item: any) => {
        // Flatten all permissions from roles
        const allPermissions = roles.flatMap((role: any) => role.permissions)

        // Check if the user has permission for the given menu item
        const hasPermission = (permission: string) => {
            if (roles.length === 0) return true // Show all menus if roles array is empty
            const allPermissions = roles.flatMap((role: any) => role.permissions)
            return allPermissions.some((p) => p.toLowerCase().includes(permission.toLowerCase()))
        }

        if (item.items) {
            // Check if the user has permission for the parent or any of the sub-items
            const hasSubItemPermission = item.items.some((subItem: any) => hasPermission(subItem.title))

            if (hasSubItemPermission || roles.length === 0) {
                return (
                    <Collapsible key={item.title} asChild defaultOpen={item.isActive} className="group/collapsible">
                        <SidebarMenuItem>
                            <CollapsibleTrigger asChild>
                                <SidebarMenuButton
                                    tooltip={item.title}
                                    className={`font-bold transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground ${
                                        item.items?.some((subItem: any) => isActive(subItem.url))
                                            ? "text-primary bg-primary/10 border-l-2 border-primary"
                                            : "text-primary"
                                    }`}
                                >
                                    {item.icon && <item.icon />}
                                    <span>{item.title}</span>
                                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                                </SidebarMenuButton>
                            </CollapsibleTrigger>
                            <CollapsibleContent>
                                <SidebarMenuSub>
                                    {item.items.map((subItem: any) => {
                                        if (hasPermission(subItem.title)) {
                                            return (
                                                <SidebarMenuSubItem
                                                    key={subItem.title}
                                                    className={`transition-all duration-200 ${
                                                        isActive(subItem.url)
                                                            ? "bg-primary shadow-sm rounded-md border-l-2 border-primary-foreground ml-2"
                                                            : "hover:bg-sidebar-accent/50 hover:ml-1"
                                                    }`}
                                                >
                                                    <SidebarMenuSubButton
                                                        className={`font-normal transition-all duration-200 ${
                                                            isActive(subItem.url)
                                                                ? "text-white font-medium"
                                                                : "text-muted-foreground hover:text-foreground"
                                                        }`}
                                                        asChild
                                                    >
                                                        <Link href={subItem.url} className="cursor-pointer">
                                                            <span>{subItem.title}</span>
                                                        </Link>
                                                    </SidebarMenuSubButton>
                                                </SidebarMenuSubItem>
                                            )
                                        }
                                        return null
                                    })}
                                </SidebarMenuSub>
                            </CollapsibleContent>
                        </SidebarMenuItem>
                    </Collapsible>
                )
            } else {
            }
        } else {
            // Check if the user has permission for this single item
            if (hasPermission(item.title)) {
                return (
                    <SidebarMenuItem
                        key={item.title}
                        className={`transition-all duration-200 ${
                            isActive(item.url)
                                ? "bg-primary text-white rounded-md shadow-sm border-l-4 border-primary-foreground"
                                : "hover:bg-sidebar-accent/50"
                        }`}
                    >
                        <SidebarMenuButton
                            tooltip={item.title}
                            className={`font-bold transition-all duration-200 ${
                                isActive(item.url)
                                    ? "text-white hover:text-white"
                                    : "text-primary hover:text-primary hover:bg-transparent"
                            }`}
                            asChild
                        >
                            <Link href={item.url} className="cursor-pointer">
                                {item.icon && <item.icon />}
                                <span>{item.title}</span>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                )
            }
        }
        return null // Return null if no permissions match
    }

    return (
        <SidebarProvider>
            <GlobalLoadingIndicator />
            <Sidebar collapsible="icon" className="bg-white">
                <SidebarHeader>
                    <TeamSwitcher teams={dashboards} />

                    <SidebarMenu>
                        <SidebarMenuItem>
                            <SidebarMenuButton
                                size="lg"
                                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                            >
                                <Image
                                    src="/images/logo/icon-dark.svg"
                                    width={40}
                                    height={40}
                                    alt="ryvyl.eu"
                                    className="overflow-hidden size-8"
                                />
                                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">
                    <Image src="/logo.jpeg" width={150} height={150} alt="ryvyl.eu" className="overflow-hidden" />
                  </span>
                                </div>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    </SidebarMenu>
                </SidebarHeader>
                <SidebarContent>
                    <SidebarGroup>
                        <SidebarMenu>{menuData.map(renderMenuItem)}</SidebarMenu>
                    </SidebarGroup>
                </SidebarContent>
                <SidebarFooter>
                    <SidebarMenu>
                        <SidebarMenuItem>
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <SidebarMenuButton
                                        size="lg"
                                        className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                                    >
                                        <Avatar className="h-8 w-8 rounded-lg">
                                            <AvatarImage src="/images/logo/icon-dark.svg" alt={user?.name} />
                                        </Avatar>
                                        <div className="grid flex-1 text-left text-sm leading-tight">
                                            <span className="truncate font-semibold">{user?.name}</span>
                                            <span className="truncate text-xs">{user?.email}</span>
                                        </div>
                                        <ChevronsUpDown className="ml-auto size-4" />
                                    </SidebarMenuButton>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent
                                    className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                                    side="bottom"
                                    align="end"
                                    sideOffset={4}
                                >
                                    <DropdownMenuLabel className="p-0 font-normal">
                                        <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                                            <Avatar className="h-8 w-8 rounded-lg">
                                                <AvatarImage src="/images/logo/icon-dark.svg" alt={user?.name} />
                                            </Avatar>
                                            <div className="grid flex-1 text-left text-sm leading-tight">
                                                <span className="truncate font-semibold">{user?.name}</span>
                                                <span className="truncate text-xs">{user?.email}</span>
                                            </div>
                                        </div>
                                    </DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    <Dialog open={isLogoutDialogOpen} onOpenChange={setIsLogoutDialogOpen}>
                                        <DialogTrigger asChild>
                                            <DropdownMenuItem onSelect={(event) => event.preventDefault()}>
                                                <LogOut className="mr-2 h-4 w-4" />
                                                <span>Log out</span>
                                            </DropdownMenuItem>
                                        </DialogTrigger>
                                        <DialogContent>
                                            <DialogHeader>
                                                <DialogTitle>Are you sure you want to log out?</DialogTitle>
                                                <DialogDescription>You will be redirected to the login page.</DialogDescription>
                                            </DialogHeader>
                                            <DialogFooter>
                                                <Button variant="outline" onClick={() => setIsLogoutDialogOpen(false)}>
                                                    Cancel
                                                </Button>
                                                <Button variant="destructive" onClick={handleLogout}>
                                                    Log out
                                                </Button>
                                            </DialogFooter>
                                        </DialogContent>
                                    </Dialog>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </SidebarMenuItem>
                    </SidebarMenu>
                </SidebarFooter>
                <SidebarRail />
            </Sidebar>
            <SidebarInset>
                <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
                    <div className="flex items-center gap-2 px-4 w-full">
                        <SidebarTrigger className="-ml-1" />
                        <Button
                            variant="ghost"
                            size="icon"
                            className="rounded-full mr-1"
                            onClick={() => router.back()}
                            aria-label="Go back"
                        >
                            <ArrowLeft className="h-4 w-4" />
                        </Button>
                        <Separator orientation="vertical" className="mr-2 h-4" />

                        <AutoBreadcrumb />
                    </div>
                </header>

                <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
                    <PageTransition>
                        <AutoLogoutProvider
                            timeoutMinutes={10}
                            warningMinutes={1}
                            onLogout={() => {
                                console.log("User logged out due to inactivity")
                                // Add any additional logout logic here
                            }}
                        >
                            {children}
                        </AutoLogoutProvider>                    </PageTransition>
                </div>
            </SidebarInset>
        </SidebarProvider>
    )
}
