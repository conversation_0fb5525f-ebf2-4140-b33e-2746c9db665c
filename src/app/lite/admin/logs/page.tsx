//@ts-nocheck
"use client"

import { useState, useEffect } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { ChevronLeft, ChevronRight, Download, Filter, RefreshCw, Search } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { DateRangePicker } from "@/components/date-range-picker"
import { Skeleton } from "@/components/ui/skeleton"
import axiosInstance from "@/utils/axiosInstance";

interface Log {
    _id: string
    remoteAddr: string
    userIdentity: string
    timestamp: string
    method: string
    url: string
    httpVersion: string
    status: number
    responseSize: string
    referer: string
    userAgent: string
    responseTime: string
    requestBody: object
    responseBody: object
}

export default function LogsPage() {
    const router = useRouter()
    const searchParams = useSearchParams()

    // Pagination state
    const [page, setPage] = useState(Number.parseInt(searchParams.get("page") || "1"))
    const [limit, setLimit] = useState(Number.parseInt(searchParams.get("limit") || "10"))
    const [totalPages, setTotalPages] = useState(1)
    const [totalLogs, setTotalLogs] = useState(0)

    // Filter state
    const [method, setMethod] = useState(searchParams.get("method") || "")
    const [status, setStatus] = useState(searchParams.get("status") || "")
    const [search, setSearch] = useState(searchParams.get("search") || "")
    const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({
        from: searchParams.get("from") ? new Date(searchParams.get("from") as string) : undefined,
        to: searchParams.get("to") ? new Date(searchParams.get("to") as string) : undefined,
    })

    // Data state
    const [logs, setLogs] = useState<Log[]>([])
    const [loading, setLoading] = useState(true)
    const [selectedLog, setSelectedLog] = useState<Log | null>(null)

    // Fetch logs with current filters and pagination
    const fetchLogs = async () => {
        setLoading(true)

        // Build query params
        const params = new URLSearchParams()
        params.set("page", page.toString())
        params.set("limit", limit.toString())

        if (method) params.set("method", method)
        if (status) params.set("status", status)
        if (search) params.set("search", search)
        if (dateRange.from) params.set("from", dateRange.from.toISOString())
        if (dateRange.to) params.set("to", dateRange.to.toISOString())

        try {
            const response = await axiosInstance.get(`/logs?${params.toString()}`)
            const data = await response.data

            if (data.success) {
                setLogs(data.logs)
                setTotalPages(data.totalPages)
                setTotalLogs(data.total)
            } else {
                console.error("Failed to fetch logs:", data.message)
            }
        } catch (error) {
            console.error("Error fetching logs:", error)
        } finally {
            setLoading(false)
        }
    }

    // Update URL with current filters and pagination
    const updateUrl = () => {
        const params = new URLSearchParams()

        params.set("page", page.toString())
        params.set("limit", limit.toString())

        if (method) params.set("method", method)
        if (status) params.set("status", status)
        if (search) params.set("search", search)
        if (dateRange.from) params.set("from", dateRange.from.toISOString())
        if (dateRange.to) params.set("to", dateRange.to.toISOString())

        router.push(`logs?${params.toString()}`)
    }

    // Apply filters
    const applyFilters = () => {
        setPage(1) // Reset to first page when filters change
        updateUrl()
        fetchLogs()
    }

    // Reset filters
    const resetFilters = () => {
        setMethod("")
        setStatus("")
        setSearch("")
        setDateRange({ from: undefined, to: undefined })
        setPage(1)
    }

    // Handle pagination
    const goToPage = (newPage: number) => {
        if (newPage < 1 || newPage > totalPages) return
        setPage(newPage)
    }

    // Effect to fetch logs when page or limit changes
    useEffect(() => {
        updateUrl()
        fetchLogs()
    }, [page, limit])

    // Format date for display
    const formatDate = (dateString: string) => {
        const date = new Date(dateString)
        try {
            return new Intl.DateTimeFormat("en-US", {
                year: "numeric",
                month: "short",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
                second: "2-digit",
            }).format(date)
        }catch (e){
            return "Invalid date"
        }

    }

    // Get status badge color
    const getStatusColor = (status: number) => {
        if (status >= 200 && status < 300) return "bg-green-500"
        if (status >= 300 && status < 400) return "bg-blue-500"
        if (status >= 400 && status < 500) return "bg-yellow-500"
        if (status >= 500) return "bg-red-500"
        return "bg-gray-500"
    }

    // Get method badge color
    const getMethodColor = (method: string) => {
        switch (method.toUpperCase()) {
            case "GET":
                return "bg-blue-500"
            case "POST":
                return "bg-green-500"
            case "PUT":
                return "bg-yellow-500"
            case "DELETE":
                return "bg-red-500"
            case "PATCH":
                return "bg-purple-500"
            default:
                return "bg-gray-500"
        }
    }

    return (
        <div className="container mx-auto py-6 space-y-6">
            <div className="flex justify-between items-center">
                <h1 className="text-3xl font-bold">Server Logs</h1>
                <div className="flex items-center gap-2">
                    <Button variant="outline" size="icon" onClick={fetchLogs} title="Refresh logs">
                        <RefreshCw className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" title="Export logs">
                        <Download className="h-4 w-4" />
                    </Button>
                </div>
            </div>

            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle>Filters</CardTitle>
                    <CardDescription>Filter logs by various criteria</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Search</label>
                            <div className="relative">
                                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    type="text"
                                    placeholder="Search URL, IP, User..."
                                    className="pl-8"
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <label className="text-sm font-medium">HTTP Method</label>
                            <Select value={method} onValueChange={setMethod}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Methods" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Methods</SelectItem>
                                    <SelectItem value="GET">GET</SelectItem>
                                    <SelectItem value="POST">POST</SelectItem>
                                    <SelectItem value="PUT">PUT</SelectItem>
                                    <SelectItem value="DELETE">DELETE</SelectItem>
                                    <SelectItem value="PATCH">PATCH</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <label className="text-sm font-medium">Status Code</label>
                            <Select value={status} onValueChange={setStatus}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Status Codes" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Status Codes</SelectItem>
                                    <SelectItem value="2xx">2xx (Success)</SelectItem>
                                    <SelectItem value="3xx">3xx (Redirection)</SelectItem>
                                    <SelectItem value="4xx">4xx (Client Error)</SelectItem>
                                    <SelectItem value="5xx">5xx (Server Error)</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <label className="text-sm font-medium">Date Range</label>
                            <DateRangePicker date={dateRange} onDateChange={setDateRange} />
                        </div>
                    </div>

                    <div className="flex justify-end gap-2 mt-4">
                        <Button variant="outline" onClick={resetFilters}>
                            Reset
                        </Button>
                        <Button onClick={applyFilters}>Apply Filters</Button>
                    </div>
                </CardContent>
            </Card>

            {/* Logs Table */}
            <Card>
                <CardHeader>
                    <div className="flex justify-between items-center">
                        <CardTitle>Logs</CardTitle>
                        <div className="text-sm text-muted-foreground">
                            Showing {logs.length} of {totalLogs} logs
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="rounded-md border overflow-hidden">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-[180px]">Timestamp</TableHead>
                                    <TableHead className="w-[100px]">Method</TableHead>
                                    <TableHead className="w-[80px]">Status</TableHead>
                                    <TableHead>URL</TableHead>
                                    <TableHead className="w-[120px]">IP Address</TableHead>
                                    <TableHead className="w-[100px]">Response Time</TableHead>
                                    <TableHead className="w-[80px]">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {loading ? (
                                    Array.from({ length: limit }).map((_, i) => (
                                        <TableRow key={i}>
                                            <TableCell>
                                                <Skeleton className="h-6 w-full" />
                                            </TableCell>
                                            <TableCell>
                                                <Skeleton className="h-6 w-16" />
                                            </TableCell>
                                            <TableCell>
                                                <Skeleton className="h-6 w-12" />
                                            </TableCell>
                                            <TableCell>
                                                <Skeleton className="h-6 w-full" />
                                            </TableCell>
                                            <TableCell>
                                                <Skeleton className="h-6 w-24" />
                                            </TableCell>
                                            <TableCell>
                                                <Skeleton className="h-6 w-16" />
                                            </TableCell>
                                            <TableCell>
                                                <Skeleton className="h-6 w-8" />
                                            </TableCell>
                                        </TableRow>
                                    ))
                                ) : logs.length > 0 ? (
                                    logs.map((log) => (
                                        <TableRow key={log._id}>
                                            <TableCell className="font-mono text-xs">{formatDate(log.timestamp)}</TableCell>
                                            <TableCell>
                                                <Badge className={`${getMethodColor(log.method)} text-white`}>{log.method}</Badge>
                                            </TableCell>
                                            <TableCell>
                                                <Badge className={`${getStatusColor(log.status)} text-white`}>{log.status}</Badge>
                                            </TableCell>
                                            <TableCell className="max-w-[300px] truncate font-mono text-xs">{log.url}</TableCell>
                                            <TableCell className="font-mono text-xs">{log.remoteAddr}</TableCell>
                                            <TableCell>{log.responseTime}</TableCell>
                                            <TableCell>
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="icon" className="h-8 w-8">
                                                            <Filter className="h-4 w-4" />
                                                            <span className="sr-only">Open menu</span>
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem onClick={() => setSelectedLog(log)}>View Details</DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => setSearch(log.url)}>Filter by URL</DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => setMethod(log.method)}>Filter by Method</DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => setStatus(log.status.toString().charAt(0) + "xx")}>
                                                            Filter by Status
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={7} className="h-24 text-center">
                                            No logs found matching your criteria
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </div>

                    {/* Pagination */}
                    <div className="flex items-center justify-between mt-4">
                        <div className="text-sm text-muted-foreground">
                            Page {page} of {totalPages} ({totalLogs} total logs)
                        </div>
                        <div className="flex items-center gap-2">
                            <Select
                                value={limit.toString()}
                                onValueChange={(value) => {
                                    setLimit(Number.parseInt(value))
                                    setPage(1)
                                }}
                            >
                                <SelectTrigger className="w-[100px]">
                                    <SelectValue placeholder="10 per page" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="10">10 per page</SelectItem>
                                    <SelectItem value="25">25 per page</SelectItem>
                                    <SelectItem value="50">50 per page</SelectItem>
                                    <SelectItem value="100">100 per page</SelectItem>
                                </SelectContent>
                            </Select>

                            <div className="flex items-center gap-1">
                                <Button variant="outline" size="icon" onClick={() => goToPage(1)} disabled={page === 1}>
                                    <ChevronLeft className="h-4 w-4" />
                                    <ChevronLeft className="h-4 w-4 -ml-2" />
                                </Button>
                                <Button variant="outline" size="icon" onClick={() => goToPage(page - 1)} disabled={page === 1}>
                                    <ChevronLeft className="h-4 w-4" />
                                </Button>

                                <div className="flex items-center gap-1 mx-2">
                                    {(() => {
                                        // Calculate which page numbers to show
                                        let startPage = Math.max(1, page - 2)
                                        const endPage = Math.min(totalPages, startPage + 4)

                                        // Adjust start page if we're near the end
                                        if (endPage - startPage < 4 && startPage > 1) {
                                            startPage = Math.max(1, endPage - 4)
                                        }

                                        const pageNumbers = []
                                        for (let i = startPage; i <= endPage; i++) {
                                            pageNumbers.push(i)
                                        }

                                        return pageNumbers.map((pageNum) => (
                                            <Button
                                                key={pageNum}
                                                variant={pageNum === page ? "default" : "outline"}
                                                size="icon"
                                                className="w-8 h-8"
                                                onClick={() => goToPage(pageNum)}
                                            >
                                                {pageNum}
                                            </Button>
                                        ))
                                    })()}
                                </div>

                                <Button variant="outline" size="icon" onClick={() => goToPage(page + 1)} disabled={page === totalPages}>
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => goToPage(totalPages)}
                                    disabled={page === totalPages}
                                >
                                    <ChevronRight className="h-4 w-4" />
                                    <ChevronRight className="h-4 w-4 -ml-2" />
                                </Button>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Log Details Modal */}
            {/* Log Details Modal */}
            {selectedLog && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
                    <div className="bg-background rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
                        <div className="flex justify-between items-center border-b p-6">
                            <h2 className="text-2xl font-bold">Request Details</h2>
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => setSelectedLog(null)}
                                className="rounded-full"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    className="lucide lucide-x h-5 w-5"
                                >
                                    <path d="M18 6 6 18" />
                                    <path d="m6 6 12 12" />
                                </svg>
                                <span className="sr-only">Close</span>
                            </Button>
                        </div>

                        <div className="overflow-auto p-6 space-y-8">
                            {/* Basic Information Section */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-primary">Overview</h3>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Server Time</p>
                                        <p className="font-mono text-sm">
                                            {formatDate(selectedLog.timestamp)}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Client Time</p>
                                        <p className="font-mono text-sm">
                                            {formatDate(selectedLog.clientTime)}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Status</p>
                                        <Badge className={`${getStatusColor(selectedLog.status)} text-white`}>
                                            {selectedLog.status}
                                        </Badge>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Method</p>
                                        <Badge className={`${getMethodColor(selectedLog.method)} text-white`}>
                                            {selectedLog.method}
                                        </Badge>
                                    </div>
                                </div>
                            </div>

                            {/* Client & Server Section */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-primary">Network</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">IP Address</p>
                                        <p className="font-mono text-sm break-all">
                                            {selectedLog.remoteAddr}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Response Time</p>
                                        <p className="font-mono text-sm">
                                            {selectedLog.responseTime} ms
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">HTTP Version</p>
                                        <p className="font-mono text-sm">
                                            {selectedLog.httpVersion}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Response Size</p>
                                        <p className="font-mono text-sm">
                                            {selectedLog.responseSize} bytes
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* URL & References Section */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-primary">Resources</h3>
                                <div className="space-y-4">
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">URL</p>
                                        <p className="font-mono text-sm break-all bg-muted p-3 rounded-md">
                                            {selectedLog.url}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">Referer</p>
                                        <p className="font-mono text-sm break-all bg-muted p-3 rounded-md">
                                            {selectedLog.referer || 'N/A'}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-sm text-muted-foreground">User Agent</p>
                                        <p className="font-mono text-sm break-all bg-muted p-3 rounded-md">
                                            {selectedLog.userAgent}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Request & Response Bodies */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-primary">Request Body</h3>
                                    <div className="bg-muted p-4 rounded-md overflow-auto max-h-64">
              <pre className="text-xs font-mono whitespace-pre-wrap text-primary">
                {JSON.stringify(selectedLog.requestBody, null, 2)}
              </pre>
                                    </div>
                                </div>
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-primary">Response Body</h3>
                                    <div className="bg-muted p-4 rounded-md overflow-auto max-h-64">
              <pre className="text-xs font-mono whitespace-pre-wrap text-primary">
                {JSON.stringify(selectedLog.responseBody, null, 2)}
              </pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}
