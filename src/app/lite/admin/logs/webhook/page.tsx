//@ts-nocheck
"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { AlertCircle, CheckCircle, Clock, Eye, Filter, RefreshCw } from 'lucide-react'
import axios from "@/utils/axiosInstance"

interface WebhookLog {
    _id: string
    event: string
    webhookUrl: string
    payloadSent: any
    responseStatus: number
    responseBody: any
    error: any
    createdAt: string
}

interface WebhookStats {
    summary: {
        total: number
        successful: number
        failed: number
    }
    eventBreakdown: Array<{
        _id: string
        count: number
    }>
}

export default function WebhookLogsPage() {
    const [logs, setLogs] = useState<WebhookLog[]>([])
    const [stats, setStats] = useState<WebhookStats | null>(null)
    const [loading, setLoading] = useState(true)
    const [selectedLog, setSelectedLog] = useState<WebhookLog | null>(null)
    const [filters, setFilters] = useState({
        event: "",
        status: "",
        page: 1,
        limit: 10,
    })
    const [pagination, setPagination] = useState({
        page: 1,
        limit: 10,
        total: 0,
        pages: 0,
    })

    const fetchLogs = async () => {
        try {
            setLoading(true)
            const params = new URLSearchParams()
            if (filters.event) params.append("event", filters.event)
            if (filters.status) params.append("status", filters.status)
            params.append("page", filters.page.toString())
            params.append("limit", filters.limit.toString())

            const response = await axios.get(`webhook-logs?${params}`)
            const data = response.data

            setLogs(data.logs)
            setPagination(data.pagination)
        } catch (error) {
            console.error("Error fetching logs:", error)
        } finally {
            setLoading(false)
        }
    }

    const fetchStats = async () => {
        try {
            const response = await axios.get("webhook-logs/stats/summary")
            setStats(response.data)
        } catch (error) {
            console.error("Error fetching stats:", error)
        }
    }

    useEffect(() => {
        fetchLogs()
        fetchStats()
    }, [filters])

    const getStatusBadge = (status: number) => {
        if (status >= 200 && status < 300) {
            return (
                <Badge variant="default" className="bg-green-100 text-green-800">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Success
                </Badge>
            )
        } else if (status >= 400) {
            return (
                <Badge variant="destructive">
                    <AlertCircle className="w-3 h-3 mr-1" />
                    Error
                </Badge>
            )
        } else {
            return (
                <Badge variant="secondary">
                    <Clock className="w-3 h-3 mr-1" />
                    Pending
                </Badge>
            )
        }
    }

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString()
    }

    const handleFilterChange = (key: string, value: string) => {
        setFilters((prev) => ({
            ...prev,
            [key]: value,
            page: 1, // Reset to first page when filtering
        }))
    }

    const handlePageChange = (newPage: number) => {
        setFilters((prev) => ({ ...prev, page: newPage }))
    }

    return (
        <div className="container mx-auto p-6 space-y-6">
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-3xl font-bold">Webhook Logs</h1>
                    <p className="text-muted-foreground">Monitor and analyze webhook delivery logs</p>
                </div>
                <Button
                    onClick={() => {
                        fetchLogs()
                        fetchStats()
                    }}
                    disabled={loading}
                >
                    <RefreshCw className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`} />
                    Refresh
                </Button>
            </div>

            {/* Stats Cards */}
            {stats && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Webhooks</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.summary.total}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Successful</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{stats.summary.successful}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Failed</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">{stats.summary.failed}</div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center">
                        <Filter className="w-4 h-4 mr-2" />
                        Filters
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="event-filter">Event Type</Label>
                            <Input
                                id="event-filter"
                                placeholder="Filter by event..."
                                value={filters.event}
                                onChange={(e) => handleFilterChange("event", e.target.value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="status-filter">Status</Label>
                            <Select value={filters.status} onValueChange={(value) => handleFilterChange("status", value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All statuses" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All statuses</SelectItem>
                                    <SelectItem value="200">Success (2xx)</SelectItem>
                                    <SelectItem value="400">Client Error (4xx)</SelectItem>
                                    <SelectItem value="500">Server Error (5xx)</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="limit-filter">Per Page</Label>
                            <Select value={filters.limit.toString()} onValueChange={(value) => handleFilterChange("limit", value)}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="10">10</SelectItem>
                                    <SelectItem value="25">25</SelectItem>
                                    <SelectItem value="50">50</SelectItem>
                                    <SelectItem value="100">100</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Logs Table */}
            <Card>
                <CardHeader>
                    <CardTitle>Webhook Logs</CardTitle>
                    <CardDescription>
                        Showing {logs.length} of {pagination.total} logs
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    {loading ? (
                        <div className="flex justify-center items-center h-32">
                            <RefreshCw className="w-6 h-6 animate-spin" />
                        </div>
                    ) : (
                        <>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Event</TableHead>
                                        <TableHead>URL</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Created At</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {logs.map((log) => (
                                        <TableRow key={log._id}>
                                            <TableCell className="font-medium">{log.event}</TableCell>
                                            <TableCell className="max-w-xs truncate">{log.webhookUrl}</TableCell>
                                            <TableCell>{getStatusBadge(log.responseStatus)}</TableCell>
                                            <TableCell>{formatDate(log.createdAt)}</TableCell>
                                            <TableCell>
                                                <Dialog>
                                                    <DialogTrigger asChild>
                                                        <Button variant="outline" size="sm" onClick={() => setSelectedLog(log)}>
                                                            <Eye className="w-4 h-4 mr-1" />
                                                            View
                                                        </Button>
                                                    </DialogTrigger>
                                                    <DialogContent className="max-w-4xl max-h-[80vh]">
                                                        <DialogHeader>
                                                            <DialogTitle>Webhook Log Details</DialogTitle>
                                                            <DialogDescription>
                                                                Event: {selectedLog?.event} | Status: {selectedLog?.responseStatus}
                                                            </DialogDescription>
                                                        </DialogHeader>
                                                        <ScrollArea className="h-[60vh]">
                                                            <div className="space-y-4">
                                                                <div>
                                                                    <h4 className="font-semibold mb-2">Webhook URL</h4>
                                                                    <p className="text-sm bg-muted p-2 rounded break-all">{selectedLog?.webhookUrl}</p>
                                                                </div>
                                                                <div>
                                                                    <h4 className="font-semibold mb-2">Payload Sent</h4>
                                                                    <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                                    {JSON.stringify(selectedLog?.payloadSent, null, 2)}
                                  </pre>
                                                                </div>
                                                                <div>
                                                                    <h4 className="font-semibold mb-2">Response Body</h4>
                                                                    <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                                    {JSON.stringify(selectedLog?.responseBody, null, 2)}
                                  </pre>
                                                                </div>
                                                                {selectedLog?.error && (
                                                                    <div>
                                                                        <h4 className="font-semibold mb-2">Error</h4>
                                                                        <pre className="text-xs bg-red-50 p-2 rounded overflow-auto text-red-800">
                                      {JSON.stringify(selectedLog.error, null, 2)}
                                    </pre>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </ScrollArea>
                                                    </DialogContent>
                                                </Dialog>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>

                            {/* Pagination */}
                            <div className="flex items-center justify-between mt-4">
                                <div className="text-sm text-muted-foreground">
                                    Page {pagination.page} of {pagination.pages}
                                </div>
                                <div className="flex space-x-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePageChange(pagination.page - 1)}
                                        disabled={pagination.page <= 1}
                                    >
                                        Previous
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePageChange(pagination.page + 1)}
                                        disabled={pagination.page >= pagination.pages}
                                    >
                                        Next
                                    </Button>
                                </div>
                            </div>
                        </>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}
