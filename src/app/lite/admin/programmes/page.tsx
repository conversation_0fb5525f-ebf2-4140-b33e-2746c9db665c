//@ts-nocheck

"use client"

import React, { useEffect, useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ChevronDown, ChevronRight, Loader2, Search, Users } from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"
import Link from "next/link"
import type { CardProgram, Company } from "@types/types"
import { useAppSelector } from "@/store/hooks"
import { formatDate } from "@/utils/helpers"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { Button } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import DataExporter from "@/components/DataExporter"
import { alertHelper } from "@/utils/alertHelper"
import { countryNameByCode } from "@/utils/data"

interface ExpandedRowData {
    loading: boolean
    data: any | null
    error: string | null
}

type Role = {
    permissions: string[]
}

export default function ProgrammesCipPage() {
    const [companies, setCompanies] = useState<Company[]>([])
    const [cardProgramme, setCardProgramme] = useState<CardProgram[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({})
    const [expandedRowsData, setExpandedRowsData] = useState<Record<string, ExpandedRowData>>({})
    const [searchTerm, setSearchTerm] = useState<string>("")

    useEffect(() => {
        async function fetchData() {
            try {
                const response = await axiosInstance.get("company")
                const companies = response.data.data
                const cardProgramme = response.data.programme

                // Extract the company IDs that are present in cardProgramme
                const cardProgrammeCompanyIds = cardProgramme.map((program) => program.company)

                // Filter companies to get only those that are in cardProgramme
                const nonUniqueCompanies = companies.filter((company) => cardProgrammeCompanyIds.includes(company._id))

                setCompanies(nonUniqueCompanies)
                setCardProgramme(cardProgramme)
            } catch (error) {
                setError("Failed to fetch companies")
            } finally {
                setLoading(false)
            }
        }

        fetchData()
    }, [])

    const user = useAppSelector((state) => state.user.user)
    const [roles, setRoles] = useState<Role[]>([])

    useEffect(() => {
        setRoles(user.roles)
    }, [user.roles])

    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions))
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }

    const userHasRequestPermission = hasPermission(roles, "Issuing Pipeline_Create CIP")
    const userHasViewPermission = hasPermission(roles, "Issuing Pipeline_View")

    const toggleRow = async (companyId: string) => {
        setExpandedRows((prev) => ({
            ...prev,
            [companyId]: !prev[companyId],
        }))

        // If row is being expanded and we don't have data yet, fetch it
        if (!expandedRows[companyId] && !expandedRowsData[companyId]) {
            setExpandedRowsData((prev) => ({
                ...prev,
                [companyId]: { loading: true, data: null, error: null },
            }))

            try {
                const response = await axiosInstance.get(`company/${companyId}/bankingClients`)
                setExpandedRowsData((prev) => ({
                    ...prev,
                    [companyId]: { loading: false, data: response.data, error: null },
                }))
            } catch (error) {
                setExpandedRowsData((prev) => ({
                    ...prev,
                    [companyId]: { loading: false, data: null, error: "Failed to fetch details" },
                }))
            }
        }
    }

    const activateAccount = async (formData) => {
        setLoading(true)
        try {
            const response = await axiosInstance.post("users/register", {
                name: `${formData.personalInfo.firstName} ${formData.personalInfo.lastName}`,
                email: formData.personalInfo.email,
                status: "Active",
                dashboard: "cardholder",
                recordId: formData._id,
            })

            if (response.status === 201) {
                await axiosInstance.post("onboarding/personal/activate", { recordId: formData._id })
                alertHelper.showToast("Cardholder login details sent to email address!", "success")
            }
        } catch (e) {
            console.error(e)
        } finally {
            setLoading(false)
        }
    }

    const activatePmAccount = async (company) => {
        setLoading(true)
        try {
            const response = await axiosInstance.post("users/register", {
                name: company.company_name,
                email: company.company_email,
                status: "Active",
                dashboard: "programmeManager",
                recordId: company._id,
            })

            if (response.status === 201) {
                await axiosInstance.get(`company/${company._id}/activate`)
                alertHelper.showToast("Programme Manager login details sent to email address!", "success")
            } else {
                alertHelper.showToast("Error while activation", "error")
            }
        } catch (e) {
            console.error(e)
            alertHelper.showToast("Error while activation", "error")
        } finally {
            setLoading(false)
        }
    }

    if (loading) {
        return <LoadingOverlay />
    }

    const data = companies.map((row, index) => ({
        id: index + 1,
        name: row.company_name,
        email: row.company_email,
        company_phone: row.company_phone,
        company_website: row.company_website,
        created_at: formatDate(row.created_at),
    }))

    const filteredCompanies = companies.filter((company) => {
        if (!searchTerm.trim()) return true

        const searchLower = searchTerm.toLowerCase()

        // Search across multiple fields
        return (
            company.company_name?.toLowerCase().includes(searchLower) ||
            company.company_email?.toLowerCase().includes(searchLower) ||
            company.company_number?.toLowerCase().includes(searchLower) ||
            company.admin_name?.toLowerCase().includes(searchLower) ||
            company.admin_email?.toLowerCase().includes(searchLower) ||
            company.company_phone?.toLowerCase().includes(searchLower) ||
            company.contact_name?.toLowerCase().includes(searchLower)
        )
    })

    return (
        <div className="space-y-6">
            <Card className="border-0 shadow-sm">
                <CardHeader className="bg-white px-6 py-5 flex flex-col md:flex-row md:items-center md:justify-between gap-4 border-b">
                    <div>
                        <div className="flex items-center gap-2">
                            <Users className="h-6 w-6 text-slate-600" />
                            <CardTitle className="text-2xl font-bold text-slate-800">Card Programmes</CardTitle>
                        </div>
                        <CardDescription className="text-slate-500 mt-1">
                            Manage and monitor all card programmes in one place
                        </CardDescription>
                    </div>
                    <div className="flex flex-col md:flex-row items-center gap-3">
                        <div className="relative w-full md:w-auto">
                            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400" />
                            <Input
                                placeholder="Search programmes..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-9 bg-white w-full md:w-[250px] border-slate-200"
                            />
                        </div>
                        <DataExporter
                            data={data}
                            filename="card-programmes"
                            title="Card Programmes Report"
                            className="w-full md:w-auto"
                        />
                    </div>
                </CardHeader>

                <CardContent className="p-0">
                    <div className="rounded-md border-0">
                        <Table>
                            <TableHeader className="bg-slate-50">
                                <TableRow>
                                    <TableHead className="w-[180px] font-medium text-slate-600">Date Created</TableHead>
                                    <TableHead className="font-medium text-slate-600">Company Name</TableHead>
                                    <TableHead className="font-medium text-slate-600">Company Number</TableHead>
                                    <TableHead className="font-medium text-slate-600">Company Email</TableHead>
                                    <TableHead className="font-medium text-slate-600">Dashboard Status</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredCompanies.length === 0 ? (
                                    <TableRow>
                                        <TableCell colSpan={5} className="h-24 text-center text-slate-500">
                                            No card programmes found
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    filteredCompanies.map((company, index) => (
                                        <React.Fragment key={company._id}>
                                            <TableRow className={index % 2 === 0 ? "bg-white" : "bg-slate-50/50"}>
                                                <TableCell className="font-medium">
                                                    <div className="flex items-center gap-2">
                                                        <button
                                                            onClick={() => toggleRow(company._id)}
                                                            className="p-1 hover:bg-slate-100 rounded-full transition-colors"
                                                            aria-label={expandedRows[company._id] ? "Collapse row" : "Expand row"}
                                                        >
                                                            {expandedRows[company._id] ? (
                                                                <ChevronDown className="h-4 w-4 text-slate-600" />
                                                            ) : (
                                                                <ChevronRight className="h-4 w-4 text-slate-600" />
                                                            )}
                                                        </button>
                                                        {formatDate(company.created_at)}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <Link
                                                        href={`programmes/company/${company._id}`}
                                                        className="font-semibold text-slate-800 hover:text-slate-600 hover:underline transition-colors"
                                                    >
                                                        {company.company_name}
                                                    </Link>
                                                </TableCell>
                                                <TableCell className="text-slate-600">{company.company_number}</TableCell>
                                                <TableCell className="text-slate-600">{company.company_email}</TableCell>
                                                <TableCell>
                                                    <Badge
                                                        variant="outline"
                                                        className="bg-emerald-50 text-emerald-700 border-emerald-100 font-medium"
                                                    >
                                                        Active
                                                    </Badge>
                                                </TableCell>
                                            </TableRow>

                                            {expandedRows[company._id] && (
                                                <TableRow>
                                                    <TableCell colSpan={5} className="p-0 border-t-0">
                                                        <ExpandedRowContent
                                                            companyId={company._id}
                                                            company={company}
                                                            activateAccount={activateAccount}
                                                        />
                                                    </TableCell>
                                                </TableRow>
                                            )}
                                        </React.Fragment>
                                    ))
                                )}
                            </TableBody>
                        </Table>
                    </div>
                </CardContent>
            </Card>

            <div className="text-center text-sm text-slate-500">
                Showing {filteredCompanies.length} of {companies.length} card programmes
            </div>
        </div>
    )
}

function ExpandedRowContent({ companyId, company, activateAccount }) {
    const [expandedRowsData, setExpandedRowsData] = useState<Record<string, ExpandedRowData>>({})

    useEffect(() => {
        async function fetchData() {
            try {
                const response = await axiosInstance.get(`company/${companyId}/bankingClients`)
                setExpandedRowsData({
                    [companyId]: {
                        loading: false,
                        data: {
                            ...response.data,
                            // Use the b2bList directly from the company object
                            b2bList: company.b2bList || [],
                        },
                        error: null,
                    },
                })
            } catch (error) {
                setExpandedRowsData({
                    [companyId]: { loading: false, data: null, error: "Failed to fetch details" },
                })
            }
        }

        fetchData()
    }, [companyId, company])

    const rowData = expandedRowsData[companyId]

    if (!rowData) {
        return (
            <div className="p-6 bg-slate-50/50 flex items-center justify-center">
                <Loader2 className="h-5 w-5 animate-spin text-slate-400 mr-2" />
                <span className="text-slate-600">Loading details...</span>
            </div>
        )
    }

    if (rowData.error) {
        return <div className="p-6 bg-slate-50/50 text-center text-red-500">{rowData.error}</div>
    }

    if (rowData.data) {
        const individualData = rowData.data.users.map((row, index) => ({
            id: index + 1,
            name: `${row.personalInfo.firstName} ${row.personalInfo.lastName}`,
            email: row.personalInfo.email,
            phone: row.personalInfo.phone,
            citizenship: countryNameByCode(row.citizenship),
            birth_country: row.personalInfo.birthCountry,
            risk_level: row.riskLevel,
            application_status: row.applicationStatus,
            created_at: formatDate(row.created_at),
        }))

        // Check if b2bList exists and has items
        const hasB2BData = rowData.data.b2bList && rowData.data.b2bList.length > 0

        // Prepare B2B data for export if available
        const b2bData = hasB2BData
            ? rowData.data.b2bList.map((b2b, index) => ({
                id: index + 1,
                name: b2b.companyName,
                client_code: b2b.clientCode,
                email: b2b.email,
                phone: b2b.phoneNumber,
                country: b2b.address?.country || "N/A",
                status: b2b.status,
                created_at: formatDate(b2b.createdAt),
            }))
            : []

        return (
            <div className="bg-slate-50/50 p-4">
                <div className="bg-white rounded-lg border border-slate-200 overflow-hidden">
                    <div className="p-4 border-b border-slate-200 bg-slate-50">
                        <h3 className="font-semibold text-slate-800 flex items-center gap-2">
                            <Users className="h-4 w-4 text-slate-600" />
                            {company.company_name} - Cardholders
                        </h3>
                    </div>

                    <Tabs defaultValue="individual" className="w-full">
                        <div className="px-4 pt-2 border-b border-slate-200">
                            <TabsList className="bg-transparent h-10 p-0">
                                <TabsTrigger
                                    value="individual"
                                    className="data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-slate-800 rounded-none h-10 px-4"
                                >
                                    Individual
                                </TabsTrigger>
                                <TabsTrigger
                                    value="btob"
                                    className="data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-slate-800 rounded-none h-10 px-4"
                                >
                                    BtoB{" "}
                                    {hasB2BData && (
                                        <span className="ml-1 bg-slate-200 text-slate-700 text-xs px-1.5 py-0.5 rounded-full">
                      {rowData.data.b2bList.length}
                    </span>
                                    )}
                                </TabsTrigger>
                            </TabsList>
                        </div>

                        <TabsContent value="individual" className="p-0 m-0">
                            <div className="p-4 flex justify-between items-center">
                                <h4 className="text-sm font-medium text-slate-600">
                                    {rowData.data.users.length} Individual Cardholders
                                </h4>
                                <DataExporter
                                    data={individualData}
                                    filename={`individual_cardholders_${company.company_name}`}
                                    title={`Individual Cardholder for ${company.company_name} Report`}
                                />
                            </div>

                            <div className="px-4 pb-4">
                                <div className="border rounded-md overflow-hidden">
                                    <Table>
                                        <TableHeader className="bg-slate-50">
                                            <TableRow>
                                                <TableHead className="font-medium text-slate-600">Date Created</TableHead>
                                                <TableHead className="font-medium text-slate-600">Customer Name</TableHead>
                                                <TableHead className="font-medium text-slate-600">Customer Email</TableHead>
                                                <TableHead className="font-medium text-slate-600">Citizenship</TableHead>
                                                <TableHead className="font-medium text-slate-600">Customer ID</TableHead>
                                                <TableHead className="font-medium text-slate-600">Dashboard Status</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {rowData.data.users.length === 0 ? (
                                                <TableRow>
                                                    <TableCell colSpan={6} className="h-24 text-center text-slate-500">
                                                        No customers found
                                                    </TableCell>
                                                </TableRow>
                                            ) : (
                                                rowData.data.users.map((onboarding, index) => (
                                                    <TableRow key={onboarding._id} className={index % 2 === 0 ? "bg-white" : "bg-slate-50/50"}>
                                                        <TableCell className="text-slate-600">{formatDate(onboarding.createdAt)}</TableCell>
                                                        <TableCell>
                                                            <Link
                                                                href={`individual/v1/${onboarding._id}/`}
                                                                className="font-semibold text-slate-800 hover:text-slate-600 hover:underline transition-colors"
                                                            >
                                                                {`${onboarding.personalInfo.firstName} ${onboarding.personalInfo.lastName}`}
                                                            </Link>
                                                        </TableCell>
                                                        <TableCell className="text-slate-600">{onboarding.personalInfo.email}</TableCell>
                                                        <TableCell className="text-slate-600">
                                                            {countryNameByCode(onboarding.citizenship)}
                                                        </TableCell>
                                                        <TableCell className="text-slate-600">{onboarding.clientID}</TableCell>
                                                        <TableCell>
                                                            {onboarding?.dashboardStatus?.toUpperCase() !== "ACTIVE" ? (
                                                                <Button
                                                                    onClick={() => activateAccount(onboarding)}
                                                                    size="sm"
                                                                    className="bg-emerald-600 hover:bg-emerald-700 text-white"
                                                                >
                                                                    Activate Account
                                                                </Button>
                                                            ) : (
                                                                <Badge
                                                                    variant="outline"
                                                                    className="bg-emerald-50 text-emerald-700 border-emerald-100 font-medium"
                                                                >
                                                                    Active
                                                                </Badge>
                                                            )}
                                                        </TableCell>
                                                    </TableRow>
                                                ))
                                            )}
                                        </TableBody>
                                    </Table>
                                </div>
                            </div>
                        </TabsContent>

                        <TabsContent value="btob" className="p-0 m-0">
                            {hasB2BData ? (
                                <>
                                    <div className="p-4 flex justify-between items-center">
                                        <h4 className="text-sm font-medium text-slate-600">{rowData.data.b2bList.length} B2B Customers</h4>
                                        <DataExporter
                                            data={b2bData}
                                            filename={`b2b_customers_${company.company_name}`}
                                            title={`B2B Customers for ${company.company_name} Report`}
                                        />
                                    </div>

                                    <div className="px-4 pb-4">
                                        <div className="border rounded-md overflow-hidden">
                                            <Table>
                                                <TableHeader className="bg-slate-50">
                                                    <TableRow>
                                                        <TableHead className="font-medium text-slate-600">Date Created</TableHead>
                                                        <TableHead className="font-medium text-slate-600">Company Name</TableHead>
                                                        <TableHead className="font-medium text-slate-600">Client Code</TableHead>
                                                        <TableHead className="font-medium text-slate-600">Email</TableHead>
                                                        <TableHead className="font-medium text-slate-600">Country</TableHead>
                                                        <TableHead className="font-medium text-slate-600">Status</TableHead>
                                                    </TableRow>
                                                </TableHeader>
                                                <TableBody>
                                                    {rowData.data.b2bList.map((b2b, index) => (
                                                        <TableRow key={b2b._id}  className={index % 2 === 0 ? "bg-white" : "bg-slate-50/50"}>
                                                            <TableCell className="text-slate-600">{formatDate(b2b.createdAt)}</TableCell>
                                                            <TableCell>
                                                                <Link
                                                                    href={`customers/b2b/${b2b._id}/`}
                                                                    className="font-semibold text-slate-800 hover:text-slate-600 hover:underline transition-colors"
                                                                >
                                                                    <span className="font-semibold text-slate-800">{b2b.companyName}</span>
                                                                </Link>

                                                            </TableCell>
                                                            <TableCell className="text-slate-600">{b2b.clientCode}</TableCell>
                                                            <TableCell className="text-slate-600">{b2b.email}</TableCell>
                                                            <TableCell className="text-slate-600">{b2b.address?.country || "N/A"}</TableCell>
                                                            <TableCell>
                                                                <Badge
                                                                    variant="outline"
                                                                    className={
                                                                        b2b.status === "active"
                                                                            ? "bg-emerald-50 text-emerald-700 border-emerald-100 font-medium"
                                                                            : "bg-amber-50 text-amber-700 border-amber-100 font-medium"
                                                                    }
                                                                >
                                                                    {b2b.status === "active" ? "Active" : "Pending"}
                                                                </Badge>
                                                            </TableCell>
                                                        </TableRow>
                                                    ))}
                                                </TableBody>
                                            </Table>
                                        </div>
                                    </div>
                                </>
                            ) : (
                                <div className="p-8 text-center text-slate-500">No B2B customers found</div>
                            )}
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        )
    }

    return null
}
