import type React from "react"
import Image from "next/image"
import { Dialog, DialogContent } from "@/components/ui/dialog"

interface ImagePopupProps {
    isOpen: boolean
    onClose: () => void
    imageSrc: string
}

const ImagePopup: React.FC<ImagePopupProps> = ({ isOpen, onClose, imageSrc }) => {
    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[90vw] sm:max-h-[90vh]">
                <div className="relative w-full h-full">
                    <Image
                        src={imageSrc || "/placeholder.svg"}
                        alt="Enlarged view"
                        layout="responsive"
                        width={1000}
                        height={1000}
                        objectFit="contain"
                    />
                </div>
            </DialogContent>
        </Dialog>
    )
}

export default ImagePopup

