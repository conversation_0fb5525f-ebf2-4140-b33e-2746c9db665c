//@ts-nocheck

import Company from "@/components/company-profile";

export default function CompanyPage({params}: { params: { id: string } }) {
    return <Company params={{id: params.id}}/>
}


// "use client"
//
// import { Badge } from "@/components/ui/badge"
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
// import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
// import { ChevronDown, Copy, Paperclip, Plus, X } from "lucide-react"
// import type React from "react"
// import { useEffect, useState } from "react"
// import axiosInstance from "@/utils/axiosInstance"
// import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
// import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
// import { LoadingOverlay } from "@/components/LoadingOverlay"
// import { Button } from "@/components/ui/button"
// import { Textarea } from "@/components/ui/textarea"
// import { formatDate } from "@/utils/helpers"
// import { useRouter } from "next/navigation"
// import Link from "next/link"
// import { alertHelper } from "@/utils/alertHelper"
// import Swal from "sweetalert2"
// import PhoneNumberDisplay from "@/components/PhoneDispaly"
// import { AddContactModal } from "@/components/add-contact-modal"
//
// interface Document {
//     name: string
//     type: string
//     size: string
// }
//
// interface Event {
//     name: string
//     type: string
//     size: string
//     title: string
// }
//
// const events: Event[] = [
//     {
//         title: "Name of requested event goes here",
//         name: "nameofevent",
//         type: "eventtype",
//         size: "2.1MB",
//     },
//     {
//         title: "Name of requested event goes here",
//         name: "nameofevent",
//         type: "eventtype",
//         size: "2.1MB",
//     },
// ]
//
// const documents: Document[] = [
//     { name: "nameofdocument", type: "doctype", size: "2.1MB" },
//     { name: "nameofdocument", type: "doctype", size: "2.1MB" },
//     { name: "nameofdocument", type: "doctype", size: "2.1MB" },
// ]
//
// interface ReviewStatus {
//     icon: React.ReactNode
//     label: string
//     actionRequired?: boolean
// }
//
// const statuses = ["Initiated", "Pending", "Pre-checked", "Queued", "On Hold"]
//
// const reviewStatuses: ReviewStatus[] = [
//     {
//         icon: <div className="w-4 h-4 rounded-full border" />,
//         label: "John Smitherson",
//     },
//     {
//         icon: (
//             <div className="w-4 h-4 rounded-full bg-orange-500 text-white flex items-center justify-center text-xs">!</div>
//         ),
//         label: "John Smitherson",
//         actionRequired: true,
//     },
//     {
//         icon: <div className="w-4 h-4 rounded-full bg-red-500 text-white flex items-center justify-center text-xs">×</div>,
//         label: "John Smitherson",
//     },
//     {
//         icon: (
//             <div className="w-4 h-4 rounded-full bg-green-500 text-white flex items-center justify-center text-xs">✓</div>
//         ),
//         label: "John Smitherson",
//     },
// ]
//
// interface Activity {
//     timestamp: string
//     description: string
// }
//
// const activities: Activity[] = [
//     {
//         timestamp: "30 March 2023, 14:00:00",
//         description: "[Employee/User name] performed an action that appears in this text line.",
//     },
//     {
//         timestamp: "30 March 2023, 14:00:00",
//         description: "[Employee/User name] performed an action that appears in this text line.",
//     },
//     {
//         timestamp: "30 March 2023, 14:00:00",
//         description: "[Employee/User name] performed an action that appears in this text line.",
//     },
//     {
//         timestamp: "30 March 2023, 14:00:00",
//         description: "[Employee/User name] performed an action that appears in this text line.",
//     },
//     {
//         timestamp: "30 March 2023, 14:00:00",
//         description: "[Employee/User name] performed an action that appears in this text line.",
//     },
// ]
// export default function Company({ params }: { params: { id: string } }) {
//     const [companyData, setCompanyData] = useState<null | any>(null)
//     const [cip, setCip] = useState<null | any>(null)
//     const [loading, setLoading] = useState(true)
//     const [error, setError] = useState<string | null>(null)
//
//     const router = useRouter()
//
//     // Move this function outside of useEffect
//     async function fetchCompanyDetails() {
//         try {
//             const response = await axiosInstance.get(`/company/${params.id}`)
//             setCompanyData(response.data.company)
//             setCip(response.data.cip)
//         } catch (error: any) {
//             setError(error.response?.data.message || "Failed to fetch company data")
//         } finally {
//             setLoading(false)
//         }
//     }
//
//     useEffect(() => {
//         fetchCompanyDetails()
//     }, [params.id])
//
//     if (loading) {
//         return <LoadingOverlay />
//     }
//
//     const handleRowClick = () => {
//         router.push(`/lite/admin/programmes/company/${params.id}/BIN-view`)
//     }
//
//     const handleUnassignProduct = async (productId: string) => {
//         const result = await Swal.fire({
//             title: "Unassign Product",
//             text: "Are you sure you want to unassign this product?",
//             icon: "warning",
//             showCancelButton: true,
//             confirmButtonColor: "#00a8a5",
//             cancelButtonColor: "#d33",
//             confirmButtonText: "Yes, unassign!",
//         })
//
//         if (result.isConfirmed) {
//             setLoading(true)
//             try {
//                 const response = await axiosInstance.delete(`/cip/${productId}`)
//                 if (response.status === 200) {
//                     setCip((prevCip) => prevCip.filter((product) => product._id !== productId))
//                     alertHelper.showToast("Product unassigned successfully", "success")
//                 }
//             } catch (error: any) {
//                 setError(error.response?.data.message || "Failed to unassign product")
//                 alertHelper.showToast(
//                     "Failed to unassign product: " + (error.response?.data.message || "Unknown error"),
//                     "error",
//                 )
//             } finally {
//                 setLoading(false)
//             }
//         }
//     }
//
//     return (
//         <div className="  w-full  space-y-6">
//             <h1 className="text-xl font-semibold">Company {companyData.ryvyl_id}</h1>
//
//             <div className="grid lg:grid-cols-[1fr,400px] gap-5">
//                 {/* Left Column */}
//                 <div className="space-y-6">
//                     <Card>
//                         <CardContent className="p-6">
//                             <div className="space-y-4">
//                                 <div>
//                                     <div>
//                                         <h2 className="text-lg font-semibold">{companyData.company_name}</h2>
//                                     </div>
//                                     <p className="text-sm text-muted-foreground">Company Profile</p>
//                                 </div>
//
//                                 <Tabs defaultValue="banking-applicant">
//                                     <TabsList className="w-full justify-start border-b rounded-none h-auto p-0 bg-transparent">
//                                         <TabsTrigger
//                                             value="banking-applicant"
//                                             className="rounded-none uppercase border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
//                                         >
//                                             Client
//                                         </TabsTrigger>
//                                         <TabsTrigger
//                                             value="addresses"
//                                             className="rounded-none uppercase border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
//                                         >
//                                             Addresses
//                                         </TabsTrigger>
//                                         <TabsTrigger
//                                             value="contacts"
//                                             className="rounded-none uppercase border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
//                                         >
//                                             Contacts
//                                         </TabsTrigger>
//
//                                         <TabsTrigger
//                                             value="products"
//                                             className="rounded-none uppercase border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
//                                         >
//                                             Products
//                                         </TabsTrigger>
//                                         <TabsTrigger
//                                             value="technical"
//                                             className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
//                                         >
//                                             DOCUMENTS
//                                         </TabsTrigger>
//                                         <TabsTrigger
//                                             value="banking-details"
//                                             className="rounded-none uppercase border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
//                                         >
//                                             Questioner
//                                         </TabsTrigger>
//                                         <TabsTrigger
//                                             value="cards"
//                                             className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
//                                         >
//                                             AUTHORIZE SIGNER
//                                         </TabsTrigger>
//
//                                         <TabsTrigger
//                                             value="pmtypes"
//                                             className="rounded-none uppercase border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
//                                         >
//                                             Programme Manager Types
//                                         </TabsTrigger>
//                                     </TabsList>
//
//                                     <TabsContent value="banking-applicant" className="pt-6">
//                                         <Table>
//                                             <TableRow className="bg-background">
//                                                 <TableHead className="font-semibold text-primary cursor-pointer" colSpan={2}>
//                                                     Company Information
//                                                 </TableHead>
//                                             </TableRow>
//                                             <TableRow>
//                                                 <TableCell className="text-muted-foreground">Company Name</TableCell>
//                                                 <TableCell>{companyData.company_name}</TableCell>
//                                             </TableRow>
//                                             <TableRow>
//                                                 <TableCell className="text-muted-foreground">Country of Incorporation</TableCell>
//                                                 <TableCell>{companyData.country_of_incorporation}</TableCell>
//                                             </TableRow>
//                                             <TableRow>
//                                                 <TableCell className="text-muted-foreground">Registration Number</TableCell>
//                                                 <TableCell>{companyData.company_number}</TableCell>
//                                             </TableRow>
//                                             <TableRow>
//                                                 <TableCell className="text-muted-foreground">Registration Date</TableCell>
//                                                 <TableCell>{new Date(companyData.registration_date).toLocaleDateString()}</TableCell>
//                                             </TableRow>
//                                             <TableRow>
//                                                 <TableCell className="text-muted-foreground">Industry</TableCell>
//                                                 <TableCell>{companyData.company_industry}</TableCell>
//                                             </TableRow>
//                                             <TableRow>
//                                                 <TableCell className="text-muted-foreground">Type of Business</TableCell>
//                                                 <TableCell>{companyData.type_of_business}</TableCell>
//                                             </TableRow>
//                                             <TableRow>
//                                                 <TableCell className="text-muted-foreground">Website</TableCell>
//                                                 <TableCell>{companyData.company_website}</TableCell>
//                                             </TableRow>
//
//                                             <TableRow>
//                                                 <TableCell className="text-muted-foreground">Client ID (Existing ID form OraSys)</TableCell>
//                                                 <TableCell>{companyData.ryvyl_id}</TableCell>
//                                             </TableRow>
//
//                                             <TableRow>
//                                                 <TableCell className="text-muted-foreground">Company Email </TableCell>
//                                                 <TableCell>{companyData.company_email}</TableCell>
//                                             </TableRow>
//
//                                             <TableRow>
//                                                 <TableCell className="text-muted-foreground">Company Phone Number</TableCell>
//                                                 <TableCell>{companyData.company_phone}</TableCell>
//                                             </TableRow>
//
//                                             {/*<TableRow className="bg-background">*/}
//                                             {/*    <TableHead className="font-semibold text-primary cursor-pointer"*/}
//                                             {/*               colSpan={2}>*/}
//                                             {/*        Company Contact Info*/}
//                                             {/*    </TableHead>*/}
//                                             {/*</TableRow>*/}
//
//                                             {/*<TableRow>*/}
//                                             {/*    <TableCell className="text-muted-foreground">Contact Name</TableCell>*/}
//                                             {/*    <TableCell>{companyData.contact_name}</TableCell>*/}
//                                             {/*</TableRow>*/}
//                                             {/*<TableRow>*/}
//                                             {/*    <TableCell className="text-muted-foreground">Contact Role</TableCell>*/}
//                                             {/*    <TableCell>{companyData.contact_role}</TableCell>*/}
//                                             {/*</TableRow>*/}
//                                             {/*<TableRow>*/}
//                                             {/*    <TableCell className="text-muted-foreground">Email Address</TableCell>*/}
//                                             {/*    <TableCell>{companyData.company_email}</TableCell>*/}
//                                             {/*</TableRow>*/}
//                                         </Table>
//                                     </TabsContent>
//                                     <TabsContent value="addresses" className="pt-6">
//                                         <Table>
//                                             <TableBody>
//                                                 <TableRow>
//                                                     <TableHead className="font-semibold text-primary cursor-pointer" colSpan={2}>
//                                                         Company Registered Address
//                                                     </TableHead>
//                                                 </TableRow>
//                                                 <TableRow>
//                                                     <TableCell className="text-muted-foreground">Street Address</TableCell>
//                                                     <TableCell>{companyData.registered_address?.street}</TableCell>
//                                                 </TableRow>
//                                                 {companyData.registered_address?.building_number && (
//                                                     <TableRow>
//                                                         <TableCell className="text-muted-foreground">Building Number</TableCell>
//                                                         <TableCell>{companyData.registered_address?.building_number}</TableCell>
//                                                     </TableRow>
//                                                 )}
//                                                 {companyData.registered_address?.apartment_number && (
//                                                     <TableRow>
//                                                         <TableCell className="text-muted-foreground">Apartment Number</TableCell>
//                                                         <TableCell>{companyData.registered_address?.apartment_number}</TableCell>
//                                                     </TableRow>
//                                                 )}
//                                                 <TableRow>
//                                                     <TableCell className="text-muted-foreground">City</TableCell>
//                                                     <TableCell>{companyData.registered_address?.city}</TableCell>
//                                                 </TableRow>
//                                                 {companyData.registered_address?.state && (
//                                                     <TableRow>
//                                                         <TableCell className="text-muted-foreground">State/Province</TableCell>
//                                                         <TableCell>{companyData.registered_address?.state}</TableCell>
//                                                     </TableRow>
//                                                 )}
//                                                 <TableRow>
//                                                     <TableCell className="text-muted-foreground">Postal Code</TableCell>
//                                                     <TableCell>{companyData.registered_address?.postal_code}</TableCell>
//                                                 </TableRow>
//                                                 <TableRow>
//                                                     <TableCell className="text-muted-foreground">Country</TableCell>
//                                                     <TableCell>{companyData.registered_address?.country}</TableCell>
//                                                 </TableRow>
//
//                                                 {companyData.operational_address && (
//                                                     <>
//                                                         <TableRow>
//                                                             <TableHead className="font-semibold text-primary cursor-pointer" colSpan={2}>
//                                                                 Company Operational Address
//                                                             </TableHead>
//                                                         </TableRow>
//                                                         <TableRow>
//                                                             <TableCell className="text-muted-foreground">Street Address</TableCell>
//                                                             <TableCell>{companyData.operational_address?.street}</TableCell>
//                                                         </TableRow>
//                                                         {companyData.operational_address?.building_number && (
//                                                             <TableRow>
//                                                                 <TableCell className="text-muted-foreground">Building Number</TableCell>
//                                                                 <TableCell>{companyData.operational_address?.building_number}</TableCell>
//                                                             </TableRow>
//                                                         )}
//                                                         {companyData.operational_address?.apartment_number && (
//                                                             <TableRow>
//                                                                 <TableCell className="text-muted-foreground">Apartment Number</TableCell>
//                                                                 <TableCell>{companyData.operational_address?.apartment_number}</TableCell>
//                                                             </TableRow>
//                                                         )}
//                                                         <TableRow>
//                                                             <TableCell className="text-muted-foreground">City</TableCell>
//                                                             <TableCell>{companyData.operational_address?.city}</TableCell>
//                                                         </TableRow>
//                                                         {companyData.operational_address?.state && (
//                                                             <TableRow>
//                                                                 <TableCell className="text-muted-foreground">State/Province</TableCell>
//                                                                 <TableCell>{companyData.operational_address?.state}</TableCell>
//                                                             </TableRow>
//                                                         )}
//                                                         <TableRow>
//                                                             <TableCell className="text-muted-foreground">Postal Code</TableCell>
//                                                             <TableCell>{companyData.operational_address?.postal_code}</TableCell>
//                                                         </TableRow>
//                                                         <TableRow>
//                                                             <TableCell className="text-muted-foreground">Country</TableCell>
//                                                             <TableCell>{companyData.operational_address?.country}</TableCell>
//                                                         </TableRow>
//                                                     </>
//                                                 )}
//                                             </TableBody>
//                                         </Table>
//                                     </TabsContent>
//
//                                     <TabsContent value="contacts" className="pt-6">
//                                         <Table>
//                                             <TableRow className="bg-background">
//                                                 <TableHead className="font-semibold text-primary cursor-pointer">
//                                                     Company Contact Info
//                                                 </TableHead>
//                                                 <TableCell className="text-right">
//
//                                                 </TableCell>
//                                             </TableRow>
//
//                                             <TableRow>
//                                                 <TableCell className="text-muted-foreground">Contact Name</TableCell>
//                                                 <TableCell>{companyData.contact_name}</TableCell>
//                                             </TableRow>
//                                             <TableRow>
//                                                 <TableCell className="text-muted-foreground">Contact Role</TableCell>
//                                                 <TableCell>{companyData.contact_role}</TableCell>
//                                             </TableRow>
//                                             <TableRow>
//                                                 <TableCell className="text-muted-foreground">Contact Email Address</TableCell>
//                                                 <TableCell>{companyData.company_email}</TableCell>
//                                             </TableRow>
//                                             <TableRow>
//                                                 <TableCell className="text-muted-foreground">Contact Phone Number</TableCell>
//                                                 <TableCell>
//                                                     <PhoneNumberDisplay phoneNumber={companyData.admin_phone} />
//                                                 </TableCell>
//                                             </TableRow>
//                                         </Table>
//                                     </TabsContent>
//                                     <TabsContent value="banking-details" className="pt-6">
//                                         <div className="space-y-6">
//                                             <div className="grid grid-cols-[300px,1fr] gap-6">
//                                                 <div className="space-y-1">
//                                                     <h3 className="font-medium">What is your business type?</h3>
//                                                 </div>
//                                                 <div className="space-y-3">
//                                                     <div>
//                                                         <p className="text-muted-foreground">Manufacturing</p>
//                                                         <p className="text-sm text-muted-foreground">10110 - Processing and preserving of meat</p>
//                                                     </div>
//                                                     <div>
//                                                         <p className="text-muted-foreground">Manufacturing</p>
//                                                         <p className="text-sm text-muted-foreground">
//                                                             10120 - Processing and preserving of poultry meat
//                                                         </p>
//                                                     </div>
//                                                     <div>
//                                                         <p className="text-muted-foreground">Manufacturing</p>
//                                                         <p className="text-sm text-muted-foreground">
//                                                             10130 - Production of meat and poultry meat products
//                                                         </p>
//                                                     </div>
//                                                 </div>
//                                             </div>
//
//                                             <div className="grid grid-cols-[300px,1fr] gap-6">
//                                                 <div className="space-y-1">
//                                                     <h3 className="font-medium">Please describe the products and/or services you offer:</h3>
//                                                 </div>
//                                                 <div>
//                                                     <p className="text-muted-foreground">
//                                                         Answer that was submitted goes here and can break unto multiple lines if it goes really far
//                                                         and the application typed out a lot.
//                                                     </p>
//                                                 </div>
//                                             </div>
//
//                                             <div className="grid grid-cols-[300px,1fr] gap-6">
//                                                 <div className="space-y-1">
//                                                     <h3 className="font-medium">What is your company website URL?</h3>
//                                                 </div>
//                                                 <div>
//                                                     <p className="text-muted-foreground">https://google.com/shopping/</p>
//                                                 </div>
//                                             </div>
//
//                                             <div className="grid grid-cols-[300px,1fr] gap-6">
//                                                 <div className="space-y-1">
//                                                     <h3 className="font-medium">Choose the source of funds for your company:</h3>
//                                                 </div>
//                                                 <div>
//                                                     <p className="text-muted-foreground">
//                                                         Business income, Shareholder funds, Loan, Deposits & Savings
//                                                     </p>
//                                                 </div>
//                                             </div>
//
//                                             <div className="grid grid-cols-[300px,1fr] gap-6">
//                                                 <div className="space-y-1">
//                                                     <h3 className="font-medium">Choose the outbound payment types that apply to your company:</h3>
//                                                 </div>
//                                                 <div>
//                                                     <p className="text-muted-foreground">Other: "Text that user inputted"</p>
//                                                 </div>
//                                             </div>
//
//                                             <div className="grid grid-cols-[300px,1fr] gap-6">
//                                                 <div className="space-y-1">
//                                                     <h3 className="font-medium">
//                                                         Choose your expected <span className="italic">inbound</span> monthly volume:
//                                                     </h3>
//                                                 </div>
//                                                 <div>
//                                                     <p className="text-muted-foreground">Less than 100,000.00 EUR</p>
//                                                 </div>
//                                             </div>
//
//                                             <div className="grid grid-cols-[300px,1fr] gap-6">
//                                                 <div className="space-y-1">
//                                                     <h3 className="font-medium">
//                                                         Choose your expected number of <span className="italic">inbound</span> monthly payments:
//                                                     </h3>
//                                                 </div>
//                                                 <div>
//                                                     <p className="text-muted-foreground">Less than 100</p>
//                                                 </div>
//                                             </div>
//
//                                             <div className="grid grid-cols-[300px,1fr] gap-6">
//                                                 <div className="space-y-1">
//                                                     <h3 className="font-medium">
//                                                         Choose your expected countries of <span className="italic">inbound</span> payments:
//                                                     </h3>
//                                                 </div>
//                                                 <div>
//                                                     <p className="text-muted-foreground">United States, Argentina, France</p>
//                                                 </div>
//                                             </div>
//
//                                             <div className="grid grid-cols-[300px,1fr] gap-6">
//                                                 <div className="space-y-1">
//                                                     <h3 className="font-medium">
//                                                         Choose your expected <span className="italic">outbound</span> monthly volume:
//                                                     </h3>
//                                                 </div>
//                                                 <div>
//                                                     <p className="text-muted-foreground">100,000 - 500,000 EUR</p>
//                                                 </div>
//                                             </div>
//                                         </div>
//                                     </TabsContent>
//                                     <TabsContent value="cards" className="pt-6">
//                                         <div className="space-y-6">
//                                             <div className="space-y-4">
//                                                 <Collapsible className="rounded-md bg-gray-50 px-4 py-3">
//                                                     <div className="flex items-center justify-between">
//                                                         <div className="flex gap-2">
//                                                             <span className="font-medium">Beneficial owner 1:</span>
//                                                             <span>John Smitherson</span>
//                                                         </div>
//                                                         <div className="flex items-center gap-4">
//                                                             <span className="text-muted-foreground">35% Ownership</span>
//                                                             <CollapsibleTrigger>
//                                                                 <ChevronDown className="h-4 w-4" />
//                                                             </CollapsibleTrigger>
//                                                         </div>
//                                                     </div>
//                                                     <CollapsibleContent className="pt-2">
//                                                         {/* Content for beneficial owner 1 */}
//                                                     </CollapsibleContent>
//                                                 </Collapsible>
//
//                                                 <Collapsible className="rounded-md bg-gray-50 px-4 py-3">
//                                                     <div className="flex items-center justify-between">
//                                                         <div className="flex gap-2">
//                                                             <span className="font-medium">Beneficial owner 2:</span>
//                                                             <span>Jane Doe</span>
//                                                         </div>
//                                                         <div className="flex items-center gap-4">
//                                                             <span className="text-muted-foreground">25% Ownership</span>
//                                                             <CollapsibleTrigger>
//                                                                 <ChevronDown className="h-4 w-4" />
//                                                             </CollapsibleTrigger>
//                                                         </div>
//                                                     </div>
//                                                     <CollapsibleContent className="pt-2">
//                                                         {/* Content for beneficial owner 2 */}
//                                                     </CollapsibleContent>
//                                                 </Collapsible>
//
//                                                 <Collapsible className="rounded-md bg-gray-50 px-4 py-3">
//                                                     <div className="flex items-center justify-between">
//                                                         <div className="flex gap-2">
//                                                             <span className="font-medium">Authorized person:</span>
//                                                             <span>Joseph Smith</span>
//                                                         </div>
//                                                         <CollapsibleTrigger>
//                                                             <ChevronDown className="h-4 w-4" />
//                                                         </CollapsibleTrigger>
//                                                     </div>
//                                                     <CollapsibleContent className="pt-2">
//                                                         {/* Content for authorized person */}
//                                                     </CollapsibleContent>
//                                                 </Collapsible>
//                                             </div>
//
//                                             <div className="grid gap-6">
//                                                 <div className="grid grid-cols-[200px,1fr] gap-4">
//                                                     <span className="font-medium">Primary contact:</span>
//                                                     <div className="space-y-1">
//                                                         <p>Sarah Smith</p>
//                                                         <p className="text-muted-foreground"><EMAIL></p>
//                                                         <p className="text-muted-foreground">+1 8889993232</p>
//                                                     </div>
//                                                 </div>
//
//                                                 <div className="grid grid-cols-[200px,1fr] gap-4">
//                                                     <span className="font-medium">Technical contact:</span>
//                                                     <div className="space-y-1">
//                                                         <p>Sarah Smith</p>
//                                                         <p className="text-muted-foreground"><EMAIL></p>
//                                                         <p className="text-muted-foreground">+1 8889993232</p>
//                                                     </div>
//                                                 </div>
//
//                                                 <div className="grid grid-cols-[200px,1fr] gap-4">
//                                                     <span className="font-medium">Financial/billing contact:</span>
//                                                     <div className="space-y-1">
//                                                         <p>Sarah Smith</p>
//                                                         <p className="text-muted-foreground"><EMAIL></p>
//                                                         <p className="text-muted-foreground">+1 8889993232</p>
//                                                     </div>
//                                                 </div>
//
//                                                 <div className="grid grid-cols-[200px,1fr] gap-4">
//                                                     <span className="font-medium">Customer service contact:</span>
//                                                     <div className="space-y-1">
//                                                         <p>Sarah Smith</p>
//                                                         <p className="text-muted-foreground"><EMAIL></p>
//                                                         <p className="text-muted-foreground">+1 8889993232</p>
//                                                     </div>
//                                                 </div>
//                                             </div>
//                                         </div>
//                                     </TabsContent>
//                                     <TabsContent value="technical" className="pt-6">
//                                         <div className="space-y-6">
//                                             <div>
//                                                 <h3 className="text-sm font-medium mb-4">APPLICATION DOCUMENTS</h3>
//                                                 <div className="space-y-4">
//                                                     <div className="grid grid-cols-[200px,1fr] gap-4 items-center">
//                                                         <span className="text-muted-foreground">Articles of Incorporation:</span>
//                                                         <div className="flex items-center gap-2 text-sm text-blue-500">
//                                                             <Paperclip className="h-4 w-4" />
//                                                             <span>[nameofdocument].[doctype]</span>
//                                                             <span className="text-muted-foreground">(2.1MB)</span>
//                                                         </div>
//                                                     </div>
//                                                     <div className="grid grid-cols-[200px,1fr] gap-4 items-center">
//                                                         <span className="text-muted-foreground">Company W-9:</span>
//                                                         <div className="flex items-center gap-2 text-sm text-blue-500">
//                                                             <Paperclip className="h-4 w-4" />
//                                                             <span>[nameofdocument].[doctype]</span>
//                                                             <span className="text-muted-foreground">(2.1MB)</span>
//                                                         </div>
//                                                     </div>
//                                                     <div className="grid grid-cols-[200px,1fr] gap-4 items-center">
//                                                         <span className="text-muted-foreground">EIN letter:</span>
//                                                         <div className="flex items-center gap-2 text-sm text-blue-500">
//                                                             <Paperclip className="h-4 w-4" />
//                                                             <span>[nameofdocument].[doctype]</span>
//                                                             <span className="text-muted-foreground">(2.1MB)</span>
//                                                         </div>
//                                                     </div>
//                                                     <div className="grid grid-cols-[200px,1fr] gap-4 items-center">
//                                                         <span className="text-muted-foreground">DBA filing:</span>
//                                                         <span className="text-muted-foreground italic">No document submitted by applicant</span>
//                                                     </div>
//                                                     <div className="grid grid-cols-[200px,1fr] gap-4">
//                                                         <span className="text-muted-foreground">Business license:</span>
//                                                         <div className="space-y-2">
//                                                             <div className="flex items-center gap-2 text-sm text-blue-500">
//                                                                 <Paperclip className="h-4 w-4" />
//                                                                 <span>[nameofdocument].[doctype]</span>
//                                                                 <span className="text-muted-foreground">(2.1MB)</span>
//                                                             </div>
//                                                             <p className="text-sm text-muted-foreground">Expires: 30 March 2024</p>
//                                                         </div>
//                                                     </div>
//                                                 </div>
//                                             </div>
//
//                                             <div>
//                                                 <h3 className="text-sm font-medium mb-4">ADDITIONAL DOCUMENTS</h3>
//                                                 <div className="space-y-4">
//                                                     <div className="grid grid-cols-[200px,1fr] gap-4 items-center">
//                                                         <span className="text-muted-foreground">[Document request name]:</span>
//                                                         <div className="flex items-center gap-2 text-sm text-blue-500">
//                                                             <Paperclip className="h-4 w-4" />
//                                                             <span>[nameofdocument].[doctype]</span>
//                                                             <span className="text-muted-foreground">(2.1MB)</span>
//                                                         </div>
//                                                     </div>
//                                                 </div>
//                                             </div>
//                                         </div>
//                                     </TabsContent>
//
//                                     <TabsContent value="products" className="pt-6">
//                                         <div className="flex items-center justify-between">
//                                             <div></div>
//                                             <div>
//                                                 <Link href={`/lite/admin/companies/${companyData._id}/cip/create`} passHref>
//                                                     <Button size="sm" className="mr-2">
//                                                         Add Product
//                                                     </Button>
//                                                 </Link>
//                                             </div>
//                                         </div>
//                                         <Table>
//                                             <TableHeader>
//                                                 <TableRow>
//                                                     <TableHead>Product Code</TableHead>
//                                                     <TableHead>Date Created</TableHead>
//                                                     <TableHead>Products</TableHead>
//                                                 </TableRow>
//                                             </TableHeader>
//                                             <TableBody>
//                                                 {cip.length > 0 ? (
//                                                     cip.map((program, index) => (
//                                                         <TableRow
//                                                             key={program._id}
//                                                             onClick={() => handleRowClick(program)}
//                                                             className="cursor-pointer hover:bg-muted"
//                                                         >
//                                                             <TableCell>
//                                                                 {program.productVersionName.map((v) => (
//                                                                     <li key={v}>{v.version_code}</li>
//                                                                 ))}
//                                                             </TableCell>
//                                                             <TableCell>{formatDate(program.createdAt)}</TableCell>
//                                                             <TableCell>
//                                                                 {program.productVersionName.map((v) => (
//                                                                     <li className="text-primary" key={v}>
//                                                                         {v.version_name}
//                                                                     </li>
//                                                                 ))}
//                                                             </TableCell>
//                                                             <TableCell>
//                                                                 <Button
//                                                                     variant="destructive"
//                                                                     size="sm"
//                                                                     onClick={(e) => {
//                                                                         e.stopPropagation() // Prevent row click
//                                                                         handleUnassignProduct(program._id)
//                                                                     }}
//                                                                 >
//                                                                     Unassign Product
//                                                                 </Button>
//                                                             </TableCell>
//                                                             <TableCell>
//                                                                 <Link
//                                                                     onClick={(e) => {
//                                                                         e.stopPropagation() // Prevent row click
//                                                                     }}
//                                                                     href={`/lite/admin/programmes/company/${params.id}/${program._id}/edit`}
//                                                                 >
//                                                                     <Button size="sm">Edit Product</Button>
//                                                                 </Link>
//                                                             </TableCell>
//                                                         </TableRow>
//                                                     ))
//                                                 ) : (
//                                                     <TableRow>
//                                                         <TableCell className="text-center text-muted-foreground" colSpan={3}>
//                                                             No products found for this company.
//                                                         </TableCell>
//                                                     </TableRow>
//                                                 )}
//                                             </TableBody>
//                                         </Table>
//                                     </TabsContent>
//
//                                     <TabsContent value="pmtypes" className="pt-6">
//                                         {cip.length > 0 &&
//                                             cip.map((program, index) => (
//                                                 <li key={program._id}>
//                                                     <small>{program.programManagerType?.manager_type || "N/A"}</small>
//                                                 </li>
//                                             ))}
//                                     </TabsContent>
//                                 </Tabs>
//                             </div>
//                         </CardContent>
//                     </Card>
//
//                     <Card>
//                         <CardHeader>
//                             <CardTitle>Activity</CardTitle>
//                         </CardHeader>
//                         <CardContent>
//                             <Tabs defaultValue="risk">
//                                 <TabsList className="w-full justify-start border-b rounded-none h-auto p-0 bg-transparent">
//                                     <TabsTrigger
//                                         value="risk"
//                                         className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
//                                     >
//                                         Risk Evaluation
//                                     </TabsTrigger>
//                                     <TabsTrigger
//                                         value="sumsub"
//                                         className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
//                                     >
//                                         SumSub
//                                     </TabsTrigger>
//                                     <TabsTrigger
//                                         value="notes"
//                                         className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
//                                     >
//                                         Internal Notes
//                                     </TabsTrigger>
//                                     <TabsTrigger
//                                         value="documents"
//                                         className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
//                                     >
//                                         Underwriting Documents
//                                     </TabsTrigger>
//                                     <TabsTrigger
//                                         value="logs"
//                                         className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
//                                     >
//                                         Activity Log
//                                     </TabsTrigger>
//                                 </TabsList>
//
//                                 <TabsContent value="risk" className="pt-6">
//                                     <div className="space-y-4">
//                                         <div className="grid grid-cols-[200px,1fr] gap-4 items-center">
//                                             <span className="text-muted-foreground">Risk Level:</span>
//                                             <span>225</span>
//                                         </div>
//                                         <div className="grid grid-cols-[200px,1fr] gap-4 items-center">
//                                             <span className="text-muted-foreground">Risk Status:</span>
//                                             <Badge variant="outline" className="w-fit bg-yellow-100 text-yellow-700 border-yellow-200">
//                                                 Medium
//                                             </Badge>
//                                         </div>
//                                     </div>
//                                 </TabsContent>
//                                 <TabsContent value="sumsub" className="pt-6">
//                                     <Card>
//                                         <CardContent className="p-6">
//                                             <div className="space-y-6">
//                                                 <div>
//                                                     <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
//                                                         <div className="w-4 h-4 rounded-full border" />
//                                                         Application review
//                                                     </h2>
//                                                     <div className="space-y-4">
//                                                         <div className="flex items-start gap-x-12">
//                                                             <div className="w-40 text-gray-600">Application ID:</div>
//                                                             <div className="flex items-center gap-2">
//                                                                 x
//                                                                 <Button variant="ghost" size="icon" className="h-6 w-6">
//                                                                     <Copy className="h-4 w-4" />
//                                                                 </Button>
//                                                             </div>
//                                                         </div>
//                                                         <div className="flex items-start gap-x-12">
//                                                             <div className="w-40 text-gray-600">Review status:</div>
//                                                             <div>
//                                                                 [
//                                                                 {statuses.map((status, i) => (
//                                                                     <span key={status}>
//                                     "{status}"{i < statuses.length - 1 ? " / " : ""}
//                                   </span>
//                                                                 ))}
//                                                                 ]
//                                                             </div>
//                                                         </div>
//                                                         <div className="flex items-start gap-x-12">
//                                                             <div className="w-40 text-gray-600">Review answer:</div>
//                                                             <div>--</div>
//                                                         </div>
//                                                     </div>
//                                                 </div>
//
//                                                 <div className="space-y-2">
//                                                     {reviewStatuses.map((status, index) => (
//                                                         <Collapsible key={index}>
//                                                             <CollapsibleTrigger className="flex w-full items-center justify-between p-4 hover:bg-gray-50 rounded-lg">
//                                                                 <div className="flex items-center gap-2">
//                                                                     {status.icon}
//                                                                     <span>
//                                     {status.label}
//                                                                         {status.actionRequired && (
//                                                                             <span className="text-gray-500 ml-2">(Action required)</span>
//                                                                         )}
//                                   </span>
//                                                                 </div>
//                                                                 <ChevronDown className="h-4 w-4" />
//                                                             </CollapsibleTrigger>
//                                                             <CollapsibleContent className="p-4">
//                                                                 <div className="text-gray-600">Content for {status.label}</div>
//                                                             </CollapsibleContent>
//                                                         </Collapsible>
//                                                     ))}
//                                                 </div>
//                                             </div>
//                                         </CardContent>
//                                     </Card>
//                                 </TabsContent>
//                                 <TabsContent value="notes" className="pt-6">
//                                     <Textarea placeholder="Type your Notes here." />
//                                 </TabsContent>
//
//                                 <TabsContent value="documents" className="pt-6">
//                                     <div className="w-full max-w-4xl mx-auto p-4">
//                                         <Card>
//                                             <CardContent className="p-6">
//                                                 <div className="space-y-4">
//                                                     {documents.map((doc, index) => (
//                                                         <div key={index} className="flex items-center justify-between group">
//                                                             <div className="flex items-center gap-2 text-teal-500">
//                                                                 <Paperclip className="h-5 w-5 text-gray-400" />
//                                                                 <span>
//                                   [{doc.name}].[{doc.type}]
//                                 </span>
//                                                                 <span className="text-gray-500">({doc.size})</span>
//                                                             </div>
//                                                             <Button
//                                                                 variant="ghost"
//                                                                 size="icon"
//                                                                 className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
//                                                             >
//                                                                 <X className="h-4 w-4" />
//                                                             </Button>
//                                                         </div>
//                                                     ))}
//
//                                                     <div className="pt-4">
//                                                         <Button
//                                                             variant="ghost"
//                                                             className="h-auto p-0 text-teal-500 hover:text-teal-600 hover:bg-transparent"
//                                                         >
//                                                             <Plus className="h-5 w-5 mr-2" />
//                                                             Upload documents
//                                                         </Button>
//                                                         <p className="text-sm text-gray-500 mt-2">Max file size: [4]MB (PNG, JPG, PDF)</p>
//                                                     </div>
//                                                 </div>
//                                             </CardContent>
//                                         </Card>
//                                     </div>
//                                 </TabsContent>
//                                 <TabsContent value="logs" className="pt-6">
//                                     <div className="space-y-4 max-h-[400px] overflow-y-auto pr-4">
//                                         {activities.map((activity, index) => (
//                                             <div key={index} className="flex items-start gap-3">
//                                                 <div className="w-2 h-2 rounded-full bg-primary mt-2.5" />
//                                                 <div className="space-y-1">
//                                                     <div className="font-medium">{activity.timestamp}</div>
//                                                     <div className="text-muted-foreground">{activity.description}</div>
//                                                 </div>
//                                             </div>
//                                         ))}
//                                     </div>
//                                 </TabsContent>
//                             </Tabs>
//                         </CardContent>
//                     </Card>
//                 </div>
//
//                 {/* Right Column */}
//                 <div className="space-y-6">
//                     <Card>
//                         <CardHeader>
//                             <CardTitle>Applications Details</CardTitle>
//                         </CardHeader>
//                         <CardContent>
//                             <div className="space-y-4">
//                                 <div className="grid grid-cols-[120px,1fr] gap-4 items-center">
//                                     <span className="text-muted-foreground">Application Status</span>
//                                     <Badge variant="outline" className="w-fit bg-green-100 text-green-700 border-green-200">
//                                         Approved
//                                     </Badge>
//                                 </div>
//                                 <div className="grid grid-cols-[120px,1fr] gap-4 items-center">
//                                     <span className="text-muted-foreground">Application ID</span>
//                                     <span>{companyData._id}</span>
//                                 </div>
//                                 <div className="grid grid-cols-[120px,1fr] gap-4 items-center">
//                                     <span className="text-muted-foreground">Origin</span>
//                                     <span>N/A</span>
//                                 </div>
//                                 <div className="grid grid-cols-[120px,1fr] gap-4 items-center">
//                                     <span className="text-muted-foreground">Received Date</span>
//                                     <span>N/A</span>
//                                 </div>
//                                 <div className="grid grid-cols-[120px,1fr] gap-4 items-center">
//                                     <span className="text-muted-foreground">Due Date</span>
//                                     <span>N/A</span>
//                                 </div>
//                                 <div className="grid grid-cols-[120px,1fr] gap-4 items-center">
//                                     <span className="text-muted-foreground">Approved Date</span>
//                                     <span>N/A</span>
//                                 </div>
//
//                                 <div className="grid grid-cols-[120px,1fr] gap-4 items-center">
//                                     <span className="text-muted-foreground">Application Approver</span>
//                                     <span>N/A</span>
//                                 </div>
//                                 <div className="grid grid-cols-[120px,1fr] gap-4 items-center">
//                                     <span className="text-muted-foreground">Assigned To</span>
//                                     <span>N/A</span>
//                                 </div>
//                             </div>
//                         </CardContent>
//                     </Card>
//
//                     <Card>
//                         <CardHeader>
//                             <CardTitle>Required Task Checklist</CardTitle>
//                         </CardHeader>
//                         <CardContent>
//                             <div className="grid grid-cols-2 gap-4">
//                                 <div>
//                                     <p className="text-sm text-muted-foreground mb-2">Sent:</p>
//                                     <p>N/A</p>
//                                 </div>
//                                 <div>
//                                     <p className="text-sm text-muted-foreground mb-2">Received:</p>
//                                     <p>N/A</p>
//                                 </div>
//                             </div>
//                         </CardContent>
//                     </Card>
//
//                     <Card>
//                         <CardHeader>
//                             <CardTitle>Additional events</CardTitle>
//                         </CardHeader>
//                         <CardContent className="space-y-4">
//                             <div className="border rounded-lg p-4">
//                                 <p className="mb-2">Name of requested event goes here</p>
//                                 <div className="flex items-center gap-2 text-sm text-blue-500">
//                                     <Paperclip className="h-4 w-4" />
//                                     <span>[nameofevent].[eventtype]</span>
//                                     <span className="text-muted-foreground">(2.1MB)</span>
//                                 </div>
//                             </div>
//                             <div className="border rounded-lg p-4">
//                                 <p className="mb-2">Name of requested event goes here</p>
//                                 <div className="flex items-center gap-2 text-sm text-blue-500">
//                                     <Paperclip className="h-4 w-4" />
//                                     <span>[nameofevent].[eventtype]</span>
//                                     <span className="text-muted-foreground">(2.1MB)</span>
//                                 </div>
//                             </div>
//                         </CardContent>
//                     </Card>
//                 </div>
//             </div>
//         </div>
//     )
// }
