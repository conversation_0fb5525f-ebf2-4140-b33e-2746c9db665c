//@ts-nocheck
"use client"
import { But<PERSON> } from "@/components/ui/button"
import type React from "react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { useEffect, useMemo, useState } from "react"
import axiosInstance from "@/utils/axiosInstance"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useRouter } from "next/navigation"
import { CustomCheckbox } from "@/components/CustomCheckbox"
import { Loader2 } from "lucide-react"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import Image from "next/image"
import { alertHelper } from "@/utils/alertHelper"

const asset = process.env.NEXT_PUBLIC_API_URL
interface FormData {
    binRangeId: string
    cardScheme: string
    programmeType: string
    binType: string
    company: string
    programManagerType: string
    productVersionName: string[]
    created_by: string | null
    binRange: string
    bin_start: string
    bin_end: string
    bin_prefix: string
    bin_suffix: string
}

const INITIAL_FORM_DATA: FormData = {
    binRangeId: "",
    cardScheme: "",
    programmeType: "",
    binType: "",
    programManagerType: "",
    company: "",
    binRange: "",
    bin_start: "",
    bin_end: "",
    bin_prefix: "",
    bin_suffix: "",
    productVersionName: [],
    created_by: null,
}

interface ProgrammeManagerType {
    _id: number
    manager_type: string
    bin_type: string
    programme_type: string
    status: string
    created_at: string
    created_by: string
}

const modifyText = (text: string) => {
    if (!text) return ""
    return text.replace(/[^a-zA-Z0-9]/g, "").toUpperCase()
}

interface CardImage {
    _id: string
    scheme_name: string
    company: string
    front_side: string
    back_side: string
    card_type: string
    card_category: string
    created_at: string
    created_by: string
}

interface BinType {
    _id: number
    type: string
    cardScheme: string
    binVariant: string
    binCategory: string
    programmeType: string
    reason: string
    created_at: string
    created_by: string
}

interface ProgrammeType {
    _id: number
    type: string
    status: string
    created_at: string
    created_by: string
}

interface BinRange {
    _id: number
    binType: string
    binCode: string
    binCodePrefix: string
    binCodeSuffix: string
    currency: string
    bin_start: string
    bin_end: string
    created_at: string
    created_by: any
    status: string
    category?: string // Keep for backward compatibility
}
export default function CardIssuingForm({ params }: { params: { id: string } }) {
    // State
    const [companyData, setCompanyData] = useState<any>(null)
    const [loading, setLoading] = useState(true)
    const [cardImages, setCardImages] = useState([])
    const [showSuccessDialog, setShowSuccessDialog] = useState(false)
    const [cardSchemes, setCardSchemes] = useState([])
    const [binTypes, setBinTypes] = useState([])
    const [cardProgrammeTypes, setCardProgrammeTypes] = useState([])
    const [productVersionNames, setProductVersionNames] = useState([])
    const [productVersions, setProductVersions] = useState([])
    const [countries, setCountries] = useState([])
    const router = useRouter()
    const [filteredBinTypes, setFilteredBinTypes] = useState([])
    const [filteredPMTypes, setFilteredPMTypes] = useState([])
    const [filteredProgrammeTypes, setFilteredProgrammeTypes] = useState([])
    const [filteredCurrency, setFilteredCurrency] = useState([])
    const [cip, setCip] = useState<null | any>(null)
    const [selectedCip, setSelectedCip] = useState<null | any>(null)
    const [formData, setFormData] = useState<FormData>({
        ...INITIAL_FORM_DATA,
        company: params.id,
        created_by: localStorage.getItem("user"),
    })
    const [savedManagerTypes, setSavedManagerTypes] = useState<ProgrammeManagerType[]>([])

    const [savedProgrammeTypes, setSavedProgrammeTypes] = useState<ProgrammeType[]>([])
    const [savedbinTypes, setSavedbinTypes] = useState<BinType[]>([])
    const [savedBinRanges, setSavedBinRanges] = useState<BinRange[]>([])
    const [existingCipVersions, setExistingCipVersions] = useState<string[]>([])
    const [isLoading, setIsLoading] = useState(false)
    const [isSubmitting, setIsSubmitting] = useState(false)

    // Event Handlers
    const handleFormChange = (field: keyof FormData, value: any) => {
        setFormData((prev) => {
            let newState = { ...prev }
            if (field === "productVersionName") {
                newState.productVersionName = prev.productVersionName.includes(value)
                    ? prev.productVersionName.filter((v) => v !== value)
                    : [...prev.productVersionName, value]
            } else if (["cardScheme", "binType"].includes(field)) {
                newState = {
                    ...newState,
                    [field]: value,
                    productVersionName: [],
                }
                if (field === "cardScheme") {
                    newState.binType = ""
                } else if (field === "binType") {
                }
            } else {
                newState[field] = value
            }
            return newState
        })

        if (field === "cardScheme") {
            try {
                const selectedScheme = cardSchemes.find((scheme) => scheme._id === value)
                if (selectedScheme) {
                    const filtered = savedbinTypes.filter((binType) =>
                        binType.type.toLowerCase().startsWith(selectedScheme.scheme_name.toLowerCase()),
                    )
                    setFilteredBinTypes(filtered)
                }
            } catch (error) {
                console.error("Error fetching bin types for scheme:", error)
                setFilteredBinTypes([])
            }
        } else if (field === "programManagerType") {
            setFormData((prev) => {
                return { ...prev, programManagerType: filteredPMTypes[0]?._id }
            })
        } else if (field === "programmeType") {
            try {
                const selectedProgrammeType = savedProgrammeTypes.find((type) => type._id === value)
                if (selectedProgrammeType) {
                    const filtered = filteredBinTypes.filter((binType) =>
                        binType.type.toLowerCase().includes(selectedProgrammeType.type.toLowerCase()),
                    )
                    setFilteredBinTypes(filtered)
                }
            } catch (error) {
                console.error("Error fetching bin types for scheme:", error)
                setFilteredBinTypes([])
            }
        } else if (field === "binType") {
            try {
                const selectedBinType = savedbinTypes.find((r) => r._id === value)
                const binRange = savedBinRanges.find((r) => r.binType._id === value)
                if (selectedBinType) {
                    const filtered = savedManagerTypes.filter((m) => {
                        return m.bin_type === selectedBinType._id
                    })

                    console.dir(filtered)
                    handleFormChange("binRangeId", binRange?._id)
                    handleFormChange("binRange", binRange?.binCode)
                    handleFormChange("bin_suffix", binRange?.binCodeSuffix)
                    handleFormChange("bin_prefix", binRange?.binCodePrefix)
                    handleFormChange("bin_start", binRange?.bin_start)
                    handleFormChange("bin_end", binRange?.bin_end)
                    setFilteredPMTypes(filtered)
                    if (filtered.length === 1) {
                        handleFormChange("programManagerType")
                    }

                    const list_version = productVersions.filter(
                        (r) =>
                            r.version_name.includes(selectedBinType?.type) &&
                            r.version_name.includes(binRange?.currency.currency_code),
                    )
                    setProductVersionNames(list_version)
                }
            } catch (error) {
                console.error("Error fetching bin types for scheme:", error)
                setFilteredPMTypes([])
            }
        }
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsSubmitting(true)
        try {
            const response = await axiosInstance.post("cip/save-data", formData)
            if (response.status === 201) {
                setShowSuccessDialog(true)
            }
        } catch (error) {
            console.error("Failed to save data:", error)
            alertHelper.showToast("Failed to save data. Please try again.", "error")
        } finally {
            setIsSubmitting(false)
        }
    }

    // Effects
    useEffect(() => {
        const fetchCompanyDetails = async () => {
            try {
                setLoading(true)
                const response = await axiosInstance.get(`/company/${params.id}`)
                const images = await axiosInstance.get<CardImage[]>("/images")

                setCompanyData(response.data.company)
                setCip(response.data.cip)

                // Find the selected program
                const selectedProgram = response.data.cip.find((cip: any) => cip._id === params.programme)
                setSelectedCip(selectedProgram)
                console.log("Selected CIP data:", selectedProgram)

                if (selectedProgram) {
                    console.log("Selected program found:", selectedProgram)

                    // Set form data with values from selectedCip
                    setFormData({
                        ...INITIAL_FORM_DATA,
                        company: params.id,
                        created_by: localStorage.getItem("user"),
                        cardScheme: selectedProgram.cardScheme?._id || "",
                        programmeType: selectedProgram.programmeType?._id || "",
                        binType: selectedProgram.binType?._id || "",
                        programManagerType: selectedProgram.programManagerType?._id || "",
                        binRangeId: selectedProgram.binRangeId?._id || "",
                        binRange: selectedProgram.binRange || "",
                        bin_prefix: selectedProgram.binRangeId?.binCodePrefix || "",
                        bin_suffix: selectedProgram.binRangeId?.binCodeSuffix || "",
                        bin_start: selectedProgram.binRangeId?.bin_start || "",
                        bin_end: selectedProgram.binRangeId?.bin_end || "",
                        productVersionName: Array.isArray(selectedProgram.productVersionName)
                            ? selectedProgram.productVersionName.map((version) => version._id)
                            : selectedProgram.productVersionName?._id
                                ? [selectedProgram.productVersionName._id]
                                : [],
                    })

                    // Process card images if available
                    if (response.data.company._id) {
                        const final_cards = images.data.filter((image) => image.company._id === response.data.company._id)
                        if (final_cards.length > 0) {
                            setCardImages(final_cards)
                        }
                    }

                    // Extract existing CIP version names
                    const existingVersions = response.data.cip.flatMap(
                        (cip: any) => cip.productDetails?.map((product: any) => product.productVersionName) || [],
                    )
                    setExistingCipVersions(existingVersions)
                }
            } catch (error: any) {
                console.error("Error fetching company data:", error)
                console.error(error.response?.data.message || "Failed to fetch company data")
            } finally {
                setLoading(false)
            }
        }

        fetchCompanyDetails()
    }, [params.id, params.programme])

    // Add a second useEffect to handle filtering after data is loaded
    useEffect(() => {
        if (selectedCip && savedbinTypes.length > 0 && savedManagerTypes.length > 0 && productVersions.length > 0) {
            console.log("Running filtering effect with selectedCip:", selectedCip)

            // Filter bin types for the selected card scheme
            if (selectedCip.cardScheme?._id) {
                const selectedScheme = cardSchemes.find((scheme) => scheme._id === selectedCip.cardScheme._id)
                if (selectedScheme) {
                    const filtered = savedbinTypes.filter((binType) =>
                        binType.type.toLowerCase().startsWith(selectedScheme.scheme_name.toLowerCase()),
                    )
                    setFilteredBinTypes(filtered)
                    console.log("Filtered bin types:", filtered)
                }
            }

            // Filter program manager types for the selected bin type
            if (selectedCip.binType?._id) {
                const filtered = savedManagerTypes.filter((m) => m.bin_type === selectedCip.binType._id)
                setFilteredPMTypes(filtered)
                console.log("Filtered PM types:", filtered)
            }

            // Update product version names based on selected bin type and currency
            if (selectedCip.binType && selectedCip.binRangeId?.currency) {
                const currencyCode =
                    typeof selectedCip.binRangeId.currency === "object"
                        ? selectedCip.binRangeId.currency.currency_code
                        : selectedCip.binRangeId.currency

                // Get all product versions that match the bin type and currency
                let list_version = productVersions.filter(
                    (r) => r.version_name.includes(selectedCip.binType.type) && r.version_name.includes(currencyCode),
                )

                // If no versions found, try to get all product versions
                if (list_version.length === 0) {
                    console.log("No product versions found with strict filtering, trying broader search")
                    list_version = productVersions
                }

                setProductVersionNames(list_version)
                console.log("Product versions set:", list_version)
            } else if (selectedCip.productVersionName && productVersions.length > 0) {
                // If we have product version names in the selectedCip but no bin type/currency info,
                // try to find the product versions directly
                console.log("Setting product versions directly from selectedCip")

                // Get the IDs of the product versions in the selectedCip
                const versionIds = Array.isArray(selectedCip.productVersionName)
                    ? selectedCip.productVersionName.map((v) => v._id)
                    : [selectedCip.productVersionName?._id].filter(Boolean)

                // Find the corresponding product versions
                const versions = productVersions.filter((v) => versionIds.includes(v._id))

                // If we found any versions, use them
                if (versions.length > 0) {
                    setProductVersionNames(versions)
                    console.log("Product versions set directly:", versions)
                } else {
                    // Otherwise, just use all product versions
                    setProductVersionNames(productVersions)
                    console.log("Using all product versions as fallback")
                }
            }
        }
    }, [selectedCip, savedbinTypes, savedManagerTypes, productVersions, cardSchemes])

    useEffect(() => {
        // This effect runs when we have a selectedCip and product versions data
        // but no productVersionNames yet
        if (
            selectedCip &&
            productVersions.length > 0 &&
            productVersionNames.length === 0 &&
            formData.productVersionName.length > 0
        ) {
            console.log("Loading product versions for editing")

            // Get all product versions that match the selected IDs
            const selectedVersions = productVersions.filter((version) => formData.productVersionName.includes(version._id))

            if (selectedVersions.length > 0) {
                // If we found matching versions, use them
                setProductVersionNames(selectedVersions)
                console.log("Found selected product versions:", selectedVersions)
            } else {
                // Otherwise, show all product versions
                setProductVersionNames(productVersions)
                console.log("Showing all product versions as fallback")
            }
        }
    }, [selectedCip, productVersions, productVersionNames.length, formData.productVersionName])
    useEffect(() => {
        const fetchDropdownData = async () => {
            try {
                const [schemes, versions, manager, pts, bts, brs] = await Promise.all([
                    axiosInstance.get("cardScheme/schemes"),

                    axiosInstance.get("product-versions"),

                    axiosInstance.get("/programme-manager-types"),
                    axiosInstance.get("/programme-type"),
                    axiosInstance.get("/bin-types"),
                    axiosInstance.get("/bin-range"),
                ])
                setCardSchemes(schemes.data)
                setProductVersions(versions.data)
                setSavedManagerTypes(manager.data)
                setSavedProgrammeTypes(pts.data)
                setSavedbinTypes(bts.data.data)
                setSavedBinRanges(brs.data)
            } catch (error) {
                console.error("Failed to fetch dropdown data:", error)
            }
        }

        fetchDropdownData()
    }, [])

    useEffect(() => {
        if (formData.binType && formData.cardProgrammeType) {
            const bin = binTypes.find((c) => c._id === formData.binType)
            const currency = bin?.currency.currency_code
            const list_version = productVersionNames.filter(
                (r) => r.version_name.includes(bin?.type) && r.version_name.includes(currency),
            )
            if (JSON.stringify(list_version) !== JSON.stringify(productVersionNames)) {
                setProductVersionNames(list_version)
            }
        }
    }, [formData.binType, formData.cardProgrammeType, binTypes, productVersionNames])

    const isFormValid = useMemo(() => {
        return formData.cardScheme && formData.binType && formData.productVersionName.length > 0
    }, [formData.cardScheme, formData.binType, formData.productVersionName])

    if (loading) {
        return <LoadingOverlay />
    }

    return (
        <Card className="w-full max-w-3xl">
            <CardHeader className="px-6 py-5 flex-row items-center justify-between">
                <div>
                    <CardTitle className="text-2xl font-bold">Create Card Issuing Programme</CardTitle>
                    <CardDescription>
                        Create Card Issuing Programme for <strong>{companyData?.company_name}</strong>.
                    </CardDescription>
                </div>
            </CardHeader>
            <Separator />

            <CardContent className="space-y-4 p-3">
                <form className="space-y-8" onSubmit={handleSubmit}>
                    <Card>
                        <CardHeader>
                            <CardTitle>CIP Details</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="cardScheme">Card Scheme</Label>
                                <Select value={formData.cardScheme} onValueChange={(value) => handleFormChange("cardScheme", value)}>
                                    <SelectTrigger id="cardScheme">
                                        <SelectValue placeholder="Select Card Scheme" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {cardSchemes
                                            .filter((t) => t.status.toLowerCase() === "active")
                                            .map((scheme) => (
                                                <SelectItem key={scheme._id} value={scheme._id}>
                                                    {scheme.scheme_name}
                                                </SelectItem>
                                            ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="programmeType">Programme Type</Label>
                                <Select
                                    value={formData.programmeType}
                                    onValueChange={(value) => handleFormChange("programmeType", value)}
                                    disabled={!formData.cardScheme}
                                >
                                    <SelectTrigger id="programmeType">
                                        <SelectValue
                                            placeholder={formData.cardScheme ? "Select Programme Type" : "Please select a Card Scheme first"}
                                        />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {savedProgrammeTypes
                                            .filter((t) => t.status.toLowerCase() === "active")
                                            .map((type) => (
                                                <SelectItem key={type._id} value={type._id}>
                                                    {type.type}
                                                </SelectItem>
                                            ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="binType">Bin Type</Label>
                                <Select
                                    value={formData.binType}
                                    disabled={!formData.programmeType}
                                    onValueChange={(value) => handleFormChange("binType", value)}
                                >
                                    <SelectTrigger id="binType">
                                        <SelectValue
                                            placeholder={formData.programmeType ? "Select Bin Type" : "Please select a Programme Type first"}
                                        />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {filteredBinTypes.map((type) => (
                                            <SelectItem key={type._id} value={type._id}>
                                                {type.type}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="programManagerType">Programme Manager Type</Label>
                                <Select
                                    value={formData.programManagerType}
                                    disabled={!formData.binType}
                                    onValueChange={(value) => handleFormChange("programManagerType", value)}
                                >
                                    <SelectTrigger id="programManagerType">
                                        <SelectValue
                                            placeholder={
                                                formData.binType ? "Select Programme Manager Type" : "Please select a BIN Type first"
                                            }
                                        />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {filteredPMTypes.map((type, index) => (
                                            <SelectItem key={type._id} value={type._id}>
                                                {type.manager_type}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="grid grid-cols-3 gap-3  ">
                                <div className=" ">
                                    <Label htmlFor="bin">BIN Prefix</Label>
                                    <Input
                                        id="bin"
                                        placeholder="Generated BIN"
                                        value={formData.bin_prefix}
                                        readOnly
                                        className="bg-muted"
                                    />
                                </div>
                                <div className=" ">
                                    <Label htmlFor="bin">BIN Suffix</Label>
                                    <Input
                                        id="bin"
                                        placeholder="Generated BIN"
                                        value={formData.bin_suffix}
                                        readOnly
                                        className="bg-muted"
                                    />
                                </div>
                                <div className="">
                                    <Label htmlFor="bin">BIN Range</Label>
                                    <Input id="bin" placeholder="Generated BIN" value={formData.binRange} readOnly className="bg-muted" />
                                </div>

                                <div className=" ">
                                    <Label htmlFor="bin">BIN Range Start</Label>
                                    <Input
                                        id="bin"
                                        placeholder="Generated BIN"
                                        value={formData.bin_start}
                                        readOnly
                                        className="bg-muted"
                                    />
                                </div>

                                <div className=" ">
                                    <Label htmlFor="bin">BIN Range End</Label>
                                    <Input id="bin" placeholder="Generated BIN" value={formData.bin_end} readOnly className="bg-muted" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Product Versions</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-2 gap-4">
                                {productVersionNames.length > 0 ? (
                                    productVersionNames.map((version, index) => (
                                        <div key={index} className="flex flex-col gap-2">
                                            <CustomCheckbox
                                                key={index}
                                                id={`version-${version._id}`}
                                                label={version.version_name}
                                                checked={formData.productVersionName.includes(version._id)}
                                                onChange={() => handleFormChange("productVersionName", version._id)}
                                            />

                                            <div className="flex justify-center w-full gap-2">
                                                {cardImages.find((img) => img.product_version._id === version._id)?.front_side && (
                                                    <Image
                                                        unoptimized
                                                        src={`${asset}${cardImages.find((img) => img.product_version._id === version._id)?.front_side}`}
                                                        alt={`Front`}
                                                        width={100}
                                                        height={60}
                                                        loading="lazy"
                                                    />
                                                )}

                                                {cardImages.find((img) => img.product_version._id === version._id)?.back_side && (
                                                    <Image
                                                        unoptimized
                                                        src={`${asset}${cardImages.find((img) => img.product_version._id === version._id)?.back_side}`}
                                                        alt={`back`}
                                                        width={100}
                                                        height={60}
                                                        loading="lazy"
                                                    />
                                                )}
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p>No product versions available for the selected options.</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    <div className="flex justify-end">
                        <Button type="submit" disabled={!isFormValid || isSubmitting}>
                            {isSubmitting ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Submitting...
                                </>
                            ) : (
                                "Submit"
                            )}
                        </Button>
                    </div>
                </form>
            </CardContent>
            <AlertDialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Success</AlertDialogTitle>
                        <AlertDialogDescription>Card Issuing Program created successfully!</AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogAction onClick={() => router.back()}>Go Back</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </Card>
    )
}
