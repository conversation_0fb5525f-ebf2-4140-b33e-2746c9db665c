import type React from "react"
import { useState } from "react"
import Image from "next/image"
import ImagePopup from "./ImagePopup"

interface ProductImagesProps {
    program: {
        productDetails: Array<{
            productVersionName: {
                _id: string
            }
        }>
    }
    cardImages: Array<{
        product_version: {
            _id: string
        }
        front_side?: string
        back_side?: string
    }>
    asset: string
}

const ProductImages: React.FC<ProductImagesProps> = ({ program, cardImages, asset }) => {
    const [popupImage, setPopupImage] = useState<string | null>(null)

    const handleImageClick = (imageSrc: string) => {
        setPopupImage(imageSrc)
    }

    const handleClosePopup = () => {
        setPopupImage(null)
    }

    return (
        <>
            {program.productDetails.map((v, index) => (
                <div key={index} className="flex gap-3 mx-3">
                    {cardImages.find((i) => i.product_version._id === v.productVersionName._id)?.front_side !== undefined && (
                        <Image
                            unoptimized
                            src={`${asset + cardImages.find((i) => i.product_version._id === v.productVersionName._id)?.front_side}`}
                            alt="Front"
                            width={150}
                            height={100}
                            loading="lazy"
                            className="cursor-pointer"
                            onClick={() =>
                                handleImageClick(
                                    `${asset + cardImages.find((i) => i.product_version._id === v.productVersionName._id)?.front_side}`,
                                )
                            }
                        />
                    )}
                    {cardImages.find((i) => i.product_version._id === v.productVersionName._id)?.back_side !== undefined && (
                        <Image
                            unoptimized
                            src={`${asset + cardImages.find((i) => i.product_version._id === v.productVersionName._id)?.back_side}`}
                            alt="Back"
                            width={150}
                            height={100}
                            loading="lazy"
                            className="cursor-pointer"
                            onClick={() =>
                                handleImageClick(
                                    `${asset + cardImages.find((i) => i.product_version._id === v.productVersionName._id)?.back_side}`,
                                )
                            }
                        />
                    )}
                </div>
            ))}
            <ImagePopup isOpen={!!popupImage} onClose={handleClosePopup} imageSrc={popupImage || ""} />
        </>
    )
}

export default ProductImages

