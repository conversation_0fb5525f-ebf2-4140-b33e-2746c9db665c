// 'use client'
//
// import { useState, useEffect } from 'react'
// import { CardContent } from "@/components/ui/card"
// import { Label } from "@/components/ui/label"
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
// import CardTypeSelector from "@/app/lite/admin/companies/[id]/cip/create-x-x0-x/cardType";
//
// // You'll need to import or define these types and components
//
//
// interface ProductConfigFormProps {
//     initialCardOption: string
//     initialProductCcy: string
//     initialProductVersionName: string
//     countries: Country[]
//     productVersionNames: ProductVersionName[]
//     onCardOptionChange: (value: string) => void
//     onProductCcyChange: (value: string) => void
//     onProductVersionNameChange: (value: string) => void
// }
//
// export default function ProductConfigForm({
//                                               initialCardOption = '',
//                                               initialProductCcy = '',
//                                               initialProductVersionName = '',
//                                               countries = [],
//                                               productVersionNames = [],
//                                               onCardOptionChange,
//                                               onProductCcyChange,
//                                               onProductVersionNameChange
//                                           }: ProductConfigFormProps) {
//     const [cardOption, setCardOption] = useState(initialCardOption)
//     const [productCcy, setProductCcy] = useState(initialProductCcy)
//     const [productVersionName, setProductVersionName] = useState(initialProductVersionName)
//
//     useEffect(() => {
//         setCardOption(initialCardOption)
//         setProductCcy(initialProductCcy)
//         setProductVersionName(initialProductVersionName)
//     }, [initialCardOption, initialProductCcy, initialProductVersionName])
//
//     const handleCardOptionChange = (value: string) => {
//         setCardOption(value)
//         onCardOptionChange(value)
//     }
//
//     const handleProductCcyChange = (value: string) => {
//         setProductCcy(value)
//         onProductCcyChange(value)
//     }
//
//     const handleProductVersionNameChange = (value: string) => {
//         setProductVersionName(value)
//         onProductVersionNameChange(value)
//     }
//
//     return (
//         <CardContent className="space-y-4">
//             <CardTypeSelector
//                 onSelectionChange={handleCardOptionChange}
//                 initialSelection={cardOption}
//             />
//             <div className="space-y-2">
//                 <Label htmlFor="productCcy">Product CCY</Label>
//                 <Select onValueChange={handleProductCcyChange} value={productCcy}>
//                     <SelectTrigger>
//                         <SelectValue placeholder="Select Product CCY" />
//                     </SelectTrigger>
//                     <SelectContent>
//                         {countries
//                             .filter(ccy => ccy.is_active)
//                             .map((ccy) => (
//                                 <SelectItem key={ccy._id} value={ccy._id}>
//                                     {ccy.currency_code}
//                                 </SelectItem>
//                             ))}
//                     </SelectContent>
//                 </Select>
//             </div>
//             <div className="space-y-2">
//                 <Label htmlFor="productVersionName">Product Version Name</Label>
//                 <Select onValueChange={handleProductVersionNameChange} value={productVersionName}>
//                     <SelectTrigger>
//                         <SelectValue placeholder="Select Product Version Name" />
//                     </SelectTrigger>
//                     <SelectContent>
//                         {productVersionNames.map((name, index) => (
//                             <SelectItem key={name._id} value={name._id}>
//                                 {name.version_name}
//                             </SelectItem>
//                         ))}
//                     </SelectContent>
//                 </Select>
//             </div>
//         </CardContent>
//     )
// }