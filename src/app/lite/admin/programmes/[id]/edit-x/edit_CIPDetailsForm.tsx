//@ts-nocheck

import { <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";

interface CIPDetailsFormProps {
    cardSchemes: Array<{ _id: string, scheme_name: string }>;
    cardTypes: Array<{ _id: string, type: string, code: string }>;
    cardProgrammeTypes: Array<{ _id: string, programme_type: string, code: string }>;
    initialValues: {
        cardScheme?: string;
        cardProgrammeType?: string;
        binType?: string;
        programManagerType?: string;
        binRange?: string;
    };
    onFormDataChange: (field: string, value: string) => void;
}
const localProgramManagerTypes = [
    { name: "WhiteLabel Program Manager (Business)", code: "WLPB" },
    { name: "WhiteLabel Program Manager (Consumer)", code: "WLPC" },
    { name: "CoBrand Program Manager (Business)", code: "CBPB" },
    { name: "CoBrand Program Manager (Consumer)", code: "CBPC" },
    { name: "CoBrand Program Manager (Ryvyl Prepaid Consumer)", code: "CBPP" },
    { name: "Standard Program Manager (Ryvyl Business)", code: "STPB" },
    { name: "Standard Program Manager (Ryvyl Consumer)", code: "STPC" },
    { name: "Standard Program Manager (Ryvyl Prepaid Consumer)", code: "STPP" }
];

const programManagerLookup = {
    "WLBL_VWCx": "WLPC", "WLBL_VWBx": "WLPB", "CBND_VCPP": "CBPP",
    "CBND_VBDB": "CBPB", "CBND_VCDB": "CBPC", "STND_VCDB": "STPC",
    "STND_VBDB": "STPB", "STND_VCPP": "STPP", "STND_VCP2": "STPP",
    "STND_VCP6": "STPP"
};

const binRangeLookup = {
    "VCDB": "4466 1415 xxxx xxxx", "VBDB": "4466 1416 xxxx xxxx",
    "VCPP": "4771 01xx xxxx xxxx", "VCP2": "4771 0120 xxxx xxxx",
    "VCP6": "4771 0160 xxxx xxxx", "VWCx": "4xxx xxxx xxxx xxxx",
    "VWBx": "4xxx xxxx xxxx xxxx"
};

export default function CIPDetailsForm({
                                           cardSchemes,
                                           cardTypes,
                                           cardProgrammeTypes,
                                           initialValues,
                                           onFormDataChange
                                       }: CIPDetailsFormProps) {
    const [selectedProgramType, setSelectedProgramType] = useState(initialValues.cardProgrammeType || '');
    const [binTypesList, setBinTypesList] = useState(cardTypes);
    const [programManagerType, setProgramManagerType] = useState(initialValues.programManagerType || '');
    const [binRange, setBinRange] = useState(initialValues.binRange || '');

    useEffect(() => {
        if (initialValues.cardProgrammeType) {
            updateBinTypes(initialValues.cardProgrammeType);
        }
    }, [initialValues.cardProgrammeType]);

    const updateBinTypes = (cpType: string) => {
        const cardProgrammeCode = cardProgrammeTypes.find(c => c._id === cpType)?.code;
        setSelectedProgramType(cardProgrammeCode || '');
        onFormDataChange("cardProgrammeType", cpType);

        const filterCondition = {
            "WLBL": (ct: any) => ct.code.toLowerCase().includes("vw"),
            "CBND": (ct: any) => ["vcdb", "vbdb", "vcpp"].some(prefix => ct.code.toLowerCase().includes(prefix)),
            "STND": (ct: any) => ["vc", "vb"].some(prefix => ct.code.toLowerCase().includes(prefix))
        }[cardProgrammeCode] || (() => false);

        setBinTypesList(cardTypes.filter(filterCondition));
    };

    const handleBinTypeChange = (value: string) => {
        const selectedBinType = binTypesList.find(c => c._id === value)?.code;
        onFormDataChange("binType", value);

        const managerCodeKey = `${selectedProgramType}_${selectedBinType?.slice(0, 4)}`;
        const calculatedProgramManager = programManagerLookup[managerCodeKey] || "Not Found";
        const calculatedBinRange = binRangeLookup[selectedBinType?.slice(0, 4) || ""] || "N/A";
const manager = localProgramManagerTypes.find((type)=>type.code == calculatedProgramManager)?.name;


        setProgramManagerType(manager!);
        setBinRange(calculatedBinRange);

        onFormDataChange("programManagerType", manager!);
        onFormDataChange("binRange", calculatedBinRange);
    };

    return (
        <>
            <CardHeader>
                <CardTitle>CIP Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <FormField
                    label="Card Scheme"
                    id="cardScheme"
                    options={cardSchemes}
                    initialValue={initialValues.cardScheme}
                    onChange={(value) => onFormDataChange("cardScheme", value)}
                />

                <FormField
                    label="Card Programme Type"
                    id="cardProgrammeType"
                    options={cardProgrammeTypes}
                    initialValue={initialValues.cardProgrammeType}
                    onChange={(value) => updateBinTypes(value)}
                />

                <FormField
                    label="BIN Type"
                    id="binType"
                    options={binTypesList}
                    initialValue={initialValues.binType}
                    onChange={(value) => handleBinTypeChange(value)}
                />

                <ReadonlyField label="Programme Manager Type" value={programManagerType} />
                <ReadonlyField label="BIN" value={binRange} />
            </CardContent>
        </>
    );
}

const FormField = ({ label, id, options, initialValue, onChange }) => (
    <div className="space-y-2">
        <Label htmlFor={id}>{label}</Label>
        <Select onValueChange={onChange} defaultValue={initialValue}>
            <SelectTrigger id={id}>
                <SelectValue placeholder={`Select ${label}`} />
            </SelectTrigger>
            <SelectContent>
                {options.map(option => (
                    <SelectItem key={option._id} value={option._id}>
                        {option.scheme_name || option.programme_type || option.type}
                    </SelectItem>
                ))}
            </SelectContent>
        </Select>
    </div>
);

const ReadonlyField = ({ label, value }) => (
    <div className="space-y-2">
        <Label htmlFor={label}>{label}</Label>
        <Input value={value} placeholder={`Generated ${label}`} readOnly />
    </div>
);
