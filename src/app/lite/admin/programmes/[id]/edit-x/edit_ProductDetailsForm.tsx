import { useEffect } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import CardTypeSelector from './edit_cardType'

interface ProductCcy {
    _id: string
    code: string
    currency_name: string
    is_active: boolean
}

interface ProductVersionName {
    _id: string
    version_name: string
}

interface ProductDetailsFormProps {
    cardOption: string
    productCcys: ProductCcy[]
    productVersionNames: ProductVersionName[]
    selectedCcy: string
    onCardOptionChange: (selection: string) => void
    onProductCcyChange: (value: string) => void
    onFormDataChange: (field: string, value: string) => void
}

export default function Edit_ProductDetailsForm({
                                               cardOption,
                                               productCcys,
                                               productVersionNames,
                                               selectedCcy,
                                               onCardOptionChange,
                                               onProductCcyChange,
                                               onFormDataChange
                                           }: ProductDetailsFormProps) {
    useEffect(() => {
        // Auto-select USD if it exists and no currency is selected
        if (!selectedCcy && productCcys.length > 0) {
            const usdCurrency = productCcys.find(ccy => ccy.code === 'USD')
            if (usdCurrency) {
                onProductCcyChange(JSON.stringify({id: usdCurrency._id, code: usdCurrency.code}))
            }
        }
    }, [productCcys, selectedCcy, onProductCcyChange])

    return (
        <>
            <CardHeader>
                <CardTitle>Product Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <CardTypeSelector
                    onSelectionChange={onCardOptionChange}
                    initialSelection={cardOption}
                />
                <div className="space-y-2">
                    <Label htmlFor="productCcy">Product CCY</Label>
                    <Select
                        onValueChange={(value) => onProductCcyChange(value)}
                        value={selectedCcy ? JSON.stringify({id: productCcys.find(ccy => ccy.code === selectedCcy)?._id, code: selectedCcy}) : undefined}
                    >
                        <SelectTrigger id="productCcy">
                            <SelectValue placeholder="Select Product CCY"/>
                        </SelectTrigger>
                        <SelectContent>
                            {productCcys
                                .filter(ccy => ccy.is_active)
                                .map((ccy) => (
                                    <SelectItem
                                        key={ccy._id}
                                        value={JSON.stringify({id: ccy._id, code: ccy.code})}
                                    >
                                        {ccy.currency_name} ({ccy.code})
                                    </SelectItem>
                                ))}
                        </SelectContent>
                    </Select>
                </div>
                <div className="space-y-2">
                    <Label htmlFor="productVersionName">Product Version Name</Label>
                    <Select onValueChange={(value) => onFormDataChange('productVersionName', value)}>
                        <SelectTrigger id="productVersionName">
                            <SelectValue placeholder="Select Product Version Name"/>
                        </SelectTrigger>
                        <SelectContent>
                            {productVersionNames
                                .filter((name) =>
                                    selectedCcy.toLowerCase() === "eur"
                                        ? name.version_name.toLowerCase().includes("eur")
                                        : true
                                )
                                .map((name) => (
                                    <SelectItem key={name._id} value={name._id}>
                                        {name.version_name}
                                    </SelectItem>
                                ))
                            }
                        </SelectContent>
                    </Select>
                </div>
            </CardContent>
        </>
    )
}