'use client'

import { useState, useEffect } from 'react'
import { ScrollArea } from "@/components/ui/scroll-area"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"

interface Industry {
    name: string
    range: string
}

interface MCCSectorSelectorProps {
    industries: Industry[]
    onSelectionChange: (selection: string) => void
    initialSelection?: string
}

export default function MCCSectorSelector({
                                              industries,
                                              onSelectionChange,
                                              initialSelection = ''
                                          }: MCCSectorSelectorProps) {
    const [selectedSectors, setSelectedSectors] = useState<string[]>(initialSelection.split('-').filter(Boolean))

    useEffect(() => {
        onSelectionChange(selectedSectors.join('-'))
    }, [selectedSectors, onSelectionChange])

    const handleSectorChange = (sectorName: string, isChecked: boolean) => {
        setSelectedSectors(prev =>
            isChecked
                ? [...prev, sectorName]
                : prev.filter(sector => sector !== sectorName)
        )
    }

    return (
        <div className="space-y-2">
            <Label>Select MCC Sector</Label>
            <ScrollArea className="h-[300px] border rounded-md p-4">
                {industries.map((industry, index) => (
                    <div key={index} className="flex items-start space-x-2 py-2">
                        <Checkbox
                            id={`mcc-sector-${index}`}
                            checked={selectedSectors.includes(industry.name)}
                            onCheckedChange={(checked) => handleSectorChange(industry.name, checked as boolean)}
                        />
                        <Label
                            htmlFor={`mcc-sector-${index}`}
                            className="text-sm font-normal leading-tight"
                        >
                            {industry.name} {industry.range}
                        </Label>
                    </div>
                ))}
            </ScrollArea>
        </div>
    )
}