'use client'

import { useState, useEffect } from 'react'
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"

interface CardTypeSelectorProps {
    onSelectionChange: (selection: string) => void
    initialSelection?: string | string[]
}

export default function CardTypeSelector({ onSelectionChange, initialSelection = '' }: CardTypeSelectorProps) {
    const [selection, setSelection] = useState<string[]>([])

    useEffect(() => {
        // Handle different types of initialSelection
        if (typeof initialSelection === 'string') {
            setSelection(initialSelection.split(',').filter(Boolean))
        } else if (Array.isArray(initialSelection)) {
            setSelection(initialSelection)
        }
    }, [initialSelection])

    const handleSelectionChange = (type: 'virtual' | 'physical') => {
        const newSelection = selection.includes(type)
            ? selection.filter(t => t !== type)
            : [...selection, type]

        setSelection(newSelection)
        onSelectionChange(newSelection.join(','))
    }

    return (
        <div className="space-y-4">
            <Label className="text-base font-semibold">Card Type</Label>
            <div className="space-y-2">
                <div className="flex items-center space-x-2">
                    <Checkbox
                        id="virtual"
                        checked={selection.includes('virtual')}
                        onCheckedChange={() => handleSelectionChange('virtual')}
                    />
                    <Label htmlFor="virtual" className="text-sm font-normal">
                        Virtual Card
                    </Label>
                </div>
                <div className="flex items-center space-x-2">
                    <Checkbox
                        id="physical"
                        checked={selection.includes('physical')}
                        onCheckedChange={() => handleSelectionChange('physical')}
                    />
                    <Label htmlFor="physical" className="text-sm font-normal">
                        Physical Card
                    </Label>
                </div>
            </div>
        </div>
    )
}