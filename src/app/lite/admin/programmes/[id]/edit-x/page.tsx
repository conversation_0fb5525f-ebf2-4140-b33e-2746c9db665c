//@ts-nocheck
'use client'

import {useEffect, useState} from 'react'
import {motion, AnimatePresence} from 'framer-motion'
import {Button} from "@/components/ui/button"
import {Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle} from "@/components/ui/card"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Separator} from "@/components/ui/separator"
import {<PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip"
import {Check, ChevronRight, CreditCard, Phone, FileText, User, Package, ChevronsUpDown, Search} from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue
} from "@/components/ui/select"
import {Checkbox} from "@/components/ui/checkbox";
import {Scroll<PERSON><PERSON>} from "@/components/ui/scroll-area";
import {router, useRouter} from "next/navigation";
import CardTypeSelector from "@/app/lite/admin/programmes/[id]/edit-x/edit_cardType";
import CIPDetailsForm from "@/app/lite/admin/programmes/[id]/edit-x/edit_CIPDetailsForm";
import MCCSectorSelector from "@/app/lite/admin/programmes/[id]/edit-x/edit_mcc_selector";


const industries = [
    {name: "Various services and miscellaneous (e.g., contractors, services)", range: "0001 - 0999"},
    {name: "Agriculture, forestry, fishing", range: "1000 - 1999"},
    {name: "Construction, manufacturing", range: "2000 - 2999"},
    {name: "Transportation services", range: "3000 - 3999"},
    {name: "Communications and utilities", range: "4000 - 4999"},
    {name: "Retail stores (e.g., department stores, specialty shops)", range: "5000 - 5999"},
    {name: "Financial services (e.g., banks, credit unions)", range: "6000 - 6999"},
    {name: "Entertainment and recreation (e.g., hotels, theaters)", range: "7000 - 7999"},
    {name: "Professional services (e.g., healthcare, legal services)", range: "8000 - 8999"},
    {name: "Government services", range: "9000 - 9999"}
];

const steps = [
    {id: 1, title: "Programmes", icon: CreditCard},
    {id: 2, title: "Products", icon: Package},
    {id: 3, title: "Customers", icon: User},
]
const applicationStatuses = [
    {key: "prospect_new_online", label: "New Online Prospect"},
    {key: "prospect_new_sales", label: "New Sales Prospect"},
    {key: "prospect_new_referral", label: "New Referral Prospect"},
    {key: "prospect_new_mobile_app_referral", label: "New Mobile App Referral Prospect"},

    {key: "application_online_received", label: "Online Prospect - Application Received"},
    {key: "application_sales_received", label: "Sales Prospect - Application Received"},
    {key: "application_referral_received", label: "Referral Party - Application Received"},
    {key: "application_mobile_app_received", label: "MApp Referral - Application Received"},

    {key: "application_online_review", label: "Online Prospect - Review Application"},
    {key: "application_sales_review", label: "Sales Prospect - Review Application"},
    {key: "application_referral_review", label: "Referral Party - Review Application"},
    {key: "application_mobile_app_review", label: "MApp Referral - Review Application"},

    {key: "application_online_further_details", label: "Online Prospect - Request Further Details"},
    {key: "application_sales_further_details", label: "Sales Prospect - Request Further Details"},
    {key: "application_referral_further_details", label: "Referral Party - Request Further Details"},

    {key: "quote_online_prepare", label: "Online Prospect - Prepare for Quote"},
    {key: "quote_sales_prepare", label: "Sales Prospect - Prepare for Quote"},
    {key: "quote_referral_prepare", label: "Referral Party - Prepare for Quote"},

    {key: "quote_online_generated", label: "Online - CIP Quote Generated"},
    {key: "quote_sales_generated", label: "Sales - CIP Quote Generated"},
    {key: "quote_referral_generated", label: "Referral - CIP Quote Generated"},

    {key: "quote_online_agreed", label: "Online - Quote Agreed"},
    {key: "quote_sales_agreed", label: "Sales - Quote Agreed"},
    {key: "quote_referral_agreed", label: "Referral - Quote Agreed"},

    {key: "quote_online_negotiation", label: "Online - Quote Negotiation"},
    {key: "quote_sales_negotiation", label: "Sales - Quote Negotiation"},
    {key: "quote_referral_negotiation", label: "Referral - Quote Negotiation"},

    {key: "quote_online_expired", label: "Online - Quote Expired"},
    {key: "quote_sales_expired", label: "Sales - Quote Expired"},
    {key: "quote_referral_expired", label: "Referral - Quote Expired"},

    {key: "quote_online_declined", label: "Online - Quote Declined"},
    {key: "quote_sales_declined", label: "Sales - Quote Declined"},
    {key: "quote_referral_declined", label: "Referral - Quote Declined"},

    {key: "onboarding_online_kyb_link_sent", label: "Online - Onboarding KYB Link Sent"},
    {key: "onboarding_sales_kyb_link_sent", label: "Sales - Onboarding KYB Link Sent"},
    {key: "onboarding_referral_kyb_link_sent", label: "Referral - Onboarding KYB Link Sent"},

    {key: "onboarding_online_kyb_tbc", label: "Online - Onboarding Prospect KYB TBC"},
    {key: "onboarding_sales_kyb_tbc", label: "Sales - Onboarding Prospect KYB TBC"},
    {key: "onboarding_referral_kyb_tbc", label: "Referral - Onboarding Prospect KYB TBC"},

    {key: "onboarding_online_kyb_error", label: "Online - Onboarding Prospect KYB Error"},
    {key: "onboarding_sales_kyb_error", label: "Sales - Onboarding Prospect KYB Error"},
    {key: "onboarding_referral_kyb_error", label: "Referral - Onboarding Prospect KYB Error"},

    {key: "onboarding_online_kyb_expired", label: "Online - Onboarding Prospect KYB Expired"},
    {key: "onboarding_sales_kyb_expired", label: "Sales - Onboarding Prospect KYB Expired"},
    {key: "onboarding_referral_kyb_expired", label: "Referral - Onboarding Prospect KYB Expired"},

    {key: "onboarding_online_kyb_completed", label: "Online - Onboarding Prospect KYB Completed"},
    {key: "onboarding_sales_kyb_completed", label: "Sales - Onboarding Prospect KYB Completed"},
    {key: "onboarding_referral_kyb_completed", label: "Referral - Onboarding Prospect KYB Completed"},

    {key: "legals_online_drafted", label: "Legals - Online Prospect Drafted"},
    {key: "legals_sales_drafted", label: "Legals - Sales Prospect Drafted"},
    {key: "legals_referral_drafted", label: "Legals - Referral Prospect Drafted"},

    {key: "legals_online_internal_review", label: "Legals - Online Prospect Internal Review"},
    {key: "legals_sales_internal_review", label: "Legals - Sales Prospect Internal Review"},
    {key: "legals_referral_internal_review", label: "Legals - Referral Prospect Internal Review"},

    {key: "legals_online_sent", label: "Legals - Online Prospect Sent"},
    {key: "legals_sales_sent", label: "Legals - Sales Prospect Sent"},
    {key: "legals_referral_sent", label: "Legals - Referral Prospect Sent"},

    {key: "legals_online_requests_changes", label: "Legals - Online Prospect Requests Changes"},
    {key: "legals_sales_requests_changes", label: "Legals - Sales Prospect Requests Changes"},
    {key: "legals_referral_requests_changes", label: "Legals - Referral Prospect Requests Changes"},

    {key: "legals_online_ryvyl_amend", label: "Legals - Online Prospect Ryvyl Amend"},
    {key: "legals_sales_ryvyl_amend", label: "Legals - Sales Prospect Ryvyl Amend"},
    {key: "legals_referral_ryvyl_amend", label: "Legals - Referral Prospect Ryvyl Amend"},
    {key: "legals_not_agreed_end", label: "Legals - Not Agreed: End Onboarding"},
    {key: "legals_partially_completed_signed_by_client", label: "Legals - Partially Completed: Signed by Client"},
    {key: "legals_completed_signed_by_ryvyl", label: "Legals - Completed: Signed by Ryvyl"},

    {key: "ips_define_client_programme", label: "IPS - Ryvyl Define Client Programme Details"},
    {key: "ips_request_iban", label: "IPS - Request IBAN"},
    {key: "ips_generate_iban", label: "IPS - Generate IBAN"},
    {key: "ips_send_iban_details", label: "IPS - Send IBAN Details"},
    {key: "ips_banking_activated", label: "IPS - Banking Activated"},

    {key: "uam_define_client_roles", label: "UAM - Ryvyl Define Client Roles"},
    {key: "uam_define_client_permissions", label: "UAM - Ryvyl Define Client Permissions"},
    {key: "uam_login_details_created", label: "UAM - Login Details Created"},
    {key: "uam_client_login_details_sent", label: "UAM - Client Login Details Sent (Email)"},
    {key: "uam_client_activates_login", label: "UAM - Client Activates Login"},

    {key: "client_status_active", label: "Client Status - Active"},
    {key: "client_status_inactive", label: "Client Status - Inactive"},
    {key: "client_status_restrict_activity", label: "Client Status - Restrict Client Activity"},
    {key: "client_status_suspend_activity", label: "Client Status - Suspend Client Activity"},
    {key: "client_status_compliance_monitor", label: "Client Status - Compliance Monitor Client"},
    {key: "client_status_compliance_hold", label: "Client Status - Compliance Hold"},
    {key: "client_status_enhanced_due_diligence", label: "Client Status - Compliance Enhanced Due Diligence"},
    {key: "client_status_notice_to_close", label: "Client Status - Client Notice to Close"},
    {key: "client_status_compliance_closed", label: "Client Status - Compliance Closed"},
    {key: "client_status_request_offboarding", label: "Client Status - Compliance Request Client Offboarding"},
    {key: "client_status_offboarded", label: "Client Status - Compliance Offboarded"},

    {key: "prog_mgr_card_config", label: "ProgMgr - Card Programme Configuration"},
    {key: "prog_mgr_funding_account_created", label: "ProgMgr - Funding Account Created"},
    {key: "prog_mgr_card_products_setup", label: "ProgMgr - Card Products Setup"},
    {key: "prog_mgr_super_admin_dashboard", label: "ProgMgr - Super Admin Dashboard Configured"},
    {key: "prog_mgr_admin_dashboard", label: "ProgMgr - Administrator Dashboard Configured"},
    {key: "prog_mgr_client_dashboard", label: "ProgMgr - Client Dashboard Configured"},
    {key: "prog_mgr_b2b_cardholder_dashboard", label: "ProgMgr - B2B Cardholder Dashboard Configured"}
];

const localCardTypes = [];
const regions = [];
const programManagerTypes = [];
const localCustomerTypes = [];
export default function editCip({params}: { params: { id: string } }) {
    const [step, setStep] = useState(1)
    const router = useRouter();
    const [loading, setLoading] = useState(true);
    const [companyData, setCompanyData] = useState<null | any>(null);
    // State to store dropdown options
    const [cardSchemes, setCardSchemes] = useState<string[]>([])
    const [binRanges, setBinRanges] = useState<string[]>([])
    const [cardProgrammeTypes, setCardProgrammeTypes] = useState<string[]>([])
    const [customerTypes, setCustomerTypes] = useState<string[]>([])
    const [issuingClientTypes, setIssuingClientTypes] = useState<string[]>([])
    const [cardTypes, setCardTypes] = useState<string[]>([])
    const [cardOption, setCardOption] = useState<string[]>([])
    const [productCcys, setProductCcys] = useState<string[]>([])
    const [productVersionNames, setProductVersionNames] = useState<string[]>([])
    const [countries, setCountries] = useState<string[]>([])
    const [filteredProductVersionNames, setFilteredProductVersionNames] = useState<string[]>([])
    const [open, setOpen] = useState(false)
    const [selectedCcy, setSelectedCcy] = useState<string>('')
    const [selectedCardTypes, setSelectedCardTypes] = useState<string[]>([])
    const [cip, setCip] = useState<null | any>(null)
    const [error, setError] = useState<string>("")
    const [formData, setFormData] = useState({
        cardScheme: '',
        binRange: '',
        cardType: '',
        cardProgrammeType: '',
        customerType: '',
        issuingClientType: '',
        binType: '',
        productCcy: '',
        productVersionName: '',
        customerRoute: '',
        operationalFunction: '',
        applicationStatus: '',
        programManagerType: '',
        isCorproateRegulated: '',
        regulatorRegion: '',
        mccSector: '',


    })
    const [selectedMCCSectors, setSelectedMCCSectors] = useState('')



    const updateFormData = (field: string, value: string) => {
        try {
            setFormData(prev => {
                const updatedData = {...prev, [field]: value};
                return updatedData;
            });
            if (field === "customerRoute") {
                getCustomerTypes(value);
            }
        if (field === "isCorproateRegulated") {
            getRegulatedRegion(value);

        }




        } catch (e) {
            console.log("error", e);
        }
    };

    const getVersion=(field: string, value: string, cardType: string,countryList,versions)=>{

        if (field === "productCcy") {



            // updateFormData("cardType", cardOption)
            const currency = countryList.find((c) => c._id === value)?.currency_code;

            // Filter productVersionNames to include items that match the currency
            const matchingProductVersions_currency = versions.filter((v) =>
                v.version_name.toLowerCase().includes(currency.toLowerCase())
            );




            if (cardType !== "") {
                const typesArray = cardType

// Map each type to its corresponding code
                const typeCodes = typesArray.map(type => {
                    if (type.toLowerCase() === "virtual") return "VTL";
                    if (type.toLowerCase() === "physical") return "PHY";
                    // Add more conditions if you have other types
                    return ""; // Or handle unmatched types
                }).filter(code => code); // Remove empty values if there are unmatched types


                // Filter `matchingProductVersions_currency` based on all values in `typesArray`
                const matchingProductVersions_card = matchingProductVersions_currency.filter((v) =>
                    typeCodes.some(type => v.version_name.toLowerCase().includes(type.toLowerCase()))
                );


                setFilteredProductVersionNames(matchingProductVersions_card);


            }


        }
    }

    const getRegulatedRegion = (value: string) => {
        // Check if a value is provided
        if (value !== "") {
            regions.length = 0; // This resets the array
            if (value.trim().toLowerCase() === "Not Regulated".toLowerCase()) {
                regions.push(
                    "Unregulated (EEA)",
                    "Unregulated (Other)"
                );
            } else if (value.trim().toLowerCase() === "Regulated Entity".toLowerCase()) {
                regions.push(
                    "Regulated (EEA)",
                    "Regulated (Africa)",
                    "Regulated (Americas)",
                    "Regulated (Asia)",
                    "Regulated (Middle East)",
                    "Regulated (UK)",
                    "Regulated (Other)"
                );
            }
        }
    }

    const gerFilteredProgramManager = (value: string) => {
        // Check if a value is provided
        if (value !== "") {
            // Find the program type based on the provided value
            const programType = cardProgrammeTypes.find((c) => c._id === value)?.code;

            // Clear existing items in programManagerTypes
            programManagerTypes.length = 0; // This resets the array

            // Push relevant options based on the program type
            if (programType === "WLBL") {
                programManagerTypes.push(
                    'WhiteLabel Program Manager (Business)',
                    'WhiteLabel Program Manager (Consumer)'
                );
            } else if (programType === "CBND") {
                programManagerTypes.push(
                    'CoBrand Program Manager (Business)',
                    'CoBrand Program Manager (Consumer)',
                    'CoBrand Program Manager (Ryvyl Prepaid Consumer)'
                );
            } else if (programType === "STND") {
                programManagerTypes.push(
                    'Standard Program Manager (Ryvyl Business)',
                    'Standard Program Manager (Ryvyl Consumer)',
                    'Standard Program Manager (Ryvyl Prepaid Consumer)'
                );
            }
        }
    };
    const getCustomerTypes = (value: string) => {

        // Check if a value is provided
        if (value !== "") {

            localCustomerTypes.length = 0; // This resets the array
            if (value.trim().toLowerCase() === "new") {
                localCustomerTypes.push(
                    "Corporate Client New",
                    "Individual New"
                );
            } else if (value.trim().toLowerCase() === "existing") {

                localCustomerTypes.push(
                    "Corporate Prospect Acquiring",
                    "Corporate Prospect Banking",
                    "Corporate Prospect Issuing",
                    "Corporate Client Acquiring (Existing)",
                    "Corporate Client Banking (Existing)",
                    "Individual Banking (Existing)"
                );
            }
        }
    };


    const updateCardType = (typeId: string, checked: boolean) => {

        if (checked) {
            setSelectedCardTypes((prev) => [...prev, typeId]);
            console.dir(selectedCardTypes)
        } else {
            setSelectedCardTypes((prev) => prev.filter((id) => id !== typeId));
            console.dir(selectedCardTypes)
        }
        // console.dir(selectedCardTypes.join(","))
    };

    const handleCardSchemeChange = (value: string) => {

        updateFormData('cardScheme', value);


    };

    const handleProductCcy = (value: string) => {

        updateFormData('productCcy', value);


    };
    // Function to handle form submission
    const handleSubmit = async () => {
        setLoading(true);

        try {
            updateFormData("cardTypes", cardOption)
            const response = await axiosInstance.patch('cip/' + params.id, formData) // API endpoint for saving data
            if (response.status === 201) {
                alert('Card Issuing program Updated!')
                router.push("/lite/admin/programmes")
            }
        } catch (error) {
            console.error('Failed to save data:', error)
            alert('Failed to save data. Please try again.')
        } finally {
            setLoading(false);
        }
    }


    // Fetch the dropdown data from backend
    useEffect(() => {
        const fetchDropdownData = async () => {
            try {
                const [
                    schemes,
                    binRangesData,
                    programmes,
                    customers,
                    clients,
                    cards,
                    ccys,
                    versionNames,
                    countries
                ] = await Promise.all([
                    axiosInstance.get('cardScheme/schemes'),
                    axiosInstance.get('cardBin/bins'),
                    axiosInstance.get('cardProgram/programme-types'),
                    axiosInstance.get('customer-types'),
                    axiosInstance.get('issuing-client-types'),
                    axiosInstance.get('card-types'),
                    axiosInstance.get('product-currencies'),
                    axiosInstance.get('product-versions'),
                    axiosInstance.get('countries')
                ])

                // @ts-ignore
                setCardSchemes(schemes.data)
                // @ts-ignore
                setBinRanges(binRangesData.data)
                // @ts-ignore
                setCardProgrammeTypes(programmes.data)
                // @ts-ignore
                setCustomerTypes(customers.data)
                // @ts-ignore
                setIssuingClientTypes(clients.data)
                // @ts-ignore
                setCardTypes(cards.data)
                // @ts-ignore
                setProductCcys(ccys.data)
                // @ts-ignore
                setProductVersionNames(versionNames.data)

                setCountries(countries.data)


                    await   fetchCompanyDetails(countries.data,versionNames.data);



            } catch (error) {
                console.error('Failed to fetch dropdown data:', error)
            }
        }

        fetchDropdownData()




    }, [params.id]);


    async function fetchCompanyDetails(countryList,versions) {
        try {
            const response = await axiosInstance.get(`/cip/` + params.id); // Correct the path
            // @ts-ignore
            setCompanyData(response.data.data.company);
            // setCip(response.data.data);
            const cipDetails = response.data.data;
            const cardType = cipDetails.cardType.split(",").map(type => type.trim());
            setCardOption(cardType)
            updateFormData("cardScheme", cipDetails.cardScheme._id);
            updateFormData("binRange", cipDetails.binRange);
            updateFormData("cardType", cardType.join(","));
            updateFormData("cardProgrammeType", cipDetails.cardProgrammeType._id);
            updateFormData("issuingClientType", cipDetails.issuingClientType._id);
            updateFormData("binType", cipDetails.binType._id);
            updateFormData("productCcy", cipDetails.productCcy._id);
            updateFormData("productVersionName", cipDetails.productVersionName._id);
            updateFormData("customerType", cipDetails.customerType);
            updateFormData("customerRoute", cipDetails.customerRoute);
            updateFormData("operationalFunction", cipDetails.operationalFunction);
            updateFormData("applicationStatus", cipDetails.applicationStatus);
            updateFormData("programManagerType", cipDetails.programManagerType);
            updateFormData("regulatorRegion", cipDetails.regulatorRegion);
            updateFormData("mccSector", cipDetails.mccSector);
            updateFormData("isCorproateRegulated", cipDetails.isCorproateRegulated);

            getVersion("productCcy", cipDetails.productCcy._id,cardType,countryList,versions)
            // setCardOption(cipDetails.cardType)


        } catch (error) {
            // @ts-ignore
            setError(error.response?.data.message || 'Failed to fetch company data');
        } finally {
            setLoading(false);
        }
    }
    const handleNext = () => setStep(prev => Math.min(prev + 1, 3))
    const handlePrev = () => setStep(prev => Math.max(prev - 1, 1))

    const isStepComplete = (stepNumber: number) => {
        switch (stepNumber) {
            case 1:
                return formData.cardScheme !== '' && formData.cardProgrammeType !== '' && formData.binType !== '' && formData.programManagerType !== '' && formData.binRange !== ''
            case 2:
                return formData.productCcy !== '' && formData.productVersionName !== ''
            case 3:
                return formData.customerType !== '' && formData.issuingClientType !== ''

            default:
                return false
        }
    }

    const getFilteredCardTypes = (v) => {
        if (v !== '') {


            // Find the selected bin by its ID
            const selectedBinObj = binRanges.find((b) => b._id === v);

            if (selectedBinObj && selectedBinObj.bin) {
                const {bin: selectedBin} = selectedBinObj;
                localCardTypes.length = 0;
                if (selectedBin.includes('1415')) {
                    localCardTypes.push(cardTypes.find(type => type.type.toLowerCase().includes('consumer debit')))
                } else if (selectedBin.includes('1416')) {
                    localCardTypes.push(cardTypes.find(type => type.type.toLowerCase().includes('business debit')))
                } else if (selectedBin.includes('01')) {
                    localCardTypes.push(cardTypes.find(type => type.type.toLowerCase().includes('consumer prepaid (general purpose)')))
                }

            } else {
                console.dir("Selected bin not found or invalid.");
            }
        } else {
            console.dir("bin empty");
        }
    };

    const isFormDataFilled = Object.values(formData).every(value => value !== '');

    const renderStep = () => {
        switch (step) {
            case 1:
                // @ts-ignore
                return (
                    <motion.div
                        initial={{opacity: 0, x: 20}}
                        animate={{opacity: 1, x: 0}}
                        exit={{opacity: 0, x: -20}}
                    >
                        {isFormDataFilled ? (
                            <CIPDetailsForm
                                cardSchemes={cardSchemes}
                                cardTypes={cardTypes}
                                cardProgrammeTypes={cardProgrammeTypes}
                                initialValues={{
                                    cardScheme: formData.cardScheme, // previously selected scheme ID
                                    cardProgrammeType: formData.cardProgrammeType,
                                    binType: formData.binType,
                                    programManagerType: formData.programManagerType,
                                    binRange: formData.binRange,
                                }}
                                onCardSchemeChange={handleCardSchemeChange}
                                onFormDataChange={updateFormData}
                            />
                        ) : (
                            <p>Loading form data...</p> // Optional loading message
                        )}
                    </motion.div>
                )
            case 3:
                return (
                    <motion.div
                        initial={{opacity: 0, x: 20}}
                        animate={{opacity: 1, x: 0}}
                        exit={{opacity: 0, x: -20}}
                    >
                        <CardHeader>
                            <CardTitle>Customer Details</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">

                            <div className="space-y-2">
                                <Label htmlFor="issuingClientType">Issuing Function</Label>
                                <Select onValueChange={(value) => updateFormData('issuingClientType', value)} defaultValue={formData.issuingClientType}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select Issuing Client Type"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        {issuingClientTypes.map((type, index) => (
                                            // @ts-ignore
                                            <SelectItem key={index} value={type._id}>

                                                {/*@ts-ignore*/}
                                                {type.type}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>


                            <div className="space-y-2">
                                <Label htmlFor="customerRoute">Customer Route</Label>
                                <Select onValueChange={(value) => updateFormData('customerRoute', value)} value={formData.customerRoute}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select Customer Route"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem key={`Existing`} value="Existing">Existing</SelectItem>
                                        <SelectItem key={`new`} value="New">New</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="customerType">Customer Type</Label>
                                <Select onValueChange={(value) => updateFormData('customerType', value)} value={formData.customerType}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select Customer Type"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        {localCustomerTypes.map((type, index) => (
                                            // @ts-ignore
                                            <SelectItem key={index} value={type}>
                                                {/*@ts-ignore*/}
                                                {type}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>


                            <div className="space-y-2">
                                <Label htmlFor="OperationalFunction">Operational Function</Label>
                                <Select onValueChange={(value) => updateFormData('operationalFunction', value)} defaultValue={formData.operationalFunction}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select Operational Function"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="online-enquiry-prospect">Online Enquiry |
                                            Prospect</SelectItem>
                                        <SelectItem value="sales-enquiry-prospect">Sales Enquiry | Prospect</SelectItem>
                                        <SelectItem value="referral-enquiry-prospect">Referral Enquiry |
                                            Prospect</SelectItem>
                                        <SelectItem value="new-application-online-prospect">New Application - Online
                                            Prospect</SelectItem>
                                        <SelectItem value="new-application-sales-prospect">New Application - Sales
                                            Prospect</SelectItem>
                                        <SelectItem value="new-application-referral-prospect">New Application - Referral
                                            Prospect</SelectItem>
                                        <SelectItem value="cip-quote-online-prospect">CIP Quote - Online
                                            Prospect</SelectItem>
                                        <SelectItem value="cip-quote-sales-prospect">CIP Quote - Sales
                                            Prospect</SelectItem>
                                        <SelectItem value="cip-quote-referral-prospect">CIP Quote - Referral
                                            Prospect</SelectItem>
                                        <SelectItem value="kyb-onboarding-online-prospect">KYB Onboarding - Online
                                            Prospect</SelectItem>
                                        <SelectItem value="kyb-onboarding-sales-prospect">KYB Onboarding - Sales
                                            Prospect</SelectItem>
                                        <SelectItem value="kyb-onboarding-referral-prospect">KYB Onboarding - Referral
                                            Prospect</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="ApplicationStatus">Application Status</Label>
                                <Select onValueChange={(value) => updateFormData('applicationStatus', value)} defaultValue={formData.applicationStatus}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select Application Status"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        {applicationStatuses.map((type, index) => (
                                            // @ts-ignore
                                            <SelectItem key={type.key} value={type.label}>
                                                {/*@ts-ignore*/}
                                                {type.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>


                            <div className="space-y-2">
                                <Label htmlFor="IsCorproateRegulated">Is Corporate Regulated?</Label>
                                <Select onValueChange={(value) => updateFormData('isCorproateRegulated', value)} defaultValue={formData.isCorproateRegulated}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem key={1} value={"Not Regulated"}>Not Regulated</SelectItem>
                                        <SelectItem key={2} value={"Regulated Entity"}>Regulated Entity</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="regulatory-status">Regulator Region</Label>
                                <Select onValueChange={(value) => updateFormData('regulatorRegion', value)} value={formData.regulatorRegion}>
                                    <SelectTrigger id="regulatory-status">
                                        <SelectValue placeholder="Select regulatory status"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        {regions.map((region, index) => (
                                            // @ts-ignore
                                            <SelectItem key={index} value={region}>
                                                {/*@ts-ignore*/}
                                                {region}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <MCCSectorSelector
                                industries={industries}
                                onSelectionChange={setSelectedMCCSectors}
                                initialSelection={formData.mccSector}
                            />

                        </CardContent>
                    </motion.div>
                )
            case 2:
                return (
                    <motion.div
                        initial={{opacity: 0, x: 20}}
                        animate={{opacity: 1, x: 0}}
                        exit={{opacity: 0, x: -20}}
                    >
                        <CardHeader>
                            <CardTitle>Product Details</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <CardTypeSelector
                                onSelectionChange={setCardOption}
                                initialSelection={cardOption}
                            />
                            <div className="space-y-2">
                                <Label htmlFor="productCcy">Product CCY</Label>
                                <Select onValueChange={(value) => handleProductCcy(value)}
                                        defaultValue={formData.productCcy}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select Product CCY"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        {countries
                                            .filter(ccy => ccy.is_active)
                                            .map((ccy, index) => (
                                                <SelectItem
                                                    key={ccy._id}
                                                    value={ccy._id}
                                                >
                                                    {ccy.currency_code}
                                                </SelectItem>
                                            ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="productVersionName">Product Version Name</Label>
                                <Select onValueChange={(value) => updateFormData('productVersionName', value)} defaultValue={formData.productVersionName}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select Product Version Name"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        {filteredProductVersionNames
                                            .map((name, index) => (
                                                // @ts-ignore
                                                <SelectItem key={index} value={name._id}>
                                                    {/* @ts-ignore */}
                                                    {name.version_name}
                                                </SelectItem>
                                            ))
                                        }
                                    </SelectContent>
                                </Select>


                            </div>
                        </CardContent>
                    </motion.div>
                )
        }
    }

    return (
        <Card className="w-full max-w-3xl">
            <CardHeader className="px-6 py-5 flex-row items-center justify-between ">
                <div>
                    <CardTitle className="text-2xl font-bold">Create Card Issuing Programme</CardTitle>
                    <CardDescription>Create Card Issuing Programme
                        for <strong>{companyData?.company_name}</strong>.</CardDescription>
                </div>

            </CardHeader>
            <Separator/>
            <div className="flex">
                <div className="bg-muted w-100 flex flex-col justify-start p-8 space-y-8">
                    {steps.map((s) => {
                        const StepIcon = s.icon
                        return (
                            <TooltipProvider key={s.id}>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <motion.div
                                            initial={false}
                                            animate={{
                                                scale: 1,
                                                color: s.id === step ? 'var(--primary)' : 'inherit'
                                            }}
                                            transition={{duration: 0.2}}
                                            className="relative flex items-center justify-start space-x-2"
                                        >
                                            <div
                                                className={`rounded-full p-2 ${
                                                    s.id < step || isStepComplete(s.id)
                                                        ? 'bg-primary text-primary-foreground'
                                                        : s.id === step
                                                            ? 'bg-primary text-white'
                                                            : 'bg-muted-foreground/20 text-muted-foreground'
                                                }`}
                                            >
                                                <StepIcon className="h-4 w-4"/>
                                            </div>
                                            <span className="text-xs font-medium">{s.title}</span>
                                        </motion.div>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p>{s.id < step ? 'Completed' : s.id === step ? 'Current Step' : 'Upcoming'}</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        )
                    })}
                </div>
                <div className="flex-1">
                    <AnimatePresence mode="wait">
                        {renderStep()}
                    </AnimatePresence>
                    <CardFooter className="flex justify-between">
                        <Button
                            onClick={handlePrev}
                            disabled={step === 1}
                            variant="outline"
                            size="sm"
                        >
                            Previous
                        </Button>
                        <Button
                            disabled={loading || !isStepComplete(step)}
                            onClick={step === 3 ? handleSubmit : handleNext}
                        >
                            {loading ? 'Loading ...' : step === 3 ? 'Update' : 'Next'}
                        </Button>
                    </CardFooter>
                </div>
            </div>
        </Card>
    )
}