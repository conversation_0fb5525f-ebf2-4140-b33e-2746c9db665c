// @ts-nocheck
'use client'

import React, {useEffect, useState} from 'react'
import {CustomFlowbiteTheme, Flowbite, Tabs} from 'flowbite-react'
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card'
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from '@/components/ui/table'
import axiosInstance from '@/utils/axiosInstance'
import {Edit2Icon, Loader2} from 'lucide-react'
import Link from "next/link";
import {Button} from "@/components/ui/button";
import {LoadingOverlay} from "@/components/LoadingOverlay";

export default function CipPage({params}: { params: { id: string } }) {
    const [cip, setCip] = useState<null | any>(null)
    const [loading, setLoading] = useState(true)

    function formatString(input: string) {
        return input
            .split('-')
            .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ')
    }

    useEffect(() => {
        const fetchCardPrograms = async () => {
            try {
                const response = await axiosInstance.get('/cip/' + params.id)
                setCip(response.data.data)
            } catch (error) {
                console.error('Error fetching card programs:', error)
            } finally {
                setLoading(false)
            }
        }

        fetchCardPrograms()
    }, [params.id])

    const customTheme: CustomFlowbiteTheme = {
        tabs: {
            base: "flex flex-col gap-2",
            tablist: {
                base: "flex text-center",
                styles: {
                    underline: "-mb-px flex-wrap border-b border-gray-200 dark:border-gray-700",
                },
                tabitem: {
                    base: "flex items-center justify-center rounded-t-lg p-4 text-sm font-medium first:ml-0 focus:outline-none disabled:cursor-not-allowed disabled:text-gray-400 disabled:dark:text-gray-500",
                    styles: {

                        underline: {
                            base: "rounded-t-lg",
                            active: {
                                on: "active rounded-t-lg border-b-2 border-primary text-primary dark:border-primary dark:text-primary",
                                off: "border-b-2 border-transparent text-gray-500 hover:border-gray-300 hover:text-gray dark:text-gray-400 dark:hover:text-gray-300"
                            }
                        },

                    },
                    icon: "mr-2 h-5 w-5"
                }
            },
            tabitemcontainer: {
                base: "",
                styles: {
                    default: "",
                    underline: "",
                    pills: "",
                    fullWidth: ""
                }
            },
            tabpanel: "py-3"
        }
    }

    if (loading) {
        return (
            <LoadingOverlay/>
        )
    }

    if (!cip) {
        return (
            <Card className="w-full max-w-md mx-auto mt-8">
                <CardContent className="py-10">
                    <p className="text-center text-lg font-medium text-muted-foreground">No CIP data found.</p>
                </CardContent>
            </Card>
        )
    }

    return (
        <Flowbite theme={{theme: customTheme}}>
            <div>


                <CardHeader className="px-6 py-5 flex-row items-center justify-between bg-background">
                    <div>
                        <CardTitle className="text-2xl font-bold">Card Issuing Program [ID#{params.id}]</CardTitle>

                    </div>
                    <div className="flex items-center space-x-2">
                        <div className="relative">
                            <Link href={`${cip._id}/edit`} passHref>
                                <Button size="sm" className="mr-2"> <Edit2Icon/> Edit CIP Details</Button>
                            </Link>
                        </div>
                    </div>
                </CardHeader>


                <div className="w-full mt-5">
                    <div className="flex flex-col gap-4 md:flex-row md:gap-6">
                        <div className="w-full md:w-1/3">
                            <Card>
                                <CardHeader>
                                    <div>
                                        <CardTitle>{cip.company.company_name}</CardTitle>
                                        <CardDescription>Company Card Program</CardDescription>
                                    </div>
                                </CardHeader>

                                <CardContent>
                                    <Tabs aria-label="Tabs with underline" variant="underline">
                                        <Tabs.Item active title="Business Info">
                                            <Table>
                                                <TableRow className="bg-background">
                                                    <TableHead className="font-semibold text-primary cursor-pointer"
                                                               colSpan={2}>
                                                        Country Of Incorporation
                                                    </TableHead>
                                                </TableRow>

                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Country Of
                                                        Incorporation</TableCell>
                                                    <TableCell>{cip.company.country_of_incorporation}</TableCell>
                                                </TableRow>

                                                <TableRow className="bg-background">
                                                    <TableHead className="font-semibold text-primary cursor-pointer"
                                                               colSpan={2}>
                                                        Company Information
                                                    </TableHead>
                                                </TableRow>

                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Industry</TableCell>
                                                    <TableCell>{cip.company.company_industry}</TableCell>
                                                </TableRow>

                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Company Registration
                                                        Number</TableCell>
                                                    <TableCell>{cip.company.company_number}</TableCell>
                                                </TableRow>

                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Company Phone
                                                        Number</TableCell>
                                                    <TableCell>{cip.company.company_phone}</TableCell>
                                                </TableRow>

                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Registration
                                                        Date</TableCell>
                                                    <TableCell>{cip.company.registration_date}</TableCell>
                                                </TableRow>

                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Company
                                                        Website</TableCell>
                                                    <TableCell>{cip.company.company_website}</TableCell>
                                                </TableRow>

                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Company
                                                        Address</TableCell>
                                                    <TableCell>{cip.company.company_address}</TableCell>
                                                </TableRow>

                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Application
                                                        Status</TableCell>
                                                    <TableCell>{formatString(cip.applicationStatus)}</TableCell>
                                                </TableRow>

                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Corporate
                                                        Regulated</TableCell>
                                                    <TableCell>{cip.isCorproateRegulated}</TableCell>
                                                </TableRow>

                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Regulator
                                                        Region</TableCell>
                                                    <TableCell>{formatString(cip.regulatorRegion)}</TableCell>
                                                </TableRow>

                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">MCC Sector</TableCell>
                                                    <TableCell>{cip.mccSector}</TableCell>
                                                </TableRow>
                                            </Table>
                                        </Tabs.Item>

                                        <Tabs.Item title="Company Contact">
                                            <Table>
                                                <TableHeader>
                                                    <TableRow className="bg-background">
                                                        <TableHead className="font-semibold text-primary cursor-pointer"
                                                                   colSpan={2}>
                                                            Company Contact Info
                                                        </TableHead>
                                                    </TableRow>
                                                </TableHeader>

                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Contact
                                                        Name</TableCell>
                                                    <TableCell>{cip.company.contact_name}</TableCell>
                                                </TableRow>

                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Contact
                                                        Role</TableCell>
                                                    <TableCell>{cip.company.contact_role}</TableCell>
                                                </TableRow>

                                                <TableRow>
                                                    <TableCell className="text-muted-foreground">Email
                                                        Address</TableCell>
                                                    <TableCell>{cip.company.company_email}</TableCell>
                                                </TableRow>
                                            </Table>
                                        </Tabs.Item>
                                    </Tabs>
                                </CardContent>
                            </Card>
                        </div>

                        <div className="w-full md:w-2/3">
                            <Card>
                                <CardHeader>
                                    <div>
                                        <CardTitle>CARD ISSUING PROGRAM</CardTitle>
                                        <CardDescription>CIP information</CardDescription>
                                    </div>
                                </CardHeader>

                                <CardContent>
                                    <Table>
                                        <TableHeader>
                                            <TableRow className="bg-background">
                                                <TableHead className="font-semibold text-primary cursor-pointer"
                                                           colSpan={2}>
                                                    Card Program Information
                                                </TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Company</TableCell>
                                                <TableCell>{cip.company.company_name}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">Card Scheme</TableCell>
                                                <TableCell>{cip.cardScheme.scheme_name}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell className="text-muted-foreground">BIN Range</TableCell>
                                                <TableCell>{cip.binRange}</TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>


                                    <Table>
                                        <TableHeader>
                                            <TableRow className="bg-background">
                                                <TableHead className="font-semibold text-primary cursor-pointer"
                                                           colSpan={2}>
                                                    Product Details
                                                </TableHead>
                                            </TableRow>

                                            <TableRow>
                                                <TableHead className="font-semibold text-primary cursor-pointer">
                                                    Product Currency
                                                </TableHead>

                                                <TableHead className="font-semibold text-primary cursor-pointer">
                                                    Product Version
                                                </TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {cip.productDetails.map((item) => {
                                                return (
                                                    <>
                                                        <TableRow>

                                                            <TableCell>{item.productCcy?.currency_code}</TableCell>

                                                            <TableCell>{item.productVersionName?.version_name}</TableCell>
                                                        </TableRow>

                                                    </>
                                                )
                                            })}
                                        </TableBody>


                                    </Table>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </Flowbite>
    )
}