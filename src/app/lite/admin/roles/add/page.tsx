//@ts-nocheck

"use client"

import type React from "react"

import { useEffect, useState } from "react"
import {
    Plus,
    Users,
    Shield,
    ChevronDown,
    ChevronRight,
    Trash2,
    Edit,
    Search,
    LayoutDashboard,
    Eye,
    Calendar,
    User,
    AlertTriangle,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTit<PERSON>,
    Dialog<PERSON>rigger,
} from "@/components/ui/dialog"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { cn } from "@/lib/utils"
import axiosInstance from "@/utils/axiosInstance"

type Module = {
    name: string
    options: string[]
}

type Role = {
    _id: string
    name: string
    description: string
    permissions: string[]
    createdAt: string
    updatedAt: string
    __v: number
    dashboard?: string
}

type Dashboard = {
    id: string
    name: string
    modules: Module[]
}

const dashboards: Dashboard[] = [
    {
        id: "infinity-dashboard",
        name: "Infinity Dashboard",
        modules: [
            // BIN Settings
            {
                name: "Card Scheme",
                options: ["Request", "Delete", "View", "Approve"],
            },
            {
                name: "Programme Type",
                options: ["Request", "Delete", "View", "Approve"],
            },
            {
                name: "BIN Category",
                options: ["Request", "Delete", "View", "Approve"],
            },
            {
                name: "BIN Variants",
                options: ["Request", "Delete", "View", "Approve"],
            },
            {
                name: "BIN Type",
                options: ["Request", "Delete", "View", "Approve"],
            },
            {
                name: "BIN Usage",
                options: ["Request", "Delete", "View", "Approve"],
            },
            {
                name: "Card Programme Type",
                options: ["Request", "Delete", "View", "Approve"],
            },
            {
                name: "Programme Manager Type",
                options: ["Request", "Delete", "View", "Approve"],
            },

            // Product Settings
            {
                name: "Product Versions",
                options: ["Request", "Delete", "View", "Approve"],
            },
            {
                name: "Product Code",
                options: ["View", "Add"],
            },

            // Global Settings
            {
                name: "Delivery Methods",
                options: ["View", "Modify"],
            },
            {
                name: "Product Currency",
                options: ["Request", "Delete", "View", "Approve"],
            },
            {
                name: "Countries",
                options: ["Request", "Delete", "View", "Approve"],
            },
            {
                name: "Card Library",
                options: ["View", "Create"],
            },

            // Ryvyl Employees
            {
                name: "Roles",
                options: ["Create", "View"],
            },
            {
                name: "Users",
                options: ["Create", "View"],
            },

            // Main Dashboard Items
            {
                name: "Events",
                options: ["View"],
            },
            {
                name: "Analytics",
                options: ["View"],
            },
            {
                name: "Issuing Pipeline",
                options: ["View", "Create CIP"],
            },
            {
                name: "CIP",
                options: ["View"],
            },

            // Customers
            {
                name: "Customers",
                options: ["View Individual", "View B2B"],
            },

            // Transactions
            {
                name: "Transactions",
                options: ["View"],
            },

            // Inventory
            {
                name: "Inventory",
                options: ["View", "Modify"],
            },

            // Logs
            {
                name: "Logs",
                options: ["View Server Logs", "View Webhook Logs"],
            },

            // External API Users
            {
                name: "External API Users",
                options: ["View", "Create", "Modify"],
            },
        ],
    },
    {
        id: "pm-dashboard",
        name: "PM Dashboard",
        modules: [
            {
                name: "Programme Management",
                options: ["Create", "Edit", "Delete", "View"],
            },
            {
                name: "Project Tracking",
                options: ["View", "Update Status"],
            },
        ],
    },
    {
        id: "b2b-dashboard",
        name: "B2B Dashboard",
        modules: [
            {
                name: "Client Management",
                options: ["Create", "Edit", "Delete", "View"],
            },
            {
                name: "Business Analytics",
                options: ["View", "Export"],
            },
        ],
    },
    {
        id: "cardholder-dashboard",
        name: "Cardholder Dashboard",
        modules: [
            {
                name: "Account Management",
                options: ["View", "Update"],
            },
            {
                name: "Transaction History",
                options: ["View", "Export"],
            },
        ],
    },
]

// Role Details Modal Component
function RoleDetailsModal({ role, trigger }: { role: Role; trigger: React.ReactNode }) {
    const [open, setOpen] = useState(false)

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        })
    }

    // Group permissions by module
    const groupedPermissions = role.permissions.reduce(
        (acc, permission) => {
            const [moduleName, action] = permission.split("_")
            if (!acc[moduleName]) {
                acc[moduleName] = []
            }
            acc[moduleName].push(action)
            return acc
        },
        {} as Record<string, string[]>,
    )

    // Get all available modules from all dashboards for reference
    const allModules = dashboards.flatMap((dashboard) => dashboard.modules)

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>{trigger}</DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[80vh]">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <Shield className="h-5 w-5" />
                        {role.name} - Role Details
                    </DialogTitle>
                    <DialogDescription>Complete overview of role permissions and metadata</DialogDescription>
                </DialogHeader>

                <ScrollArea className="max-h-[60vh] pr-4">
                    <div className="space-y-6">
                        {/* Role Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg flex items-center gap-2">
                                    <User className="h-4 w-4" />
                                    Role Information
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Role Name</Label>
                                        <p className="text-sm font-semibold">{role.name}</p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Total Permissions</Label>
                                        <p className="text-sm font-semibold">{role.permissions.length}</p>
                                    </div>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium text-muted-foreground">Description</Label>
                                    <p className="text-sm">{role.description}</p>
                                </div>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                                            <Calendar className="h-3 w-3" />
                                            Created
                                        </Label>
                                        <p className="text-sm">{formatDate(role.createdAt)}</p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                                            <Calendar className="h-3 w-3" />
                                            Last Updated
                                        </Label>
                                        <p className="text-sm">{formatDate(role.updatedAt)}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Permissions by Module */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg flex items-center gap-2">
                                    <Shield className="h-4 w-4" />
                                    Permissions by Module
                                </CardTitle>
                                <CardDescription>{Object.keys(groupedPermissions).length} modules with permissions</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {Object.entries(groupedPermissions).map(([moduleName, actions]) => {
                                        // Find the module definition to get all available options
                                        const moduleDefinition = allModules.find((m) => m.name === moduleName)
                                        const availableActions = moduleDefinition?.options || []

                                        return (
                                            <Card key={moduleName} className="border border-gray-200">
                                                <CardHeader className="p-4">
                                                    <div className="flex items-center justify-between">
                                                        <CardTitle className="text-base font-medium">{moduleName}</CardTitle>
                                                        <Badge variant="secondary" className="text-xs">
                                                            {actions.length} of {availableActions.length || actions.length} permissions
                                                        </Badge>
                                                    </div>
                                                </CardHeader>
                                                <CardContent className="p-4 pt-0">
                                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                                                        {availableActions.length > 0
                                                            ? // Show all available actions with status
                                                            availableActions.map((action) => (
                                                                <div
                                                                    key={action}
                                                                    className={cn(
                                                                        "flex items-center gap-2 p-2 rounded-md text-sm",
                                                                        actions.includes(action)
                                                                            ? "bg-green-50 text-green-700 border border-green-200"
                                                                            : "bg-gray-50 text-gray-500 border border-gray-200",
                                                                    )}
                                                                >
                                                                    <div
                                                                        className={cn(
                                                                            "w-2 h-2 rounded-full",
                                                                            actions.includes(action) ? "bg-green-500" : "bg-gray-300",
                                                                        )}
                                                                    />
                                                                    {action}
                                                                </div>
                                                            ))
                                                            : // Show only granted actions if no module definition found
                                                            actions.map((action) => (
                                                                <div
                                                                    key={action}
                                                                    className="flex items-center gap-2 p-2 rounded-md text-sm bg-green-50 text-green-700 border border-green-200"
                                                                >
                                                                    <div className="w-2 h-2 rounded-full bg-green-500" />
                                                                    {action}
                                                                </div>
                                                            ))}
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        )
                                    })}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Permission Summary */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">Permission Summary</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                                        <div className="text-2xl font-bold text-blue-600">{Object.keys(groupedPermissions).length}</div>
                                        <div className="text-sm text-blue-600">Modules</div>
                                    </div>
                                    <div className="text-center p-4 bg-green-50 rounded-lg">
                                        <div className="text-2xl font-bold text-green-600">{role.permissions.length}</div>
                                        <div className="text-sm text-green-600">Total Permissions</div>
                                    </div>
                                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                                        <div className="text-2xl font-bold text-purple-600">
                                            {role.permissions.filter((p) => p.includes("_View")).length}
                                        </div>
                                        <div className="text-sm text-purple-600">View Permissions</div>
                                    </div>
                                    <div className="text-center p-4 bg-orange-50 rounded-lg">
                                        <div className="text-2xl font-bold text-orange-600">
                                            {role.permissions.filter((p) => p.includes("_Request") || p.includes("_Create")).length}
                                        </div>
                                        <div className="text-sm text-orange-600">Create/Request</div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </ScrollArea>
            </DialogContent>
        </Dialog>
    )
}

// Delete Role Confirmation Dialog Component
function DeleteRoleDialog({
                              role,
                              trigger,
                              onConfirm,
                              isDeleting,
                          }: {
    role: Role
    trigger: React.ReactNode
    onConfirm: () => void
    isDeleting: boolean
}) {
    const [open, setOpen] = useState(false)
    const [confirmText, setConfirmText] = useState("")

    const handleConfirm = () => {
        onConfirm()
        setOpen(false)
        setConfirmText("")
    }

    const handleCancel = () => {
        setOpen(false)
        setConfirmText("")
    }

    const isConfirmValid = confirmText === role.name

    return (
        <AlertDialog open={open} onOpenChange={setOpen}>
            <AlertDialogTrigger asChild>{trigger}</AlertDialogTrigger>
            <AlertDialogContent className="max-w-md">
                <AlertDialogHeader>
                    <AlertDialogTitle className="flex items-center gap-2 text-red-600">
                        <AlertTriangle className="h-5 w-5" />
                        Delete Role
                    </AlertDialogTitle>
                    <AlertDialogDescription className="space-y-3">
                        <p>You are about to permanently delete the role:</p>
                        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                            <p className="font-semibold text-red-800">{role.name}</p>
                            <p className="text-sm text-red-600">{role.description}</p>
                            <p className="text-xs text-red-500 mt-1">{role.permissions.length} permissions will be removed</p>
                        </div>
                        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                            <div className="flex items-start gap-2">
                                <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                                <div className="text-sm text-yellow-800">
                                    <p className="font-medium">Warning:</p>
                                    <ul className="mt-1 space-y-1 text-xs">
                                        <li>• This action cannot be undone</li>
                                        <li>• Users with this role will lose associated permissions</li>
                                        <li>• All role assignments will be removed</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="confirmText" className="text-sm font-medium">
                                Type <span className="font-mono bg-gray-100 px-1 rounded">{role.name}</span> to confirm:
                            </Label>
                            <Input
                                id="confirmText"
                                value={confirmText}
                                onChange={(e) => setConfirmText(e.target.value)}
                                placeholder={`Type "${role.name}" here`}
                                className="text-sm"
                            />
                        </div>
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel onClick={handleCancel} disabled={isDeleting}>
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        onClick={handleConfirm}
                        disabled={!isConfirmValid || isDeleting}
                        className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                    >
                        {isDeleting ? (
                            <>
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                                Deleting...
                            </>
                        ) : (
                            <>
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete Role
                            </>
                        )}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}

export default function RoleManagement() {
    const [roles, setRoles] = useState<Role[]>([])
    const [roleName, setRoleName] = useState("")
    const [description, setDescription] = useState("")
    const [permissions, setPermissions] = useState<Record<string, boolean>>({})
    const [expandedModules, setExpandedModules] = useState<Record<string, boolean>>({})
    const [searchTerm, setSearchTerm] = useState("")
    const [isCreating, setIsCreating] = useState(false)
    const [editingRole, setEditingRole] = useState<Role | null>(null)
    const [activeDashboard, setActiveDashboard] = useState("infinity-dashboard")
    const [deletingRoleId, setDeletingRoleId] = useState<string | null>(null)

    const currentDashboard = dashboards.find((d) => d.id === activeDashboard)

    // For Infinity Dashboard, show all roles. For other dashboards, filter by dashboard field
    const currentRoles = roles.filter((role) => role.dashboard === activeDashboard)

    const handleModuleToggle = (moduleName: string) => {
        const modulePermissions = currentDashboard?.modules.find((m) => m.name === moduleName)?.options || []
        const updatedPermissions = { ...permissions }

        if (permissions[moduleName]) {
            modulePermissions.forEach((option) => {
                delete updatedPermissions[`${moduleName}_${option}`]
            })
            delete updatedPermissions[moduleName]
        } else {
            updatedPermissions[moduleName] = true
            modulePermissions.forEach((option) => {
                updatedPermissions[`${moduleName}_${option}`] = true
            })
        }

        setPermissions(updatedPermissions)
        setExpandedModules((prev) => ({ ...prev, [moduleName]: !prev[moduleName] }))
    }

    const handleOptionToggle = (moduleName: string, option: string) => {
        setPermissions((prev) => ({
            ...prev,
            [`${moduleName}_${option}`]: !prev[`${moduleName}_${option}`],
        }))
    }

    const handleCreateRole = async () => {
        if (roleName.trim() === "") return

        setIsCreating(true)
        const newRole = {
            name: roleName,
            description,
            dashboard: activeDashboard,
            permissions: Object.entries(permissions)
                .filter(([key, value]) => value && key.includes("_"))
                .map(([key]) => key),
        }

        try {
            const response = await axiosInstance.post("/roles", newRole)
            const savedRole = response.data
            setRoles((prev) => [...prev, savedRole])
            resetForm()
        } catch (error) {
            console.error("Error creating role:", error.response?.data || error.message)
        } finally {
            setIsCreating(false)
        }
    }

    const handleUpdateRole = async () => {
        if (!editingRole || roleName.trim() === "") return

        setIsCreating(true)
        const updatedRole = {
            name: roleName,
            description,
            dashboard: activeDashboard,
            permissions: Object.entries(permissions)
                .filter(([key, value]) => value && key.includes("_"))
                .map(([key]) => key),
        }

        try {
            const response = await axiosInstance.put(`/roles/${editingRole._id}`, updatedRole)
            const savedRole = response.data
            setRoles((prev) => prev.map((role) => (role._id === editingRole._id ? savedRole : role)))
            resetForm()
        } catch (error) {
            console.error("Error updating role:", error.response?.data || error.message)
        } finally {
            setIsCreating(false)
        }
    }

    const resetForm = () => {
        setRoleName("")
        setDescription("")
        setPermissions({})
        setExpandedModules({})
        setEditingRole(null)
    }

    const handleDeleteRole = async (roleId: string) => {
        setDeletingRoleId(roleId)
        try {
            await axiosInstance.delete(`/roles/${roleId}`)
            setRoles((prev) => prev.filter((role) => role._id !== roleId))
        } catch (error) {
            console.error("Error deleting role:", error.response?.data || error.message)
        } finally {
            setDeletingRoleId(null)
        }
    }

    const handleEditRole = (role: Role) => {
        setEditingRole(role)
        setRoleName(role.name)
        setDescription(role.description)

        const rolePermissions: Record<string, boolean> = {}
        role.permissions.forEach((permission) => {
            rolePermissions[permission] = true
            const [moduleName] = permission.split("_")
            rolePermissions[moduleName] = true
        })
        setPermissions(rolePermissions)
    }

    const moduleHasPermissions = (moduleName: string, rolePermissions: string[]) => {
        return rolePermissions.some((permission) => permission.startsWith(`${moduleName}_`))
    }

    const getModulesWithPermissions = (rolePermissions: string[]) => {
        const moduleNames = new Set<string>()
        rolePermissions.forEach((permission) => {
            const [moduleName] = permission.split("_")
            moduleNames.add(moduleName)
        })
        return Array.from(moduleNames)
    }

    const filteredRoles = currentRoles.filter(
        (role) =>
            role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            role.description.toLowerCase().includes(searchTerm.toLowerCase()),
    )

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
        })
    }

    useEffect(() => {
        const fetchRoles = async () => {
            try {
                const response = await axiosInstance.get("/roles")
                setRoles(response.data)
            } catch (err) {
                console.error("Error fetching roles:", err)
            }
        }
        fetchRoles()
    }, [])

    // Reset form when switching dashboards
    useEffect(() => {
        if (!editingRole) {
            resetForm()
        }
    }, [activeDashboard])

    return (
        <div className="min-h-screen bg-gray-50/50 p-6">
            <div className="mx-auto max-w-7xl space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Role Management</h1>
                        <p className="text-muted-foreground">Manage user roles and permissions across all dashboards</p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="px-3 py-1">
                            <Users className="mr-1 h-3 w-3" />
                            {roles.length} Total Roles
                        </Badge>
                    </div>
                </div>

                {/* Dashboard Tabs */}
                <Tabs value={activeDashboard} onValueChange={setActiveDashboard} className="w-full">
                    <TabsList className="grid w-full grid-cols-4">
                        {dashboards.map((dashboard) => (
                            <TabsTrigger key={dashboard.id} value={dashboard.id} className="flex items-center gap-2">
                                <LayoutDashboard className="h-4 w-4" />
                                {dashboard.name}
                            </TabsTrigger>
                        ))}
                    </TabsList>

                    {dashboards.map((dashboard) => (
                        <TabsContent key={dashboard.id} value={dashboard.id} className="mt-6">
                            <div className="grid grid-cols-12 gap-6">
                                {/* Roles List */}
                                <Card className="col-span-12 lg:col-span-5">
                                    <CardHeader>
                                        <div className="flex items-center justify-between">
                                            <div>
                                                <CardTitle className="flex items-center gap-2">
                                                    <Shield className="h-5 w-5" />
                                                    {dashboard.name} Roles
                                                </CardTitle>
                                                <CardDescription>
                                                    {dashboard.id === "infinity-dashboard"
                                                        ? "All system roles"
                                                        : `Manage roles for ${dashboard.name} dashboard`}
                                                </CardDescription>
                                            </div>
                                            <Badge variant="outline">{currentRoles.length} roles</Badge>
                                        </div>
                                        <div className="relative">
                                            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                                            <Input
                                                placeholder="Search roles..."
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                                className="pl-9"
                                            />
                                        </div>
                                    </CardHeader>
                                    <CardContent>
                                        <ScrollArea className="h-[600px] pr-4">
                                            <div className="space-y-3">
                                                {filteredRoles.length === 0 ? (
                                                    <div className="text-center py-8 text-muted-foreground">
                                                        <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                                        <p>No roles found for this dashboard</p>
                                                        <p className="text-sm">Create your first role to get started</p>
                                                    </div>
                                                ) : (
                                                    filteredRoles.map((role) => (
                                                        <Card key={role._id} className="border border-gray-200 hover:shadow-md transition-shadow">
                                                            <CardContent className="p-4">
                                                                <div className="flex items-start justify-between">
                                                                    <div className="flex-1">
                                                                        <div className="flex items-center gap-2 mb-2">
                                                                            <h3 className="font-semibold">{role.name}</h3>
                                                                            <Badge variant="outline" className="text-xs">
                                                                                {role.permissions.length} permissions
                                                                            </Badge>
                                                                        </div>
                                                                        <p className="text-sm text-muted-foreground mb-3">{role.description}</p>
                                                                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                                                            <span>Created {formatDate(role.createdAt)}</span>
                                                                            {role.updatedAt !== role.createdAt && (
                                                                                <span>Updated {formatDate(role.updatedAt)}</span>
                                                                            )}
                                                                        </div>
                                                                    </div>
                                                                    <div className="flex gap-1">
                                                                        <RoleDetailsModal
                                                                            role={role}
                                                                            trigger={
                                                                                <Button variant="ghost" size="sm">
                                                                                    <Eye className="h-4 w-4" />
                                                                                </Button>
                                                                            }
                                                                        />
                                                                        <Button variant="ghost" size="sm" onClick={() => handleEditRole(role)}>
                                                                            <Edit className="h-4 w-4" />
                                                                        </Button>
                                                                        <DeleteRoleDialog
                                                                            role={role}
                                                                            trigger={
                                                                                <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                                                                                    <Trash2 className="h-4 w-4" />
                                                                                </Button>
                                                                            }
                                                                            onConfirm={() => handleDeleteRole(role._id)}
                                                                            isDeleting={deletingRoleId === role._id}
                                                                        />
                                                                    </div>
                                                                </div>

                                                                {/* Permission Preview */}
                                                                <Separator className="my-3" />
                                                                <div className="space-y-2">
                                                                    <p className="text-xs font-medium text-muted-foreground">Modules with permissions:</p>
                                                                    <div className="flex flex-wrap gap-1">
                                                                        {getModulesWithPermissions(role.permissions)
                                                                            .slice(0, 4)
                                                                            .map((moduleName) => (
                                                                                <Badge key={moduleName} variant="secondary" className="text-xs">
                                                                                    {moduleName}
                                                                                </Badge>
                                                                            ))}
                                                                        {getModulesWithPermissions(role.permissions).length > 4 && (
                                                                            <Badge variant="secondary" className="text-xs">
                                                                                +{getModulesWithPermissions(role.permissions).length - 4} more
                                                                            </Badge>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            </CardContent>
                                                        </Card>
                                                    ))
                                                )}
                                            </div>
                                        </ScrollArea>
                                    </CardContent>
                                </Card>

                                {/* Create/Edit Role Form */}
                                <Card className="col-span-12 lg:col-span-7">
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Plus className="h-5 w-5" />
                                            {editingRole ? "Edit Role" : "Create New Role"}
                                        </CardTitle>
                                        <CardDescription>
                                            {editingRole
                                                ? `Modify role for ${dashboard.name} dashboard`
                                                : `Create a new role for ${dashboard.name} dashboard`}
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <ScrollArea className="h-[600px] pr-4">
                                            <div className="space-y-6">
                                                {/* Basic Information */}
                                                <div className="space-y-4">
                                                    <div>
                                                        <Label htmlFor="roleName">Role Name</Label>
                                                        <Input
                                                            id="roleName"
                                                            value={roleName}
                                                            onChange={(e) => setRoleName(e.target.value)}
                                                            placeholder="Enter role name"
                                                            className="mt-1"
                                                        />
                                                    </div>
                                                    <div>
                                                        <Label htmlFor="description">Description</Label>
                                                        <Textarea
                                                            id="description"
                                                            value={description}
                                                            onChange={(e) => setDescription(e.target.value)}
                                                            placeholder="Describe the role's purpose and responsibilities"
                                                            rows={3}
                                                            className="mt-1"
                                                        />
                                                    </div>
                                                </div>

                                                <Separator />

                                                {/* Permissions */}
                                                <div className="space-y-4">
                                                    <div>
                                                        <Label className="text-base font-semibold">Permissions</Label>
                                                        <p className="text-sm text-muted-foreground">
                                                            Select modules and specific permissions for this role in {dashboard.name}
                                                        </p>
                                                    </div>

                                                    <div className="space-y-3">
                                                        {dashboard.modules.map((module) => {
                                                            const isExpanded = expandedModules[module.name]
                                                            const hasModulePermission = permissions[module.name]
                                                            const selectedOptions = module.options.filter(
                                                                (option) => permissions[`${module.name}_${option}`],
                                                            ).length

                                                            return (
                                                                <Card
                                                                    key={module.name}
                                                                    className={cn(
                                                                        "border transition-all",
                                                                        hasModulePermission ? "border-primary/20 bg-primary/5" : "border-gray-200",
                                                                    )}
                                                                >
                                                                    <CardHeader className="p-4 pb-2">
                                                                        <div className="flex items-center justify-between">
                                                                            <div className="flex items-center gap-3">
                                                                                <Button
                                                                                    variant="ghost"
                                                                                    size="sm"
                                                                                    onClick={() =>
                                                                                        setExpandedModules((prev) => ({
                                                                                            ...prev,
                                                                                            [module.name]: !prev[module.name],
                                                                                        }))
                                                                                    }
                                                                                    className="h-6 w-6 p-0"
                                                                                >
                                                                                    {isExpanded ? (
                                                                                        <ChevronDown className="h-4 w-4" />
                                                                                    ) : (
                                                                                        <ChevronRight className="h-4 w-4" />
                                                                                    )}
                                                                                </Button>
                                                                                <div>
                                                                                    <CardTitle className="text-sm font-medium">{module.name}</CardTitle>
                                                                                    {hasModulePermission && (
                                                                                        <p className="text-xs text-muted-foreground">
                                                                                            {selectedOptions} of {module.options.length} permissions selected
                                                                                        </p>
                                                                                    )}
                                                                                </div>
                                                                            </div>
                                                                            <Switch
                                                                                checked={hasModulePermission || false}
                                                                                onCheckedChange={() => handleModuleToggle(module.name)}
                                                                            />
                                                                        </div>
                                                                    </CardHeader>

                                                                    {isExpanded && (
                                                                        <CardContent className="p-4 pt-0">
                                                                            <div className="grid gap-3 pl-9">
                                                                                {module.options.map((option) => (
                                                                                    <div key={option} className="flex items-center justify-between py-1">
                                                                                        <Label
                                                                                            htmlFor={`${module.name}_${option}`}
                                                                                            className="text-sm font-normal cursor-pointer"
                                                                                        >
                                                                                            {option}
                                                                                        </Label>
                                                                                        <Switch
                                                                                            id={`${module.name}_${option}`}
                                                                                            checked={permissions[`${module.name}_${option}`] || false}
                                                                                            onCheckedChange={() => handleOptionToggle(module.name, option)}
                                                                                            size="sm"
                                                                                        />
                                                                                    </div>
                                                                                ))}
                                                                            </div>
                                                                        </CardContent>
                                                                    )}
                                                                </Card>
                                                            )
                                                        })}
                                                    </div>
                                                </div>

                                                {/* Actions */}
                                                <div className="flex gap-3 pt-4">
                                                    <Button
                                                        onClick={editingRole ? handleUpdateRole : handleCreateRole}
                                                        disabled={!roleName.trim() || isCreating}
                                                        className="flex-1"
                                                    >
                                                        {isCreating ? (
                                                            editingRole ? (
                                                                "Updating..."
                                                            ) : (
                                                                "Creating..."
                                                            )
                                                        ) : (
                                                            <>
                                                                <Plus className="mr-2 h-4 w-4" />
                                                                {editingRole ? "Update Role" : "Create Role"}
                                                            </>
                                                        )}
                                                    </Button>
                                                    {editingRole && (
                                                        <Button variant="outline" onClick={resetForm}>
                                                            Cancel
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                        </ScrollArea>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>
                    ))}
                </Tabs>
            </div>
        </div>
    )
}
