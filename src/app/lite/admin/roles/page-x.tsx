// @ts-nocheck
'use client'

import {useEffect, useState} from "react"
import {Plus, X} from 'lucide-react'
import {Button} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Switch} from "@/components/ui/switch"
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow,} from "@/components/ui/table"
import {Card, CardContent, CardDescription, CardHeader, CardTitle,} from "@/components/ui/card"
import {Textarea} from "@/components/ui/textarea"
import {cn} from "@/lib/utils"
import axiosInstance from "@/utils/axiosInstance"

type Module = {
    name: string
    options: string[]
}

const modules: Module[] = [
    {
        name: "Card Scheme",
        options: ["Request", "Delete", "View", "Approve"]
    }, {
        name: "Card Programme Type",
        options: ["Request", "Delete", "View", "Approve"]
    }, {
        name: "BIN Type",
        options: ["Request", "Delete", "View", "Approve"]
    }, {
        name: "Issuing Client Type",
        options: ["Request", "Delete", "View", "Approve"]
    }, {
        name: "Product Currency",
        options: ["Request", "Delete", "View", "Approve"]
    }, {
        name: "Allowed Countries",
        options: ["Request", "Delete", "View", "Approve"]
    }, {
        name: "Product Versions",
        options: ["Request", "Delete", "View", "Approve"]
    }, {
        name: "Issuing Pipeline",
        options: ["View", "Create CIP"]
    }, {
        name: "CIP",
        options: ["View"]
    }, {
        name: "Users",
        options: ["Create", "View"]
    }, {
        name: "Roles",
        options: ["Create", "View"]
    }, {
        name: "Analytics",
        options: ["View"]
    },

]

export default function RoleManagement() {
    const [roles, setRoles] = useState([])
    const [roleName, setRoleName] = useState("")
    const [description, setDescription] = useState("")
    const [permissions, setPermissions] = useState<Record<string, boolean>>({})
    const [expandedModules, setExpandedModules] = useState<Record<string, boolean>>({})

    const handleModuleToggle = (moduleName: string) => {
        const modulePermissions = modules.find(m => m.name === moduleName)?.options || []
        const updatedPermissions = {...permissions}

        if (permissions[moduleName]) {
            // If turning off, remove all permissions for this module
            modulePermissions.forEach(option => {
                delete updatedPermissions[`${moduleName}_${option}`]
            })
            delete updatedPermissions[moduleName]
        } else {
            // If turning on, add all permissions for this module
            updatedPermissions[moduleName] = true
            modulePermissions.forEach(option => {
                updatedPermissions[`${moduleName}_${option}`] = true
            })
        }

        setPermissions(updatedPermissions)
        setExpandedModules(prev => ({...prev, [moduleName]: !prev[moduleName]}))
    }

    const handleOptionToggle = (moduleName: string, option: string) => {
        setPermissions(prev => ({
            ...prev,
            [`${moduleName}_${option}`]: !prev[`${moduleName}_${option}`]
        }))
    }

    const handleCreateRole = async () => {
        if (roleName.trim() === "") return;

        const newRole = {
            name: roleName,
            description,
            permissions: Object.entries(permissions)
                .filter(([key, value]) => value && key.includes('_'))
                .map(([key]) => key)
        };

        try {
            const response = await axiosInstance.post('/roles', newRole);
            const savedRole = response.data;

            setRoles([...roles, savedRole]);
            setRoleName("");
            setDescription("");
            setPermissions({});
            setExpandedModules({});
        } catch (error) {
            console.error('Error submitting role:', error.response?.data || error.message);
        }
    };

    useEffect(() => {
        const fetchRoles = async () => {
            try {
                const response = await axiosInstance.get('roles')
                setRoles(response.data)
            } catch (err) {
                console.error('Error fetching roles:', err)
            }
        }

        fetchRoles()
    }, [])

    const handleDeleteRole = (id: number) => {
        setRoles(roles.filter((role) => role.id !== id))
    }

    // Function to check if a module has any permissions
    const moduleHasPermissions = (moduleName: string, rolePermissions: string[]) => {
        return rolePermissions.some(permission => permission.startsWith(`${moduleName}_`))
    }

    return (
        <div className="grid grid-cols-12 gap-3">
            <Card className='col-span-12 md:col-span-4'>
                <CardHeader>
                    <CardTitle>Create New Role</CardTitle>
                    <CardDescription>Define a new role and assign permissions</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="roleName">Role Name</Label>
                            <Input
                                id="roleName"
                                value={roleName}
                                onChange={(e) => setRoleName(e.target.value)}
                                placeholder="Enter role name"
                            />
                        </div>
                        <div>
                            <Label htmlFor="description">Description</Label>
                            <Textarea
                                id="description"
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                placeholder="Describe the role's purpose"
                                rows={3}
                            />
                        </div>
                        <div className="space-y-4">
                            <Label>Permissions</Label>
                            {modules.map((module) => (
                                <Card key={module.name} className="border border-gray-200">
                                    <CardHeader className="p-4">
                                        <div className="flex items-center justify-between">
                                            <CardTitle className="text-lg">{module.name}</CardTitle>
                                            <Switch
                                                checked={permissions[module.name] || false}
                                                onCheckedChange={() => handleModuleToggle(module.name)}
                                            />
                                        </div>
                                    </CardHeader>
                                    <CardContent className={cn(
                                        "grid gap-4 p-4 pt-0",
                                        !expandedModules[module.name] && "hidden"
                                    )}>
                                        {module.options.map((option, index) => (
                                            <div key={option} className={cn(
                                                "flex items-center justify-between py-2",
                                                index !== module.options.length - 1 && "border-b border-gray-200"
                                            )}>
                                                <Label htmlFor={`${module.name}_${option}`}
                                                       className="flex items-center space-x-2">
                                                    <span>{option}</span>
                                                </Label>
                                                <Switch
                                                    id={`${module.name}_${option}`}
                                                    checked={permissions[`${module.name}_${option}`] || false}
                                                    onCheckedChange={() => handleOptionToggle(module.name, option)}
                                                />
                                            </div>
                                        ))}
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                        <Button onClick={handleCreateRole}>
                            <Plus className="mr-2 h-4 w-4"/> Create Role
                        </Button>
                    </div>
                </CardContent>
            </Card>

            <Card className="col-span-12 md:col-span-8">
                <CardHeader>
                    <CardTitle>Existing Roles</CardTitle>
                    <CardDescription>Manage and view existing roles and their permissions</CardDescription>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Role Name</TableHead>
                                <TableHead>Permissions</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {roles.map((role) => (
                                <TableRow key={role.id}>
                                    <TableCell className="font-medium">{role.name}</TableCell>
                                    <TableCell>
                                        <div className="flex flex-wrap gap-1">
                                            {modules.map((module) => (
                                                moduleHasPermissions(module.name, role.permissions) && (
                                                    <div key={module.name} className="flex items-center">
                            <span
                                className="text-xs font-semibold bg-primary text-primary-foreground rounded px-1 py-0.5 mr-1">
                              {module.name}
                            </span>
                                                        {module.options.map((option) => (
                                                            role.permissions.includes(`${module.name}_${option}`) && (
                                                                <span key={`${module.name}_${option}`}
                                                                      className="text-xs bg-secondary text-secondary-foreground rounded px-1 py-0.5 mr-1">
                                  {option.split(' ').map(word => word[0]).join('')}
                                </span>
                                                            )
                                                        ))}
                                                    </div>
                                                )
                                            ))}
                                        </div>
                                    </TableCell>
                                    <TableCell className="text-right">
                                        <Button variant="destructive" size="sm"
                                                onClick={() => handleDeleteRole(role.id)}>
                                            <X className="mr-2 h-4 w-4"/> Delete
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </div>
    )
}