//@ts-nocheck
"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import axiosInstance from "@/utils/axiosInstance"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
    AlertCircle,
    CheckCircle2,
    Zap,
    Copy,
    XCircle,
    Plus,
    Building2,
    <PERSON><PERSON>ard,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>p,
} from "lucide-react"
import InputMask from "react-input-mask"
import {useAppSelector} from "@/store/hooks";

interface BinSettings {
    binPrefix: string
    binSuffix: string
    binRange: string
    rangeStart: string
    rangeEnd: string
}

interface BinRangeId {
    _id: string
    binCodePrefix: string
    binCodeSuffix: string
    binCode: string
    bin_start: string
    bin_end: string
}

interface ProductVersion {
    _id: string
    version_name: string
    version_code: string
}

interface ValidationErrors {
    binStart?: string
    binEnd?: string
    range?: string
}

export default function BinAllocationTable({ params }: { params: { company: string; cip: string } }) {
    const [companyData, setCompanyData] = useState<any>(null)
    const [binBlock, setBin] = useState<any[]>([])
    const [programmes, setCip] = useState<any[]>([])
    const [isSheetOpen, setIsSheetOpen] = useState(false)
    const [selectedVersion, setSelectedVersion] = useState("")
    const [preSelectedVersion, setPreSelectedVersion] = useState("")
    const [binSettings, setBinSettings] = useState<BinSettings>({
        binPrefix: "",
        binSuffix: "",
        binRange: "",
        rangeStart: "",
        rangeEnd: "",
    })
    const [binStart, setBinStart] = useState("")
    const [binCompanyCode, setBinCompanyCode] = useState("")
    const [binEnd, setBinEnd] = useState("")
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [successMessage, setSuccessMessage] = useState<string | null>(null)
    const [validationErrors, setValidationErrors] = useState<ValidationErrors>({})
    const router = useRouter()
    const user = useAppSelector((state) => state.user.user)


    const [roles, setRoles] = useState<Role[]>([])

    useEffect(() => {
        setRoles(user.roles)
    }, [])
    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions))
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }


    const userHasModifyPermission = hasPermission(roles, "Inventory Management_Modify")
    // const userHasEditPermission = hasPermission(roles, "Programme Pipeline_Edit Company")
    // const userHasAssignProductPermission = hasPermission(roles, "Programme Pipeline_Assign Products")
    // const userHasSendCredentialsPermission = hasPermission(roles, "Programme Pipeline_Send Credentials")

    // Generate BIN settings based on product version using actual data
    const generateBinSettings = (versionName: string): BinSettings => {
        const currentProgramme = programmes[0]
        if (!currentProgramme)
            return {
                binPrefix: "",
                binSuffix: "",
                binRange: "",
                rangeStart: "",
                rangeEnd: "",
            }

        const binRangeId = currentProgramme.binRangeId as BinRangeId
        return {
            binPrefix: binRangeId?.binCodePrefix || "",
            binSuffix: binRangeId?.binCodeSuffix || "",
            binRange: binRangeId?.binCode || "",
            rangeStart: binRangeId?.bin_start || "",
            rangeEnd: binRangeId?.bin_end || "",
        }
    }

    // Get unallocated product versions
    const getUnallocatedVersions = () => {
        if (!programmes[0]?.productVersionName) return []

        return programmes[0].productVersionName.filter((version: ProductVersion) => {
            return !binBlock.some((allocation) => allocation.version_name === version.version_name)
        })
    }

    // Validation function for BIN ranges
    const validateBinRange = (start: string, end: string, settings: BinSettings): ValidationErrors => {
        const errors: ValidationErrors = {}
        if (!start || !end) {
            return errors
        }

        const startNum = Number.parseInt(start.replace(/\D/g, ""))
        const endNum = Number.parseInt(end.replace(/\D/g, ""))

        // Check for overlapping with existing allocations
        const existingAllocations = binBlock.filter((allocation) => allocation.version_name !== selectedVersion)
        for (const allocation of existingAllocations) {
            const existingStart = Number.parseInt(allocation.binStart.replace(/\D/g, ""))
            const existingEnd = Number.parseInt(allocation.binEnd.replace(/\D/g, ""))
            if (
                (startNum >= existingStart && startNum <= existingEnd) ||
                (endNum >= existingStart && endNum <= existingEnd) ||
                (startNum <= existingStart && endNum >= existingEnd)
            ) {
                errors.range = `Range overlaps with existing allocation (${allocation.binStart} - ${allocation.binEnd})`
                break
            }
        }

        return errors
    }

    // Handle BIN start change with validation
    const handleBinStartChange = (value: string) => {
        setBinStart(value)
        const errors = validateBinRange(value, binEnd, binSettings)
        setValidationErrors(errors)
    }

    // Handle BIN end change with validation
    const handleBinEndChange = (value: string) => {
        setBinEnd(value)
        const errors = validateBinRange(binStart, value, binSettings)
        setValidationErrors(errors)
    }

    async function fetchCompanyDetails() {
        try {
            const response = await axiosInstance.get(`/company/${params.company}`)
            const bin = await axiosInstance.get(`/bin/cip/${params.cip}`)
            setCompanyData(response.data.company)
            setCip(response.data.cip.filter((r: any) => r._id === params.cip))
            setBin(bin.data || [])
        } catch (error: any) {
            setError(error.response?.data.message || "Failed to fetch company data")
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchCompanyDetails()
    }, [params.company, params.cip])

    const handleVersionChange = (versionName: string) => {
        setSelectedVersion(versionName)
        setBinSettings(generateBinSettings(versionName))
        setBinStart("")
        setBinEnd("")
        setValidationErrors({})
    }

    const openAllocationDialog = (versionName?: string) => {
        if (versionName) {
            setPreSelectedVersion(versionName)
            setSelectedVersion(versionName)
            setBinSettings(generateBinSettings(versionName))
        } else {
            setPreSelectedVersion("")
            setSelectedVersion("")
        }
        setIsSheetOpen(true)
        setBinStart("")
        setBinEnd("")
        setBinCompanyCode("")
        setValidationErrors({})
    }

    const handleSave = async () => {
        try {
            // console.log(programmes[0].productVersionName)
            await axiosInstance.post("/bin", {
                product_version: programmes[0].productVersionName.find(r=>r.version_name === selectedVersion)?._id,
                company: companyData?._id,
                programme: programmes[0]?._id,
                binStart: binStart,
                binEnd: binEnd,
                binSettings: binSettings,
                cardRange: binCompanyCode,
            })
            setIsSheetOpen(false)
            setSuccessMessage("BIN Block allocated successfully")
            await fetchCompanyDetails()
            // Clear form fields
            setSelectedVersion("")
            setPreSelectedVersion("")
            setBinStart("")
            setBinEnd("")
            setBinCompanyCode("")
            setValidationErrors({})
            setBinSettings({
                binPrefix: "",
                binSuffix: "",
                binRange: "",
                rangeStart: "",
                rangeEnd: "",
            })
        } catch (error) {
            console.error("Error saving BIN Block", error)
            setError("Failed to allocate BIN Block. Please try again.")
        }
    }

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text)
    }

    // Check if form is valid
    const isFormValid = selectedVersion && binStart && binEnd && Object.keys(validationErrors).length === 0

    // Calculate statistics
    const totalVersions = programmes[0]?.productVersionName?.length || 0
    const allocatedVersions = binBlock.length
    const pendingVersions = totalVersions - allocatedVersions
    const totalAllocatedBins = binBlock.reduce((sum, allocation) => {
        const quantity =
            Number.parseInt(allocation.binEnd.replace(/\D/g, "")) -
            Number.parseInt(allocation.binStart.replace(/\D/g, "")) +
            1
        return sum + quantity
    }, 0)

    if (loading) {
        return <LoadingOverlay />
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-primary-50 to-indigo-50">
            <div className="w-full max-w-[1400px] mx-auto p-6 space-y-8">
                {/* Success/Error Messages */}
                {successMessage && (
                    <Alert className="bg-emerald-50 text-emerald-800 border-emerald-200 shadow-sm">
                        <CheckCircle2 className="h-4 w-4" />
                        <AlertTitle>Success</AlertTitle>
                        <AlertDescription>{successMessage}</AlertDescription>
                    </Alert>
                )}
                {error && (
                    <Alert variant="destructive" className="shadow-sm">
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Error</AlertTitle>
                        <AlertDescription>{error}</AlertDescription>
                    </Alert>
                )}

                {/* Header Section */}
                <div className="bg-white rounded-2xl shadow-xl border border-slate-200 overflow-hidden">
                    <div className="bg-gradient-to-r from-primary-600 via-primary-700 to-indigo-700 px-8 py-6">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                                <div className="bg-white/20 p-3 rounded-xl">
                                    <Building2 className="h-8 w-8 text-white" />
                                </div>
                                <div>
                                    <h1 className="text-3xl font-bold text-white">BIN Allocation Management</h1>
                                    <p className="text-primary-100 mt-1">Manage BIN block allocations for product versions</p>
                                </div>
                            </div>
                            {userHasModifyPermission &&(


                            <Button
                                onClick={() => openAllocationDialog()}
                                className="bg-white text-primary-700 hover:bg-primary-50 shadow-lg font-semibold px-6 py-3 h-auto"
                                disabled={getUnallocatedVersions().length === 0}
                            >
                                <Plus className="h-5 w-5 mr-2" />
                                New Allocation
                            </Button>
                            )}
                        </div>
                    </div>

                    {/* Company Info & Stats */}
                    <div className="p-8">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            {/* Company Details */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-slate-800 flex items-center gap-2">
                                    <Building2 className="h-5 w-5 text-primary-600" />
                                    Company Information
                                </h3>
                                <div className="bg-slate-50 rounded-xl p-6 space-y-3">
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm font-medium text-slate-600">Company Name</span>
                                        <span className="font-semibold text-slate-900">{companyData?.company_name}</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm font-medium text-slate-600">Ryvyl ID</span>
                                        <span className="font-mono text-sm bg-primary-100 text-primary-800 px-3 py-1 rounded-full">
                      {companyData?.ryvyl_id}
                    </span>
                                    </div>
                                </div>
                            </div>

                            {/* Statistics */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-slate-800 flex items-center gap-2">
                                    <TrendingUp className="h-5 w-5 text-green-600" />
                                    Allocation Statistics
                                </h3>
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="bg-green-50 rounded-xl p-4 text-center border border-green-200">
                                        <div className="text-2xl font-bold text-green-700">{allocatedVersions}</div>
                                        <div className="text-sm text-green-600">Allocated</div>
                                    </div>
                                    <div className="bg-orange-50 rounded-xl p-4 text-center border border-orange-200">
                                        <div className="text-2xl font-bold text-orange-700">{pendingVersions}</div>
                                        <div className="text-sm text-orange-600">Pending</div>
                                    </div>
                                    <div className="bg-primary-50 rounded-xl p-4 text-center border border-primary-200 col-span-2">
                                        <div className="text-2xl font-bold text-primary-700">{totalAllocatedBins.toLocaleString()}</div>
                                        <div className="text-sm text-primary-600">Total BIN Numbers Allocated</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Main Table */}
                <Card className="shadow-xl border-0 overflow-hidden">
                    <CardHeader className="bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200">
                        <CardTitle className="text-xl font-semibold text-slate-800 flex items-center gap-2">
                            <CreditCard className="h-6 w-6 text-slate-600" />
                            Product Version Allocations
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-slate-50 hover:bg-slate-50">
                                        <TableHead className="font-semibold text-slate-700 py-4">#</TableHead>
                                        <TableHead className="font-semibold text-slate-700">Product Version</TableHead>
                                        <TableHead className="font-semibold text-slate-700">Card Range</TableHead>
                                        <TableHead className="font-semibold text-slate-700">BIN Start</TableHead>
                                        <TableHead className="font-semibold text-slate-700">BIN End</TableHead>
                                        <TableHead className="font-semibold text-slate-700">Status</TableHead>
                                        <TableHead className="font-semibold text-slate-700 text-right">Quantity</TableHead>
                                        <TableHead className="font-semibold text-slate-700 text-center">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {programmes[0]?.productVersionName.map((allocation: ProductVersion, index: number) => {
                                        const binAllocation = binBlock.find((r) => r.product_version._id === allocation._id)
                                        const quantity = binAllocation
                                            ? Number.parseInt(binAllocation.binEnd.replace(/\D/g, "")) -
                                            Number.parseInt(binAllocation.binStart.replace(/\D/g, "")) +
                                            1
                                            : 0
                                        const isAllocated = !!binAllocation

                                        return (
                                            <TableRow
                                                key={allocation._id}
                                                className={`hover:bg-slate-50 transition-colors ${isAllocated ? "bg-green-50/30" : "bg-orange-50/30"}`}
                                            >
                                                <TableCell className="font-medium py-4">
                                                    <div className="w-8 h-8 bg-slate-100 rounded-full flex items-center justify-center text-sm font-semibold text-slate-600">
                                                        {index + 1}
                                                    </div>
                                                </TableCell>
                                                <TableCell className="font-semibold text-slate-900">
                                                    <div className="flex items-center gap-2">
                                                        <Hash className="h-4 w-4 text-slate-400" />
                                                        {allocation.version_name}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    {binAllocation?.cardRange ? (
                                                        <span className="font-mono bg-slate-100 px-3 py-1 rounded-full text-sm">
                              {binAllocation.cardRange}
                            </span>
                                                    ) : (
                                                        <span className="text-slate-400">-</span>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    {binAllocation?.binStart ? (
                                                        <span className="font-mono bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm">
                              {binAllocation.binStart}
                            </span>
                                                    ) : (
                                                        <span className="text-slate-400">-</span>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    {binAllocation?.binEnd ? (
                                                        <span className="font-mono bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm">
                              {binAllocation.binEnd}
                            </span>
                                                    ) : (
                                                        <span className="text-slate-400">-</span>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <Badge
                                                        variant={isAllocated ? "default" : "secondary"}
                                                        className={
                                                            isAllocated
                                                                ? "bg-green-100 text-green-800 hover:bg-green-100"
                                                                : "bg-orange-100 text-orange-800 hover:bg-orange-100"
                                                        }
                                                    >
                                                        {isAllocated ? "Allocated" : "Pending"}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    {quantity > 0 ? (
                                                        <span className="font-semibold text-slate-900">{quantity.toLocaleString()}</span>
                                                    ) : (
                                                        <span className="text-slate-400">-</span>
                                                    )}
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    {!isAllocated && userHasModifyPermission ? (
                                                        <Button
                                                            size="sm"
                                                            onClick={() => openAllocationDialog(allocation.version_name)}
                                                            className="bg-primary-600 hover:bg-primary-700 text-white shadow-sm"
                                                        >
                                                            <Plus className="h-4 w-4 mr-1" />
                                                            Allocate
                                                        </Button>
                                                    ) : (
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => router.push(`${params.cip}/bin-allocation`)}
                                                            className="text-slate-600 hover:text-slate-800"
                                                        >
                                                            View Details
                                                        </Button>
                                                    )}
                                                </TableCell>
                                            </TableRow>
                                        )
                                    })}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>

                {/* Enhanced BIN Allocation Dialog */}
                <Dialog open={isSheetOpen} onOpenChange={setIsSheetOpen}>
                    <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
                        <DialogHeader className="border-b border-slate-200 pb-4">
                            <DialogTitle className="text-2xl font-semibold flex items-center gap-3">
                                <div className="bg-primary-100 p-2 rounded-lg">
                                    <Zap className="h-6 w-6 text-primary-600" />
                                </div>
                                Allocate BIN Block
                            </DialogTitle>
                        </DialogHeader>

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 py-6">
                            {/* Left Column - Product Selection */}
                            <div className="space-y-6">
                                <div className="bg-slate-50 rounded-xl p-6 space-y-4">
                                    <h3 className="font-semibold text-slate-800 flex items-center gap-2">
                                        <CreditCard className="h-5 w-5 text-slate-600" />
                                        Product Configuration
                                    </h3>

                                    <div>
                                        <Label htmlFor="version-select" className="text-sm font-medium text-slate-700">
                                            Product Version
                                        </Label>
                                        <Select onValueChange={handleVersionChange} value={selectedVersion} disabled={!!preSelectedVersion}>
                                            <SelectTrigger id="version-select" className="mt-2">
                                                <SelectValue placeholder="Select Product Version" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {preSelectedVersion ? (
                                                    <SelectItem value={preSelectedVersion}>{preSelectedVersion}</SelectItem>
                                                ) : (
                                                    getUnallocatedVersions().map((version: ProductVersion) => (
                                                        <SelectItem key={version._id} value={version.version_name}>
                                                            {version.version_name}
                                                        </SelectItem>
                                                    ))
                                                )}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div>
                                        <Label htmlFor="cardRange" className="text-sm font-medium text-slate-700">
                                            Card Range
                                        </Label>
                                        <InputMask
                                            mask="99"
                                            maskChar="-"
                                            value={binCompanyCode}
                                            onChange={(e) => setBinCompanyCode(e.target.value)}
                                        >
                                            {(inputProps: any) => (
                                                <Input
                                                    {...inputProps}
                                                    id="cardRange"
                                                    placeholder="Enter Card Range"
                                                    className="mt-2 font-mono"
                                                />
                                            )}
                                        </InputMask>
                                    </div>
                                </div>

                                <div className="bg-slate-50 rounded-xl p-6 space-y-4">
                                    <h3 className="font-semibold text-slate-800 flex items-center gap-2">
                                        <Hash className="h-5 w-5 text-slate-600" />
                                        BIN Range Configuration
                                    </h3>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="bin-start" className="text-sm font-medium text-slate-700">
                                                BIN Start
                                            </Label>
                                            <InputMask
                                                mask="99 9999"
                                                maskChar="-"
                                                value={binStart}
                                                onChange={(e) => handleBinStartChange(e.target.value)}
                                            >
                                                {(inputProps: any) => (
                                                    <Input
                                                        {...inputProps}
                                                        id="bin-start"
                                                        placeholder="Card Subrange Low"
                                                        className={`mt-2 font-mono ${validationErrors.binStart ? "border-red-500" : ""}`}
                                                    />
                                                )}
                                            </InputMask>
                                            {validationErrors.binStart && (
                                                <p className="text-xs text-red-500 mt-1 flex items-center gap-1">
                                                    <XCircle className="h-3 w-3" />
                                                    {validationErrors.binStart}
                                                </p>
                                            )}
                                        </div>
                                        <div>
                                            <Label htmlFor="bin-end" className="text-sm font-medium text-slate-700">
                                                BIN End
                                            </Label>
                                            <InputMask
                                                mask="99 9999"
                                                maskChar="-"
                                                value={binEnd}
                                                onChange={(e) => handleBinEndChange(e.target.value)}
                                            >
                                                {(inputProps: any) => (
                                                    <Input
                                                        {...inputProps}
                                                        id="bin-end"
                                                        placeholder="Card Subrange High"
                                                        className={`mt-2 font-mono ${validationErrors.binEnd ? "border-red-500" : ""}`}
                                                    />
                                                )}
                                            </InputMask>
                                            {validationErrors.binEnd && (
                                                <p className="text-xs text-red-500 mt-1 flex items-center gap-1">
                                                    <XCircle className="h-3 w-3" />
                                                    {validationErrors.binEnd}
                                                </p>
                                            )}
                                        </div>
                                    </div>

                                    {validationErrors.range && (
                                        <Alert variant="destructive">
                                            <XCircle className="h-4 w-4" />
                                            <AlertDescription>{validationErrors.range}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>
                            </div>

                            {/* Right Column - Generated BIN Range Information */}
                            {selectedVersion && (
                                <div className="space-y-6">
                                    <Card className="bg-gradient-to-br from-primary-50 to-indigo-50 border-primary-200 shadow-lg">
                                        <CardHeader className="pb-4">
                                            <CardTitle className="text-lg flex items-center gap-2 text-primary-700">
                                                <div className="bg-primary-100 p-2 rounded-lg">
                                                    <Zap className="h-5 w-5" />
                                                </div>
                                                Global BIN Range Information
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <Label className="text-xs font-medium text-slate-600">BIN Prefix</Label>
                                                    <div className="flex items-center gap-2 mt-1">
                                                        <Input value={binSettings.binPrefix} readOnly className="bg-white font-mono text-sm" />
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => copyToClipboard(binSettings.binPrefix)}
                                                            className="shrink-0"
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                </div>
                                                <div>
                                                    <Label className="text-xs font-medium text-slate-600">BIN Suffix</Label>
                                                    <div className="flex items-center gap-2 mt-1">
                                                        <Input value={binSettings.binSuffix} readOnly className="bg-white font-mono text-sm" />
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => copyToClipboard(binSettings.binSuffix)}
                                                            className="shrink-0"
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div>
                                                <Label className="text-xs font-medium text-slate-600">BIN Range</Label>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <Input
                                                        value={binSettings.binRange}
                                                        readOnly
                                                        className="bg-white font-mono text-lg font-semibold"
                                                    />
                                                    <Button
                                                        size="sm"
                                                        variant="outline"
                                                        onClick={() => copyToClipboard(binSettings.binRange)}
                                                        className="shrink-0"
                                                    >
                                                        <Copy className="h-3 w-3" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {binStart && binEnd && !validationErrors.range && (
                                        <Card className="bg-gradient-to-br from-emerald-50 to-green-50 border-emerald-200 shadow-lg">
                                            <CardHeader className="pb-3">
                                                <CardTitle className="text-lg flex items-center gap-2 text-emerald-700">
                                                    <CheckCircle2 className="h-5 w-5" />
                                                    Allocation Summary
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="text-center">
                                                    <div className="text-3xl font-bold text-emerald-700 mb-2">
                                                        {(
                                                            Number.parseInt(binEnd.replace(/\D/g, "")) -
                                                            Number.parseInt(binStart.replace(/\D/g, "")) +
                                                            1
                                                        ).toLocaleString()}
                                                    </div>
                                                    <p className="text-sm text-emerald-600">BIN numbers will be allocated</p>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    )}
                                </div>
                            )}
                        </div>

                        <DialogFooter className="border-t border-slate-200 pt-4 gap-3">
                            <Button variant="outline" onClick={() => setIsSheetOpen(false)} className="px-6">
                                Cancel
                            </Button>
                            <Button onClick={handleSave} disabled={!isFormValid} className="bg-primary-600 hover:bg-primary-700 px-6">
                                <Zap className="h-4 w-4 mr-2" />
                                Allocate BIN Block
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
        </div>
    )
}
