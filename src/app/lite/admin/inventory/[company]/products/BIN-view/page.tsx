export default function bin(){
    return "x";
}


// // @ts-nocheck
// "use client"
//
// import React, {useEffect, useState} from "react"
// import {Card, CardContent} from "@/components/ui/card"
// import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table"
// import axiosInstance from "@/utils/axiosInstance"
// import axios from "@/utils/axiosInstance"
// import {LoadingOverlay} from "@/components/LoadingOverlay";
// import {formatDate} from "@/utils/helpers";
// import {useRouter} from "next/navigation";
//
// interface CardImage {
//     _id: string
//     scheme_name: string
//     front_side: string
//     back_side: string
//     card_type: string
//     card_category: string
//     created_at: string
//     created_by: string
//     product_version: string
// }
//
// const asset = process.env.NEXT_PUBLIC_API_URL
// export default function Company({params}: { params: { company: string } }) {
//     const [companyData, setCompanyData] = useState<null | any>(null)
//     const [cip, setCip] = useState<null | any>(null)
//     const [currencies, setCurrencies] = useState([])
//     const [cardImages, setCardImages] = useState<CardImage[]>([])
//     const [loading, setLoading] = useState(true)
//     const [error, setError] = useState<string | null>(null)
//     const [sortConfig, setSortConfig] = useState({key: null, direction: "ascending"})
//     const [savedBinCategories, setSavedBinCategories] = useState([])
//     const [savedBinVariants, setSavedBinCVariants] = useState([])
//     const [isLoading, setIsLoading] = useState(false)
//     const [selectedProgram, setSelectedProgram] = useState<Program | null>(null)
//     const [isSheetOpen, setIsSheetOpen] = useState(false)
//     const router =useRouter()
//     useEffect(() => {
//         async function fetchCompanyDetails() {
//             try {
//                 const response = await axiosInstance.get(`/company/${params.company}`)
//                 setCompanyData(response.data.company)
//                 setCip(response.data.cip)
//                 const images = await axiosInstance.get<CardImage[]>("/images")
//                 if (response.data.company._id) {
//
//                     var final_cards = images.data.filter(image => image.company._id === response.data.company._id)
//                     console.dir(final_cards)
//                     if (final_cards.length > 0) {
//                         setCardImages(final_cards)
//                     } else {
//
//                     }
//
//                 }
//             } catch (error: any) {
//                 setError(error.response?.data.message || "Failed to fetch company data")
//             } finally {
//                 setLoading(false)
//             }
//         }
//
//         fetchCompanyDetails()
//     }, [params.company])
//     useEffect(() => {
//         fetchCardTypes()
//     }, [])
//     const fetchCardTypes = async () => {
//         setIsLoading(true)
//         try {
//             const [categories, variants, currencies] = await Promise.all([
//                 axios.get("/bin-category"),
//                 axios.get("/bin-variant"),
//                 axios.get("product-currencies"),
//             ])
//
//             setSavedBinCategories(categories.data)
//             setSavedBinCVariants(variants.data)
//             setCurrencies(currencies.data)
//             // const response = await axios.get<CardImage[]>("/images")
//             // setCardImages(response.data)
//         } catch (error) {
//             console.error("Error fetching BIN Types", error)
//             setMessage({type: "error", content: "Error fetching data. Please try again."})
//         } finally {
//             setIsLoading(false)
//         }
//     }
//     const [selectedImage, setSelectedImage] = useState<string | null>(null)
//
//     const handleImageClick = (imageSrc: string) => {
//         setSelectedImage(imageSrc)
//     }
//
//     const handleClosePopup = () => {
//         setSelectedImage(null)
//     }
//     const [countries, setCountries] = useState([])
//
//
//     if (loading) {
//         return <LoadingOverlay/>
//     }
//
//     if (!companyData) {
//         return (
//             <Card className="w-full max-w-md mx-auto mt-8">
//                 <CardContent className="py-10">
//                     <p className="text-center text-lg font-medium text-muted-foreground"> Data Not Found.</p>
//                 </CardContent>
//             </Card>
//         )
//     }
//
//
//     return (
//         <div className="p-6">
//             <div className="mb-8">
//                 <h1 className="text-2xl font-semibold text-teal-600 mb-4">PROGRAMME DETAILS</h1>
//
//             </div>
//
//             <div className="border rounded-lg overflow-hidden">
//                 <Table>
//                     <TableHeader>
//                         <TableRow className="bg-gray-50">
//                             <TableHead className="font-medium">ID</TableHead>
//                             <TableHead className="font-medium">Date</TableHead>
//                             <TableHead className="font-medium">Scheme</TableHead>
//                             <TableHead className="font-medium">Prog Type</TableHead>
//                             <TableHead className="font-medium">BIN Type</TableHead>
//                             <TableHead className="font-medium">Card Type</TableHead>
//                             <TableHead className="font-medium text-teal-600">BIN Prefix</TableHead>
//                             <TableHead className="font-medium">BIN Suffix</TableHead>
//                             <TableHead className="font-medium">BIN Currency</TableHead>
//                             <TableHead className="font-medium">Product Variants</TableHead>
//                         </TableRow>
//                     </TableHeader>
//                     <TableBody>
//                         {cip.map((program, index) => (
//                             <TableRow className="cursor-pointer" onClick={()=>   router.push(`BIN-view/${program._id}`)}>
//                                 <TableCell className="text-navy-800">R00{index+1}</TableCell>
//                                 <TableCell>{formatDate(program.createdAt)}</TableCell>
//                                 <TableCell className="text-navy-800">{program.cardScheme.scheme_name}</TableCell>
//                                 <TableCell
//                                     className="text-navy-800">{program.programmeType.type}</TableCell>
//                                 <TableCell className="text-primary">{program.binType.type}</TableCell>
//                                 <TableCell className="text-navy-800">Debit</TableCell>
//                                 <TableCell className="text-teal-600">{program.binRangeId.binCodeSuffix}</TableCell>
//                                 <TableCell>{program.binRangeId.binCodePrefix}</TableCell>
//                                 <TableCell
//                                     className="text-navy-800">{currencies.find(r => r._id === program.binRangeId.currency)?.currency_code}</TableCell>
//                                 <TableCell>
//                                     {program.productVersionName.map(v => (
//                                         <span className="ml-2" key={v}>{v.version_name.includes("PHY") ? "Virtual" : "Physical"}</span>
//                                     ))}
//
//                                 </TableCell>
//                             </TableRow>
//                         ))}
//
//                     </TableBody>
//                 </Table>
//             </div>
//         </div>
//     )
// }
//
