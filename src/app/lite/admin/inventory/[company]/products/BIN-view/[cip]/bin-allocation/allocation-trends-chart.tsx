"use client"

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Legend, ResponsiveContainer, AreaChart, Area } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { TrendingUp, Calendar } from "lucide-react"

interface TrendData {
    month: string
    issued: number
    allocated: number
    utilization: number
}

// Sample trend data for the last 6 months
const trendData: TrendData[] = [
    { month: "Jan", issued: 850, allocated: 15000, utilization: 5.7 },
    { month: "Feb", issued: 1200, allocated: 18000, utilization: 6.7 },
    { month: "Mar", issued: 1800, allocated: 22000, utilization: 8.2 },
    { month: "Apr", issued: 2500, allocated: 28000, utilization: 8.9 },
    { month: "May", issued: 3200, allocated: 35000, utilization: 9.1 },
    { month: "Jun", issued: 4560, allocated: 42000, utilization: 10.9 },
]

export default function AllocationTrendsChart() {
    return (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Allocation Trends Line Chart */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <TrendingUp className="h-5 w-5 text-blue-600" />
                        Allocation Trends (6 Months)
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <ChartContainer
                        config={{
                            issued: {
                                label: "Cards Issued",
                                color: "hsl(var(--chart-1))",
                            },
                            allocated: {
                                label: "Total Allocated",
                                color: "hsl(var(--chart-2))",
                            },
                        }}
                        className="h-[300px]"
                    >
                        <ResponsiveContainer width="100%" height="100%">
                            <LineChart data={trendData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="month" />
                                <YAxis />
                                <ChartTooltip content={<ChartTooltipContent />} />
                                <Legend />
                                <Line
                                    type="monotone"
                                    dataKey="issued"
                                    stroke="var(--color-issued)"
                                    strokeWidth={3}
                                    dot={{ fill: "var(--color-issued)", strokeWidth: 2, r: 4 }}
                                    name="Cards Issued"
                                />
                                <Line
                                    type="monotone"
                                    dataKey="allocated"
                                    stroke="var(--color-allocated)"
                                    strokeWidth={3}
                                    dot={{ fill: "var(--color-allocated)", strokeWidth: 2, r: 4 }}
                                    name="Total Allocated"
                                />
                            </LineChart>
                        </ResponsiveContainer>
                    </ChartContainer>
                </CardContent>
            </Card>

            {/* Utilization Rate Area Chart */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Calendar className="h-5 w-5 text-green-600" />
                        Utilization Rate Trend
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <ChartContainer
                        config={{
                            utilization: {
                                label: "Utilization Rate %",
                                color: "hsl(var(--chart-3))",
                            },
                        }}
                        className="h-[300px]"
                    >
                        <ResponsiveContainer width="100%" height="100%">
                            <AreaChart data={trendData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="month" />
                                <YAxis />
                                <ChartTooltip content={<ChartTooltipContent />} />
                                <Area
                                    type="monotone"
                                    dataKey="utilization"
                                    stroke="var(--color-utilization)"
                                    fill="var(--color-utilization)"
                                    fillOpacity={0.3}
                                    strokeWidth={2}
                                />
                            </AreaChart>
                        </ResponsiveContainer>
                    </ChartContainer>
                </CardContent>
            </Card>
        </div>
    )
}
