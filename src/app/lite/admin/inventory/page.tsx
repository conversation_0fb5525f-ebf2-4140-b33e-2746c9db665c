//@ts-nocheck

"use client"

import type React from "react"
import { useEffect, useState } from "react"
import axiosInstance from "@/utils/axiosInstance"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { CreditCard, Database, TrendingUp, RefreshCw, AlertCircle, Activity, Search, Download } from "lucide-react"

// Types
interface GlobalUsageData {
    totalCards: number
    totalBins: number
}

interface CompanyUsage {
    companyId: string
    totalBins: number
    usedBins: number
    availableBins: number
}

interface Company {
    _id: string
    company_name: string
    company_number: string
    company_email: string
    company_phone?: string
    company_website?: string
    admin_name?: string
    admin_email?: string
    contact_name?: string
    created_at: string
    status?: string
}

interface CardProgram {
    _id: string
    company: string
    programme_name: string
    created_at: string
}

interface Role {
    _id: string
    name: string
    permissions: string[]
}

interface User {
    _id: string
    name: string
    email: string
    roles: Role[]
}

interface MetricCardProps {
    title: string
    value: number
    icon: React.ReactNode
    description: string
    trend?: {
        value: number
        isPositive: boolean
    }
    loading?: boolean
}

// Utility functions
const formatDate = (dateString: string): string => {
    if (!dateString) return "N/A"
    try {
        const date = new Date(dateString)
        return date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
        })
    } catch (error) {
        return "Invalid Date"
    }
}

// Mock user selector (replace with your actual implementation)
const useAppSelector = (selector: any) => {
    return {
        user: {
            roles: [
                {
                    _id: "1",
                    name: "Admin",
                    permissions: ["Issuing Pipeline_Create CIP", "Issuing Pipeline_View"],
                },
            ],
        },
    }
}

// Metric Card Component
function MetricCard({ title, value, icon, description, trend, loading }: MetricCardProps) {
    if (loading) {
        return (
            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                        <Skeleton className="h-4 w-24" />
                    </CardTitle>
                    <Skeleton className="h-4 w-4" />
                </CardHeader>
                <CardContent>
                    <Skeleton className="h-8 w-16 mb-1" />
                    <Skeleton className="h-3 w-32" />
                </CardContent>
            </Card>
        )
    }

    return (
        <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
                <div className="text-muted-foreground">{icon}</div>
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold">{value.toLocaleString()}</div>
                <div className="flex items-center justify-between">
                    <p className="text-xs text-muted-foreground mt-1">{description}</p>
                    {trend && (
                        <Badge variant={trend.isPositive ? "default" : "secondary"} className="text-xs">
                            <TrendingUp className="w-3 h-3 mr-1" />
                            {trend.value}%
                        </Badge>
                    )}
                </div>
            </CardContent>
        </Card>
    )
}

// Data Exporter Component
function DataExporter({ data, filename, title }: { data: any[]; filename: string; title: string }) {
    const exportToCSV = () => {
        if (!data || data.length === 0) {
            alert("No data to export")
            return
        }

        const headers = Object.keys(data[0])
        const csvContent = [
            headers.join(","),
            ...data.map((row) =>
                headers
                    .map((header) =>
                        typeof row[header] === "string" && row[header].includes(",") ? `"${row[header]}"` : row[header],
                    )
                    .join(","),
            ),
        ].join("\n")

        const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
        const link = document.createElement("a")
        const url = URL.createObjectURL(blob)
        link.setAttribute("href", url)
        link.setAttribute("download", `${filename}.csv`)
        link.style.visibility = "hidden"
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    }

    return (
        <Button onClick={exportToCSV} variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export CSV
        </Button>
    )
}

// Main Component
export default function MetricsPage() {
    // Metrics Dashboard State
    const [metricsData, setMetricsData] = useState<GlobalUsageData | null>(null)
    const [metricsLoading, setMetricsLoading] = useState(true)
    const [metricsError, setMetricsError] = useState<string | null>(null)
    const [companyUsage, setCompanyUsage] = useState<CompanyUsage[]>([])
    const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

    // Company Table State
    const [companies, setCompanies] = useState<Company[]>([])
    const [cardProgramme, setCardProgramme] = useState<CardProgram[]>([])
    const [companiesLoading, setCompaniesLoading] = useState(true)
    const [companiesError, setCompaniesError] = useState<string | null>(null)
    const [searchTerm, setSearchTerm] = useState<string>("")

    // User permissions
    const user = useAppSelector((state: any) => state.user.user)
    const [roles, setRoles] = useState<Role[]>([])

    useEffect(() => {
        if (user?.roles) {
            setRoles(user.roles)
        }
    }, [user])

    // Permission functions
    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions))
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }

    const userHasViewPermission = hasPermission(roles, "Issuing Pipeline_View")

    // Fetch metrics data
    const fetchGlobalUsage = async () => {
        try {
            setMetricsLoading(true)
            setMetricsError(null)
            const response = await axiosInstance.get("bin/global-usage")
            const usageResponse = await axiosInstance.get("bin/bins-per-company")

            if (usageResponse.data && Array.isArray(usageResponse.data)) {
                setCompanyUsage(usageResponse.data)
            } else {
                console.warn("Company usage data is not in expected format:", usageResponse.data)
                setCompanyUsage([])
            }

            if (response.data) {
                setMetricsData(response.data)
                setLastUpdated(new Date())
            }
        } catch (err: any) {
            console.error("Error fetching global usage:", err)
            setMetricsError(err.response?.data?.message || "Failed to fetch global usage data")
        } finally {
            setMetricsLoading(false)
        }
    }

    // Fetch companies data
    const fetchCompanies = async () => {
        try {
            setCompaniesLoading(true)
            setCompaniesError(null)
            const response = await axiosInstance.get("company")
            const companies = response.data.data
            const cardProgramme = response.data.programme

            if (Array.isArray(companies) && Array.isArray(cardProgramme)) {
                const cardProgrammeCompanyIds = cardProgramme.map((program: CardProgram) => program.company)
                const uniqueCompanies = companies.filter((company: Company) => cardProgrammeCompanyIds.includes(company._id))
                setCompanies(uniqueCompanies)
                setCardProgramme(cardProgramme)
            } else {
                console.error("Invalid data format received from API")
                setCompaniesError("Invalid data format received")
            }
        } catch (error) {
            console.error("Error fetching companies:", error)
            setCompaniesError("Failed to fetch companies")
        } finally {
            setCompaniesLoading(false)
        }
    }

    useEffect(() => {
        fetchGlobalUsage()
        fetchCompanies()
    }, [])

    const handleRefresh = () => {
        fetchGlobalUsage()
        fetchCompanies()
    }

    // Calculate utilization rate
    const utilizationRate = metricsData ? Math.round((metricsData.totalCards / metricsData.totalBins) * 100) : 0

    // Get company usage data
    const getCompanyUsage = (companyId: string): CompanyUsage => {
        const usage = companyUsage.find((u) => u.companyId === companyId)
        return usage || { companyId, totalBins: 0, usedBins: 0, availableBins: 0 }
    }

    // Prepare export data
    const exportData = companies.map((company, index) => {
        const usage = getCompanyUsage(company._id)
        return {
            id: index + 1,
            name: company.company_name,
            email: company.company_email,
            phone: company.company_phone || "N/A",
            website: company.company_website || "N/A",
            totalBins: usage.totalBins,
            usedBins: usage.usedBins,
            availableBins: usage.availableBins,
            created_at: formatDate(company.created_at),
        }
    })

    // Filter companies
    const filteredCompanies = companies.filter((company) => {
        if (!searchTerm.trim()) return true
        const searchLower = searchTerm.toLowerCase()
        return (
            company.company_name?.toLowerCase().includes(searchLower) ||
            company.company_email?.toLowerCase().includes(searchLower) ||
            company.company_number?.toLowerCase().includes(searchLower) ||
            company.admin_name?.toLowerCase().includes(searchLower) ||
            company.admin_email?.toLowerCase().includes(searchLower) ||
            company.company_phone?.toLowerCase().includes(searchLower) ||
            company.contact_name?.toLowerCase().includes(searchLower)
        )
    })

    return (
        <div className="container mx-auto py-6 space-y-8">
            {/* Metrics Dashboard Section */}
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h2 className="text-3xl font-bold tracking-tight">Global Usage Metrics</h2>
                        <p className="text-muted-foreground">Overview of card and bin utilization across the system</p>
                    </div>
                    <div className="flex items-center space-x-2">
                        {lastUpdated && (
                            <p className="text-sm text-muted-foreground">Last updated: {lastUpdated.toLocaleTimeString()}</p>
                        )}
                        <Button variant="outline" size="sm" onClick={handleRefresh} disabled={metricsLoading || companiesLoading}>
                            <RefreshCw className={`w-4 h-4 mr-2 ${metricsLoading || companiesLoading ? "animate-spin" : ""}`} />
                            Refresh
                        </Button>
                    </div>
                </div>

                {/* Error Alert */}
                {metricsError && (
                    <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{metricsError}</AlertDescription>
                    </Alert>
                )}

                {/* Metrics Grid */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <MetricCard
                        title="Total Cards"
                        value={metricsData?.totalCards || 0}
                        icon={<CreditCard className="h-4 w-4" />}
                        description="Active cards in system"
                        loading={metricsLoading}
                    />
                    <MetricCard
                        title="Total Bins"
                        value={metricsData?.totalBins || 0}
                        icon={<Database className="h-4 w-4" />}
                        description="Available bin ranges"
                        loading={metricsLoading}
                    />
                    <MetricCard
                        title="Utilization Rate"
                        value={utilizationRate}
                        icon={<Activity className="h-4 w-4" />}
                        description="Cards per bin ratio (%)"
                        trend={{
                            value: 12,
                            isPositive: true,
                        }}
                        loading={metricsLoading}
                    />
                    <MetricCard
                        title="Available Capacity"
                        value={metricsData ? metricsData.totalBins - metricsData.totalCards : 0}
                        icon={<TrendingUp className="h-4 w-4" />}
                        description="Remaining bin capacity"
                        loading={metricsLoading}
                    />
                </div>

                {/* Usage Progress Bar */}
                {!metricsLoading && metricsData && (
                    <Card>
                        <CardHeader>
                            <CardTitle>System Utilization</CardTitle>
                            <CardDescription>Visual representation of current system usage</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                <div className="flex justify-between text-sm">
                                    <span>Cards Used</span>
                                    <span>
                    {metricsData.totalCards.toLocaleString()} / {metricsData.totalBins.toLocaleString()}
                  </span>
                                </div>
                                <div className="w-full bg-secondary rounded-full h-2">
                                    <div
                                        className={`h-2 rounded-full transition-all duration-500 ${
                                            utilizationRate > 80 ? "bg-destructive" : utilizationRate > 60 ? "bg-yellow-500" : "bg-primary"
                                        }`}
                                        style={{ width: `${Math.min(utilizationRate, 100)}%` }}
                                    />
                                </div>
                                <div className="flex justify-between text-xs text-muted-foreground">
                                    <span>0%</span>
                                    <span>{utilizationRate}% utilized</span>
                                    <span>100%</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>

            {/* Company Table Section */}
            <div className="border-t pt-8">
                <div className="space-y-4">
                    {/* Company Table Header */}
                    <CardHeader className="px-6 py-5 flex-row items-center justify-between bg-background">
                        <div>
                            <CardTitle className="text-2xl font-bold">Programme Managers</CardTitle>
                            <CardDescription>{`Total Records: ${companies.length}`}</CardDescription>
                        </div>
                        <div className="flex items-center space-x-2">
                            <div className="relative">
                                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search companies..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-8 bg-white min-w-[250px]"
                                />
                            </div>
                            <DataExporter data={exportData} filename="issuing_pipeline" title="Issuing Pipeline Report" />
                        </div>
                    </CardHeader>

                    {/* Company Table */}
                    {!userHasViewPermission ? (
                        <div className="flex items-center justify-center p-8">
                            <div className="text-center">
                                <h3 className="text-lg font-semibold text-muted-foreground">Access Denied</h3>
                                <p className="text-sm text-muted-foreground">{"You don't have permission to view this data."}</p>
                            </div>
                        </div>
                    ) : (
                        <Card>
                            <CardContent className="p-0">
                                {companiesError && (
                                    <Alert variant="destructive" className="m-4">
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertDescription>{companiesError}</AlertDescription>
                                    </Alert>
                                )}
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Date Created</TableHead>
                                            <TableHead>Company Name</TableHead>
                                            <TableHead>Total BIN</TableHead>
                                            <TableHead>Used</TableHead>
                                            <TableHead>Available</TableHead>
                                            <TableHead>Company Email</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {companiesLoading ? (
                                            Array.from({ length: 5 }).map((_, index) => (
                                                <TableRow key={index}>
                                                    <TableCell>
                                                        <Skeleton className="h-4 w-full" />
                                                    </TableCell>
                                                    <TableCell>
                                                        <Skeleton className="h-4 w-full" />
                                                    </TableCell>
                                                    <TableCell>
                                                        <Skeleton className="h-4 w-full" />
                                                    </TableCell>
                                                    <TableCell>
                                                        <Skeleton className="h-4 w-full" />
                                                    </TableCell>
                                                    <TableCell>
                                                        <Skeleton className="h-4 w-full" />
                                                    </TableCell>
                                                    <TableCell>
                                                        <Skeleton className="h-4 w-full" />
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        ) : filteredCompanies.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                                                    No companies found
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            filteredCompanies.map((company) => {
                                                const usage = getCompanyUsage(company._id)
                                                return (
                                                    <TableRow
                                                        key={company._id}
                                                        className="cursor-pointer hover:bg-muted/50"
                                                        onClick={() => {
                                                            window.location.href = `inventory/${company._id}/products`
                                                        }}
                                                    >
                                                        <TableCell>{formatDate(company.created_at)}</TableCell>
                                                        <TableCell>
                                                            <Link href="#" className="font-bold hover:underline">
                                                                {company.company_name}
                                                            </Link>
                                                        </TableCell>
                                                        <TableCell>
                                                            <Badge variant="outline">{usage.totalBins.toLocaleString()}</Badge>
                                                        </TableCell>
                                                        <TableCell>
                                                            <Badge variant={usage.usedBins > 0 ? "default" : "secondary"}>
                                                                {usage?.usedBins }
                                                            </Badge>
                                                        </TableCell>
                                                        <TableCell>
                                                            <Badge variant="outline" className="text-green-600">
                                                                {usage?.totalBins - usage?.usedBins }
                                                            </Badge>
                                                        </TableCell>
                                                        <TableCell>{company.company_email}</TableCell>
                                                    </TableRow>
                                                )
                                            })
                                        )}
                                    </TableBody>
                                </Table>
                            </CardContent>
                        </Card>
                    )}

                    {/* Table Summary */}
                    <div className="text-sm text-muted-foreground text-center">
                        Showing {filteredCompanies.length} of {companies.length} companies
                    </div>
                </div>
            </div>
        </div>
    )
}
