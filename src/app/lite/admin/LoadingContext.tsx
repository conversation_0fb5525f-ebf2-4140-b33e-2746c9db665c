'use client'

import React, { createContext, useContext, useState, ReactNode } from 'react'
import { Loader2 } from 'lucide-react'
import { toast } from 'sonner'

interface LoadingContextType {
    isLoading: boolean
    setIsLoading: (isLoading: boolean) => void
    showSuccessToast: (message: string) => void
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined)

export const useLoading = () => {
    const context = useContext(LoadingContext)
    if (!context) {
        throw new Error('useLoading must be used within a LoadingProvider')
    }
    return context
}

export const LoadingProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [isLoading, setIsLoading] = useState(false)

    const showSuccessToast = (message: string) => {
        toast.success(message)
    }

    return (
        <LoadingContext.Provider value={{ isLoading, setIsLoading, showSuccessToast }}>
            {isLoading && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <Loader2 className="h-8 w-8 animate-spin text-white" />
                </div>
            )}
            {children}
        </LoadingContext.Provider>
    )
}