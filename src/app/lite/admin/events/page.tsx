// @ts-nocheck
'use client'
import React, {Suspense, useEffect, useMemo, useState} from 'react'
import {
    CaretSortIcon,
    MagnifyingGlassIcon,
    CheckIcon,
    ClockIcon,
    ExclamationTriangleIcon,
    GlobeIcon
} from '@radix-ui/react-icons'
import {
    ColumnDef,
    ColumnFiltersState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    SortingState,
    useReactTable,
    VisibilityState,
} from '@tanstack/react-table'
import {Button} from '@/components/ui/button'
import {Checkbox} from '@/components/ui/checkbox'
import {Input} from '@/components/ui/input'
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from '@/components/ui/table'
import {Badge} from '@/components/ui/badge'
import {Tabs, TabsContent, TabsList, TabsTrigger} from "@/components/ui/tabs"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip"
import {Avatar, AvatarFallback, AvatarImage} from "@/components/ui/avatar"
import {Separator} from "@/components/ui/separator"
import axiosInstance from "@/utils/axiosInstance"
import Link from "next/link"
import {formatDate} from "@/utils/helpers"
import {LoadingOverlay} from "@/components/LoadingOverlay"
import {Textarea} from "@/components/ui/textarea"

type Task = {
    id: string
    type: string
    title: string
    status: string
    date: string
    user: {
        email: string
        name: string
    }
    ipAddress: string
    _id: string
    createdAt: string
    declineReason?: string
    modificationReason?: string
}

const typeRouteMap = {
    'Card Scheme': '/lite/admin/configurations/card-scheme',
    'Card Programme Type': '/lite/admin/configurations/card-programme-type',
    'BIN Type': '/lite/admin/configurations/bin-type',
    'Issuing Client Type': '/lite/admin/configurations/issuing-client-type',
    'Currency': '/lite/admin/configurations/currency',
    'Country': '/lite/admin/configurations/country',
    'Product Version': '/lite/admin/configurations/product-versions',
    'BIN Variant': '/lite/admin/configurations/bin-variants',
    'BIN Category': '/lite/admin/configurations/bin-category',
    'BIN Usage': '/lite/admin/configurations/bin-usage',
    'Programme Manager Type': '/lite/admin/configurations/programme-manager-type',
    'Bin Range': '/lite/admin/configurations/bin-range',
    'Programme Type': '/lite/admin/configurations/programme-type',
}

const allowModify = ['Card Scheme', 'Card Programme Type', 'BIN Variant', 'BIN Category', "BIN Usage"]

const StatusBadge = ({status}: { status: string }) => {
    const getStatusConfig = (status: string) => {
        switch (status) {
            case 'In Progress':
                return {color: 'bg-blue-100 text-blue-800 border-blue-200', icon: ClockIcon}
            case 'Backlog':
                return {color: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: ExclamationTriangleIcon}
            case 'Todo':
                return {color: 'bg-red-100 text-red-800 border-red-200', icon: ExclamationTriangleIcon}
            case 'Done':
                return {color: 'bg-green-100 text-green-800 border-green-200', icon: CheckIcon}
            case 'Declined':
                return {color: 'bg-red-100 text-red-800 border-red-200', icon: ExclamationTriangleIcon}
            case 'Modification Required':
                return {color: 'bg-orange-100 text-orange-800 border-orange-200', icon: ClockIcon}
            case 'Canceled':
                return {color: 'bg-gray-100 text-gray-800 border-gray-200', icon: ExclamationTriangleIcon}
            default:
                return {color: 'bg-gray-100 text-gray-800 border-gray-200', icon: ClockIcon}
        }
    }
    const config = getStatusConfig(status)
    const Icon = config.icon
    return (
        <Badge variant="outline" className={`${config.color} font-medium`}>
            <Icon className="w-3 h-3 mr-1"/>
            {status}
        </Badge>
    )
}

const IPAddressBadge = ({ipAddress}: { ipAddress: string }) => {
    const isPrivateIP = (ip: string) => {
        const parts = ip.split('.').map(Number)
        if (parts.length !== 4) return false
        // Check for private IP ranges
        return (
            (parts[0] === 10) ||
            (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) ||
            (parts[0] === 192 && parts[1] === 168) ||
            (parts[0] === 127) // localhost
        )
    }
    const isPrivate = isPrivateIP(ipAddress)
    return (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild>
                    <div className={`inline-flex items-center gap-1.5 px-2 py-1 rounded-md text-xs font-mono border ${
                        isPrivate
                            ? 'bg-blue-50 text-blue-700 border-blue-200'
                            : 'bg-orange-50 text-orange-700 border-orange-200'
                    }`}>
                        <GlobeIcon className="w-3 h-3"/>
                        {ipAddress}
                    </div>
                </TooltipTrigger>
                <TooltipContent>
                    <p>{isPrivate ? 'Private IP Address' : 'Public IP Address'}</p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    )
}

const TaskCard = ({task, onComplete, onDecline, onModify, onNavigate}: {
    task: Task,
    onComplete: (task: Task) => void,
    onDecline: (task: Task) => void,
    onModify: (task: Task) => void,
    onNavigate: (type: string) => void
}) => {
    const regex = /^(.*?)"([^"]+)"/
    const match = task.title.match(regex)
    return (
        <Card
            className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-primary/20 hover:border-l-primary/40">
            <CardHeader className="pb-4">
                <div className="flex items-start justify-between gap-4">
                    <div className="space-y-2 flex-1">
                        <CardTitle className="text-base leading-6 line-clamp-2">
                            {match ? (
                                <>
                                    {match[1]} <span className="text-primary font-semibold">"{match[2]}"</span>
                                </>
                            ) : (
                                task.title
                            )}
                        </CardTitle>
                        <div className="flex items-center gap-2 flex-wrap">
                            {typeRouteMap[task.type] ? (
                                <Link href={typeRouteMap[task.type]}>
                                    <Badge variant="secondary"
                                           className="text-xs hover:bg-secondary/80 transition-colors">
                                        {task.type}
                                    </Badge>
                                </Link>
                            ) : (
                                <Badge variant="secondary" className="text-xs">
                                    {task.type}
                                </Badge>
                            )}
                            <StatusBadge status={task.status}/>
                        </div>
                    </div>
                    <div className="flex gap-2 flex-wrap">
                        <Button
                            size="sm"
                            onClick={() => onComplete(task)}
                            disabled={task.status === 'Done'}
                            className="flex-1"
                            variant={task.status === 'Done' ? 'secondary' : 'default'}
                        >
                            {task.status === 'Done' ? 'Completed' : 'Complete'}
                        </Button>
                        {task.status !== 'Done' && (
                            <>
                                <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => onDecline(task)}
                                    className="flex-1"
                                >
                                    Decline
                                </Button>
                                {allowModify.includes(task.type) && (
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => onModify(task)}
                                        className="flex-1"
                                    >
                                        Modify
                                    </Button>
                                )}
                            </>
                        )}
                        {typeRouteMap[task.type] && (
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={() => onNavigate(task.type)}
                                className="w-full mt-2"
                            >
                                Go to {task.type} Page
                            </Button>
                        )}
                    </div>
                </div>
            </CardHeader>
            <CardContent className="pt-0">
                <div className="space-y-4">
                    <Separator/>
                    {/* User Information */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <Avatar className="w-8 h-8">
                                <AvatarFallback className="text-xs bg-primary/10 text-primary">
                                    {task.user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                                </AvatarFallback>
                            </Avatar>
                            <div>
                                <div className="font-medium text-sm">{task.user.name}</div>
                                <div className="text-xs text-muted-foreground">{task.user.email}</div>
                            </div>
                        </div>
                        <div className="text-right text-sm text-muted-foreground">
                            {formatDate(task.createdAt)}
                        </div>
                    </div>
                    {/* IP Address Information */}
                    <div className="flex items-center justify-between pt-2">
                        <div className="flex items-center gap-2">
                            <span className="text-xs text-muted-foreground font-medium">Origin:</span>
                            <IPAddressBadge ipAddress={task.ipAddress}/>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}

const TaskTable = React.memo(({data, columns}: { data: Task[], columns: ColumnDef<Task>[] }) => {
    const [sorting, setSorting] = useState<SortingState>([])
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
    const [rowSelection, setRowSelection] = useState({})

    const table = useReactTable({
        data,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
        },
    })

    return (
        <div className="space-y-4">
            <Card className="overflow-hidden">
                <Table>
                    <TableHeader className="bg-muted/30">
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id} className="border-b hover:bg-transparent">
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id} className="font-semibold text-foreground">
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={row.getIsSelected() && 'selected'}
                                    className="hover:bg-muted/30 transition-colors"
                                >
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id} className="py-4">
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={columns.length} className="h-32 text-center">
                                    <div className="flex flex-col items-center gap-3 text-muted-foreground">
                                        <div
                                            className="w-12 h-12 rounded-full bg-muted flex items-center justify-center">
                                            <MagnifyingGlassIcon className="w-6 h-6"/>
                                        </div>
                                        <div>
                                            <div className="text-lg font-medium">No tasks found</div>
                                            <div className="text-sm">Try adjusting your search or filter criteria</div>
                                        </div>
                                    </div>
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </Card>
            {table.getRowModel().rows?.length > 0 && (
                <div className="flex items-center justify-between bg-muted/20 rounded-lg p-4">
                    <div className="text-sm text-muted-foreground">
                        {table.getFilteredSelectedRowModel().rows.length} of{' '}
                        {table.getFilteredRowModel().rows.length} row(s) selected.
                    </div>
                    <div className="flex items-center space-x-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => table.previousPage()}
                            disabled={!table.getCanPreviousPage()}
                        >
                            Previous
                        </Button>
                        <div className="text-sm text-muted-foreground px-2">
                            Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
                        </div>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => table.nextPage()}
                            disabled={!table.getCanNextPage()}
                        >
                            Next
                        </Button>
                    </div>
                </div>
            )}
        </div>
    )
})

export default function TaskManager() {
    const [events, setEvents] = useState<Task[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [activeTab, setActiveTab] = useState("all")
    const [viewMode, setViewMode] = useState<'table' | 'cards'>('table')
    const [isConfirmOpen, setIsConfirmOpen] = useState(false)
    const [taskToComplete, setTaskToComplete] = useState<Task | null>(null)
    const [searchQuery, setSearchQuery] = useState("")
    const [statusFilter, setStatusFilter] = useState<string>("all")
    const [isDeclineOpen, setIsDeclineOpen] = useState(false)
    const [isModifyOpen, setIsModifyOpen] = useState(false)
    const [taskToDecline, setTaskToDecline] = useState<Task | null>(null)
    const [taskToModify, setTaskToModify] = useState<Task | null>(null)
    const [declineReason, setDeclineReason] = useState("")
    const [modifyReason, setModifyReason] = useState("")

    useEffect(() => {
        fetchOnboardingDetails()
    }, [])

    const fetchOnboardingDetails = async () => {
        try {
            setLoading(true)
            const response = await axiosInstance.get(`events`)
            setEvents(response.data)
            setError(null)
        } catch (error) {
            console.error('Error fetching details:', error)
            setError('Failed to fetch events. Please try again later.')
        } finally {
            setLoading(false)
        }
    }

    const navigateToConfigPage = (type: string) => {
        const route = typeRouteMap[type]
        if (route) {
            window.open(route, '_blank')
        }
    }

    const columns: ColumnDef<Task>[] = useMemo(() => [
        {
            id: 'select',
            header: ({table}) => (
                <Checkbox
                    checked={table.getIsAllPageRowsSelected()}
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                />
            ),
            cell: ({row}) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: 'type',
            header: 'Type',
            cell: ({row}) => {
                const type = row.getValue('type') as string
                const route = typeRouteMap[type]
                if (route) {
                    return (
                        <Link href={route}>
                            <Badge variant="secondary"
                                   className="hover:bg-secondary/80 cursor-pointer transition-colors">
                                {type}
                            </Badge>
                        </Link>
                    )
                }
                return (
                    <Badge variant="secondary">
                        {type}
                    </Badge>
                )
            },
        },
        {
            accessorKey: 'title',
            header: ({column}) => {
                return (
                    <Button
                        variant="ghost"
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                        className="h-auto p-0 font-semibold hover:bg-transparent"
                    >
                        Title
                        <CaretSortIcon className="ml-2 h-4 w-4"/>
                    </Button>
                )
            },
            cell: ({row}) => {
                const title = row.getValue('title') as string
                // Regex to match all quoted parts
                const regex = /"([^"]+)"/g
                // Split the string and build parts
                const parts: React.ReactNode[] = []
                let lastIndex = 0
                let match: RegExpExecArray | null
                while ((match = regex.exec(title)) !== null) {
                    const quotedText = match[1]
                    const startIndex = match.index
                    const endIndex = regex.lastIndex
                    // Push text before quoted string
                    if (startIndex > lastIndex) {
                        parts.push(title.slice(lastIndex, startIndex))
                    }
                    // Push quoted string with primary color
                    parts.push(<span key={startIndex} className="text-primary">"{quotedText}"</span>)
                    // Update lastIndex
                    lastIndex = endIndex
                }
                // Push remaining text after last match
                if (lastIndex < title.length) {
                    parts.push(title.slice(lastIndex))
                }
                return (
                    <div className="max-w-[400px] font-medium">
                        {parts}
                    </div>
                )
            },
        },
        {
            accessorKey: 'status',
            header: 'Status',
            cell: ({row}) => {
                const status = row.getValue('status') as string
                return <StatusBadge status={status}/>
            },
        },
        {
            accessorKey: 'user',
            header: 'Requester',
            cell: ({row}) => {
                const user = row.getValue('user') as Task['user']
                return (
                    <div className="flex items-center gap-3">
                        <Avatar className="w-8 h-8">
                            <AvatarFallback className="text-xs bg-primary/10 text-primary">
                                {user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                            </AvatarFallback>
                        </Avatar>
                        <div>
                            <div className="font-medium">{user.name}</div>
                            <div className="text-sm text-muted-foreground">{user.email}</div>
                        </div>
                    </div>
                )
            },
        },
        {
            accessorKey: 'ipAddress',
            header: ({column}) => {
                return (
                    <Button
                        variant="ghost"
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                        className="h-auto p-0 font-semibold hover:bg-transparent"
                    >
                        IP Address
                        <CaretSortIcon className="ml-2 h-4 w-4"/>
                    </Button>
                )
            },
            cell: ({row}) => {
                const ipAddress = row.getValue('ipAddress') as string
                return <IPAddressBadge ipAddress={ipAddress}/>
            },
        },
        {
            accessorKey: 'createdAt',
            header: ({column}) => {
                return (
                    <Button
                        variant="ghost"
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                        className="h-auto p-0 font-semibold hover:bg-transparent"
                    >
                        Date
                        <CaretSortIcon className="ml-2 h-4 w-4"/>
                    </Button>
                )
            },
            cell: ({row}) => (
                <div className="font-medium">{formatDate(row.getValue('createdAt'))}</div>
            ),
        },
        {
            id: 'actions',
            header: 'Actions',
            cell: ({row}) => {
                const task = row.original
                return (
                    <div className="flex gap-2 flex-wrap">
                        <Button
                            size="sm"
                            onClick={() => handleCompleteTask(task)}
                            disabled={task.status === 'Done'}
                            variant={task.status === 'Done' ? 'secondary' : 'default'}
                        >
                            {task.status === 'Done' ? 'Completed' : 'Complete'}
                        </Button>
                        {task.status !== 'Done' && (
                            <>
                                <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleDeclineTask(task)}
                                >
                                    Decline
                                </Button>
                                {allowModify.includes(task.type) && ( // Added conditional rendering here
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleModifyTask(task)}
                                    >
                                        Modify
                                    </Button>
                                )}
                            </>
                        )}
                        <Button
                            size="sm"
                            variant="outline"
                            onClick={() => navigateToConfigPage(task.type)}
                            title={`Go to ${task.type} configuration page`}
                        >
                            View Page
                        </Button>
                    </div>
                )
            },
        },
    ], [])

    const handleCompleteTask = (task: Task) => {
        setTaskToComplete(task)
        setIsConfirmOpen(true)
    }

    const confirmCompleteTask = async () => {
        if (taskToComplete) {
            try {
                await axiosInstance.put(`events/${taskToComplete._id}/complete`, {refId: taskToComplete._id})
                fetchOnboardingDetails()
                setIsConfirmOpen(false)
                setTaskToComplete(null)
            } catch (error) {
                console.error('Error completing task:', error)
                setError('Failed to complete task. Please try again later.')
            }
        }
    }

    const handleDeclineTask = (task: Task) => {
        setTaskToDecline(task)
        setDeclineReason("")
        setIsDeclineOpen(true)
    }

    const handleModifyTask = (task: Task) => {
        setTaskToModify(task)
        setModifyReason("")
        setIsModifyOpen(true)
    }

    const confirmDeclineTask = async () => {
        if (taskToDecline && declineReason.trim()) {
            try {
                await axiosInstance.put(`events/${taskToDecline._id}/decline`, {
                    refId: taskToDecline._id,
                    reason: declineReason.trim()
                })
                fetchOnboardingDetails()
                setIsDeclineOpen(false)
                setTaskToDecline(null)
                setDeclineReason("")
            } catch (error) {
                console.error('Error declining task:', error)
                setError('Failed to decline task. Please try again later.')
            }
        }
    }

    const confirmModifyTask = async () => {
        if (taskToModify && modifyReason.trim()) {
            try {
                await axiosInstance.put(`events/${taskToModify._id}/modify`, {
                    refId: taskToModify._id,
                    reason: modifyReason.trim()
                })
                fetchOnboardingDetails()
                setIsModifyOpen(false)
                setTaskToModify(null)
                setModifyReason("")
            } catch (error) {
                console.error('Error modifying task:', error)
                setError('Failed to modify task. Please try again later.')
            }
        }
    }

    const filteredEvents = useMemo(() => {
        return events.filter(task => {
            const matchesSearch = task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                task.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                task.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
                task.ipAddress.toLowerCase().includes(searchQuery.toLowerCase())
            const matchesStatus = statusFilter === 'all' || task.status === statusFilter
            return matchesSearch && matchesStatus
        })
    }, [events, searchQuery, statusFilter])

    const stats = useMemo(() => {
        const total = events.length
        const todo = events.filter(task => task.status === 'Todo').length
        const inProgress = events.filter(task => task.status === 'In Progress').length
        const backlog = events.filter(task => task.status === 'Backlog').length
        const done = events.filter(task => task.status === 'Done').length
        const declined = events.filter(task => task.status === 'Declined').length
        const modificationRequired = events.filter(task => task.status === 'Modification Required').length
        const canceled = events.filter(task => task.status === 'Canceled').length
        const uniqueIPs = new Set(events.map(task => task.ipAddress)).size
        return {total, todo, inProgress, backlog, done, declined, modificationRequired, canceled, uniqueIPs}
    }, [events])

    const allTasks = useMemo(() => filteredEvents, [filteredEvents])
    const todoTasks = useMemo(() => filteredEvents.filter(task => task.status === 'Todo'), [filteredEvents])
    const inProgressTasks = useMemo(() => filteredEvents.filter(task => task.status === 'In Progress'), [filteredEvents])
    const backlogTasks = useMemo(() => filteredEvents.filter(task => task.status === 'Backlog'), [filteredEvents])
    const doneTasks = useMemo(() => filteredEvents.filter(task => task.status === 'Done'), [filteredEvents])
    const declinedTasks = useMemo(() => filteredEvents.filter(task => task.status === 'Declined'), [filteredEvents])
    const modificationTasks = useMemo(() => filteredEvents.filter(task => task.status === 'Modification Required'), [filteredEvents])
    const canceledTasks = useMemo(() => filteredEvents.filter(task => task.status === 'Canceled'), [filteredEvents])

    if (loading) {
        return <LoadingOverlay/>
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/30">
            <div className="container mx-auto p-6 space-y-8">
                {/* Header Section */}
                <div className="space-y-6">
                    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                        <div className="space-y-2">
                            <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
                                Event Requests
                            </h1>
                            <p className="text-lg text-muted-foreground">
                                Manage and approve configuration requests with full audit trail
                            </p>
                        </div>
                        <div className="flex items-center gap-3">
                            <Select value={viewMode} onValueChange={(value: 'table' | 'cards') => setViewMode(value)}>
                                <SelectTrigger className="w-32">
                                    <SelectValue/>
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="table">Table View</SelectItem>
                                    <SelectItem value="cards">Card View</SelectItem>
                                </SelectContent>
                            </Select>
                            <Button onClick={fetchOnboardingDetails} variant="outline" className="gap-2">
                                <GlobeIcon className="w-4 h-4"/>
                                Refresh
                            </Button>
                        </div>
                    </div>
                    {/* Enhanced Stats Cards */}
                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
                        <Card className="bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium text-blue-700">Total</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-blue-900">{stats.total}</div>
                            </CardContent>
                        </Card>
                        <Card className="bg-gradient-to-br from-red-50 to-red-100/50 border-red-200">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium text-red-700">Todo</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-red-900">{stats.todo}</div>
                            </CardContent>
                        </Card>
                        <Card className="bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium text-blue-700">In Progress</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-blue-900">{stats.inProgress}</div>
                            </CardContent>
                        </Card>
                        <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100/50 border-yellow-200">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium text-yellow-700">Backlog</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-yellow-900">{stats.backlog}</div>
                            </CardContent>
                        </Card>
                        <Card className="bg-gradient-to-br from-green-50 to-green-100/50 border-green-200">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium text-green-700">Done</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-green-900">{stats.done}</div>
                            </CardContent>
                        </Card>
                        <Card className="bg-gradient-to-br from-red-50 to-red-100/50 border-red-200">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium text-red-700">Declined</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-red-900">{stats.declined}</div>
                            </CardContent>
                        </Card>
                        <Card className="bg-gradient-to-br from-orange-50 to-orange-100/50 border-orange-200">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium text-orange-700">Needs Changes</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-orange-900">{stats.modificationRequired}</div>
                            </CardContent>
                        </Card>
                        <Card className="bg-gradient-to-br from-gray-50 to-gray-100/50 border-gray-200">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium text-gray-700">Canceled</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">{stats.canceled}</div>
                            </CardContent>
                        </Card>
                        <Card className="bg-gradient-to-br from-purple-50 to-purple-100/50 border-purple-200">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium text-purple-700">Unique IPs</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-purple-900">{stats.uniqueIPs}</div>
                            </CardContent>
                        </Card>
                    </div>
                    {/* Enhanced Filters */}
                    <Card className="p-6 bg-gradient-to-r from-background to-muted/20">
                        <div className="flex flex-col gap-4 md:flex-row md:items-center">
                            <div className="relative flex-1">
                                <MagnifyingGlassIcon
                                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"/>
                                <Input
                                    placeholder="Search tasks, users, types, or IP addresses..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="pl-10 bg-background/50"
                                />
                            </div>
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger className="w-48 bg-background/50">
                                    <SelectValue placeholder="Filter by status"/>
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Statuses</SelectItem>
                                    <SelectItem value="Todo">Todo</SelectItem>
                                    <SelectItem value="In Progress">In Progress</SelectItem>
                                    <SelectItem value="Backlog">Backlog</SelectItem>
                                    <SelectItem value="Done">Done</SelectItem>
                                    <SelectItem value="Declined">Declined</SelectItem>
                                    <SelectItem value="Modification Required">Modification Required</SelectItem>
                                    <SelectItem value="Canceled">Canceled</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </Card>
                    {error && (
                        <Card className="border-destructive/50 bg-destructive/5">
                            <CardContent className="pt-6">
                                <p className="text-destructive font-medium">{error}</p>
                            </CardContent>
                        </Card>
                    )}
                </div>
                {/* Main Content */}
                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                    <div className="overflow-x-auto">
                        <TabsList className="grid w-full grid-cols-8 lg:w-full bg-muted/50 min-w-[800px]">
                            <TabsTrigger value="all" className="flex items-center gap-2 text-xs">
                                All
                                <Badge variant="secondary" className="ml-1 bg-blue-100 text-blue-800 text-xs">
                                    {allTasks.length}
                                </Badge>
                            </TabsTrigger>
                            <TabsTrigger value="todo" className="flex items-center gap-2 text-xs">
                                Todo
                                <Badge variant="secondary" className="ml-1 bg-red-100 text-red-800 text-xs">
                                    {todoTasks.length}
                                </Badge>
                            </TabsTrigger>
                            <TabsTrigger value="inprogress" className="flex items-center gap-2 text-xs">
                                In Progress
                                <Badge variant="secondary" className="ml-1 bg-blue-100 text-blue-800 text-xs">
                                    {inProgressTasks.length}
                                </Badge>
                            </TabsTrigger>
                            <TabsTrigger value="backlog" className="flex items-center gap-2 text-xs">
                                Backlog
                                <Badge variant="secondary" className="ml-1 bg-yellow-100 text-yellow-800 text-xs">
                                    {backlogTasks.length}
                                </Badge>
                            </TabsTrigger>
                            <TabsTrigger value="done" className="flex items-center gap-2 text-xs">
                                Done
                                <Badge variant="secondary" className="ml-1 bg-green-100 text-green-800 text-xs">
                                    {doneTasks.length}
                                </Badge>
                            </TabsTrigger>
                            <TabsTrigger value="declined" className="flex items-center gap-2 text-xs">
                                Declined
                                <Badge variant="secondary" className="ml-1 bg-red-100 text-red-800 text-xs">
                                    {declinedTasks.length}
                                </Badge>
                            </TabsTrigger>
                            <TabsTrigger value="modification" className="flex items-center gap-2 text-xs">
                                Changes
                                <Badge variant="secondary" className="ml-1 bg-orange-100 text-orange-800 text-xs">
                                    {modificationTasks.length}
                                </Badge>
                            </TabsTrigger>
                            <TabsTrigger value="canceled" className="flex items-center gap-2 text-xs">
                                Canceled
                                <Badge variant="secondary" className="ml-1 bg-gray-100 text-gray-800 text-xs">
                                    {canceledTasks.length}
                                </Badge>
                            </TabsTrigger>
                        </TabsList>
                    </div>
                    <TabsContent value="all" className="space-y-6">
                        <Suspense fallback={<div>Loading all tasks...</div>}>
                            {viewMode === 'table' ? (
                                <TaskTable data={allTasks} columns={columns}/>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {allTasks.map((task) => (
                                        <TaskCard
                                            key={task._id}
                                            task={task}
                                            onComplete={handleCompleteTask}
                                            onDecline={handleDeclineTask}
                                            onModify={handleModifyTask}
                                            onNavigate={navigateToConfigPage}
                                        />
                                    ))}
                                    {allTasks.length === 0 && (
                                        <div className="col-span-full">
                                            <Card className="p-12 text-center">
                                                <div className="space-y-3">
                                                    <div
                                                        className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto">
                                                        <MagnifyingGlassIcon className="w-8 h-8 text-muted-foreground"/>
                                                    </div>
                                                    <div className="text-lg font-medium">No tasks found</div>
                                                    <div className="text-muted-foreground">Try adjusting your search or
                                                        filter criteria.
                                                    </div>
                                                </div>
                                            </Card>
                                        </div>
                                    )}
                                </div>
                            )}
                        </Suspense>
                    </TabsContent>
                    <TabsContent value="todo" className="space-y-6">
                        <Suspense fallback={<div>Loading todo tasks...</div>}>
                            {viewMode === 'table' ? (
                                <TaskTable data={todoTasks} columns={columns}/>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {todoTasks.map((task) => (
                                        <TaskCard
                                            key={task._id}
                                            task={task}
                                            onComplete={handleCompleteTask}
                                            onDecline={handleDeclineTask}
                                            onModify={handleModifyTask}
                                            onNavigate={navigateToConfigPage}
                                        />
                                    ))}
                                    {todoTasks.length === 0 && (
                                        <div className="col-span-full">
                                            <Card className="p-12 text-center">
                                                <div className="space-y-3">
                                                    <div
                                                        className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mx-auto">
                                                        <ExclamationTriangleIcon className="w-8 h-8 text-red-600"/>
                                                    </div>
                                                    <div className="text-lg font-medium">No todo tasks</div>
                                                    <div className="text-muted-foreground">All todo tasks have been
                                                        processed.
                                                    </div>
                                                </div>
                                            </Card>
                                        </div>
                                    )}
                                </div>
                            )}
                        </Suspense>
                    </TabsContent>
                    <TabsContent value="inprogress" className="space-y-6">
                        <Suspense fallback={<div>Loading in progress tasks...</div>}>
                            {viewMode === 'table' ? (
                                <TaskTable data={inProgressTasks} columns={columns}/>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {inProgressTasks.map((task) => (
                                        <TaskCard
                                            key={task._id}
                                            task={task}
                                            onComplete={handleCompleteTask}
                                            onDecline={handleDeclineTask}
                                            onModify={handleModifyTask}
                                            onNavigate={navigateToConfigPage}
                                        />
                                    ))}
                                    {inProgressTasks.length === 0 && (
                                        <div className="col-span-full">
                                            <Card className="p-12 text-center">
                                                <div className="space-y-3">
                                                    <div
                                                        className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center mx-auto">
                                                        <ClockIcon className="w-8 h-8 text-blue-600"/>
                                                    </div>
                                                    <div className="text-lg font-medium">No tasks in progress</div>
                                                    <div className="text-muted-foreground">Tasks currently being worked
                                                        on will appear here.
                                                    </div>
                                                </div>
                                            </Card>
                                        </div>
                                    )}
                                </div>
                            )}
                        </Suspense>
                    </TabsContent>
                    <TabsContent value="backlog" className="space-y-6">
                        <Suspense fallback={<div>Loading backlog tasks...</div>}>
                            {viewMode === 'table' ? (
                                <TaskTable data={backlogTasks} columns={columns}/>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {backlogTasks.map((task) => (
                                        <TaskCard
                                            key={task._id}
                                            task={task}
                                            onComplete={handleCompleteTask}
                                            onDecline={handleDeclineTask}
                                            onModify={handleModifyTask}
                                            onNavigate={navigateToConfigPage}
                                        />
                                    ))}
                                    {backlogTasks.length === 0 && (
                                        <div className="col-span-full">
                                            <Card className="p-12 text-center">
                                                <div className="space-y-3">
                                                    <div
                                                        className="w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mx-auto">
                                                        <ExclamationTriangleIcon className="w-8 h-8 text-yellow-600"/>
                                                    </div>
                                                    <div className="text-lg font-medium">No backlog tasks</div>
                                                    <div className="text-muted-foreground">Tasks waiting to be
                                                        prioritized will appear here.
                                                    </div>
                                                </div>
                                            </Card>
                                        </div>
                                    )}
                                </div>
                            )}
                        </Suspense>
                    </TabsContent>
                    <TabsContent value="done" className="space-y-6">
                        <Suspense fallback={<div>Loading completed tasks...</div>}>
                            {viewMode === 'table' ? (
                                <TaskTable data={doneTasks} columns={columns}/>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {doneTasks.map((task) => (
                                        <TaskCard
                                            key={task._id}
                                            task={task}
                                            onComplete={handleCompleteTask}
                                            onDecline={handleDeclineTask}
                                            onModify={handleModifyTask}
                                            onNavigate={navigateToConfigPage}
                                        />
                                    ))}
                                    {doneTasks.length === 0 && (
                                        <div className="col-span-full">
                                            <Card className="p-12 text-center">
                                                <div className="space-y-3">
                                                    <div
                                                        className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mx-auto">
                                                        <CheckIcon className="w-8 h-8 text-green-600"/>
                                                    </div>
                                                    <div className="text-lg font-medium">No completed tasks</div>
                                                    <div className="text-muted-foreground">Completed tasks will appear
                                                        here.
                                                    </div>
                                                </div>
                                            </Card>
                                        </div>
                                    )}
                                </div>
                            )}
                        </Suspense>
                    </TabsContent>
                    <TabsContent value="declined" className="space-y-6">
                        <Suspense fallback={<div>Loading declined tasks...</div>}>
                            {viewMode === 'table' ? (
                                <TaskTable data={declinedTasks} columns={columns}/>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {declinedTasks.map((task) => (
                                        <TaskCard
                                            key={task._id}
                                            task={task}
                                            onComplete={handleCompleteTask}
                                            onDecline={handleDeclineTask}
                                            onModify={handleModifyTask}
                                            onNavigate={navigateToConfigPage}
                                        />
                                    ))}
                                    {declinedTasks.length === 0 && (
                                        <div className="col-span-full">
                                            <Card className="p-12 text-center">
                                                <div className="space-y-3">
                                                    <div
                                                        className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mx-auto">
                                                        <ExclamationTriangleIcon className="w-8 h-8 text-red-600"/>
                                                    </div>
                                                    <div className="text-lg font-medium">No declined tasks</div>
                                                    <div className="text-muted-foreground">Declined tasks will appear
                                                        here.
                                                    </div>
                                                </div>
                                            </Card>
                                        </div>
                                    )}
                                </div>
                            )}
                        </Suspense>
                    </TabsContent>
                    <TabsContent value="modification" className="space-y-6">
                        <Suspense fallback={<div>Loading modification tasks...</div>}>
                            {viewMode === 'table' ? (
                                <TaskTable data={modificationTasks} columns={columns}/>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {modificationTasks.map((task) => (
                                        <TaskCard
                                            key={task._id}
                                            task={task}
                                            onComplete={handleCompleteTask}
                                            onDecline={handleDeclineTask}
                                            onModify={handleModifyTask}
                                            onNavigate={navigateToConfigPage}
                                        />
                                    ))}
                                    {modificationTasks.length === 0 && (
                                        <div className="col-span-full">
                                            <Card className="p-12 text-center">
                                                <div className="space-y-3">
                                                    <div
                                                        className="w-16 h-16 rounded-full bg-orange-100 flex items-center justify-center mx-auto">
                                                        <ClockIcon className="w-8 h-8 text-orange-600"/>
                                                    </div>
                                                    <div className="text-lg font-medium">No modification requests</div>
                                                    <div className="text-muted-foreground">Tasks requiring changes will
                                                        appear here.
                                                    </div>
                                                </div>
                                            </Card>
                                        </div>
                                    )}
                                </div>
                            )}
                        </Suspense>
                    </TabsContent>
                    <TabsContent value="canceled" className="space-y-6">
                        <Suspense fallback={<div>Loading canceled tasks...</div>}>
                            {viewMode === 'table' ? (
                                <TaskTable data={canceledTasks} columns={columns}/>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {canceledTasks.map((task) => (
                                        <TaskCard
                                            key={task._id}
                                            task={task}
                                            onComplete={handleCompleteTask}
                                            onDecline={handleDeclineTask}
                                            onModify={handleModifyTask}
                                            onNavigate={navigateToConfigPage}
                                        />
                                    ))}
                                    {canceledTasks.length === 0 && (
                                        <div className="col-span-full">
                                            <Card className="p-12 text-center">
                                                <div className="space-y-3">
                                                    <div
                                                        className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mx-auto">
                                                        <ExclamationTriangleIcon className="w-8 h-8 text-gray-600"/>
                                                    </div>
                                                    <div className="text-lg font-medium">No canceled tasks</div>
                                                    <div className="text-muted-foreground">Canceled tasks will appear
                                                        here.
                                                    </div>
                                                </div>
                                            </Card>
                                        </div>
                                    )}
                                </div>
                            )}
                        </Suspense>
                    </TabsContent>
                </Tabs>
                {/* Enhanced Confirmation Dialog */}
                <AlertDialog open={isConfirmOpen} onOpenChange={setIsConfirmOpen}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle className="flex items-center gap-2">
                                <CheckIcon className="w-5 h-5 text-primary-600"/>
                                Complete Task
                            </AlertDialogTitle>
                            <AlertDialogDescription className="space-y-2">
                                <p>Are you sure you want to mark this task as complete?</p>
                                {taskToComplete && (
                                    <div className="p-3 bg-muted/50 rounded-lg space-y-1">
                                        <div className="font-medium text-sm">{taskToComplete.title}</div>
                                        <div className="text-xs text-muted-foreground">
                                            Requested by {taskToComplete.user.name} from {taskToComplete.ipAddress}
                                        </div>
                                    </div>
                                )}
                                <p className="text-xs text-muted-foreground">This action cannot be undone.</p>
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={confirmCompleteTask}
                                               className="bg-primary-600 hover:bg-primary-700">
                                Complete Task
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
                {/* Decline Task Dialog */}
                <AlertDialog open={isDeclineOpen} onOpenChange={setIsDeclineOpen}>
                    <AlertDialogContent className="max-w-md">
                        <AlertDialogHeader>
                            <AlertDialogTitle className="flex items-center gap-2 text-destructive">
                                <ExclamationTriangleIcon className="w-5 h-5"/>
                                Decline Task
                            </AlertDialogTitle>
                            <AlertDialogDescription className="space-y-3">
                                <p>Please provide a reason for declining this task:</p>
                                {taskToDecline && (
                                    <div className="p-3 bg-muted/50 rounded-lg space-y-1">
                                        <div className="font-medium text-sm">{taskToDecline.title}</div>
                                        <div className="text-xs text-muted-foreground">
                                            Requested by {taskToDecline.user.name}
                                        </div>
                                    </div>
                                )}
                                <Textarea
                                    placeholder="Enter reason for declining..."
                                    value={declineReason}
                                    onChange={(e) => setDeclineReason(e.target.value)}
                                    className="min-h-[100px]"
                                />
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                                onClick={confirmDeclineTask}
                                disabled={!declineReason.trim()}
                                className="bg-destructive hover:bg-destructive/90"
                            >
                                Decline Task
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
                {/* Modify Task Dialog */}
                <AlertDialog open={isModifyOpen} onOpenChange={setIsModifyOpen}>
                    <AlertDialogContent className="max-w-md">
                        <AlertDialogHeader>
                            <AlertDialogTitle className="flex items-center gap-2 text-orange-600">
                                <ClockIcon className="w-5 h-5"/>
                                Request Modification
                            </AlertDialogTitle>
                            <AlertDialogDescription className="space-y-3">
                                <p>Please provide details for the requested modifications:</p>
                                {taskToModify && (
                                    <div className="p-3 bg-muted/50 rounded-lg space-y-1">
                                        <div className="font-medium text-sm">{taskToModify.title}</div>
                                        <div className="text-xs text-muted-foreground">
                                            Requested by {taskToModify.user.name}
                                        </div>
                                    </div>
                                )}
                                <Textarea
                                    placeholder="Enter modification requirements..."
                                    value={modifyReason}
                                    onChange={(e) => setModifyReason(e.target.value)}
                                    className="min-h-[100px]"
                                />
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                                onClick={confirmModifyTask}
                                disabled={!modifyReason.trim()}
                                className="bg-orange-600 hover:bg-orange-700"
                            >
                                Request Changes
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </div>
        </div>
    )
}
