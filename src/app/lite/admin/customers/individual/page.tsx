//@ts-nocheck
"use client"

import { useEffect, useState, useMemo } from "react"
import {
    AlertTriangle,
    Search,
    Filter,
    X,
    Calendar,
    User,
    MapPin,
    Shield,
    Building2,
    Check,
    ChevronsUpDown,
} from "lucide-react"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { cn } from "@/lib/utils"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Separator } from "@/components/ui/separator"
import axiosInstance from "@/utils/axiosInstance"
import { useAppSelector } from "@/store/hooks"
import DataExporter from "@/components/DataExporter"
import { DataTable } from "@/components/data-table"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { countryNameByCode } from "@/utils/data"
import { useRouter } from "next/navigation"
import { formatDate } from "@/utils/helpers"
import PhoneNumberDisplay from "@/components/PhoneDispaly"

// Company Dropdown Component
interface CompanyDropdownProps {
    companies: string[]
    selectedCompany: string
    onCompanyChange: (company: string) => void
    clientsData: any[]
}

function CompanyDropdown({ companies, selectedCompany, onCompanyChange, clientsData }: CompanyDropdownProps) {
    const [open, setOpen] = useState(false)

    const getCompanyCount = (companyName: string) => {
        return clientsData.filter((client) => client.company?.company_name === companyName).length
    }

    return (
        <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-[250px] justify-between bg-transparent"
        >
          <div className="flex items-center">
            <Building2 className="mr-2 h-4 w-4 text-gray-400" />
              {selectedCompany ? companies.find((company) => company === selectedCompany) : "Select company..."}
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0">
        <Command>
          <CommandInput placeholder="Search companies..." />
          <CommandList>
            <CommandEmpty>No company found.</CommandEmpty>
            <CommandGroup className="max-h-[300px] overflow-auto">
              <CommandItem
                  value="all"
                  onSelect={() => {
                      onCompanyChange("")
                      setOpen(false)
                  }}
              >
                <Check className={cn("mr-2 h-4 w-4", selectedCompany === "" ? "opacity-100" : "opacity-0")} />
                <div className="flex flex-col">
                  <span className="font-medium">All Companies</span>
                  <span className="text-xs text-muted-foreground">{clientsData.length} total records</span>
                </div>
              </CommandItem>
                {companies.sort().map((company) => (
                    <CommandItem
                        key={company}
                        value={company}
                        onSelect={(currentValue) => {
                            onCompanyChange(currentValue === selectedCompany ? "" : currentValue)
                            setOpen(false)
                        }}
                    >
                  <Check className={cn("mr-2 h-4 w-4", selectedCompany === company ? "opacity-100" : "opacity-0")} />
                  <div className="flex flex-col">
                    <span className="font-medium">{company}</span>
                    <span className="text-xs text-muted-foreground">{getCompanyCount(company)} record(s)</span>
                  </div>
                </CommandItem>
                ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
    )
}

// Types
interface PersonalInfo {
    firstName: string
    middleName?: string
    lastName: string
    dateOfBirth: string
    email: string
    phone: string
    birthCountry?: string
}

interface Address {
    streetAddress: string
    building?: string
    apartment?: string
    city: string
    stateProvince: string
    postalCode: string
    country: string
}

interface Company {
    _id: string
    company_name: string
}

interface IndividualCustomer {
    _id: string
    clientID: string
    personalInfo: PersonalInfo
    address: Address
    company?: Company
    citizenship?: string
    riskLevel?: string
    applicationStatus?: string
    createdAt: string
    updatedAt?: string
}

interface OnboardingData extends Array<IndividualCustomer> {}

interface FilterState {
    search: string
    firstName: string
    lastName: string
    email: string
    phoneNumber: string
    clientID: string
    country: string
    city: string
    citizenship: string
    riskLevel: string
    applicationStatus: string
    dateFrom: Date | null
    dateTo: Date | null
    birthCountry: string
    companyName: string
}

const initialFilterState: FilterState = {
    search: "",
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    clientID: "",
    country: "",
    city: "",
    citizenship: "",
    riskLevel: "",
    applicationStatus: "",
    dateFrom: null,
    dateTo: null,
    birthCountry: "",
    companyName: "",
}

export default function Dashboard() {
    const [onboarding, setOnboarding] = useState<OnboardingData | null>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [filters, setFilters] = useState<FilterState>(initialFilterState)
    const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
    const router = useRouter()
    const user = useAppSelector((state) => state.user.user)

    useEffect(() => {
        const fetchOnboardingDetails = async () => {
            try {
                const response = await axiosInstance.get("onboarding/personal")
                console.dir(response.data.users)
                setOnboarding(response.data.data)
            } catch (error) {
                console.error("Error fetching onboarding details:", error)
                setError("Failed to fetch onboarding details")
            } finally {
                setLoading(false)
            }
        }

        fetchOnboardingDetails()
    }, [])

    // Advanced filtering logic with performance optimization
    const filteredData = useMemo(() => {
        if (!onboarding || !Array.isArray(onboarding)) return []

        return onboarding.filter((customer) => {
            // Global search filter
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase()
                const searchableFields = [
                    customer.personalInfo?.firstName || "",
                    customer.personalInfo?.lastName || "",
                    customer.personalInfo?.email || "",
                    customer.personalInfo?.phone || "",
                    customer.clientID || "",
                    customer.address?.city || "",
                    customer.address?.country || "",
                    customer.address?.streetAddress || "",
                    customer.citizenship || "",
                    customer.riskLevel || "",
                    customer.applicationStatus || "",
                    customer.personalInfo?.birthCountry || "",
                    customer.company?.company_name || "",
                ]
                    .join(" ")
                    .toLowerCase()

                if (!searchableFields.includes(searchTerm)) return false
            }

            // Company name filter
            if (filters.companyName) {
                const companyNameFilter = filters.companyName.toLowerCase()
                const customerCompanyName = (customer.company?.company_name || "").toLowerCase()

                if (!customerCompanyName.includes(companyNameFilter)) return false
            }

            // Individual field filters with null checks
            if (
                filters.firstName &&
                !customer.personalInfo?.firstName?.toLowerCase().includes(filters.firstName.toLowerCase())
            ) {
                return false
            }

            if (
                filters.lastName &&
                !customer.personalInfo?.lastName?.toLowerCase().includes(filters.lastName.toLowerCase())
            ) {
                return false
            }

            if (filters.email && !customer.personalInfo?.email?.toLowerCase().includes(filters.email.toLowerCase())) {
                return false
            }

            if (filters.phoneNumber) {
                const phoneDigits = filters.phoneNumber.replace(/\D/g, "")
                const customerPhoneDigits = (customer.personalInfo?.phone || "").replace(/\D/g, "")
                if (!customerPhoneDigits.includes(phoneDigits)) return false
            }

            if (filters.clientID && !customer.clientID?.toLowerCase().includes(filters.clientID.toLowerCase())) {
                return false
            }

            if (filters.country && filters.country !== "all" && customer.address?.country !== filters.country) {
                return false
            }

            if (filters.city && !customer.address?.city?.toLowerCase().includes(filters.city.toLowerCase())) {
                return false
            }

            if (filters.citizenship && filters.citizenship !== "all" && customer.citizenship !== filters.citizenship) {
                return false
            }

            if (filters.riskLevel && filters.riskLevel !== "all" && customer.riskLevel !== filters.riskLevel) {
                return false
            }

            if (
                filters.applicationStatus &&
                filters.applicationStatus !== "all" &&
                customer.applicationStatus !== filters.applicationStatus
            ) {
                return false
            }

            if (
                filters.birthCountry &&
                filters.birthCountry !== "all" &&
                customer.personalInfo?.birthCountry !== filters.birthCountry
            ) {
                return false
            }

            // Date range filter
            if (filters.dateFrom || filters.dateTo) {
                const customerDate = new Date(customer.createdAt)
                if (filters.dateFrom && customerDate < filters.dateFrom) return false
                if (filters.dateTo && customerDate > filters.dateTo) return false
            }

            return true
        })
    }, [onboarding, filters])

    // Get unique values for dropdown filters with null checks
    const uniqueCountries = useMemo(() => {
        if (!onboarding) return []
        return [...new Set(onboarding.map((customer) => customer.address?.country).filter(Boolean))]
    }, [onboarding])

    const uniqueCitizenships = useMemo(() => {
        if (!onboarding) return []
        return [...new Set(onboarding.map((customer) => customer.citizenship).filter(Boolean))]
    }, [onboarding])

    const uniqueRiskLevels = useMemo(() => {
        if (!onboarding) return []
        return [...new Set(onboarding.map((customer) => customer.riskLevel).filter(Boolean))]
    }, [onboarding])

    const uniqueApplicationStatuses = useMemo(() => {
        if (!onboarding) return []
        return [...new Set(onboarding.map((customer) => customer.applicationStatus).filter(Boolean))]
    }, [onboarding])

    const uniqueBirthCountries = useMemo(() => {
        if (!onboarding) return []
        return [...new Set(onboarding.map((customer) => customer.personalInfo?.birthCountry).filter(Boolean))]
    }, [onboarding])

    const uniqueCompanyNames = useMemo(() => {
        if (!onboarding) return []
        return [...new Set(onboarding.map((customer) => customer.company?.company_name).filter(Boolean))]
    }, [onboarding])

    // Clear all filters
    const clearAllFilters = () => {
        setFilters(initialFilterState)
    }

    // Get active filter count
    const activeFilterCount = useMemo(() => {
        return Object.entries(filters).filter(([key, value]) => {
            if (key === "dateFrom" || key === "dateTo") return value !== null
            return value !== "" && value !== "all"
        }).length
    }, [filters])

    if (loading) {
        return <LoadingOverlay />
    }

    const columns = [
        {
            header: "Date Created",
            accessorKey: "createdAt" as const,
            cell: (row) => formatDate(row.createdAt),
        },
        {
            header: "Customer ID",
            accessorKey: "clientID" as const,
            cell: (row) => <div className="font-mono text-sm">{row.clientID}</div>,
        },
        {
            header: "Name",
            accessorKey: "name" as const,
            cell: (row) => (
                <div className="flex flex-col">
          <span className="font-medium">
            {`${row.personalInfo?.firstName || ""} ${row.personalInfo?.lastName || ""}`}
          </span>
                    {row.personalInfo?.middleName && (
                        <span className="text-xs text-muted-foreground">Middle: {row.personalInfo.middleName}</span>
                    )}
        </div>
            ),
        },
        {
            header: "Email",
            accessorKey: "email" as const,
            cell: (row) => row.personalInfo?.email || "N/A",
        },
        {
            header: "Mobile",
            accessorKey: "mobile" as const,
            cell: (row) => (row.personalInfo?.phone ? <PhoneNumberDisplay phoneNumber={row.personalInfo.phone} /> : "N/A"),
        },
        {
            header: "Company",
            accessorKey: "company" as const,
            cell: (row) => (
                <div className="flex flex-col">
          <span className="font-medium">{row.company?.company_name || "N/A"}</span>
        </div>
            ),
        },
        {
            header: "Country",
            accessorKey: "country" as const,
            cell: (row) => (row.address?.country ? countryNameByCode(row.address.country) : "N/A"),
        },


    ]

    if (error || !onboarding) {
        return (
            <Card className="w-full max-w-md mx-auto mt-8">
        <CardContent className="py-10">
          <AlertTriangle className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
          <p className="text-center text-lg font-medium text-muted-foreground">
            {error || "No Onboarding Details found."}
          </p>
        </CardContent>
      </Card>
        )
    }

    // Export data with better formatting
    const exportData = filteredData.map((row, index) => ({
        id: index + 1,
        customer_id: row.clientID || "",
        first_name: row.personalInfo?.firstName || "",
        middle_name: row.personalInfo?.middleName || "",
        last_name: row.personalInfo?.lastName || "",
        email: row.personalInfo?.email || "",
        phone: row.personalInfo?.phone || "",
        date_of_birth: row.personalInfo?.dateOfBirth || "",
        company_name: row.company?.company_name || "",
        citizenship: row.citizenship || "",
        birth_country: row.personalInfo?.birthCountry || "",
        risk_level: row.riskLevel || "",
        application_status: row.applicationStatus || "",
        street_address: row.address?.streetAddress || "",
        city: row.address?.city || "",
        state_province: row.address?.stateProvince || "",
        postal_code: row.address?.postalCode || "",
        country: row.address?.country || "",
        created_at: formatDate(row.createdAt),
        updated_at: row.updatedAt ? formatDate(row.updatedAt) : "",
    }))

    return (
        <div className="container mx-auto px-4 py-8 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-bold mb-2 flex items-center gap-2">
            <User className="h-5 w-5" />
            Individual Banking Customers
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataExporter
              data={exportData}
              filename="individual_banking_customers"
              title="Individual Banking Customers Report"
          />

          <div className="mt-6 space-y-4">
            {/* Main Search Bar with Quick Filters */}
              <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                    placeholder="Search customers by name, email, phone, ID, company..."
                    value={filters.search}
                    onChange={(e) => setFilters((prev) => ({ ...prev, search: e.target.value }))}
                    className="pl-10"
                />
              </div>

                  {/* Company Name Dropdown Filter */}
                  <CompanyDropdown
                      companies={uniqueCompanyNames}
                      selectedCompany={filters.companyName}
                      onCompanyChange={(company) => setFilters((prev) => ({ ...prev, companyName: company }))}
                      clientsData={onboarding || []}
                  />

               

                  {/* Quick Country Filter */}
                  <Select
                      value={filters.country}
                      onValueChange={(value) => setFilters((prev) => ({ ...prev, country: value === "all" ? "" : value }))}
                  >
                <SelectTrigger className="w-[150px]">
                  <MapPin className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Country" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Countries</SelectItem>
                    {uniqueCountries.map((country) => (
                        <SelectItem key={country} value={country}>
                      {countryNameByCode(country)}
                    </SelectItem>
                    ))}
                </SelectContent>
              </Select>


                  {activeFilterCount > 0 && (
                      <Button variant="ghost" onClick={clearAllFilters} className="flex items-center gap-2">
                  <X className="h-4 w-4" />
                  Clear All
                </Button>
                  )}
            </div>

              {/* Advanced Filters Panel */}
              {showAdvancedFilters && (
                  <Card className="p-4 bg-gray-50">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* Personal Information Filters */}
                    <div className="col-span-full">
                    <h4 className="font-medium text-sm text-gray-700 mb-3 flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Personal Information
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div>
                        <label className="text-sm font-medium mb-1 block">First Name</label>
                        <Input
                            placeholder="Filter by first name..."
                            value={filters.firstName}
                            onChange={(e) => setFilters((prev) => ({ ...prev, firstName: e.target.value }))}
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-1 block">Last Name</label>
                        <Input
                            placeholder="Filter by last name..."
                            value={filters.lastName}
                            onChange={(e) => setFilters((prev) => ({ ...prev, lastName: e.target.value }))}
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-1 block">Email</label>
                        <Input
                            placeholder="Filter by email..."
                            value={filters.email}
                            onChange={(e) => setFilters((prev) => ({ ...prev, email: e.target.value }))}
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-1 block">Phone Number</label>
                        <Input
                            placeholder="Filter by phone number..."
                            value={filters.phoneNumber}
                            onChange={(e) => setFilters((prev) => ({ ...prev, phoneNumber: e.target.value }))}
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-1 block">Customer ID</label>
                        <Input
                            placeholder="Filter by customer ID..."
                            value={filters.clientID}
                            onChange={(e) => setFilters((prev) => ({ ...prev, clientID: e.target.value }))}
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-1 block">Birth Country</label>
                        <Select
                            value={filters.birthCountry}
                            onValueChange={(value) =>
                                setFilters((prev) => ({ ...prev, birthCountry: value === "all" ? "" : value }))
                            }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select birth country..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Birth Countries</SelectItem>
                              {uniqueBirthCountries.map((country) => (
                                  <SelectItem key={country} value={country}>
                                {country}
                              </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <Separator className="col-span-full" />

                    {/* Company Information */}
                    <div className="col-span-full">
                    <h4 className="font-medium text-sm text-gray-700 mb-3 flex items-center gap-2">
                      <Building2 className="h-4 w-4" />
                      Company Information
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div>
                        <label className="text-sm font-medium mb-1 block">Company Name</label>
                        <Select
                            value={filters.companyName}
                            onValueChange={(value) =>
                                setFilters((prev) => ({ ...prev, companyName: value === "all" ? "" : value }))
                            }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select company..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Companies</SelectItem>
                              {uniqueCompanyNames.map((company) => (
                                  <SelectItem key={company} value={company}>
                                {company}
                              </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <Separator className="col-span-full" />

                    {/* Location Filters */}
                    <div className="col-span-full">
                    <h4 className="font-medium text-sm text-gray-700 mb-3 flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      Location & Address
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div>
                        <label className="text-sm font-medium mb-1 block">Country</label>
                        <Select
                            value={filters.country}
                            onValueChange={(value) =>
                                setFilters((prev) => ({ ...prev, country: value === "all" ? "" : value }))
                            }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select country..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Countries</SelectItem>
                              {uniqueCountries.map((country) => (
                                  <SelectItem key={country} value={country}>
                                {countryNameByCode(country)}
                              </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-1 block">City</label>
                        <Input
                            placeholder="Filter by city..."
                            value={filters.city}
                            onChange={(e) => setFilters((prev) => ({ ...prev, city: e.target.value }))}
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-1 block">Citizenship</label>
                        <Select
                            value={filters.citizenship}
                            onValueChange={(value) =>
                                setFilters((prev) => ({ ...prev, citizenship: value === "all" ? "" : value }))
                            }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select citizenship..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Citizenships</SelectItem>
                              {uniqueCitizenships.map((citizenship) => (
                                  <SelectItem key={citizenship} value={citizenship}>
                                {citizenship}
                              </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <Separator className="col-span-full" />

                    {/* Status & Risk Filters */}
                    <div className="col-span-full">
                    <h4 className="font-medium text-sm text-gray-700 mb-3 flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      Status & Risk Assessment
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div>
                        <label className="text-sm font-medium mb-1 block">Risk Level</label>
                        <Select
                            value={filters.riskLevel}
                            onValueChange={(value) =>
                                setFilters((prev) => ({ ...prev, riskLevel: value === "all" ? "" : value }))
                            }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select risk level..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Risk Levels</SelectItem>
                              {uniqueRiskLevels.map((riskLevel) => (
                                  <SelectItem key={riskLevel} value={riskLevel}>
                                {riskLevel.charAt(0).toUpperCase() + riskLevel.slice(1)}
                              </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-1 block">Application Status</label>
                        <Select
                            value={filters.applicationStatus}
                            onValueChange={(value) =>
                                setFilters((prev) => ({ ...prev, applicationStatus: value === "all" ? "" : value }))
                            }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select status..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Statuses</SelectItem>
                              {uniqueApplicationStatuses.map((status) => (
                                  <SelectItem key={status} value={status}>
                                {status.charAt(0).toUpperCase() + status.slice(1)}
                              </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <Separator className="col-span-full" />

                    {/* Date Range Filters */}
                    <div className="col-span-full">
                    <h4 className="font-medium text-sm text-gray-700 mb-3 flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Date Range
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium mb-1 block">Date From</label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                                variant="outline"
                                className="w-full justify-start text-left font-normal bg-transparent"
                            >
                              <Calendar className="mr-2 h-4 w-4" />
                                {filters.dateFrom ? formatDate(filters.dateFrom.toISOString()) : "Select date..."}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <CalendarComponent
                                mode="single"
                                selected={filters.dateFrom}
                                onSelect={(date) => setFilters((prev) => ({ ...prev, dateFrom: date }))}
                                initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-1 block">Date To</label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                                variant="outline"
                                className="w-full justify-start text-left font-normal bg-transparent"
                            >
                              <Calendar className="mr-2 h-4 w-4" />
                                {filters.dateTo ? formatDate(filters.dateTo.toISOString()) : "Select date..."}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <CalendarComponent
                                mode="single"
                                selected={filters.dateTo}
                                onSelect={(date) => setFilters((prev) => ({ ...prev, dateTo: date }))}
                                initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
              )}

              {/* Active Filters Display */}
              {activeFilterCount > 0 && (
                  <div className="flex flex-wrap gap-2">
                {Object.entries(filters).map(([key, value]) => {
                    if (!value || (typeof value === "string" && (value === "" || value === "all"))) return null

                    const displayValue =
                        key === "dateFrom" || key === "dateTo"
                            ? formatDate((value as Date).toISOString())
                            : (value as string)

                    return (
                        <Badge key={key} variant="secondary" className="flex items-center gap-1">
                      {key.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase())}: {displayValue}
                            <X
                                className="h-3 w-3 cursor-pointer"
                                onClick={() =>
                                    setFilters((prev) => ({
                                        ...prev,
                                        [key]: key === "dateFrom" || key === "dateTo" ? null : "",
                                    }))
                                }
                            />
                    </Badge>
                    )
                })}
              </div>
              )}
          </div>

          <Separator className="my-6" />

          <DataTable
              data={filteredData}
              columns={columns}
              title="Individual Banking Customers"
              description={`Showing ${filteredData.length} of ${onboarding?.length || 0} customers`}
              loading={loading}
              error={error}
              getRowId={(c) => c._id}
              onRowClick={(r) => router.push(`/lite/admin/individual/v1/${r._id}`)}
          />
        </CardContent>
      </Card>
    </div>
    )
}
