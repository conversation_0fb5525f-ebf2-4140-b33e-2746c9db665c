"use client";

import Link from 'next/link';
import Image from "next/image";
import {
    Building2,
    Home,
    Menu,
    Users2,
    CogIcon,
    ChevronRight,
    ChevronDown,
    CircleUser,
    Search,
    List
} from 'lucide-react';
import {usePathname, useRouter} from 'next/navigation';
import React, {useEffect, useState} from "react";

import {Button} from '@/components/ui/button';
import {Sheet, SheetContent, SheetTrigger} from '@/components/ui/sheet';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel, DropdownMenuSeparator,
    DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {Input} from "@/components/ui/input";
import axiosInstance from "@/utils/axiosInstance";


export default function DashboardLayout({children}: {
    children: React.ReactNode;
}) {
    const pathname = usePathname();
    const [openMenu, setOpenMenu] = useState<string | null>(null); // Track open menu item
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const router = useRouter();
    const [roleId, setRoleId] = useState();
    useEffect(() => {
        const checkAuth = async () => {
            try {
                const response = await axiosInstance.get('users/me', {withCredentials: true});
                if (response.data) {

                    // @ts-ignore
                    setRoleId(response.data.roles[0])
                    setIsAuthenticated(true);
                } else {
                    console.log(response)
                    router.push('/login'); // Redirect to login if not authenticated
                }
            } catch (error) {
                console.error(error);
                router.push('/login'); // Redirect to login on error
            } finally {
                console.dir("yes")
            }
        };

        checkAuth();
    }, [router]);


    const logout = async () => {
        try {
            localStorage.removeItem('accessToken');
            // Clear the local state
            setIsAuthenticated(false);

            // Redirect to login page after successful logout
            router.push('/login');
        } catch (error) {
            console.error('Error during logout:', error);
        }
    };


    if (!isAuthenticated) {
        return null; // Prevent rendering until authentication is verified
    }
    const isActive = (href: string) => {
        return pathname === href;
    };

    const isSubMenuItemActive = (subMenuItems: any[]) => {
        return subMenuItems.some(subItem => isActive(subItem.href));
    };

    const toggleMenu = (menuHref: string) => {
        setOpenMenu(openMenu === menuHref ? null : menuHref); // Toggle open/close menu
    };

    const isSubMenuOpen = (href: string) => openMenu === href || isSubMenuItemActive(configurationDropdowns);

    const configurationDropdowns = [
        {label: "Card Scheme", href: "/lite/admin/configurations/card-scheme"},
        {label: "BIN Ranges", href: "/lite/admin/configurations/card-bin"},
        {label: "Card Programme Type", href: "/lite/admin/configurations/card-programme-type"},
        {label: "Card Type", href: "/lite/admin/configurations/bin-type"},
        {label: "Customer Type", href: "/lite/admin/configurations/customer-type"},
        {label: "Issuing Client Type", href: "/lite/admin/configurations/issuing-client-type"},
        {label: "Product CCY", href: "/lite/admin/configurations/product-ccy"},
        {label: "Product Versions", href: "/lite/admin/configurations/product-versions"},
    ];

    const menuItems = [
        {href: "/lite/admin/dashboard", icon: Home, label: "CIOL"},
        {href: "/lite/admin/companies", icon: Building2, label: "Onboarded Companies"},
        {href: "/lite/admin/programmes", icon: List, label: "CIP Programme"},
        {href: "/lite/admin/users", icon: Users2, label: "Ryvyl Users"},
        {
            href: "/lite/admin/configurations",
            icon: CogIcon,
            label: "Global Setup",
            submenu: [
                ...configurationDropdowns
            ]
        },
    ];

    const renderMenuItem = (item: any) => (
        <React.Fragment key={item.href}>
            {item.submenu ? (
                <div>
                    <Button
                        variant="ghost"
                        className={`mx-[-0.65rem] w-full justify-start gap-2 px-3 py-2 text-left ${
                            isSubMenuItemActive(item.submenu) ? 'bg-muted text-foreground' : ''
                        }`}
                        onClick={() => toggleMenu(item.href)} // Toggle submenu
                    >
                        <item.icon className="h-5 w-5"/>
                        {item.label}
                        {isSubMenuOpen(item.href) ? (
                            <ChevronDown className="ml-auto h-4 w-4"/>
                        ) : (
                            <ChevronRight className="ml-auto h-4 w-4"/>
                        )}
                    </Button>
                    <div
                        className={`ml-4 overflow-hidden transition-all duration-300 ease-in-out ${
                            isSubMenuOpen(item.href) ? 'max-h-screen' : 'max-h-0'
                        } `}
                    >
                        {item.submenu.map((subItem: any) => (
                            <Link
                                key={subItem.href}
                                href={subItem.href}
                                className={`block px-3 py-2 text-sm rounded-sm ${
                                    isActive(subItem.href) ? 'text-foreground bg-primary/20' : 'text-muted-foreground'
                                } hover:text-foreground`}
                            >
                                {subItem.label}
                            </Link>
                        ))}
                    </div>
                </div>
            ) : (
                <Link
                    href={item.href}
                    className={`mx-[-0.65rem] flex items-center gap-4 rounded-sm px-3 py-2 hover:text-foreground ${
                        isActive(item.href) ? 'bg-primary/20 text-foreground' : 'text-muted-foreground'
                    }`}
                >
                    <item.icon className="h-5 w-5"/>
                    {item.label}
                </Link>
            )}
        </React.Fragment>
    );

    return (
        <div className="grid min-h-screen w-full md:grid-cols-[220px_1fr] lg:grid-cols-[280px_1fr]">
            {/* Sidebar */}
            <div className="hidden border-r bg-muted/40 md:block">
                <div className="flex h-full max-h-screen flex-col gap-2">
                    <div className="flex h-14 items-center border-b px-4 lg:h-[60px] lg:px-6">
                        <Link href="/" className="flex items-center gap-2 font-semibold">
                            <Image
                                src="/images/logo/icon-dark.svg"
                                width={40} height={40}
                                alt="ryvyl.eu"
                                className="overflow-hidden"
                            />
                            <Image
                                src="/logo.jpeg"
                                width={150} height={150}
                                alt="ryvyl.eu"
                                className="overflow-hidden"
                            />
                        </Link>
                    </div>
                    <div className="flex-1 overflow-auto">
                        <nav className="grid items-start px-2 text-sm font-medium lg:px-4">
                            {menuItems.map(renderMenuItem)}
                        </nav>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="flex flex-col">
                <header className="flex h-14 items-center gap-4 border-b bg-muted/40 px-4 lg:h-[60px] lg:px-6">
                    <Sheet>
                        <SheetTrigger asChild>
                            <Button
                                variant="outline"
                                size="icon"
                                className="shrink-0 md:hidden"
                            >
                                <Menu className="h-5 w-5"/>
                                <span className="sr-only">Toggle navigation menu</span>
                            </Button>
                        </SheetTrigger>
                        <SheetContent side="left" className="flex flex-col">
                            <nav className="grid gap-2 text-lg font-medium">
                                <Link
                                    href="/"
                                    className="flex items-center gap-2 text-lg font-semibold"
                                >
                                    <Image
                                        src="/images/logo/icon-dark.svg"
                                        width={40} height={40}
                                        alt="ryvyl.eu"
                                        className="overflow-hidden"
                                    />
                                    <Image
                                        src="/images/logo/logo-dark.svg"
                                        width={150} height={150}
                                        alt="ryvyl.eu"
                                        className="overflow-hidden"
                                    />
                                </Link>

                                {/* Menu items with expandable submenu */}
                                {menuItems.map(renderMenuItem)}
                            </nav>
                        </SheetContent>
                    </Sheet>

                    {/* Search bar */}
                    <div className="w-full flex-1">
                        <form>
                            <div className="relative">
                                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"/>
                                <Input
                                    type="search"
                                    placeholder="Search..."
                                    className="w-full appearance-none bg-background pl-8 shadow-none md:w-2/3 lg:w-1/3"
                                />
                            </div>
                        </form>
                    </div>

                    {/* User dropdown */}
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="secondary" size="icon" className="rounded-full">
                                <CircleUser className="h-5 w-5"/>
                                <span className="sr-only">Toggle user menu</span>
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>My Account</DropdownMenuLabel>
                            <DropdownMenuSeparator/>
                            <DropdownMenuItem>Settings</DropdownMenuItem>
                            <DropdownMenuItem>Support</DropdownMenuItem>
                            <DropdownMenuSeparator/>
                            <DropdownMenuItem onClick={logout}>Logout</DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </header>

                {/* Main content area */}
                <main className="flex flex-1 flex-col gap-4 p-4 lg:gap-6 lg:p-6">
                    {children}
                </main>
            </div>
        </div>
    );
}
