//@ts-nocheck
"use client"

import { useEffect, useState } from "react"
import { Edit, Plus, Trash, Users, CreditCard, Building2, User<PERSON><PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import Link from "next/link"
import axiosInstance from "@/utils/axiosInstance"
import { formatDate } from "@/utils/helpers"
import { DataTable } from "@/components/data-table"
import {Strong} from "@radix-ui/themes";
import {useAppSelector} from "@/store/hooks";

export default function UserManagementPage() {
    const [users, setUsers] = useState([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState(null)
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
    const [userToDelete, setUserToDelete] = useState(null)
    const [activeTab, setActiveTab] = useState("employees")
    const user = useAppSelector((state) => state.user.user)


    const [roles, setRoles] = useState<Role[]>([])

    useEffect(() => {
        setRoles(user.roles)
    }, [])
    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions))
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }


    const userHasCreatePermission = hasPermission(roles, "Users_Create")
    const userHasEditPermission = hasPermission(roles, "Users_Edit")
    const userHasDeletePermission = hasPermission(roles, "Users_Delete")

    useEffect(() => {
        const fetchUsers = async () => {
            try {
                const response = await axiosInstance.get("users")
                setUsers(response.data)
            } catch (err) {
                setError(err.message)
            } finally {
                setLoading(false)
            }
        }
        fetchUsers()
    }, [])

    const handleDeleteClick = (userId) => {
        setUserToDelete(userId)
        setDeleteDialogOpen(true)
    }

    const handleConfirmDelete = async () => {
        if (!userToDelete) return
        try {
            await axiosInstance.delete(`users/${userToDelete}`)
            setUsers(users.filter((user) => user._id !== userToDelete))
            setDeleteDialogOpen(false)
            setUserToDelete(null)
        } catch (error) {
            console.error("Error deleting user:", error)
        }
    }

    // Filter users by dashboard type
    const getUsersByType = (type) => {
        return users.filter((user) => {
            switch (type) {
                case "cardholders":
                    return user.dashboard === "cardholder"
                case "programme-managers":
                    return user.dashboard === "programme-manager" || user.dashboard === "programmeManager"
                case "employees":
                    return user.dashboard === "infinity" // As specified, infinity dashboard users go to employees tab
                case "b2b":
                    return user.dashboard === "corporate" || user.dashboard === "business"
                default:
                    return true
            }
        })
    }

    // Define columns for the DataTable
    const getColumns = (userType) => [
        {
            header: "Date Created",
            accessorKey: "createdAt",
            cell: (user) => <div className="text-sm">{formatDate(user.createdAt)}</div>,
            enableSorting: true,
        },
        {
            header: "User Details",
            accessorKey: "name",
            cell: (user) => (
                <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
              <Users className="w-4 h-4 text-primary" />
            </div>
          </div>
          <div>
            <div className="font-medium text-sm">{user.name}</div>
            <div className="text-xs text-muted-foreground">{user.email}</div>
          </div>
        </div>
            ),
            enableSorting: true,
        },
        {
            header: "Permissions",
            accessorKey: "roles",
            cell: (user) => (
                <div className="flex flex-wrap gap-1">
          {user.roles.slice(0, 2).map((role, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
              {role.name || "Unknown"}
            </Badge>
          ))}
                    {user.roles.length > 2 && (
                        <Badge variant="outline" className="text-xs">
              +{user.roles.length - 2} more
            </Badge>
                    )}
        </div>
            ),
            enableSorting: false,
        },
        {
            header: "Status",
            accessorKey: "status",
            cell: (user) => (
                <Badge
                    variant={user.status === "active" ? "default" : "secondary"}
                    className={user.status === "active" ? "bg-green-100 text-green-800 hover:bg-green-100" : ""}
                >
          {user.status ? user.status.charAt(0).toUpperCase() + user.status.slice(1) : "N/A"}
        </Badge>
            ),
            enableSorting: true,
        },{
            header: "Last Login",
            accessorKey: "lastLoginAt",
            cell: (user) => (
                <>
                <span>{user.lastLoginAt !=null ?formatDate(user.lastLoginAt): "N/A"}</span>
                    <br/>
                <span>IP: {user.lastLoginIP}</span>
                </>
            ),
            enableSorting: true,
        },{
            header: "Email Status",
            accessorKey: "sent",
            cell: (user) => (
                <>
               Sent
                </>
            ),
            enableSorting: true,
        },
        {
            header: "Actions",
            accessorKey: "_id",
            cell: (user) => (
                <div className="flex items-center gap-2">
                    {userHasEditPermission &&(
          <Link href={`users/${user._id}/edit`}>
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>)}
                    {userHasDeletePermission &&(
          <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                  e.stopPropagation()
                  handleDeleteClick(user._id)
              }}
              className="text-destructive hover:text-destructive"
          >
            <Trash className="h-4 w-4" />
          </Button>
                    )}
        </div>
            ),
            enableSorting: false,
        },
    ]

    const filterOptions = {
        key: "status",
        options: [
            { label: "All", value: null },
            { label: "Active", value: "active" },
            { label: "Inactive", value: "inactive" },
        ],
    }

    const tabConfig = [
        {
            value: "employees",
            label: "Employees",
            icon: Users,
            description: "Manage users with infinity dashboard access",
            addLink: "/lite/admin/users/add",
        },
        {
            value: "programme-managers",
            label: "Programme Managers",
            icon: UserCheck,
            description: "Manage users with programme manager or admin dashboard access",
            addLink: "/onboarding",
        },

        {
            value: "b2b",
            label: "B2B Partners",
            icon: Building2,
            description: "Manage users with business or B2B dashboard access",
            addLink: "/b2b",
        },  {
            value: "cardholders",
            label: "Cardholders",
            icon: CreditCard,
            description: "Manage users with cardholder dashboard access",
            addLink: "/individual",
        },
    ]

    const getTabData = (tabValue) => {
        const userData = getUsersByType(tabValue)
        return {
            data: userData,
            count: userData.length,
            activeCount: userData.filter((user) => user.status === "active").length,
        }
    }

    return (
        <div className="space-y-6 p-6">
      {/* Header */}
            <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
            <p className="text-muted-foreground">Manage users across different categories and their permissions</p>
          </div>
        </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {tabConfig.map((tab) => {
              const tabData = getTabData(tab.value)
              const Icon = tab.icon
              return (
                  <Card
                      key={tab.value}
                      className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => setActiveTab(tab.value)}
                  >
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Icon className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">{tab.label}</p>
                      <div className="flex items-center space-x-2">
                        <p className="text-2xl font-bold">{tabData.count}</p>
                        <Badge variant="secondary" className="text-xs">
                          {tabData.activeCount} active
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              )
          })}
        </div>
      </div>

            {/* Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          {tabConfig.map((tab) => {
              const Icon = tab.icon
              return (
                  <TabsTrigger key={tab.value} value={tab.value} className="flex items-center gap-2">
                <Icon className="h-4 w-4" />
                <span className="hidden sm:inline">{tab.label}</span>
              </TabsTrigger>
              )
          })}
        </TabsList>

                {tabConfig.map((tab) => {
                    const tabData = getTabData(tab.value)
                    return (
                        <TabsContent key={tab.value} value={tab.value} className="space-y-4">
              <Card>
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <tab.icon className="h-5 w-5" />
                          {tab.label}
                      </CardTitle>
                      <CardDescription>{tab.description}</CardDescription>
                    </div>
                      {userHasCreatePermission &&(
                    <Link href={tab.addLink} target="_blank">
                      <Button className="gap-2">
                        <Plus className="h-4 w-4" />
                        Add {tab.label.slice(0, -1)}
                      </Button>
                    </Link>)}
                  </div>
                </CardHeader>
                <CardContent>
                  <DataTable
                      data={tabData.data}
                      columns={getColumns(tab.value)}
                      loading={loading}
                      error={error}
                      filterOptions={filterOptions}
                      enableSelection={false}
                      getRowId={(user) => user._id}
                  />
                </CardContent>
              </Card>
            </TabsContent>
                    )
                })}
      </Tabs>

            {/* Delete Confirmation Dialog */}
            <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this user? This action cannot be undone and will remove all associated
              data.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete}>
              Delete User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
    )
}
