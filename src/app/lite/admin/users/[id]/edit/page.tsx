// @ts-nocheck
"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRout<PERSON>, useParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import {
    EyeIcon,
    EyeOffIcon,
    InfoIcon,
    PenIcon as UserPen,
    Mail,
    Lock,
    Shield,
    Settings,
    Eye,
    ArrowLeft,
} from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, CheckCircle, XCircle } from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"

export default function EditUserPage() {
    const router = useRouter()
    const params = useParams()
    const userId = params?.id

    const [roles, setRoles] = useState([])
    const [user, setUser] = useState(null)
    const [isLoadingUser, setIsLoadingUser] = useState(true)
    const [showPassword, setShowPassword] = useState(false)
    const [showConfirmPassword, setShowConfirmPassword] = useState(false)
    const [passwordsMatch, setPasswordsMatch] = useState(true)
    const [isLoading, setIsLoading] = useState(false)
    const [alertInfo, setAlertInfo] = useState<{ type: "success" | "error"; message: string } | null>(null)
    const [formData, setFormData] = useState({
        name: "",
        email: "",
        password: "",
        confirmPassword: "",
        status: "",
        roles: [],
    })

    const [selectedRole, setSelectedRole] = useState(null)
    const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false)

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData((prev) => ({ ...prev, [name]: value }))

        if (name === "password" || name === "confirmPassword") {
            const newPasswordsMatch = name === "password" ? value === formData.confirmPassword : formData.password === value
            setPasswordsMatch(newPasswordsMatch)
        }
    }

    useEffect(() => {
        const fetchData = async () => {
            try {
                // Fetch roles
                const rolesResponse = await axiosInstance.get("roles")
                setRoles(rolesResponse.data)

                // Fetch user data
                if (userId) {
                    const userResponse = await axiosInstance.get(`users/user/${userId}`)
                    const userData = userResponse.data
                    setUser(userData)

                    // Pre-populate form with user data
                    setFormData({
                        name: userData.name || "",
                        email: userData.email || "",
                        password: "",
                        confirmPassword: "",
                        status: userData.status || "",
                        roles: userData.roles?.map((role) => role._id || role) || [],
                    })
                }
            } catch (err) {
                console.error("Error fetching data:", err)
                setAlertInfo({ type: "error", message: "Failed to load user data" })
            } finally {
                setIsLoadingUser(false)
            }
        }

        fetchData()
    }, [userId])

    const handleStatusChange = (value: string) => {
        setFormData((prev) => ({ ...prev, status: value }))
    }

    const handlePermissionChange = (roleId: string) => {
        setFormData((prev) => {
            const isRoleSelected = prev.roles.includes(roleId)
            return {
                ...prev,
                roles: isRoleSelected ? prev.roles.filter((id) => id !== roleId) : [...prev.roles, roleId],
            }
        })
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        // Only check password match if password is being updated
        if (formData.password && formData.password !== formData.confirmPassword) {
            setPasswordsMatch(false)
            return
        }

        try {
            setIsLoading(true)
            setAlertInfo(null)

            // Prepare update data - only include password if it's provided
            const updateData = {
                name: formData.name,
                email: formData.email,
                status: formData.status,
                roles: formData.roles,
            }

            if (formData.password) {
                updateData.password = formData.password
            }

            const response = await axiosInstance.put(`users/${userId}`, updateData)

            if (response.status === 200) {
                setAlertInfo({ type: "success", message: "User updated successfully!" })
                setTimeout(() => {
                    router.push("/lite/admin/users")
                }, 2000)
            }
        } catch (error) {
            setAlertInfo({ type: "error", message: error.response?.data?.message || "An unexpected error occurred" })
            console.error("Error updating user:", error)
        } finally {
            setIsLoading(false)
        }
    }

    const togglePasswordVisibility = (field: "password" | "confirmPassword") => {
        if (field === "password") {
            setShowPassword(!showPassword)
        } else {
            setShowConfirmPassword(!showConfirmPassword)
        }
    }

    const getStatusColor = (status: string) => {
        switch (status) {
            case "active":
                return "bg-green-100 text-green-800 border-green-200"
            case "inactive":
                return "bg-red-100 text-red-800 border-red-200"
            case "pending":
                return "bg-yellow-100 text-yellow-800 border-yellow-200"
            default:
                return "bg-gray-100 text-gray-800 border-gray-200"
        }
    }

    if (isLoadingUser) {
        return (
            <div className="container mx-auto py-8 px-4 max-w-4xl">
                <div className="mb-8">
                    <div className="flex items-center gap-3 mb-2">
                        <Skeleton className="h-10 w-10 rounded-lg" />
                        <div>
                            <Skeleton className="h-8 w-48 mb-2" />
                            <Skeleton className="h-4 w-64" />
                        </div>
                    </div>
                </div>
                <Card className="shadow-lg">
                    <CardHeader className="pb-6">
                        <Skeleton className="h-6 w-32 mb-2" />
                        <Skeleton className="h-4 w-64" />
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <Skeleton className="h-32 w-full" />
                        <Skeleton className="h-32 w-full" />
                        <Skeleton className="h-32 w-full" />
                    </CardContent>
                </Card>
            </div>
        )
    }

    return (
        <div className="container mx-auto py-8 px-4 max-w-4xl">
            <div className="mb-8">
                <Button variant="ghost" onClick={() => router.push("/lite/admin/users")} className="mb-4 -ml-4">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Users
                </Button>
                <div className="flex items-center gap-3 mb-2">
                    <div className="p-2 bg-blue-100 rounded-lg">
                        <UserPen className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Edit User</h1>
                        <p className="text-muted-foreground">
                            Update user information and permissions for {user?.name || "this user"}
                        </p>
                    </div>
                </div>
            </div>

            {alertInfo && (
                <Alert variant={alertInfo.type === "success" ? "default" : "destructive"} className="mb-6">
                    <div className="flex items-center gap-2">
                        {alertInfo.type === "success" ? <CheckCircle className="h-4 w-4" /> : <XCircle className="h-4 w-4" />}
                        <AlertDescription className="font-medium">{alertInfo.message}</AlertDescription>
                    </div>
                </Alert>
            )}

            <Card className="shadow-lg">
                <CardHeader className="pb-6">
                    <CardTitle className="flex items-center gap-2">
                        <Settings className="h-5 w-5" />
                        User Information
                    </CardTitle>
                    <CardDescription>Update user details and modify roles and permissions</CardDescription>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-8">
                        {/* Basic Information Section */}
                        <div className="space-y-6">
                            <div className="flex items-center gap-2 mb-4">
                                <Mail className="h-4 w-4 text-muted-foreground" />
                                <h3 className="text-lg font-semibold">Basic Information</h3>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-sm font-medium">
                                        Full Name
                                    </Label>
                                    <Input
                                        id="name"
                                        name="name"
                                        placeholder="Enter full name"
                                        value={formData.name}
                                        onChange={handleInputChange}
                                        className="h-11"
                                        required
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="email" className="text-sm font-medium">
                                        Email Address
                                    </Label>
                                    <Input
                                        id="email"
                                        name="email"
                                        type="email"
                                        placeholder="Enter email address"
                                        value={formData.email}
                                        onChange={handleInputChange}
                                        className="h-11"
                                        required
                                    />
                                </div>
                            </div>
                        </div>

                        <Separator />

                        {/* Security Section */}
                        <div className="space-y-6">
                            <div className="flex items-center gap-2 mb-4">
                                <Lock className="h-4 w-4 text-muted-foreground" />
                                <h3 className="text-lg font-semibold">Security</h3>
                                <Badge variant="outline" className="text-xs">
                                    Optional
                                </Badge>
                            </div>

                            <div className="p-4 bg-muted/50 rounded-lg mb-4">
                                <p className="text-sm text-muted-foreground">
                                    Leave password fields empty to keep the current password unchanged.
                                </p>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="password" className="text-sm font-medium">
                                        New Password
                                    </Label>
                                    <div className="relative">
                                        <Input
                                            id="password"
                                            name="password"
                                            type={showPassword ? "text" : "password"}
                                            placeholder="Enter new password (optional)"
                                            value={formData.password}
                                            onChange={handleInputChange}
                                            className="h-11 pr-10"
                                        />
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                            onClick={() => togglePasswordVisibility("password")}
                                        >
                                            {showPassword ? (
                                                <EyeOffIcon className="h-4 w-4 text-muted-foreground" />
                                            ) : (
                                                <EyeIcon className="h-4 w-4 text-muted-foreground" />
                                            )}
                                        </Button>
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="confirmPassword" className="text-sm font-medium">
                                        Confirm New Password
                                    </Label>
                                    <div className="relative">
                                        <Input
                                            id="confirmPassword"
                                            name="confirmPassword"
                                            type={showConfirmPassword ? "text" : "password"}
                                            placeholder="Confirm new password"
                                            value={formData.confirmPassword}
                                            onChange={handleInputChange}
                                            className={`h-11 pr-10 ${!passwordsMatch && formData.confirmPassword ? "border-red-500" : ""}`}
                                        />
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                            onClick={() => togglePasswordVisibility("confirmPassword")}
                                        >
                                            {showConfirmPassword ? (
                                                <EyeOffIcon className="h-4 w-4 text-muted-foreground" />
                                            ) : (
                                                <EyeIcon className="h-4 w-4 text-muted-foreground" />
                                            )}
                                        </Button>
                                    </div>
                                    {!passwordsMatch && formData.confirmPassword && (
                                        <p className="text-sm text-red-500 flex items-center gap-1">
                                            <XCircle className="h-3 w-3" />
                                            Passwords do not match
                                        </p>
                                    )}
                                </div>
                            </div>
                        </div>

                        <Separator />

                        {/* Status & Permissions Section */}
                        <div className="space-y-6">
                            <div className="flex items-center gap-2 mb-4">
                                <Shield className="h-4 w-4 text-muted-foreground" />
                                <h3 className="text-lg font-semibold">Status & Permissions</h3>
                            </div>

                            <div className="space-y-6">
                                <div className="space-y-2">
                                    <Label htmlFor="status" className="text-sm font-medium">
                                        Account Status
                                    </Label>
                                    <Select onValueChange={handleStatusChange} value={formData.status}>
                                        <SelectTrigger className="h-11">
                                            <SelectValue placeholder="Select account status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="active">
                                                <div className="flex items-center gap-2">
                                                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                                    Active
                                                </div>
                                            </SelectItem>
                                            <SelectItem value="inactive">
                                                <div className="flex items-center gap-2">
                                                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                                                    Inactive
                                                </div>
                                            </SelectItem>
                                            <SelectItem value="pending">
                                                <div className="flex items-center gap-2">
                                                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                                    Pending
                                                </div>
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {formData.status && (
                                        <Badge variant="outline" className={getStatusColor(formData.status)}>
                                            {formData.status.charAt(0).toUpperCase() + formData.status.slice(1)}
                                        </Badge>
                                    )}
                                </div>

                                <div className="space-y-4">
                                    <Label className="text-sm font-medium">User Roles</Label>
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        <TooltipProvider>
                                            {roles.map((role) => (
                                                <div
                                                    key={role.name}
                                                    className="flex items-start space-x-3 p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                                                >
                                                    <Checkbox
                                                        id={role.name}
                                                        checked={formData.roles.includes(role._id)}
                                                        onCheckedChange={() => handlePermissionChange(role._id)}
                                                        className="mt-0.5"
                                                    />
                                                    <div className="flex-1 min-w-0">
                                                        <div className="flex items-center justify-between">
                                                            <Label
                                                                htmlFor={role.name}
                                                                className="text-sm font-medium cursor-pointer flex items-center gap-2"
                                                            >
                                                                {role.name}
                                                                <Tooltip>
                                                                    <TooltipTrigger asChild>
                                                                        <InfoIcon className="h-3 w-3 text-muted-foreground hover:text-foreground transition-colors" />
                                                                    </TooltipTrigger>
                                                                    <TooltipContent side="top" className="max-w-xs">
                                                                        <div className="space-y-1">
                                                                            <p className="font-medium">Permissions:</p>
                                                                            <ul className="text-xs space-y-0.5">
                                                                                {role.permissions?.slice(0, 3).map((permission, index) => (
                                                                                    <li key={index} className="flex items-center gap-1">
                                                                                        <div className="w-1 h-1 bg-current rounded-full"></div>
                                                                                        {permission
                                                                                            .split("_")
                                                                                            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                                                                                            .join(" ")}
                                                                                    </li>
                                                                                ))}
                                                                                {role.permissions?.length > 3 && (
                                                                                    <li className="text-muted-foreground">
                                                                                        +{role.permissions.length - 3} more...
                                                                                    </li>
                                                                                )}
                                                                            </ul>
                                                                        </div>
                                                                    </TooltipContent>
                                                                </Tooltip>
                                                            </Label>
                                                            <Dialog
                                                                open={isRoleDialogOpen && selectedRole?._id === role._id}
                                                                onOpenChange={(open) => {
                                                                    setIsRoleDialogOpen(open)
                                                                    if (!open) setSelectedRole(null)
                                                                }}
                                                            >
                                                                <DialogTrigger asChild>
                                                                    <Button
                                                                        type="button"
                                                                        variant="ghost"
                                                                        size="sm"
                                                                        className="h-6 px-2 text-xs"
                                                                        onClick={() => {
                                                                            setSelectedRole(role)
                                                                            setIsRoleDialogOpen(true)
                                                                        }}
                                                                    >
                                                                        <Eye className="h-3 w-3 mr-1" />
                                                                        View
                                                                    </Button>
                                                                </DialogTrigger>
                                                                <DialogContent className="max-w-md">
                                                                    <DialogHeader>
                                                                        <DialogTitle className="flex items-center gap-2">
                                                                            <Shield className="h-4 w-4" />
                                                                            {role.name} Role
                                                                        </DialogTitle>
                                                                        <DialogDescription>Complete list of permissions for this role</DialogDescription>
                                                                    </DialogHeader>
                                                                    <ScrollArea className="max-h-96 pr-4">
                                                                        <div className="space-y-3">
                                                                            <div>
                                                                                <h4 className="text-sm font-medium mb-2 text-muted-foreground">
                                                                                    Permissions ({role.permissions?.length || 0})
                                                                                </h4>
                                                                                <div className="space-y-2">
                                                                                    {role.permissions?.map((permission, index) => (
                                                                                        <div
                                                                                            key={index}
                                                                                            className="flex items-center gap-2 p-2 bg-muted/50 rounded-md"
                                                                                        >
                                                                                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full flex-shrink-0"></div>
                                                                                            <span className="text-sm">
                                                {permission
                                                    .split("_")
                                                    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                                                    .join(" ")}
                                              </span>
                                                                                        </div>
                                                                                    )) || (
                                                                                        <p className="text-sm text-muted-foreground italic">
                                                                                            No permissions assigned to this role
                                                                                        </p>
                                                                                    )}
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </ScrollArea>
                                                                </DialogContent>
                                                            </Dialog>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </TooltipProvider>
                                    </div>
                                    {formData.roles.length > 0 && (
                                        <div className="flex flex-wrap gap-2 mt-3">
                                            {formData.roles.map((roleId) => {
                                                const role = roles.find((r) => r._id === roleId)
                                                return role ? (
                                                    <Badge key={roleId} variant="secondary" className="text-xs">
                                                        {role.name}
                                                    </Badge>
                                                ) : null
                                            })}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        <Separator />

                        {/* Action Buttons */}
                        <div className="flex flex-col sm:flex-row justify-end gap-3 pt-6">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => router.push("/lite/admin/users")}
                                className="h-11 px-8"
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={isLoading || (formData.password && !passwordsMatch)}
                                className="h-11 px-8"
                            >
                                {isLoading ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Updating User...
                                    </>
                                ) : (
                                    <>
                                        <UserPen className="mr-2 h-4 w-4" />
                                        Update User
                                    </>
                                )}
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    )
}
