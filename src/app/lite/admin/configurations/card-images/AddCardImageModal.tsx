//@ts-nocheck
"use client"

import type React from "react"
import { useEffect, useState, useCallback } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Footer, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Al<PERSON>, AlertDescription } from "@/components/ui/alert"
import Image from "next/image"
import { Loader2, Upload, X, Plus, AlertCircle, CheckCircle2, ImageIcon, Info } from "lucide-react"
import { toast } from "sonner"
import axiosInstance from "@/utils/axiosInstance"

interface AdvancedAddCardModalProps {
    isOpen: boolean
    onClose: () => void
    onAdd: (newCardImages: CardImageSet[]) => void
}

interface CardImage {
    product_version: string
    front_side: string
    back_side: string
    company: string
    front_side_name?: string
    back_side_name?: string
}

interface CardImageSet {
    id: string
    frontSideFile: File | null
    backSideFile: File | null
    frontSidePreview: string
    backSidePreview: string
    frontSideName: string
    backSideName: string
    status: "pending" | "uploading" | "completed" | "error"
    progress: number
    error?: string
}

export function AdvancedAddCardModal({ isOpen, onClose, onAdd }: AdvancedAddCardModalProps) {
    const [productVersions, setProductVersions] = useState([])
    const [companies, setCompanies] = useState([])
    const [cardSets, setCardSets] = useState<CardImageSet[]>([])
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [activeTab, setActiveTab] = useState("sets")
    const [globalProgress, setGlobalProgress] = useState(0)

    // Global selections
    const [selectedCompany, setSelectedCompany] = useState("")
    const [selectedProductVersion, setSelectedProductVersion] = useState("")

    useEffect(() => {
        async function fetchData() {
            try {
                const [productResponse, companyResponse] = await Promise.all([
                    axiosInstance.get(`/product-versions`),
                    axiosInstance.get(`/company`),
                ])
                setProductVersions(productResponse.data)
                setCompanies(companyResponse.data.data)
            } catch (error: any) {
                toast.error("Failed to load data: " + error.message)
            }
        }
        if (isOpen) {
            fetchData()
        }
    }, [isOpen])

    // Check if selected version allows multiple sets
    const selectedVersion = productVersions.find((v) => v._id === selectedProductVersion)
    const allowsMultipleSets =
        selectedVersion?.version_name?.toLowerCase().includes("cobrand") ||
        selectedVersion?.version_name?.toLowerCase().includes("whitelabel")

    const createNewSet = useCallback(() => {
        const selectedVersionData = productVersions.find((v) => v._id === selectedProductVersion)
        const versionCode = selectedVersionData?.version_code || ""

        const newSet: CardImageSet = {
            id: `set-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            frontSideFile: null,
            backSideFile: null,
            frontSidePreview: "",
            backSidePreview: "",
            frontSideName: `CIF_${versionCode}`,
            backSideName: `CIB_${versionCode}`,
            status: "pending",
            progress: 0,
        }
        setCardSets((prev) => [...prev, newSet])
        return newSet.id
    }, [selectedProductVersion, productVersions])

    const updateSet = useCallback((id: string, updates: Partial<CardImageSet>) => {
        setCardSets((prev) => prev.map((set) => (set.id === id ? { ...set, ...updates } : set)))
    }, [])

    const removeSet = useCallback((id: string) => {
        setCardSets((prev) => prev.filter((set) => set.id !== id))
    }, [])

    // Update all sets when version changes
    useEffect(() => {
        if (selectedProductVersion && cardSets.length > 0) {
            const selectedVersionData = productVersions.find((v) => v._id === selectedProductVersion)
            const versionCode = selectedVersionData?.version_code || ""

            setCardSets((prev) =>
                prev.map((set) => ({
                    ...set,
                    frontSideName: set.frontSideFile ? `CIF_${versionCode}` : `CIF_${versionCode}`,
                    backSideName: set.backSideFile ? `CIB_${versionCode}` : `CIB_${versionCode}`,
                })),
            )
        }
    }, [selectedProductVersion, productVersions])

    // Limit sets to 1 if version doesn't allow multiple
    useEffect(() => {
        if (!allowsMultipleSets && cardSets.length > 1) {
            setCardSets((prev) => prev.slice(0, 1))
            toast.info("Multiple sets are only allowed for cobrand/whitelabel versions")
        }
    }, [allowsMultipleSets, cardSets.length])

    const handleFileUpload = useCallback(
        (id: string, file: File, side: "front" | "back") => {
            const reader = new FileReader()
            reader.onload = () => {
                const previewUrl = reader.result as string
                const selectedVersionData = productVersions.find((v) => v._id === selectedProductVersion)
                const versionCode = selectedVersionData?.version_code || ""

                const updates: Partial<CardImageSet> = {}
                if (side === "front") {
                    updates.frontSideFile = file
                    updates.frontSidePreview = previewUrl
                    updates.frontSideName = `CIF_${versionCode}`
                } else {
                    updates.backSideFile = file
                    updates.backSidePreview = previewUrl
                    updates.backSideName = `CIB_${versionCode}`
                }
                updateSet(id, updates)
            }
            reader.readAsDataURL(file)
        },
        [selectedProductVersion, productVersions, updateSet],
    )

    const handleDragOver = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
    }, [])

    const handleDrop = useCallback(
        (e: React.DragEvent, id: string, side: "front" | "back") => {
            e.preventDefault()
            e.stopPropagation()
            const files = Array.from(e.dataTransfer.files)
            const imageFile = files.find((file) => file.type.startsWith("image/"))
            if (imageFile) {
                handleFileUpload(id, imageFile, side)
            }
        },
        [handleFileUpload],
    )

    const validateSet = (set: CardImageSet): boolean => {
        return !!(selectedCompany && selectedProductVersion && set.frontSideFile && set.backSideFile)
    }

    const uploadSet = async (set: CardImageSet): Promise<boolean> => {
        try {
            updateSet(set.id, { status: "uploading", progress: 0 })
            const formData = new FormData()
            formData.append("product_version", selectedProductVersion)
            formData.append("company", selectedCompany)

            if (set.frontSideFile) {
                formData.append("front_side", set.frontSideFile)
                formData.append("front_side_name", set.frontSideName)
            }
            if (set.backSideFile) {
                formData.append("back_side", set.backSideFile)
                formData.append("back_side_name", set.backSideName)
            }

            // Simulate progress
            for (let i = 0; i <= 100; i += 20) {
                updateSet(set.id, { progress: i })
                await new Promise((resolve) => setTimeout(resolve, 100))
            }

            const response = await axiosInstance.post("/images", formData, {
                headers: { "Content-Type": "multipart/form-data" },
            })

            if (response.status === 201) {
                updateSet(set.id, { status: "completed", progress: 100 })
                return true
            } else {
                throw new Error(response.statusText)
            }
        } catch (error: any) {
            updateSet(set.id, {
                status: "error",
                error: error.message || "Upload failed",
                progress: 0,
            })
            return false
        }
    }

    const handleSubmitAll = async () => {
        const validSets = cardSets.filter(validateSet)
        if (validSets.length === 0) {
            toast.error("Please add at least one complete card set")
            return
        }

        if (!selectedCompany || !selectedProductVersion) {
            toast.error("Please select company and product version")
            return
        }

        setIsSubmitting(true)
        setGlobalProgress(0)
        const results = []

        for (let i = 0; i < validSets.length; i++) {
            const success = await uploadSet(validSets[i])
            if (success) {
                results.push({
                    ...validSets[i],
                    company: selectedCompany,
                    productVersion: selectedProductVersion,
                })
            }
            setGlobalProgress(((i + 1) / validSets.length) * 100)
        }

        const successCount = results.length
        const failCount = validSets.length - successCount

        if (successCount > 0) {
            toast.success(`Successfully uploaded ${successCount} card set(s)`)
            onAdd(results)
        }
        if (failCount > 0) {
            toast.error(`Failed to upload ${failCount} card set(s)`)
        }

        if (successCount === validSets.length) {
            onClose()
        }
        setIsSubmitting(false)
    }

    const resetModal = () => {
        setCardSets([])
        setSelectedCompany("")
        setSelectedProductVersion("")
        setGlobalProgress(0)
        setActiveTab("sets")
    }

    useEffect(() => {
        if (isOpen && cardSets.length === 0) {
            createNewSet()
        }
        if (!isOpen) {
            resetModal()
        }
    }, [isOpen, cardSets.length, createNewSet])

    const completedSets = cardSets.filter((set) => set.status === "completed").length
    const totalSets = cardSets.length
    const validSets = cardSets.filter(validateSet).length

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Advanced Card Image Manager
            <Badge variant="outline" className="ml-auto">
              {validSets}/{totalSets} Ready
            </Badge>
          </DialogTitle>
        </DialogHeader>

          {/* Global Settings */}
          <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-medium text-sm">Global Settings</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Company</Label>
              <Select value={selectedCompany} onValueChange={setSelectedCompany}>
                <SelectTrigger>
                  <SelectValue placeholder="Select company" />
                </SelectTrigger>
                <SelectContent>
                  {companies.map((company) => (
                      <SelectItem key={company._id} value={company._id}>
                      {company.company_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Product Version</Label>
              <Select value={selectedProductVersion} onValueChange={setSelectedProductVersion}>
                <SelectTrigger>
                  <SelectValue placeholder="Select version" />
                </SelectTrigger>
                <SelectContent>
                  {productVersions
                      .filter((version) => version.status === "active")
                      .map((version) => (
                          <SelectItem key={version._id} value={version._id}>
                        {version.version_name} ({version.version_code || ""})
                      </SelectItem>
                      ))}
                </SelectContent>
              </Select>
            </div>
          </div>

              {selectedVersion && !allowsMultipleSets && (
                  <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Multiple card sets are only available for cobrand/whitelabel versions. Current version allows only one
                set.
              </AlertDescription>
            </Alert>
              )}
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="sets">Card Sets ({totalSets})</TabsTrigger>
            <TabsTrigger value="preview">Preview & Review</TabsTrigger>
            <TabsTrigger value="upload">Upload Progress</TabsTrigger>
          </TabsList>

          <TabsContent value="sets" className="mt-4">
            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-4">
                {cardSets.map((set, index) => (
                    <Card key={set.id} className="relative">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">Set {index + 1}</CardTitle>
                        <div className="flex items-center gap-2">
                          {validateSet(set) && (
                              <Badge variant="default" className="bg-green-500">
                              <CheckCircle2 className="h-3 w-3 mr-1" />
                              Ready
                            </Badge>
                          )}
                            {cardSets.length > 1 && (
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeSet(set.id)}
                                    className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                                >
                              <X className="h-4 w-4" />
                            </Button>
                            )}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-6">
                        {/* Front Side */}
                          <div className="space-y-3">
                          <Label className="text-sm font-medium">Front Side</Label>
                          <div
                              className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors cursor-pointer"
                              onDragOver={handleDragOver}
                              onDrop={(e) => handleDrop(e, set.id, "front")}
                              onClick={() => {
                                  const input = document.createElement("input")
                                  input.type = "file"
                                  input.accept = "image/*"
                                  input.onchange = (e) => {
                                      const file = (e.target as HTMLInputElement).files?.[0]
                                      if (file) handleFileUpload(set.id, file, "front")
                                  }
                                  input.click()
                              }}
                          >
                            {set.frontSidePreview ? (
                                <div className="space-y-2">
                                <Image
                                    src={set.frontSidePreview || "/placeholder.svg"}
                                    alt="Front preview"
                                    width={120}
                                    height={80}
                                    className="mx-auto rounded-md object-cover"
                                />
                                <Input
                                    value={set.frontSideName}
                                    onChange={(e) => updateSet(set.id, { frontSideName: e.target.value })}
                                    placeholder="Front side name"
                                    className="text-xs"
                                />
                              </div>
                            ) : (
                                <div className="py-8">
                                <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                                <p className="text-sm text-gray-500">Drop front image here or click to browse</p>
                              </div>
                            )}
                          </div>
                        </div>

                          {/* Back Side */}
                          <div className="space-y-3">
                          <Label className="text-sm font-medium">Back Side</Label>
                          <div
                              className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors cursor-pointer"
                              onDragOver={handleDragOver}
                              onDrop={(e) => handleDrop(e, set.id, "back")}
                              onClick={() => {
                                  const input = document.createElement("input")
                                  input.type = "file"
                                  input.accept = "image/*"
                                  input.onchange = (e) => {
                                      const file = (e.target as HTMLInputElement).files?.[0]
                                      if (file) handleFileUpload(set.id, file, "back")
                                  }
                                  input.click()
                              }}
                          >
                            {set.backSidePreview ? (
                                <div className="space-y-2">
                                <Image
                                    src={set.backSidePreview || "/placeholder.svg"}
                                    alt="Back preview"
                                    width={120}
                                    height={80}
                                    className="mx-auto rounded-md object-cover"
                                />
                                <Input
                                    value={set.backSideName}
                                    onChange={(e) => updateSet(set.id, { backSideName: e.target.value })}
                                    placeholder="Back side name"
                                    className="text-xs"
                                />
                              </div>
                            ) : (
                                <div className="py-8">
                                <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                                <p className="text-sm text-gray-500">Drop back image here or click to browse</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                        {set.status === "uploading" && (
                            <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Uploading...</span>
                            <span>{set.progress}%</span>
                          </div>
                          <Progress value={set.progress} />
                        </div>
                        )}

                        {set.status === "error" && (
                            <div className="flex items-center gap-2 text-red-600 text-sm">
                          <AlertCircle className="h-4 w-4" />
                                {set.error}
                        </div>
                        )}
                    </CardContent>
                  </Card>
                ))}

                  {allowsMultipleSets && (
                      <Button
                          variant="outline"
                          onClick={createNewSet}
                          className="w-full border-dashed border-2 h-16 bg-transparent"
                          disabled={!selectedProductVersion}
                      >
                    <Plus className="h-5 w-5 mr-2" />
                    Add Another Card Set
                  </Button>
                  )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="preview" className="mt-4">
            <ScrollArea className="h-[500px] pr-4">
              <div className="space-y-6">
                {cardSets.filter(validateSet).map((set, index) => {
                    const company = companies.find((c) => c._id === selectedCompany)
                    const version = productVersions.find((v) => v._id === selectedProductVersion)
                    return (
                        <Card key={set.id}>
                      <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                          <span>Set {index + 1} Preview</span>
                          <Badge variant="outline">
                            {company?.company_name} - {version?.version_name}
                          </Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-6">
                          <div className="text-center">
                            <Label className="text-sm font-medium mb-2 block">Front Side</Label>
                            <Image
                                src={set.frontSidePreview || "/placeholder.svg"}
                                alt="Front preview"
                                width={200}
                                height={130}
                                className="mx-auto rounded-lg border shadow-sm"
                            />
                            <p className="text-xs text-gray-500 mt-2">{set.frontSideName}</p>
                          </div>
                          <div className="text-center">
                            <Label className="text-sm font-medium mb-2 block">Back Side</Label>
                            <Image
                                src={set.backSidePreview || "/placeholder.svg"}
                                alt="Back preview"
                                width={200}
                                height={130}
                                className="mx-auto rounded-lg border shadow-sm"
                            />
                            <p className="text-xs text-gray-500 mt-2">{set.backSideName}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    )
                })}

                  {cardSets.filter(validateSet).length === 0 && (
                      <div className="text-center py-12 text-gray-500">
                    <ImageIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No complete card sets to preview</p>
                    <p className="text-sm">Complete at least one card set to see preview</p>
                  </div>
                  )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="upload" className="mt-4">
            <div className="space-y-6">
              {isSubmitting && (
                  <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Overall Progress</span>
                    <span>{Math.round(globalProgress)}%</span>
                  </div>
                  <Progress value={globalProgress} />
                </div>
              )}

                <ScrollArea className="h-[400px] pr-4">
                <div className="space-y-4">
                  {cardSets.map((set, index) => (
                      <Card
                          key={set.id}
                          className={`${
                              set.status === "completed"
                                  ? "border-green-200 bg-green-50"
                                  : set.status === "error"
                                      ? "border-red-200 bg-red-50"
                                      : set.status === "uploading"
                                          ? "border-blue-200 bg-blue-50"
                                          : "border-gray-200"
                          }`}
                      >
                      <CardContent className="pt-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium">Set {index + 1}</h4>
                            <p className="text-sm text-gray-500">
                              {companies.find((c) => c._id === selectedCompany)?.company_name} -{" "}
                                {productVersions.find((v) => v._id === selectedProductVersion)?.version_name}
                            </p>
                          </div>
                          <div className="flex items-center gap-2">
                            {set.status === "completed" && (
                                <Badge variant="default" className="bg-green-500">
                                <CheckCircle2 className="h-3 w-3 mr-1" />
                                Completed
                              </Badge>
                            )}
                              {set.status === "error" && (
                                  <Badge variant="destructive">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                Failed
                              </Badge>
                              )}
                              {set.status === "uploading" && (
                                  <Badge variant="secondary">
                                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                Uploading
                              </Badge>
                              )}
                          </div>
                        </div>

                          {set.status === "uploading" && (
                              <div className="mt-3">
                            <Progress value={set.progress} />
                          </div>
                          )}

                          {set.status === "error" && set.error && (
                              <p className="text-sm text-red-600 mt-2">{set.error}</p>
                          )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>
        </Tabs>

        <Separator />

        <DialogFooter className="flex items-center justify-between">
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <span>
              {validSets} of {totalSets} sets ready
            </span>
              {completedSets > 0 && <span className="text-green-600">{completedSets} completed</span>}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
              {completedSets > 0 ? "Close" : "Cancel"}
            </Button>
            <Button onClick={handleSubmitAll} disabled={isSubmitting || validSets === 0} className="min-w-[120px]">
              {isSubmitting ? (
                  <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                  <>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload All ({validSets})
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    )
}
