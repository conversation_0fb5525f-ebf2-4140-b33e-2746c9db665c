import React, {useState} from "react";
import {<PERSON><PERSON><PERSON>, Too<PERSON><PERSON>Content, TooltipTrigger} from "@/components/ui/tooltip"; // Assuming a tooltip component is available
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from "@/components/ui/dialog";
import {Label} from "@/components/ui/label";
import {Textarea} from "@/components/ui/textarea";
import {CheckCircleIcon, XCircleIcon} from "@heroicons/react/24/outline";
import {Edit2Icon, TrashIcon} from "lucide-react";
import {Button} from "@/components/ui/button";
import axiosInstance from "@/utils/axiosInstance";
import Swal from 'sweetalert2';

export const SchemeActionsButtonGroup = ({
                                             scheme,
                                             userHasApprovePermission,
                                             userHasDeletePermission,
                                             handleDeleteClick,
                                             fetchCardScheme
                                         }) => {
    const [isDialogOpen, setIsDialogOpen] = useState({
        approve: false,
        decline: false,
        modify: false,
        delete: false,
    });

    const [declineReason, setDeclineReason] = useState("");
    const [modifyInstructions, setModifyInstructions] = useState("");

    const handleDialogToggle = (type, state) => {
        setIsDialogOpen((prev) => ({...prev, [type]: state}));
    };

    const handleApprove = async () => {
        try {
            const response = await axiosInstance.post("cardScheme/approve-scheme", {
                schemeId: scheme?._id, // Pass the scheme ID or any relevant data
            });

            if (response.status === 200) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Scheme approved successfully!',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                });
                fetchCardScheme();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: `Approval failed with status: ${response.status}`,
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                });
            }
        } catch (error) {
            // Handle errors
            console.error("Error approving scheme:", error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'An error occurred while approving the scheme. Please try again.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            });
        } finally {
            handleDialogToggle("approve", false); // Close the dialog
        }
    };

    const handleDecline = async () => {
        try {
            if (!declineReason.trim()) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Warning',
                    text: 'Please provide a reason for declining.',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                });
                return;
            }

            const response = await axiosInstance.post("cardScheme/decline-scheme", {
                schemeId: scheme?._id, // Pass the scheme ID or any relevant data
                reason: declineReason, // Include the reason for declining
            });

            if (response.status === 200) {
                // Handle success
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Scheme declined successfully!',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: `Decline failed with status: ${response.status}`,
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                });
            }
        } catch (error) {
            console.error("Error declining scheme:", error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'An error occurred while declining the scheme. Please try again.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            });
        } finally {
            setDeclineReason(""); // Clear the input field
            handleDialogToggle("decline", false); // Close the dialog
        }
    };

    const handleModify = async () => {
        try {
            if (!modifyInstructions.trim()) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Warning',
                    text: 'Please provide modification instructions.',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                });
                return;
            }

            const response = await axiosInstance.post("cardScheme/modify-scheme", {
                schemeId: scheme?._id, // Pass the scheme ID or any relevant data
                instructions: modifyInstructions, // Include modification instructions
            });

            if (response.status === 200) {
                // Handle success
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Scheme modification request submitted successfully!',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: `Modification failed with status: ${response.status}`,
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                });
            }
        } catch (error) {
            console.error("Error modifying scheme:", error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'An error occurred while modifying the scheme. Please try again.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            });
        } finally {
            setModifyInstructions(""); // Clear the input field
            handleDialogToggle("modify", false); // Close the dialog
        }
    };

    return (
        <div className="flex space-x-4">
            {userHasApprovePermission && (
                <>
                    {/* Approve Button */}
                    <Tooltip>
                        <TooltipTrigger>
                            <Button
                                className="p-2 rounded-md bg-green-500 text-white hover:bg-green-600"
                                onClick={() => handleDialogToggle("approve", true)}
                            >
                                <CheckCircleIcon className="h-6 w-6"/>
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Approve</TooltipContent>
                    </Tooltip>
                    <Dialog
                        open={isDialogOpen.approve}
                        onOpenChange={(state) => handleDialogToggle("approve", state)}
                    >
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Confirm Approval</DialogTitle>
                                <DialogDescription>
                                    Are you sure you want to approve the card scheme "{scheme?.scheme_name}"?
                                </DialogDescription>
                            </DialogHeader>
                            <DialogFooter>
                                <Button className="btn btn-outline"
                                        onClick={() => handleDialogToggle("approve", false)}>
                                    Cancel
                                </Button>
                                <Button className="btn btn-primary" onClick={handleApprove}>
                                    Confirm Approval
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>

                    {/* Decline Button */}
                    <Tooltip>
                        <TooltipTrigger>
                            <Button
                                className="p-2 rounded-md bg-yellow-500 text-white hover:bg-yellow-600"
                                onClick={() => handleDialogToggle("decline", true)}
                            >
                                <XCircleIcon className="h-6 w-6"/>
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Decline</TooltipContent>
                    </Tooltip>
                    <Dialog
                        open={isDialogOpen.decline}
                        onOpenChange={(state) => handleDialogToggle("decline", state)}
                    >
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Decline Card Scheme</DialogTitle>
                                <DialogDescription>
                                    Please provide a reason for declining "{scheme?.scheme_name}".
                                </DialogDescription>
                            </DialogHeader>
                            <div className="py-4">
                                <Label htmlFor="decline-reason">Reason for Decline</Label>
                                <Textarea
                                    id="decline-reason"
                                    value={declineReason}
                                    onChange={(e) => setDeclineReason(e.target.value)}
                                    placeholder="Enter the reason for declining this card scheme..."
                                    className="mt-2"
                                    required
                                />
                            </div>
                            <DialogFooter>
                                <Button
                                    className="btn btn-outline"
                                    onClick={() => {
                                        handleDialogToggle("decline", false);
                                        setDeclineReason("");
                                    }}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    className="btn btn-danger"
                                    onClick={handleDecline}
                                    disabled={!declineReason.trim()}
                                >
                                    Confirm Decline
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>

                    {/* Modify Button */}
                    <Tooltip>
                        <TooltipTrigger>
                            <Button
                                className="p-2 rounded-md bg-blue-500 text-white hover:bg-blue-600"
                                onClick={() => handleDialogToggle("modify", true)}
                            >
                                <Edit2Icon className="h-6 w-6"/>
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Modify</TooltipContent>
                    </Tooltip>
                    <Dialog
                        open={isDialogOpen.modify}
                        onOpenChange={(state) => handleDialogToggle("modify", state)}
                    >
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Modify Card Scheme</DialogTitle>
                                <DialogDescription>
                                    Please provide modification instructions for "{scheme?.scheme_name}".
                                </DialogDescription>
                            </DialogHeader>
                            <div className="py-4">
                                <Label htmlFor="modify-instructions">
                                    Modification Instructions
                                </Label>
                                <Textarea
                                    id="modify-instructions"
                                    value={modifyInstructions}
                                    onChange={(e) => setModifyInstructions(e.target.value)}
                                    placeholder="Enter detailed instructions for modifications needed..."
                                    className="mt-2"
                                    required
                                />
                            </div>
                            <DialogFooter>
                                <Button
                                    className="btn btn-outline"
                                    onClick={() => {
                                        handleDialogToggle("modify", false);
                                        setModifyInstructions("");
                                    }}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    className="btn btn-primary"
                                    onClick={handleModify}
                                    disabled={!modifyInstructions.trim()}
                                >
                                    Confirm Modification
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                </>
            )}

            {userHasDeletePermission && (
                <>
                    {/* Delete Button */}
                    <Tooltip>
                        <TooltipTrigger>
                            <Button
                                className="p-2 rounded-md bg-red-500 text-white hover:bg-red-600"
                                onClick={() => handleDialogToggle("delete", true)}
                            >
                                <TrashIcon className="h-6 w-6"/>
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Delete</TooltipContent>
                    </Tooltip>
                    <Dialog
                        open={isDialogOpen.delete}
                        onOpenChange={(state) => handleDialogToggle("delete", state)}
                    >
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Delete Card Scheme</DialogTitle>
                                <DialogDescription>
                                    Deletion of this record will be sent to the administrator for approval. Are you sure
                                    to proceed?
                                </DialogDescription>
                            </DialogHeader>
                            <DialogFooter>
                                <Button
                                    onClick={() => handleDialogToggle("delete", false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    className="btn btn-danger"
                                    onClick={() => {
                                        handleDeleteClick(scheme._id);
                                        handleDialogToggle("delete", false);
                                    }}
                                >
                                    Delete
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                </>
            )}
        </div>
    );
};