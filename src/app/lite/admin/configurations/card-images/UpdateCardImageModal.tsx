//@ts-nocheck
'use client'

import {useState} from 'react'
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/components/ui/dialog"
import {Button} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import Image from 'next/image'
import axios from '@/utils/axiosInstance'

interface UpdateCardImageModalProps {
    isOpen: boolean
    onClose: () => void
    cardImage: CardImage
    onUpdate: (updatedCardImage: CardImage) => void
}

interface CardImage {
    _id: string
    scheme_name: string
    front_side: string
    back_side: string
    card_type: string
    card_category: string
    created_at: string
    created_by: string
}

export function UpdateCardImageModal({isOpen, onClose, cardImage, onUpdate}: UpdateCardImageModalProps) {
    const [updatedCardImage, setUpdatedCardImage] = useState<CardImage>(cardImage)
    const [frontSidePreview, setFrontSidePreview] = useState<string>(cardImage.front_side)
    const [backSidePreview, setBackSidePreview] = useState<string>(cardImage.back_side)

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const {name, value} = e.target
        setUpdatedCardImage(prev => ({...prev, [name]: value}))
    }

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, side: 'front_side' | 'back_side') => {
        const file = e.target.files?.[0]
        if (file) {
            const reader = new FileReader()
            reader.onload = () => {
                const previewUrl = reader.result as string
                if (side === 'front_side') {
                    setFrontSidePreview(previewUrl)
                    setUpdatedCardImage(prev => ({...prev, front_side: previewUrl}))
                } else if (side === 'back_side') {
                    setBackSidePreview(previewUrl)
                    setUpdatedCardImage(prev => ({...prev, back_side: previewUrl}))
                }
            }
            reader.readAsDataURL(file)
        }
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        try {
            const formData = new FormData()
            formData.append('scheme_name', updatedCardImage.scheme_name)
            formData.append('card_type', updatedCardImage.card_type)
            formData.append('card_category', updatedCardImage.card_category)
            formData.append('front_side', updatedCardImage.front_side)
            formData.append('back_side', updatedCardImage.back_side)

            const response = await axios.put(`images/${updatedCardImage._id}`, formData)

            if (response.status === 200) {
                onUpdate(response.data)
                onClose()
            } else {
                throw new Error('Error updating card image')
            }
        } catch (error) {
            console.error('Error:', error)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Update Card Image</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="scheme_name" className="text-right">Scheme Name</Label>
                            <Input
                                id="scheme_name"
                                name="scheme_name"
                                value={updatedCardImage.scheme_name}
                                onChange={handleInputChange}
                                className="col-span-3"
                                required
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="front_side" className="text-right">Front Side</Label>
                            <Input
                                id="front_side"
                                name="front_side"
                                type="file"
                                accept="image/*"
                                onChange={(e) => handleFileChange(e, 'front_side')}
                                className="col-span-3"
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="back_side" className="text-right">Back Side</Label>
                            <Input
                                id="back_side"
                                name="back_side"
                                type="file"
                                accept="image/*"
                                onChange={(e) => handleFileChange(e, 'back_side')}
                                className="col-span-3"
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="card_type" className="text-right">Card Type</Label>
                            <select
                                id="card_type"
                                name="card_type"
                                value={updatedCardImage.card_type}
                                onChange={handleInputChange}
                                className="col-span-3"
                            >
                                <option value="virtual">Virtual</option>
                                <option value="physical">Physical</option>
                            </select>
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="card_category" className="text-right">Card Category</Label>
                            <select
                                id="card_category"
                                name="card_category"
                                value={updatedCardImage.card_category}
                                onChange={handleInputChange}
                                className="col-span-3"
                            >
                                <option value="Visa prepaid">Visa Prepaid</option>
                                <option value="Consumer Debit">Consumer Debit</option>
                            </select>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label>Front Side Preview</Label>
                                {frontSidePreview && (
                                    <Image
                                        src={frontSidePreview}
                                        alt="Front Side Preview"
                                        width={160}
                                        height={100}
                                        className="mt-2 rounded-md"
                                    />
                                )}
                            </div>
                            <div>
                                <Label>Back Side Preview</Label>
                                {backSidePreview && (
                                    <Image
                                        src={backSidePreview}
                                        alt="Back Side Preview"
                                        width={160}
                                        height={100}
                                        className="mt-2 rounded-md"
                                    />
                                )}
                            </div>
                        </div>
                    </div>
                    <DialogFooter>
                        <Button type="submit">Save</Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}
