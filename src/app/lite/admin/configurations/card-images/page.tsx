//@ts-nocheck

"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useAppSelector } from "@/store/hooks"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { UpdateCardImageModal } from "./UpdateCardImageModal"
import axios from "@/utils/axiosInstance"
import { ChevronDown, ChevronUp, Loader2, SortAsc, Trash2 } from "lucide-react"
import {  AdvancedAddCardModal as AddCardImageModal} from "./AddCardImageModal"
import { formatDate } from "@/utils/helpers"
import Swal from 'sweetalert2'

interface CardImage {
    _id: string
    scheme_name: string
    company: string
    company_name?: string
    front_side: string
    back_side: string
    card_type: string
    card_category: string
    created_at: string
    createdAt: string
    created_by: string
    front_side_name?: string
    back_side_name?: string
    product_version?: { version_name: string }
}

interface Role {
    _id: string
    name: string
    description: string
    permissions: string[]
    createdAt: string
    updatedAt: string
}

type SortKey = 'id' | 'product_version' | 'company' | 'createdAt' | '';
type SortDirection = 'asc' | 'desc';

const asset = process.env.NEXT_PUBLIC_API_URL

export default function CardImages() {
    const [cardImages, setCardImages] = useState<CardImage[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [selectedCardImage, setSelectedCardImage] = useState<CardImage | null>(null)
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [isAddModalOpen, setIsAddModalOpen] = useState(false)
    const [sortKey, setSortKey] = useState<SortKey>('')
    const [sortDirection, setSortDirection] = useState<SortDirection>('asc')
    const user = useAppSelector((state) => state.user.user)
    const roles = user.roles || []

    useEffect(() => {
        fetchData()
    }, [])

    const fetchData = async () => {
        try {
            setIsLoading(true)
            setError(null)
            const response = await axios.get<CardImage[]>("/images")
            setCardImages(response.data)
        } catch (error) {
            console.error("Error fetching data", error)
            setError("Failed to fetch card images. Please try again later.")
        } finally {
            setIsLoading(false)
        }
    }

    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions))
    }

    const hasPermission = (permission: string): boolean => {
        if (roles.length === 0) return true
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }

    const userHasViewPermission = hasPermission("Card Library_View")
    const userHasUpdatePermission = hasPermission("Card Library_Create")
    const userHasDeletePermission = hasPermission("Card Library_Delete")

    const handleUpdateClick = (cardImage: CardImage) => {
        setSelectedCardImage(cardImage)
        setIsModalOpen(true)
    }

    const handleCloseModal = () => {
        setIsModalOpen(false)
        setSelectedCardImage(null)
    }

    const handleUpdateCardImage = (updatedCardImage: CardImage) => {
        setCardImages((prevImages) => prevImages.map((img) => (img._id === updatedCardImage._id ? updatedCardImage : img)))
    }

    const handleAddCardImage = (newCardImage: CardImage) => {
        setCardImages((prevImages) => [...prevImages, newCardImage])
    }

    const handleDeleteCardImage = async (id: string) => {
        const result = await Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        })

        if (result.isConfirmed) {
            try {
                await axios.delete(`/images/${id}`)
                setCardImages((prevImages) => prevImages.filter((img) => img._id !== id))
                Swal.fire({
                    icon: 'success',
                    title: 'Deleted!',
                    text: 'Card image has been deleted.',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                })
            } catch (error) {
                console.error("Error deleting card image", error)
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to delete card image. Please try again.',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                })
            }
        }
    }

    const handleSort = (key: SortKey) => {
        if (sortKey === key) {
            // Toggle direction if same key
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
        } else {
            // Set new key and default to ascending
            setSortKey(key)
            setSortDirection('asc')
        }
    }

    const getSortedData = () => {
        if (!sortKey) return cardImages

        return [...cardImages].sort((a, b) => {
            let aValue, bValue;

            switch (sortKey) {
                case 'id':
                    // Sort by index (we'll just use the current array position)
                    return sortDirection === 'asc' ? 1 : -1;
                case 'product_version':
                    aValue = a.product_version?.version_name || '';
                    bValue = b.product_version?.version_name || '';
                    break;
                case 'company':
                    aValue = a.company?.company_name || a.company || '';
                    bValue = b.company?.company_name || b.company || '';
                    break;
                case 'createdAt':
                    aValue = new Date(a.createdAt || a.created_at || '').getTime();
                    bValue = new Date(b.createdAt || b.created_at || '').getTime();
                    break;
                default:
                    return 0;
            }

            if (aValue < bValue) {
                return sortDirection === 'asc' ? -1 : 1;
            }
            if (aValue > bValue) {
                return sortDirection === 'asc' ? 1 : -1;
            }
            return 0;
        });
    }

    const SortIcon = ({ columnKey }: { columnKey: SortKey }) => {
        if (sortKey !== columnKey) {
            return <SortAsc className="ml-1 h-4 w-4 inline opacity-30" />;
        }
        return sortDirection === 'asc' ?
            <ChevronUp className="ml-1 h-4 w-4 inline" /> :
            <ChevronDown className="ml-1 h-4 w-4 inline" />;
    };

    return (
        <div className="w-full px-4 md:px-6 lg:px-8">
            <Card>
                <CardHeader>
                    <CardTitle>Saved Card Images</CardTitle>
                    <CardDescription>View and manage your saved Card Images</CardDescription>
                </CardHeader>
                <CardContent>
                    {userHasUpdatePermission && (
                        <Button onClick={() => setIsAddModalOpen(true)} className="mb-4">
                            Add New Card Image
                        </Button>
                    )}

                    {isLoading ? (
                        <div className="flex justify-center items-center h-64">
                            <Loader2 className="h-8 w-8 animate-spin" />
                        </div>
                    ) : error ? (
                        <div className="text-center text-red-500">{error}</div>
                    ) : (
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead  className="cursor-pointer hover:bg-gray-100"   >
                                        ID
                                    </TableHead>
                                    <TableHead
                                        className="cursor-pointer hover:bg-gray-100"
                                        onClick={() => handleSort('product_version')}
                                    >
                                        Product Version <SortIcon columnKey="product_version" />
                                    </TableHead>
                                    <TableHead
                                        className="cursor-pointer hover:bg-gray-100"
                                        onClick={() => handleSort('company')}
                                    >
                                        Company Name <SortIcon columnKey="company" />
                                    </TableHead>
                                    <TableHead>Front Side</TableHead>
                                    <TableHead>Back Side</TableHead>
                                    <TableHead>Visual IDs</TableHead>
                                    <TableHead
                                        className="cursor-pointer hover:bg-gray-100"
                                        onClick={() => handleSort('createdAt')}
                                    >
                                        Created At <SortIcon columnKey="createdAt" />
                                    </TableHead>
                                    {userHasDeletePermission && <TableHead>Actions</TableHead>}
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {getSortedData().map((image, index) => (
                                    <TableRow key={image._id}>
                                        <TableCell>{index + 1}</TableCell>
                                        <TableCell>{image.product_version?.version_name}</TableCell>
                                        <TableCell>{image.company?.company_name || image.company}</TableCell>
                                        <TableCell>
                                            <Image
                                                unoptimized
                                                src={`${asset}${image.front_side}`}
                                                alt={`${image.scheme_name} Front`}
                                                width={100}
                                                height={60}
                                                loading="lazy"
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <Image
                                                unoptimized
                                                src={`${asset}${image.back_side}`}
                                                alt={`${image.scheme_name} Back`}
                                                width={100}
                                                height={60}
                                                loading="lazy"
                                            />
                                        </TableCell>
                                        <TableCell>{image.front_side_name}<br/>{image.back_side_name}</TableCell>
                                        <TableCell>{formatDate(image.createdAt || image.created_at)}</TableCell>
                                        {userHasDeletePermission && (
                                            <TableCell>
                                                <Button variant="destructive" size="sm" onClick={() => handleDeleteCardImage(image._id)}>
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </TableCell>
                                        )}
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    )}
                </CardContent>
            </Card>
            {selectedCardImage && (
                <UpdateCardImageModal
                    isOpen={isModalOpen}
                    onClose={handleCloseModal}
                    cardImage={selectedCardImage}
                    onUpdate={handleUpdateCardImage}
                />
            )}
            <AddCardImageModal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} onAdd={handleAddCardImage} />
        </div>
    )
}