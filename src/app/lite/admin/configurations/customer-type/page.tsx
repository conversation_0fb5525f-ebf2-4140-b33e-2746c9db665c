//@ts-nocheck
'use client'
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import axios from "@/utils/axiosInstance"
import { TrashIcon, ArrowUpDown } from "lucide-react"
import { formatDate } from "@/utils/helpers"

interface CustomerType {
    _id: number
    type: string
    code: string
    status: string
    created_at: string
    created_by: string
}

export default function CustomerType() {
    const [formData, setFormData] = useState({
        type: "",
        code: "",created_by: localStorage.getItem("user")
    })
    const [savedCustomerTypes, setSavedCustomerTypes] = useState<CustomerType[]>([])
    const [searchTerm, setSearchTerm] = useState("")
    const [sortConfig, setSortConfig] = useState<{ key: keyof CustomerType; direction: 'asc' | 'desc' } | null>(null)
    const [error, setError] = useState<string>("")
    const [deleteId, setDeleteId] = useState<number | null>(null)

    const fetchCustomerTypes = async () => {
        try {
            const { data } = await axios.get("/customer-types")
            setSavedCustomerTypes(data)
        } catch (error) {
            console.error("Error fetching customer types", error)
        }
    }

    useEffect(() => {
        fetchCustomerTypes()
    }, [])

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData((prev) => ({ ...prev, [name]: value }))

        if (name === "type") {
            const isDuplicate = savedCustomerTypes.some(
                (t) => t.type.trim().toLowerCase() === value.trim().toLowerCase()
            )
            setError(isDuplicate ? "This Customer type already exists. Please enter a unique name." : "")
        }
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        try {
            await axios.post("/customer-types", formData)
            setFormData({ type: "", code: "" })
            await fetchCustomerTypes()
        } catch (error) {
            console.error("Error creating customer type", error)
        }
    }

    const handleDelete = async () => {
        if (deleteId === null) return

        try {
            await axios.delete(`/customer-types/${deleteId}`)
            await fetchCustomerTypes()
            setDeleteId(null)
            await fetchCustomerTypes()
        } catch (error) {
            console.error("Error deleting customer type", error)
        }
    }

    const handleSort = (key: keyof CustomerType) => {
        let direction: 'asc' | 'desc' = 'asc'
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc'
        }
        setSortConfig({ key, direction })
    }

    const filteredAndSortedTypes = savedCustomerTypes
        .filter((type) =>
            type.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
            type.code.toLowerCase().includes(searchTerm.toLowerCase())
        )
        .sort((a, b) => {
            if (sortConfig === null) {
                return 0
            }
            const { key, direction } = sortConfig
            if (a[key] < b[key]) {
                return direction === 'asc' ? -1 : 1
            }
            if (a[key] > b[key]) {
                return direction === 'asc' ? 1 : -1
            }
            return 0
        })

    return (
        <div className="w-full px-4 md:px-6 lg:px-8">
            <div className="flex flex-col gap-4 md:flex-row md:gap-6">
                <div className="w-full md:w-1/3">
                    <Card>
                        <CardHeader>
                            <CardTitle>Customer Type Request</CardTitle>
                            <CardDescription>Add new customer type</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="type">Type</Label>
                                    <Input
                                        id="type"
                                        name="type"
                                        placeholder="Enter Customer Type"
                                        value={formData.type}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                                {error && <p className="text-red-600">{error}</p>}
                                <div className="space-y-2">
                                    <Label htmlFor="code">Code</Label>
                                    <Input
                                        id="code"
                                        name="code"
                                        placeholder="Enter Code"
                                        value={formData.code}
                                        onChange={handleInputChange}
                                        required
                                    />
                                </div>
                                <div className="flex justify-between">
                                    <Button type="button" variant="outline">
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={!!error}>Request</Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
                <div className="w-full md:w-2/3">
                    <Card>
                        <CardHeader>
                            <CardTitle>Saved Customer Types</CardTitle>
                            <CardDescription>View and manage your saved customer types</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="mb-4">
                                <Input
                                    placeholder="Search customer types..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="w-[100px]">ID</TableHead>
                                        <TableHead className="cursor-pointer" onClick={() => handleSort('type')}>
                                            Type <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                                        </TableHead>
                                        <TableHead className="cursor-pointer" onClick={() => handleSort('code')}>
                                            Code <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                                        </TableHead>
                                        <TableHead className="cursor-pointer" onClick={() => handleSort('created_by')}>
                                            Created By <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                                        </TableHead>

                                        <TableHead className="cursor-pointer" onClick={() => handleSort('created_at')}>
                                            Created At <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                                        </TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredAndSortedTypes.map((type, index) => (
                                        <TableRow key={type.id}>
                                            <TableCell>{index + 1}</TableCell>
                                            <TableCell>{type.type || 'N/A'}</TableCell>
                                            <TableCell>{type.code?.toUpperCase() || 'N/A'}</TableCell>
                                            <TableCell>{type.created_by.name || 'N/A'}</TableCell>
                                            <TableCell>{type.created_at ? formatDate(type.created_at) : 'N/A'}</TableCell>
                                            <TableCell>{type.status || 'N/A'}</TableCell>
                                            <TableCell>
                                                <AlertDialog>
                                                    <AlertDialogTrigger asChild>
                                                        <Button
                                                            variant="destructive"
                                                            size="sm"
                                                            onClick={() => setDeleteId(type._id)}
                                                        >
                                                            <TrashIcon className="h-4 w-4" />
                                                        </Button>
                                                    </AlertDialogTrigger>
                                                    <AlertDialogContent>
                                                        <AlertDialogHeader>
                                                            <AlertDialogTitle>Action: Delete</AlertDialogTitle>
                                                            <AlertDialogDescription>
                                                                Deletion of this record will be sent to administrator for approval. Are you sure to proceed?
                                                            </AlertDialogDescription>
                                                        </AlertDialogHeader>
                                                        <AlertDialogFooter>
                                                            <AlertDialogCancel onClick={() => setDeleteId(null)}>
                                                                Cancel
                                                            </AlertDialogCancel>
                                                            <AlertDialogAction onClick={handleDelete}>
                                                                Delete
                                                            </AlertDialogAction>
                                                        </AlertDialogFooter>
                                                    </AlertDialogContent>
                                                </AlertDialog>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}