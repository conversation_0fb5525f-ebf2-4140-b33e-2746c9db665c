//@ts-nocheck
"use client"

import React, {useEffect, useState} from "react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {Tabs} from "flowbite-react"
import {Sheet, SheetClose, SheetContent, SheetHeader, SheetTitle, SheetTrigger} from "@/components/ui/sheet"
import axios from "@/utils/axiosInstance"
import {ArrowUpDown, Trash2} from "lucide-react"
import {formatDate} from "@/utils/helpers"
import {useAppSelector} from "@/store/hooks"
import {ActionsButtonGroup} from "@/components/ActionButtons"
import {LoadingOverlay} from "@/components/LoadingOverlay"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import DataExporter from "@/components/DataExporter";

interface ProgrammeManagerType {
    _id: number
    manager_type: string
    bin_type: string
    programme_type: string
    status: string
    created_at: string
    created_by: string
}

interface Message {
    type: "success" | "error"
    content: string
}

interface BinRange {
    _id: number
    binType: string
    binCode: string
    binCodePrefix: string
    binCodeSuffix: string
    currency: string
    bin_start: string
    bin_end: string
    created_at: string
    created_by: any
    status: string
    category?: string // Keep for backward compatibility
}

interface BinUsage {
    _id: number
    usage: string
    status: string
    created_at: string
    created_by: string
}

export default function ProgrammeManagerType() {
    const [formData, setFormData] = useState({
        bin_currency: "",
        bin_usage: "",
        manager_type: "",
        bin_type: "",
        bin_range: "",
        created_by: localStorage.getItem("user"),
    })
    const [savedManagerTypes, setSavedManagerTypes] = useState<ProgrammeManagerType[]>([])
    const [searchTerm, setSearchTerm] = useState("")
    const [sortConfig, setSortConfig] = useState<{
        key: keyof ProgrammeManagerType
        direction: "asc" | "desc"
    } | null>(null)
    const [error, setError] = useState<string>("")
    const [selectedBin, setSelectedBin] = useState<string>("")
    const [selectedCurrency, setSelectedCurrency] = useState<string>("")
    const [deleteId, setDeleteId] = useState<number | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [message, setMessage] = useState<Message | null>(null)
    const [isOverlayVisible, setIsOverlayVisible] = useState(false)
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [savedProgrammeTypes, setSavedProgrammeTypes] = useState<any[]>([])
    const [binTypes, setBinTypes] = useState<any[]>([])
    const user = useAppSelector((state) => state.user.user)
    const [roles, setRoles] = useState<Role[]>([])
    const [savedBinRanges, setSavedBinRanges] = useState<BinRange[]>([])
    const [savedBinUsages, setSavedBinUsages] = useState<BinUsage[]>([])

    useEffect(() => {
        setRoles(user.roles)
        fetchManagerTypes()
        fetchData()
    }, [user.roles])

    useEffect(() => {
        fetchInitialData()
    }, [])

    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions))
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }

    const userHasRequestPermission = hasPermission(roles, "Programme Manager Type_Request")
    const userHasDeletePermission = hasPermission(roles, "Programme Manager Type_Delete")
    const userHasApprovePermission = hasPermission(roles, "Programme Manager Type_Approve")
    const userHasViewPermission = hasPermission(roles, "Programme Manager Type_View")

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        if (error) return

        setIsLoading(true)
        try {
            await axios.post("/programme-manager-types", formData)
            await fetchManagerTypes()
            setMessage({ type: "success", content: "Programme Manager Type created successfully!" })
            setFormData({
                bin_currency: "",
                bin_usage: "",
                manager_type: "",
                bin_type: "",
                bin_range: "",
                created_by: localStorage.getItem("user"),
            })
            setSelectedBin("")
            setSelectedCurrency("")
        } catch (error) {
            console.error("Error creating Programme Manager Type", error)
            setMessage({ type: "error", content: "Error creating Programme Manager Type. Please try again." })
        } finally {
            setIsLoading(false)
        }
    }

    const fetchInitialData = async () => {
        setIsLoading(true)
        try {
            const response = await axios.get("/bin-range")
            setSavedBinRanges(response.data)
            const r = await axios.get("/bin-usage")
            setSavedBinUsages(r.data)
        } catch (error) {
            console.error("Error fetching BIN Types", error)
            setAlert({message: "Error fetching data. Please try again.", type: "error"})
        } finally {
            setIsLoading(false)
        }
    }

    const fetchData = async () => {
        setIsLoading(true)
        try {
            const [programmeTypes, bin_types] = await Promise.all([
                axios.get("cardProgram/programme-types"),
                axios.get("card-types"),
            ])
            setSavedProgrammeTypes(programmeTypes.data)
            setBinTypes(bin_types.data)
        } catch (error) {
            console.error("Error fetching data", error)
            setMessage({ type: "error", content: "Error fetching data. Please try again." })
        } finally {
            setIsLoading(false)
        }
    }

    const fetchManagerTypes = async () => {
        setIsLoading(true)
        try {
            const response = await axios.get("/programme-manager-types")
            setSavedManagerTypes(response.data)
        } catch (error) {
            console.error("Error fetching Programme Manager Type", error)
            setMessage({ type: "error", content: "Error fetching data. Please try again." })
        } finally {
            setIsLoading(false)
        }
    }

    const handleSort = (key: keyof ProgrammeManagerType) => {
        let direction: "asc" | "desc" = "asc"
        if (sortConfig && sortConfig.key === key && sortConfig.direction === "asc") {
            direction = "desc"
        }
        setSortConfig({ key, direction })
    }

    const filteredAndSortedManagerTypes = savedManagerTypes
        .filter((v) => v.manager_type.toLowerCase().includes(searchTerm.toLowerCase()))
        .sort((a, b) => {
            if (sortConfig === null) {
                return 0
            }
            const { key, direction } = sortConfig
            if (a[key] < b[key]) {
                return direction === "asc" ? -1 : 1
            }
            if (a[key] > b[key]) {
                return direction === "asc" ? 1 : -1
            }
            return 0
        })

    const handleDelete = async () => {
        if (deleteId === null) return

        setIsLoading(true)
        setIsOverlayVisible(true)
        try {
            await axios.delete(`/programme-manager-types/${deleteId}`)
            setSavedManagerTypes((prev) => prev.filter((v) => v._id !== deleteId))
            setDeleteId(null)
            await fetchManagerTypes()
            setMessage({ type: "success", content: "Programme Manager Type deleted successfully!" })
        } catch (error) {
            console.error("Error deleting Programme Manager Type", error)
            setMessage({ type: "error", content: "Error deleting Programme Manager Type. Please try again." })
        } finally {
            setIsLoading(false)
            setIsOverlayVisible(false)
            setDeleteModalOpen(false)
        }
    }

    const handleDeleteClick = (id: string) => {
        setDeleteId(Number.parseInt(id))
        setDeleteModalOpen(true)
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement> | string, fieldName?: string) => {
        let name, value
        if (typeof e === "string") {
            name = fieldName
            value = e
        } else {
            name = e.target.name
            value = e.target.value
        }


        if (name === "bin_range") {

            const bin_range = savedBinRanges.find((r) => r._id === value)

            if (bin_range) {
                setSelectedBin(bin_range?.binType.type)
                setSelectedCurrency(bin_range?.currency.currency_code)
                setFormData((prev) => ({
                    ...prev,
                    bin_type: bin_range?.binType._id,
                    bin_currency: bin_range?.currency._id,

                }))


            }
        } else if (name === "bin_usage") {
            var bin_usage = savedBinUsages.find(r => r._id === value)?.usage;
            const newManagerType = `${selectedBin} ${selectedCurrency} ${bin_usage}`
            setFormData((prev) => ({
                ...prev,
                manager_type: newManagerType,
                bin_usage: value,

            }))

            // Check for uniqueness
            const isDuplicate = savedManagerTypes.some(
                (type) => type.manager_type.toLowerCase() === newManagerType.toLowerCase(),
            )
            setError(
                isDuplicate ? "This Programme Manager Type already exists. Please choose a different combination." : "",
            )
        } else {
            setFormData((prev) => ({ ...prev, [name]: value }))
        }
    }

    function modifyText(inputText: string): string {
        return inputText.replace(/(\[.*?\])/, "PROGRAMME MANAGER $1").trim()
    }

    const renderProgrammeManagerTable = (status: string) => {
        const data = savedManagerTypes
            .filter(row => row.status.toLowerCase() === status.toLowerCase()) // Filtering by status
            .map(row => ({
                id: row._id,
                manager_type: row.manager_type,
                created_by: row.created_by?.name || "N/A",
                created_at: formatDate(row.created_at),
                status: row.status.toUpperCase()
            }));
        return (
            <>
                <DataExporter
                    data={data}
                    filename="programmeManager"
                    title="Programme Manager Type Report"
                />
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead className="w-[100px]">ID</TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("manager_type")}>
                            Programme Type <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                        </TableHead>
                        <TableHead className="cursor-pointer">
                             BIN Code
                        </TableHead>
                        <TableHead className="cursor-pointer" >
                            Currency
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("created_by")}>
                            Created By <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("created_at")}>
                            Created At <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                        </TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {filteredAndSortedManagerTypes
                        .filter((usage) => usage.status.toLowerCase() === status)
                        .map((usage, index) => (
                            <TableRow key={usage._id}>
                                <TableCell>{index + 1}</TableCell>
                                <TableCell>{usage.manager_type}</TableCell>
                                <TableCell>{usage.bin_type.binCategory.bin_prefix}{usage.bin_type.binVariant.bin_suffix} </TableCell>

                                <TableCell>{usage.bin_currency.currency_code}</TableCell>

                                <TableCell>{usage.created_by?.name || "N/A"}</TableCell>
                                <TableCell>{formatDate(usage.created_at)}</TableCell>
                                <TableCell>{usage.status.toUpperCase()}</TableCell>
                                <TableCell>
                                    {userHasDeletePermission && status === "active" && (
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button variant="destructive" size="sm"
                                                        onClick={() => setDeleteId(usage._id)}>
                                                    <Trash2 className="h-4 w-4"/>
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Action: Delete</AlertDialogTitle>
                                                    <AlertDialogDescription>
                                                        Deletion of this record will be sent to administrator for
                                                        approval. Are you sure to proceed?
                                                    </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel
                                                        onClick={() => setDeleteId(null)}>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    )}
                                    {status !== "active" && (
                                        <ActionsButtonGroup
                                            entity={usage}
                                            entityType="programme-manager-types"
                                            entityName={usage.manager_type}
                                            userHasApprovePermission={userHasApprovePermission}
                                            userHasDeletePermission={userHasDeletePermission}
                                            handleDeleteClick={handleDeleteClick}
                                            fetchEntities={fetchManagerTypes}
                                        />
                                    )}
                                </TableCell>
                            </TableRow>
                        ))}
                </TableBody>
            </Table>
            </>
        )
    }

    return (
        <div className="w-full px-4 md:px-6 lg:px-8">
            {isLoading && <LoadingOverlay />}
            {isOverlayVisible && <div className="fixed inset-0 bg-black bg-opacity-50 z-50" />}
            <Card className="w-full">
                <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>Programme Manager Type</CardTitle>
                        <CardDescription>View and manage your Programme Manager Type</CardDescription>
                    </div>
                    {userHasRequestPermission && (
                        <Sheet>
                            <SheetTrigger asChild>
                                <Button>Add New Programme Manager Type</Button>
                            </SheetTrigger>
                            <SheetContent className="bg-white w-[50%] ">
                                <SheetHeader>
                                    <SheetTitle>Add Programme Manager Type</SheetTitle>
                                </SheetHeader>
                                <form onSubmit={handleSubmit} className="space-y-4 mt-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="bin_range">Bin Type (With Currency)</Label>
                                        <Select onValueChange={(value) => handleInputChange(value, "bin_range")}>
                                            <SelectTrigger id="bin_range">
                                                <SelectValue placeholder="Select Bin Type"/>
                                            </SelectTrigger>
                                            <SelectContent>
                                                {savedBinRanges
                                                    .filter((s) => s.status === "active")
                                                    .map((scheme) => (
                                                        <SelectItem key={scheme._id} value={scheme._id}>
                                                            {scheme.binType.type} {scheme.currency.currency_code}
                                                        </SelectItem>
                                                    ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="bin_usage">Bin Usage</Label>
                                        <Select onValueChange={(value) => handleInputChange(value, "bin_usage")}>
                                            <SelectTrigger id="bin_usage">
                                                <SelectValue placeholder="Select Bin Usage"/>
                                            </SelectTrigger>
                                            <SelectContent>
                                                {savedBinUsages
                                                    .filter((s) => s.status === "active")
                                                    .map((scheme) => (
                                                        <SelectItem key={scheme._id} value={scheme._id}>
                                                            {scheme.usage}
                                                        </SelectItem>
                                                    ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="manager_type">Generated Programme Manager Type</Label>
                                        <Input
                                            id="manager_type"
                                            name="manager_type"
                                            value={formData.manager_type}
                                            placeholder=""
                                            readOnly
                                        />
                                    </div>
                                    {error && <p className="text-red-600 text-sm">{error}</p>}
                                    <div className="flex justify-end space-x-2">
                                        <SheetClose asChild>
                                            <Button type="button" variant="outline">
                                                Cancel
                                            </Button>
                                        </SheetClose>
                                        <Button type="submit" disabled={!!error}>
                                            Save
                                        </Button>
                                    </div>
                                </form>
                            </SheetContent>
                        </Sheet>
                    )}
                </CardHeader>
                <CardContent>
                    {userHasViewPermission && (
                        <>
                            <div className="mb-4">
                                <Input
                                    placeholder="Search Programme Manager Type..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>

                            <Tabs aria-label="Tabs with underline" variant="underline">
                                <Tabs.Item active title="Approved">
                                    {renderProgrammeManagerTable("active")}
                                </Tabs.Item>
                                <Tabs.Item  title="Pending">{renderProgrammeManagerTable("pending")}</Tabs.Item>
                                <Tabs.Item title="Modify">{renderProgrammeManagerTable("modify")}</Tabs.Item>
                                <Tabs.Item title="Declined">{renderProgrammeManagerTable("declined")}</Tabs.Item>
                            </Tabs>
                        </>
                    )}
                </CardContent>
            </Card>

            <AlertDialog open={deleteModalOpen} onOpenChange={setDeleteModalOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete this Programme Manager Type? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    )
}

