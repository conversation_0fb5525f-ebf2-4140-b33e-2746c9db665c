//@ts-nocheck
"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { ArrowUpDown, ChevronDown, ChevronUp, Loader2, Plus, Trash2, Search, Filter } from "lucide-react"
import axios from "@/utils/axiosInstance"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useAppSelector } from "@/store/hooks"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import DataExporter from "@/components/DataExporter"

interface Country {
    _id: number
    country_name: string
    country_code: string
    currency_code: string
    created_at: string
    is_active: boolean
}

interface ProductVersion {
    _id: number
    version_name: string
    version_number: string
    version_variant: string
    status: string
    version_code?: string
    created_at: string
    created_by: any
}

interface BinRange {
    _id: number
    binType: string
    binCode: string
    binCodePrefix: string
    binCodeSuffix: string
    currency: string
    bin_start: string
    bin_end: string
    created_at: string
    created_by: any
    status: string
    category?: string
}

type SortKey = keyof ProductVersion
type SortOrder = "asc" | "desc"

const formatDate = (dateString: string): string => {
    const options: Intl.DateTimeFormatOptions = { year: "numeric", month: "short", day: "numeric" }
    return new Date(dateString).toLocaleString("en-GB", options)
}

interface CardProgrammeType {
    _id: number
    programme_type: string
    description: string
    status: string
    created_at: string
    created_by: string
    bin_type: any
}

interface Role {
    permissions: string[]
}

const initialFormData = {
    version: "",
    version_code: "",
    created_by: "",
}

export default function ProductVersion() {
    const [savedProductVersions, setSavedProductVersions] = useState<ProductVersion[]>([])
    const [savedProgrammeTypes, setSavedProgrammeTypes] = useState<CardProgrammeType[]>([])
    const [savedCountries, setSavedCountries] = useState<Country[]>([])
    const [sortKey, setSortKey] = useState<SortKey>("_id")
    const [sortOrder, setSortOrder] = useState<SortOrder>("asc")
    const [cardSchemes, setCardSchemes] = useState<any[]>([])
    const [deleteId, setDeleteId] = useState<number | null>(null)
    const [cardTypes, setCardTypes] = useState<any[]>([])
    const [formData, setFormData] = useState({
        ...initialFormData,
        created_by: localStorage.getItem("user") || "",
    })
    const [savedBinRanges, setSavedBinRanges] = useState<BinRange[]>([])
    const [selectedBin, setSelectedBin] = useState<string>("")
    const [selectedCurrency, setSelectedCurrency] = useState<string>("")
    const [error, setError] = useState("")
    const [isLoading, setIsLoading] = useState(false)
    const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false)
    const [isSubmissionSuccessful, setIsSubmissionSuccessful] = useState(false)
    const [searchTerm, setSearchTerm] = useState("")
    const [activeTab, setActiveTab] = useState("approved")

    // User permissions
    const user = useAppSelector((state) => state.user.user)
    const [roles, setRoles] = useState<Role[]>([])

    useEffect(() => {
        setRoles(user.roles || [])
    }, [user.roles])

    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions))
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }

    const userHasRequestPermission = hasPermission(roles, "Product Code Request_Create")
    const userHasDeletePermission = hasPermission(roles, "Product Code Request_Delete")
    const userHasApprovePermission = hasPermission(roles, "Product Code Request_Approve")
    const userHasViewPermission = hasPermission(roles, "Product Code Request_View")

    useEffect(() => {
        fetchData()
    }, [])

    const fetchData = async () => {
        setIsLoading(true)
        try {
            const [versions] = await Promise.all([axios.get("/product-versions")])
            setSavedProductVersions(versions.data)
        } catch (error) {
            console.error("Error fetching data", error)
        } finally {
            setIsLoading(false)
        }
    }

    const handleInputChange = (value: string, fieldName: string) => {
        setFormData((prev) => ({ ...prev, [fieldName]: value }))
        if (error) {
            setError("")
        }
    }

    const handleVersionCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value
        setFormData((prev) => ({ ...prev, version_code: value }))
        if (error) {
            setError("")
        }
    }

    const resetForm = () => {
        setFormData({
            ...initialFormData,
            created_by: localStorage.getItem("user") || "",
        })
        setError("")
        setIsSubmissionSuccessful(false)
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        // Validation
        if (!formData.version || !formData.version_code.trim()) {
            setError("Please select a product version and enter a version code")
            return
        }
        setIsConfirmDialogOpen(true)
    }

    const handleDelete = async () => {
        if (deleteId === null) return
        setIsLoading(true)
        try {
            await axios.delete(`/product-versions/${deleteId}`)
            await fetchData()
            setDeleteId(null)
        } catch (error) {
            console.error("Error deleting product version", error)
        } finally {
            setIsLoading(false)
        }
    }

    const confirmSubmit = async () => {
        setIsLoading(true)
        setIsConfirmDialogOpen(false)
        try {
            await axios.put("/product-versions", formData)
            await fetchData()
            setIsSubmissionSuccessful(true)
            resetForm()
            // Auto-hide success message after 3 seconds
            setTimeout(() => {
                setIsSubmissionSuccessful(false)
            }, 3000)
        } catch (error) {
            console.error("Error creating product version", error)
            setError("An error occurred while creating the product version. Please try again.")
        } finally {
            setIsLoading(false)
        }
    }

    const handleSort = (key: SortKey) => {
        setSortOrder(sortOrder === "asc" && sortKey === key ? "desc" : "asc")
        setSortKey(key)
    }

    const sortedProductVersions = [...savedProductVersions].sort((a, b) => {
        const aValue = a[sortKey]
        const bValue = b[sortKey]
        if (sortKey === "created_at") {
            return sortOrder === "asc"
                ? new Date(aValue).getTime() - new Date(bValue).getTime()
                : new Date(bValue).getTime() - new Date(aValue).getTime()
        }
        if (typeof aValue === "string" && typeof bValue === "string") {
            return sortOrder === "asc" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue)
        }
        if (typeof aValue === "number" && typeof bValue === "number") {
            return sortOrder === "asc" ? aValue - bValue : bValue - aValue
        }
        return 0
    })

    const getSortIcon = (key: SortKey) => {
        if (sortKey === key) {
            return sortOrder === "asc" ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
        }
        return <ArrowUpDown className="h-4 w-4" />
    }

    // Get available versions for dropdown (show all active versions)
    const getAvailableVersions = () => {
        return savedProductVersions.filter((s) => s.status === "active")
    }

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            active: {
                label: "Approved",
                variant: "default" as const,
                className: "bg-green-100 text-green-800 hover:bg-green-100",
            },
            pending: {
                label: "Pending",
                variant: "secondary" as const,
                className: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100",
            },
            modify: {
                label: "Modify",
                variant: "outline" as const,
                className: "bg-blue-100 text-blue-800 hover:bg-blue-100",
            },
            decline: {
                label: "Declined",
                variant: "destructive" as const,
                className: "bg-red-100 text-red-800 hover:bg-red-100",
            },
        }
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
        return (
            <Badge variant={config.variant} className={config.className}>
                {config.label}
            </Badge>
        )
    }

    const getStatusCount = (status: string) => {
        return sortedProductVersions.filter((v) => v.status.toLowerCase() === status.toLowerCase()).length
    }

    const filteredVersions = sortedProductVersions.filter((version) => {
        const matchesSearch =
            version.version_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            version.version_code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            version.created_by?.name?.toLowerCase().includes(searchTerm.toLowerCase())

        const statusMap = {
            approved: "active",
            pending: "pending",
            modify: "modify",
            declined: "decline",
        }

        return matchesSearch && version.status.toLowerCase() === statusMap[activeTab as keyof typeof statusMap]
    })

    const renderMobileCard = (version: ProductVersion) => (
        <Card key={version._id} className="mb-4">
            <CardContent className="p-4">
                <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                        <h3 className="font-semibold text-lg mb-1">{version.version_name}</h3>
                        <p className="text-sm text-muted-foreground mb-2">Code: {version.version_code || "N/A"}</p>
                    </div>
                    {getStatusBadge(version.status)}
                </div>
                <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                        <span className="text-muted-foreground">Created by:</span>
                        <span>{version.created_by?.name || "N/A"}</span>
                    </div>
                    <div className="flex justify-between">
                        <span className="text-muted-foreground">Created:</span>
                        <span>{formatDate(version.created_at)}</span>
                    </div>
                </div>
                {userHasDeletePermission && (
                    <div className="mt-4 pt-3 border-t">
                        <Button variant="destructive" size="sm" onClick={() => setDeleteId(version._id)} className="w-full">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                        </Button>
                    </div>
                )}
            </CardContent>
        </Card>
    )

    const renderDesktopTable = () => {
        const data = filteredVersions.map((row) => ({
            id: row._id,
            version_name: row.version_name,
            version_code: row.version_code || "N/A",
            created_by: row.created_by?.name || "N/A",
            created_at: formatDate(row.created_at),
            status: row.status.toUpperCase(),
        }))

        return (
            <>
                <div className="mb-4">
                    <DataExporter data={data} filename="product_version" title="Product Version Report" />
                </div>
                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                {[
                                    { key: "version_name", label: "Version Name" },
                                    { key: "version_code", label: "Product Code" },
                                    { key: "created_by", label: "Created By" },
                                    { key: "created_at", label: "Created At" },
                                    { key: "status", label: "Status" },
                                ].map(({ key, label }) => (
                                    <TableHead key={key} className="px-4 py-3">
                                        <Button
                                            variant="ghost"
                                            onClick={() => handleSort(key as SortKey)}
                                            className="hover:bg-transparent text-left font-semibold p-0 h-auto"
                                        >
                      <span className="flex items-center gap-2">
                        {label}
                          {getSortIcon(key as SortKey)}
                      </span>
                                        </Button>
                                    </TableHead>
                                ))}
                                {userHasDeletePermission && <TableHead className="px-4 py-3">Actions</TableHead>}
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredVersions.map((version) => (
                                <TableRow key={version._id} className="hover:bg-muted/50">
                                    <TableCell className="px-4 py-3 font-medium">{version.version_name}</TableCell>
                                    <TableCell className="px-4 py-3">{version.version_code || "N/A"}</TableCell>
                                    <TableCell className="px-4 py-3">{version.created_by?.name || "N/A"}</TableCell>
                                    <TableCell className="px-4 py-3">{formatDate(version.created_at)}</TableCell>
                                    <TableCell className="px-4 py-3">{getStatusBadge(version.status)}</TableCell>
                                    {userHasDeletePermission && (
                                        <TableCell className="px-4 py-3">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => setDeleteId(version._id)}
                                                className="text-destructive hover:text-destructive hover:bg-destructive/10"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </TableCell>
                                    )}
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            </>
        )
    }

    if (isLoading) {
        return <LoadingOverlay />
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4 md:p-6 lg:p-8">
            <div className="max-w-7xl mx-auto space-y-6">
                {/* Header */}
                <div className="text-center space-y-2">
                    <h1 className="text-3xl md:text-4xl font-bold text-slate-900">Product Version Management</h1>
                    <p className="text-slate-600 max-w-2xl mx-auto">
                        Manage and track your product versions with approval workflows and comprehensive reporting.
                    </p>
                </div>

                {/* Success Message */}
                {isSubmissionSuccessful && (
                    <Card className="border-green-200 bg-green-50">
                        <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                                <p className="text-green-800 font-medium">Product version code has been successfully submitted!</p>
                            </div>
                        </CardContent>
                    </Card>
                )}

                <div className="grid grid-cols-1 xl:grid-cols-12 gap-6">
                    {/* Form Section */}
                    {userHasRequestPermission && (
                        <div className="xl:col-span-4">
                            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                                <CardHeader className="pb-4">
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-blue-100 rounded-lg">
                                            <Plus className="h-5 w-5 text-blue-600" />
                                        </div>
                                        <div>
                                            <CardTitle className="text-xl">New Version Request</CardTitle>
                                            <CardDescription>Attach Approved Product Code From ITCard</CardDescription>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <form onSubmit={handleSubmit} className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="version" className="text-sm font-medium">
                                                Product Version
                                            </Label>
                                            <Select value={formData.version} onValueChange={(value) => handleInputChange(value, "version")}>
                                                <SelectTrigger id="version" className="h-11">
                                                    <SelectValue placeholder="Select Version" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {getAvailableVersions().map((version) => (
                                                        <SelectItem key={version._id} value={version._id.toString()}>
                                                            {version.version_name}
                                                            {version.version_code && (
                                                                <span className="text-gray-500 ml-2">({version.version_code})</span>
                                                            )}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="version_code" className="text-sm font-medium">
                                                Approved Version Code
                                            </Label>
                                            <Input
                                                id="version_code"
                                                name="version_code"
                                                value={formData.version_code}
                                                onChange={handleVersionCodeChange}
                                                placeholder="Enter Approved Version Code"
                                                className="h-11"
                                            />
                                        </div>

                                        {error && (
                                            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                                                <p className="text-red-600 text-sm">{error}</p>
                                            </div>
                                        )}

                                        <div className="flex gap-3 pt-2">
                                            <Button type="button" variant="outline" onClick={resetForm} className="flex-1 bg-transparent">
                                                Cancel
                                            </Button>
                                            <Button type="submit" disabled={isLoading} className="flex-1">
                                                {isLoading ? (
                                                    <>
                                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                        Saving...
                                                    </>
                                                ) : (
                                                    <>
                                                        <Plus className="mr-2 h-4 w-4" />
                                                        Save
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    </form>
                                </CardContent>
                            </Card>
                        </div>
                    )}

                    {/* Table Section */}
                    {userHasViewPermission && (
                        <div className={userHasRequestPermission ? "xl:col-span-8" : "xl:col-span-12"}>
                            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                                <CardHeader className="pb-4">
                                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                                        <div>
                                            <CardTitle className="text-xl">Saved Product Versions</CardTitle>
                                            <CardDescription>View and manage your saved product versions</CardDescription>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <div className="relative">
                                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                                <Input
                                                    placeholder="Search versions..."
                                                    value={searchTerm}
                                                    onChange={(e) => setSearchTerm(e.target.value)}
                                                    className="pl-10 w-full sm:w-64"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                                        <TabsList className="grid w-full grid-cols-4 mb-6">
                                            <TabsTrigger value="approved" className="relative">
                                                Approved
                                                <Badge variant="secondary" className="ml-2 text-xs">
                                                    {getStatusCount("active")}
                                                </Badge>
                                            </TabsTrigger>
                                            <TabsTrigger value="pending" className="relative">
                                                Pending
                                                <Badge variant="secondary" className="ml-2 text-xs">
                                                    {getStatusCount("pending")}
                                                </Badge>
                                            </TabsTrigger>
                                            <TabsTrigger value="modify" className="relative">
                                                Modify
                                                <Badge variant="secondary" className="ml-2 text-xs">
                                                    {getStatusCount("modify")}
                                                </Badge>
                                            </TabsTrigger>
                                            <TabsTrigger value="declined" className="relative">
                                                Declined
                                                <Badge variant="secondary" className="ml-2 text-xs">
                                                    {getStatusCount("decline")}
                                                </Badge>
                                            </TabsTrigger>
                                        </TabsList>

                                        {["approved", "pending", "modify", "declined"].map((status) => (
                                            <TabsContent key={status} value={status} className="mt-0">
                                                {filteredVersions.length === 0 ? (
                                                    <div className="text-center py-12">
                                                        <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                                                            <Filter className="h-8 w-8 text-muted-foreground" />
                                                        </div>
                                                        <h3 className="text-lg font-semibold mb-2">No versions found</h3>
                                                        <p className="text-muted-foreground">
                                                            {searchTerm ? "Try adjusting your search terms" : `No ${status} versions available`}
                                                        </p>
                                                    </div>
                                                ) : (
                                                    <>
                                                        {/* Mobile View */}
                                                        <div className="block md:hidden">
                                                            <ScrollArea className="h-[600px]">{filteredVersions.map(renderMobileCard)}</ScrollArea>
                                                        </div>

                                                        {/* Desktop View */}
                                                        <div className="hidden md:block">
                                                            <ScrollArea className="h-[600px]">{renderDesktopTable()}</ScrollArea>
                                                        </div>
                                                    </>
                                                )}
                                            </TabsContent>
                                        ))}
                                    </Tabs>
                                </CardContent>
                            </Card>
                        </div>
                    )}
                </div>
            </div>

            {/* Confirmation Dialog */}
            <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Submission</AlertDialogTitle>
                        <AlertDialogDescription>This product version has been approved by ITCard?</AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={confirmSubmit} disabled={isLoading}>
                            {isLoading ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Confirming...
                                </>
                            ) : (
                                "Confirm"
                            )}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={deleteId !== null} onOpenChange={() => setDeleteId(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete this product version? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDelete}
                            disabled={isLoading}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            {isLoading ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Deleting...
                                </>
                            ) : (
                                <>
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete
                                </>
                            )}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    )
}
