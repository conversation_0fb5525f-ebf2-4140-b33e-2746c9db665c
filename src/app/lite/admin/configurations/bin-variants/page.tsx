//@ts-nocheck
"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Tabs } from "flowbite-react"
import { Sheet, SheetClose, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import axios from "@/utils/axiosInstance"
import { ArrowUpDown, Trash2 } from "lucide-react"
import { formatDate } from "@/utils/helpers"
import { useAppSelector } from "@/store/hooks"
import { ActionsButtonGroup } from "@/components/ActionButtons"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { Alert } from "@/components/alert"
import DataExporter from "@/components/DataExporter"
import InputMask from "react-input-mask";

interface BinVariant {
    _id: number
    variant: string
    bin_suffix: string
    status: string
    created_at: string
    created_by: string
}

interface Message {
    type: "success" | "error"
    content: string
}

interface Role {
    permissions: string[]
}

export default function BinVariant() {
    const [formData, setFormData] = useState({
        variant: "",
        bin_suffix: "",
        created_by: localStorage.getItem("user"),
    })
    const [savedBinVariants, setSavedBinVariants] = useState<BinVariant[]>([])
    const [searchTerm, setSearchTerm] = useState("")
    const [sortConfig, setSortConfig] = useState<{ key: keyof BinVariant; direction: "asc" | "desc" } | null>(null)
    const [error, setError] = useState<string>("")
    const [suffixError, setSuffixError] = useState<string>("")
    const [deleteId, setDeleteId] = useState<number | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [message, setMessage] = useState<Message | null>(null)
    const [isOverlayVisible, setIsOverlayVisible] = useState(false)
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [isSheetOpen, setIsSheetOpen] = useState(false)
    const [alert, setAlert] = useState<{ message: string; type: "success" | "error" } | null>(null)

    const user = useAppSelector((state) => state.user.user)
    const [roles, setRoles] = useState<Role[]>([])

    useEffect(() => {
        setRoles(user.roles)
        fetchBinVariants()
    }, [user.roles])

    useEffect(() => {
        if (message) {
            alert(message.content)
            setMessage(null)
        }
    }, [message])

    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions))
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }

    const userHasRequestPermission = hasPermission(roles, "BIN Variants_Request")
    const userHasDeletePermission = hasPermission(roles, "BIN Variants_Delete")
    const userHasApprovePermission = hasPermission(roles, "BIN Variants_Approve")
    const userHasViewPermission = hasPermission(roles, "BIN Variants_View")

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        if (error || suffixError) return

        setIsLoading(true)
        try {
            await axios.post("/bin-variant", formData)
            await fetchBinVariants()
            setFormData({ ...formData, variant: "", bin_suffix: "" })
            setIsSheetOpen(false)
            setAlert({ message: "BIN Variant created successfully!", type: "success" })
        } catch (error) {
            console.error("Error creating BIN Variant", error)
            setAlert({ message: "Error creating BIN Variant. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
        }
    }

    const fetchBinVariants = async () => {
        setIsLoading(true)
        try {
            const response = await axios.get("/bin-variant")
            setSavedBinVariants(response.data)
        } catch (error) {
            console.error("Error fetching BIN Variants", error)
            setMessage({ type: "error", content: "Error fetching data. Please try again." })
        } finally {
            setIsLoading(false)
        }
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData((prev) => ({ ...prev, [name]: value }))

        if (name === "variant") {
            const isDuplicate = savedBinVariants.some((v) => v.variant.toLowerCase() === value.toLowerCase())
            setError(isDuplicate ? "This BIN Variant already exists. Please enter a unique name." : "")
        }


    }

    const handleSort = (key: keyof BinVariant) => {
        let direction: "asc" | "desc" = "asc"
        if (sortConfig && sortConfig.key === key && sortConfig.direction === "asc") {
            direction = "desc"
        }
        setSortConfig({ key, direction })
    }

    const filteredAndSortedVariant = savedBinVariants
        .filter(
            (v) =>
                v.variant.toLowerCase().includes(searchTerm.toLowerCase()) ||
                v.bin_suffix.toLowerCase().includes(searchTerm.toLowerCase()),
        )
        .sort((a, b) => {
            if (sortConfig === null) {
                return 0
            }
            const { key, direction } = sortConfig
            if (a[key] < b[key]) {
                return direction === "asc" ? -1 : 1
            }
            if (a[key] > b[key]) {
                return direction === "asc" ? 1 : -1
            }
            return 0
        })

    const handleDelete = async (deleteId) => {
        if (deleteId === null) return
        setIsLoading(true)
        setIsOverlayVisible(true)
        try {
            await axios.delete(`/bin-variant/${deleteId}`)
            setSavedBinVariants((prev) => prev.filter((v) => v._id !== deleteId))
            setDeleteId(null)
            await fetchBinVariants()
            setAlert({ message: "BIN Variant deleted successfully!", type: "success" })
        } catch (error) {
            console.error("Error deleting BIN Variant", error)
            setAlert({ message: "Error deleting BIN Variant. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
            setIsOverlayVisible(false)
            setDeleteModalOpen(false)
        }
    }

    const handleDeleteClick = (id: string) => {
        setDeleteId(Number.parseInt(id))
        setDeleteModalOpen(true)
    }

    const renderTable = (status: string) => {
        const data = savedBinVariants
            .filter((row) => row.status.toLowerCase() === status.toLowerCase())
            .map((row) => ({
                id: row._id,
                variant: row.variant,
                bin_suffix: row.bin_suffix,
                created_by: row.created_by?.name || "N/A",
                created_at: formatDate(row.created_at),
                status: row.status.toUpperCase(),
            }))

        return (
            <>
                <DataExporter data={data} filename="bin_variant" title="BIN variant Report" />
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[100px]">ID</TableHead>
                            <TableHead className="cursor-pointer" onClick={() => handleSort("variant")}>
                                Variant <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                            </TableHead>
                            <TableHead className="cursor-pointer" onClick={() => handleSort("bin_suffix")}>
                                BIN Suffix <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                            </TableHead>
                            <TableHead className="cursor-pointer" onClick={() => handleSort("created_by")}>
                                Created By <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                            </TableHead>
                            <TableHead className="cursor-pointer" onClick={() => handleSort("created_at")}>
                                Created At <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                            </TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredAndSortedVariant
                            .filter((variant) => variant.status.toLowerCase() === status)
                            .map((variant, index) => (
                                <TableRow key={variant._id}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>{variant.variant}</TableCell>
                                    <TableCell>{variant.bin_suffix}</TableCell>
                                    <TableCell>{variant.created_by?.name || "N/A"}</TableCell>
                                    <TableCell>{formatDate(variant.created_at)}</TableCell>
                                    <TableCell>{variant.status.toUpperCase()}</TableCell>
                                    <TableCell>
                                        {userHasDeletePermission && status === "active" && (
                                            <AlertDialog>
                                                <AlertDialogTrigger asChild>
                                                    <Button variant="destructive" size="sm" onClick={() => setDeleteId(variant._id)}>
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </AlertDialogTrigger>
                                                <AlertDialogContent>
                                                    <AlertDialogHeader>
                                                        <AlertDialogTitle>Action: Delete</AlertDialogTitle>
                                                        <AlertDialogDescription>
                                                            Deletion of this record will be sent to administrator for approval. Are you sure to
                                                            proceed?
                                                        </AlertDialogDescription>
                                                    </AlertDialogHeader>
                                                    <AlertDialogFooter>
                                                        <AlertDialogCancel onClick={() => setDeleteId(null)}>Cancel</AlertDialogCancel>
                                                        <AlertDialogAction onClick={() => handleDelete(variant._id)}>Delete</AlertDialogAction>
                                                    </AlertDialogFooter>
                                                </AlertDialogContent>
                                            </AlertDialog>
                                        )}
                                        {status !== "active" && (
                                            <ActionsButtonGroup
                                                entity={variant}
                                                entityType="bin-variant"
                                                entityName={variant.variant}
                                                userHasApprovePermission={userHasApprovePermission}
                                                userHasDeletePermission={userHasDeletePermission}
                                                handleDeleteClick={handleDelete}
                                                fetchEntities={fetchBinVariants}
                                                editFields={[
                                                    { key: 'variant', label: 'Variant', type: 'text', required: true },
                                                    { key: 'bin_suffix', label: 'BIN Suffix', type: 'text', required: true }
                                                ]}
                                            />
                                        )}
                                    </TableCell>
                                </TableRow>
                            ))}
                    </TableBody>
                </Table>
            </>
        )
    }

    return (
        <div className="w-full px-4 md:px-6 lg:px-8">
            {isLoading && <LoadingOverlay />}
            {isOverlayVisible && <div className="fixed inset-0 bg-black bg-opacity-50 z-50" />}
            <Card className="w-full">
                <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>BIN Variants</CardTitle>
                        <CardDescription>View and Request BIN Variants</CardDescription>
                    </div>
                    {userHasRequestPermission && (
                        <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
                            <SheetTrigger asChild>
                                <Button onClick={() => setIsSheetOpen(true)}>Add New BIN Variant</Button>
                            </SheetTrigger>
                            <SheetContent>
                                <SheetHeader>
                                    <SheetTitle>Add BIN Variant</SheetTitle>
                                </SheetHeader>
                                <form onSubmit={handleSubmit} className="space-y-4 mt-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="variant">BIN Variant</Label>
                                        <Input
                                            id="variant"
                                            name="variant"
                                            value={formData.variant}
                                            onChange={handleInputChange}
                                            placeholder="Enter BIN Variant"
                                            required
                                        />
                                        {error && <p className="text-red-600 text-sm">{error}</p>}
                                    </div>



                                    <div className="space-y-2">
                                        <Label htmlFor="bin_suffix">BIN Suffix</Label>
                                        <InputMask
                                            mask="99"
                                            maskChar={null}
                                            value={formData.binCodeSuffix}
                                            onChange={handleInputChange}
                                        >
                                            {(inputProps: any) => (
                                                <Input
                                                    {...inputProps}
                                                    id="bin_suffix"
                                                    name="bin_suffix"
                                                    placeholder="Enter BIN Suffix"
                                                    required
                                                />
                                            )}
                                        </InputMask>
                                        {suffixError && <p className="text-red-600 text-sm">{suffixError}</p>}
                                    </div>


                                    <div className="flex justify-end space-x-2">
                                        <SheetClose asChild>
                                            <Button type="button" variant="outline">
                                                Cancel
                                            </Button>
                                        </SheetClose>
                                        <Button type="submit" disabled={!!error || !!suffixError || isLoading}>
                                            Save
                                        </Button>
                                    </div>
                                </form>
                            </SheetContent>
                        </Sheet>
                    )}
                </CardHeader>
                <CardContent>
                    {alert && <Alert message={alert.message} type={alert.type} onClose={() => setAlert(null)} />}
                    {userHasViewPermission && (
                        <>
                            <div className="mb-4">
                                <Input
                                    placeholder="Search BIN Variants or Suffixes..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>
                            <Tabs aria-label="Tabs with underline" variant="underline">
                                <Tabs.Item active title="Approved">
                                    {renderTable("active")}
                                </Tabs.Item>
                                <Tabs.Item title="Pending">{renderTable("pending")}</Tabs.Item>
                                <Tabs.Item title="Modify">{renderTable("modify")}</Tabs.Item>
                                <Tabs.Item title="Declined">{renderTable("declined")}</Tabs.Item>
                            </Tabs>
                        </>
                    )}
                </CardContent>
            </Card>
            <AlertDialog open={deleteModalOpen} onOpenChange={setDeleteModalOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete this BIN Variant? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    )
}
