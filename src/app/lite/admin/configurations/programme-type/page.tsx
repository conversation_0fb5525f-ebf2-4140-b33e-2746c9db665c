//@ts-nocheck
"use client"

import {useEffect, useState} from "react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {Tabs} from "flowbite-react"

import {Sheet, SheetClose, SheetContent, SheetHeader, SheetTitle, SheetTrigger} from "@/components/ui/sheet"

import axios from "@/utils/axiosInstance"
import {ArrowUpDown, Trash2} from "lucide-react"
import {formatDate} from "@/utils/helpers"
import {useAppSelector} from "@/store/hooks"
import {ActionsButtonGroup} from "@/components/ActionButtons"
import {LoadingOverlay} from "@/components/LoadingOverlay"
import {Alert} from "@/components/alert"
import DataExporter from "@/components/DataExporter";

interface ProgrammeType {
    _id: number
    type: string
    status: string
    created_at: string
    created_by: string
}

interface Message {
    type: "success" | "error"
    content: string
}

interface Role {
    name: string
    permissions: string[]
}

export default function ProgrammeType() {
    const [formData, setFormData] = useState({
        type: "",
        created_by: localStorage.getItem("user"),
    })
    const [savedProgrammeTypes, setSavedProgrammeTypes] = useState<ProgrammeType[]>([])
    const [searchTerm, setSearchTerm] = useState("")
    const [sortConfig, setSortConfig] = useState<{ key: keyof ProgrammeType; direction: "asc" | "desc" } | null>(null)
    const [error, setError] = useState<string>("")
    const [deleteId, setDeleteId] = useState<number | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [message, setMessage] = useState<Message | null>(null)
    const [isOverlayVisible, setIsOverlayVisible] = useState(false)
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [alert, setAlert] = useState<{ message: string; type: "success" | "error" } | null>(null)
    const [isSheetOpen, setIsSheetOpen] = useState(false)

    const user = useAppSelector((state) => state.user.user)
    const [roles, setRoles] = useState<Role[]>([])

    useEffect(() => {
        setRoles(user.roles)
        fetchProgrammeTypes()
    }, [])

    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions))
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }

    const userHasRequestPermission = hasPermission(roles, "Programme Type_Request")
    const userHasDeletePermission = hasPermission(roles, "Programme Type_Delete")
    const userHasApprovePermission = hasPermission(roles, "Programme Type_Approve")
    const userHasViewPermission = hasPermission(roles, "Programme Type_View")

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsLoading(true)
        try {
            await axios.post("/programme-type", formData)
            await fetchProgrammeTypes()
            setFormData({ ...formData, type: "" })
            setAlert({ message: "Programme type created successfully!", type: "success" })
            setIsSheetOpen(false)
        } catch (error) {
            console.error("Error creating BIN type", error)
            setAlert({ message: "Error creating programme type. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
        }
    }

    const fetchProgrammeTypes = async () => {
        setIsLoading(true)
        try {
            const response = await axios.get("/programme-type")
            setSavedProgrammeTypes(response.data)
        } catch (error) {
            console.error("Error fetching programme type", error)
            setMessage({ type: "error", content: "Error fetching data. Please try again." })
        } finally {
            setIsLoading(false)
        }
    }
    const userEditFields = [
        { key: 'type', label: 'Programme Type', type: 'text', required: true },
    ];
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData((prev) => ({ ...prev, [name]: value }))

        const isDuplicate = savedProgrammeTypes.some((v) => v.type.toLowerCase() === value.toLowerCase())
        setError(isDuplicate ? "This programme type already exists. Please enter a unique name." : "")
    }

    const handleSort = (key: keyof ProgrammeType) => {
        let direction: "asc" | "desc" = "asc"
        if (sortConfig && sortConfig.key === key && sortConfig.direction === "asc") {
            direction = "desc"
        }
        setSortConfig({ key, direction })
    }

    const filteredAndSortedUsage = savedProgrammeTypes
        .filter((v) => v.type.toLowerCase().includes(searchTerm.toLowerCase()))
        .sort((a, b) => {
            if (sortConfig === null) {
                return 0
            }
            const { key, direction } = sortConfig
            if (a[key] < b[key]) {
                return direction === "asc" ? -1 : 1
            }
            if (a[key] > b[key]) {
                return direction === "asc" ? 1 : -1
            }
            return 0
        })

    const handleDelete = async () => {
        if (deleteId === null) return

        setIsLoading(true)
        setIsOverlayVisible(true)
        try {
            await axios.delete(`/programme-type/${deleteId}`)
            setSavedProgrammeTypes((prev) => prev.filter((v) => v._id !== deleteId))
            setDeleteId(null)
            await fetchProgrammeTypes()
            setAlert({ message: "programme type deleted successfully!", type: "success" })
        } catch (error) {
            console.error("Error deleting programme type", error)
            setAlert({ message: "Error deleting programme type. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
            setIsOverlayVisible(false)
            setDeleteModalOpen(false)
        }
    }

    const handleDeleteClick = (id: string) => {
        setDeleteId(Number.parseInt(id))
        setDeleteModalOpen(true)
    }

    const renderTable = (status: string) => {
        const data = savedProgrammeTypes
            .filter(row => row.status.toLowerCase() === status.toLowerCase()) // Filtering by status
            .map(row => ({
                id: row._id,
                programme_type: row.type,
                created_by: row.created_by?.name || "N/A",
                created_at: formatDate(row.created_at),
                status: row.status.toUpperCase()
            }));

        return (
        <>
            <DataExporter
                data={data}
                filename="programme_type"
                title="Programme Type Report"
            />
        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead className="w-[100px]">ID</TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("type")}>
                        Type <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("created_by")}>
                        Created By <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("created_at")}>
                        Created At <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                    </TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                {filteredAndSortedUsage
                    .filter((type) => type.status.toLowerCase() === status)
                    .map((type, index) => (
                        <TableRow key={type._id}>
                            <TableCell>{index + 1}</TableCell>
                            <TableCell>{type.type}</TableCell>
                            <TableCell>{type.created_by?.name || "N/A"}</TableCell>
                            <TableCell>{formatDate(type.created_at)}</TableCell>
                            <TableCell>{type.status.toUpperCase()}</TableCell>
                            <TableCell>
                                {userHasDeletePermission && status === "active" && (
                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <Button variant="destructive" size="sm" onClick={() => setDeleteId(type._id)}>
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Action: Delete</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    Deletion of this record will be sent to administrator for approval. Are you sure to proceed?
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel onClick={() => setDeleteId(null)}>Cancel</AlertDialogCancel>
                                                <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>
                                )}
                                {status !== "active" && (
                                    <ActionsButtonGroup
                                        entity={type}
                                        entityType="programme-type"
                                        entityName={type.type}
                                        userHasApprovePermission={userHasApprovePermission}
                                        userHasDeletePermission={userHasDeletePermission}
                                        handleDeleteClick={handleDeleteClick}
                                        editFields={userEditFields}
                                        fetchEntities={fetchProgrammeTypes}
                                    />
                                )}
                            </TableCell>
                        </TableRow>
                    ))}
            </TableBody>
        </Table>
        </>
    )}

    return (
        <div className="w-full px-4 md:px-6 lg:px-8">
            {isLoading && <LoadingOverlay />}
            {isOverlayVisible && <div className="fixed inset-0 bg-black bg-opacity-50 z-50" />}

            <Card className="w-full">
                <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>Programme type</CardTitle>
                        <CardDescription>View and Request Programme Types</CardDescription>
                    </div>
                    {userHasRequestPermission && (
                        <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
                            <SheetTrigger asChild>
                                <Button>Add New programme type</Button>
                            </SheetTrigger>
                            <SheetContent>
                                <SheetHeader>
                                    <SheetTitle>Add new Programme Type</SheetTitle>
                                </SheetHeader>
                                <form onSubmit={handleSubmit} className="space-y-4 mt-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="type">Programme type</Label>
                                        <Input
                                            id="type"
                                            name="type"
                                            value={formData.type}
                                            onChange={handleInputChange}
                                            placeholder="Enter programme type"
                                            required
                                        />
                                    </div>
                                    {error && <p className="text-red-600 text-sm">{error}</p>}
                                    <div className="flex justify-end space-x-2">
                                        <SheetClose asChild>
                                            <Button type="button" variant="outline">
                                                Cancel
                                            </Button>
                                        </SheetClose>
                                        <Button type="submit" disabled={!!error && isLoading}>
                                            Save
                                        </Button>
                                    </div>
                                </form>
                            </SheetContent>
                        </Sheet>
                    )}
                </CardHeader>
                <CardContent>
                    {alert && <Alert message={alert.message} type={alert.type} onClose={() => setAlert(null)} />}
                    {userHasViewPermission && (
                        <>
                            <div className="mb-4">
                                <Input
                                    placeholder="Search programme type..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>

                            <Tabs aria-label="Tabs with underline" variant="underline">
                                <Tabs.Item active title="Approved">
                                    {" "}
                                    {renderTable("active")}{" "}
                                </Tabs.Item>
                                <Tabs.Item  title="Pending">
                                    {" "}
                                    {renderTable("pending")}{" "}
                                </Tabs.Item>
                                <Tabs.Item   title="Modify">
                                    {" "}
                                    {renderTable("modify")}{" "}
                                </Tabs.Item>
                                <Tabs.Item   title="Declined">
                                    {" "}
                                    {renderTable("declined")}{" "}
                                </Tabs.Item>
                            </Tabs>
                        </>
                    )}
                </CardContent>
            </Card>

            <AlertDialog open={deleteModalOpen} onOpenChange={setDeleteModalOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete this programme type? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    )
}

