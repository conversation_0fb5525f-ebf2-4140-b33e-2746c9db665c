//@ts-nocheck
"use client"

import React, {useEffect, useState} from "react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table"
import {Input} from "@/components/ui/input"
import {Switch} from "@/components/ui/switch"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger
} from "@/components/ui/alert-dialog"
import axios from "@/utils/axiosInstance"
import {ArrowUpDown, TrashIcon} from "lucide-react"
import {formatDate} from "@/utils/helpers"
import {ComboboxDemo} from "@/app/lite/admin/configurations/product-ccy/combobox"
import {currencyOptions} from "@/utils/data"
import {useAppSelector} from "@/store/hooks";

interface ProductCurrency {
    _id: number
    currency_name: string
    code: string
    status: string
    created_at: string
    created_by: string
}

export default function ProductCurrency() {
    const [formData, setFormData] = useState({
        currency_name: "",
        code: "",
        created_by: localStorage.getItem("user")
    })
    const [savedCurrencies, setSavedCurrencies] = useState<ProductCurrency[]>([])
    const [searchTerm, setSearchTerm] = useState("")
    const [filteredCurrencies, setFilteredCurrencies] = useState(currencyOptions)
    const [tableSortConfig, setTableSortConfig] = useState<{
        key: keyof ProductCurrency;
        direction: 'asc' | 'desc'
    } | null>(null)
    const [error, setError] = useState<string>("")
    const [currencyToDelete, setCurrencyToDelete] = useState<number | null>(null)
    const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false)
// Combine all permissions
    const user = useAppSelector((state) => state.user.user);
    const [roles, setRoles] = useState<Role[]>([])
    useEffect(() => {
        setRoles(user.roles)
    }, [])
    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions);
        return Array.from(new Set(allPermissions)); // Remove duplicates
    };

    const hasPermission = (roles: Role[], permission: string): boolean => {
        // If roles array is empty, allow all permissions
        if (!roles || roles.length === 0) {
            return true;
        }
        const allPermissions = getAllPermissions(roles);
        return allPermissions.includes(permission);
    };

    const userHasRequestPermission = hasPermission(roles, "Product Currency_Request"); // true
    const userHasDeletePermission = hasPermission(roles, "Product Currency_Delete"); // true
    const userHasApprovePermission = hasPermission(roles, "Product Currency_Approve"); // true
    const userHasViewPermission = hasPermission(roles, "Product Currency_View"); // false

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsConfirmDialogOpen(true)
    }
    useEffect(() => {
        fetchProductCurrencies()
    }, [])

    const fetchProductCurrencies = async () => {
        try {
            const {data} = await axios.get("/product-currencies")
            setSavedCurrencies(data)
        } catch (error) {
            console.error("Error fetching product currencies", error)
        }
    }

    const handleCurrencyChange = (currencyCode: string) => {
        const isDuplicate = savedCurrencies.some(
            (scheme) => scheme.currency_name.trim().toLowerCase() === currencyCode.trim().toLowerCase()
        )
        if (isDuplicate) {
            setError("This Currency already exists. Please enter a unique name.")
            return
        } else {
            setError("")
            const selectedCurrency = currencyOptions.find((option) => option.name === currencyCode)
            if (selectedCurrency) {
                setFormData({
                    currency_name: selectedCurrency.name,
                    code: selectedCurrency.code,
                    created_by: localStorage.getItem("user")
                })
            }
        }
    }

    const confirmSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsConfirmDialogOpen(false)
        try {
            const {data} = await axios.post("/product-currencies", formData)
            setSavedCurrencies((prev) => [...prev, data.productCurrency])
            setFormData({currency_name: "", code: "", created_by: localStorage.getItem("user")})
        } catch (error) {
            console.error("Error creating product currency", error)
        }
    }

    const handleDelete = async (id: number) => {
        try {
            await axios.delete(`/product-currencies/${id}`)
            setSavedCurrencies((prev) => prev.filter((currency) => currency.id !== id))
            setCurrencyToDelete(null)
        } catch (error) {
            console.error("Error deleting product currency", error)
        }
    }

    const handleToggleStatus = async (id: number, currentStatus: boolean) => {
        try {
            await axios.patch(`/product-currencies/${id}`, {is_active: !currentStatus})
            setSavedCurrencies((prev) =>
                prev.map((currency) =>
                    currency._id === id ? {...currency, is_active: !currentStatus} : currency
                )
            )
        } catch (error) {
            console.error("Error updating product currency status", error)
        }
    }

    const handleSort = (key: keyof ProductCurrency) => {
        let direction: 'asc' | 'desc' = 'asc'
        if (tableSortConfig && tableSortConfig.key === key && tableSortConfig.direction === 'asc') {
            direction = 'desc'
        }
        setTableSortConfig({key, direction})
    }

    const sortedCurrencies = [...savedCurrencies].sort((a, b) => {
        if (tableSortConfig === null) {
            return 0
        }
        const {key, direction} = tableSortConfig
        if (a[key] < b[key]) {
            return direction === 'asc' ? -1 : 1
        }
        if (a[key] > b[key]) {
            return direction === 'asc' ? 1 : -1
        }
        return 0
    })

    const filteredAndSortedCurrencies = sortedCurrencies.filter((currency) =>
        currency.currency_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        currency.code.toLowerCase().includes(searchTerm.toLowerCase())
    )

    return (
        <div className="w-full px-4 md:px-6 lg:px-8">
            <div className="flex flex-col gap-4 md:flex-row md:gap-6">
                {userHasRequestPermission && (
                    <div className="w-full md:w-1/3">
                        <Card>
                            <CardHeader>
                                <CardTitle>Product Currency Request</CardTitle>
                                <CardDescription>Add new product currency</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <ComboboxDemo onValueChange={handleCurrencyChange}/>
                                    {error && <p className="text-red-600">{error}</p>}
                                    <div className="flex justify-between">
                                        <Button type="button" variant="outline">
                                            Cancel
                                        </Button>
                                        <Button type="submit" disabled={!!error}>Request</Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>
                )}
                {userHasViewPermission && (
                    <div className="w-full md:w-2/3">
                        <Card>
                            <CardHeader>
                                <CardTitle>Saved Product Currencies</CardTitle>
                                <CardDescription>View and manage your saved product currencies</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="mb-4">
                                    <Input
                                        placeholder="Search currencies..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead className="w-[100px]">ID</TableHead>
                                            <TableHead className="cursor-pointer"
                                                       onClick={() => handleSort('currency_name')}>
                                                Currency Name <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                            </TableHead>
                                            <TableHead className="cursor-pointer" onClick={() => handleSort('code')}>
                                                Code <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                            </TableHead>
                                            <TableHead className="cursor-pointer"
                                                       onClick={() => handleSort('created_by')}>
                                                Created By <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                            </TableHead>

                                            <TableHead className="cursor-pointer"
                                                       onClick={() => handleSort('created_at')}>
                                                Created At <ArrowUpDown className="ml-2 h-4 w-4 inline"/>
                                            </TableHead>
                                            <TableHead>Active</TableHead>
                                            <TableHead>Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {filteredAndSortedCurrencies.map((currency, index) => (
                                            <TableRow key={currency.id}>
                                                <TableCell>{index + 1}</TableCell>
                                                <TableCell>{currency.currency_name}</TableCell>
                                                <TableCell>{currency.code.toUpperCase()}</TableCell>
                                                <TableCell>{formatDate(currency.created_by.name)}</TableCell>
                                                <TableCell>{formatDate(currency.created_at)}</TableCell>
                                                <TableCell>
                                                    <Switch
                                                        checked={currency.is_active}
                                                        onCheckedChange={() => handleToggleStatus(currency._id, currency.is_active)}
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <AlertDialog>
                                                        <AlertDialogTrigger asChild>
                                                            <Button
                                                                variant="destructive"
                                                                size="sm"
                                                            >
                                                                <TrashIcon className="h-4 w-4"/>
                                                            </Button>
                                                        </AlertDialogTrigger>
                                                        <AlertDialogContent>
                                                            <AlertDialogHeader>
                                                                <AlertDialogTitle>Delete Currency</AlertDialogTitle>
                                                                <AlertDialogDescription>
                                                                    Are you sure you want to
                                                                    delete {currency.currency_name}?
                                                                    This action cannot be undone.
                                                                </AlertDialogDescription>
                                                            </AlertDialogHeader>
                                                            <AlertDialogFooter>
                                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                                <AlertDialogAction
                                                                    onClick={() => handleDelete(currency._id)}
                                                                    className="bg-red-600 hover:bg-red-700"
                                                                >
                                                                    Delete
                                                                </AlertDialogAction>
                                                            </AlertDialogFooter>
                                                        </AlertDialogContent>
                                                    </AlertDialog>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </CardContent>
                        </Card>
                    </div>
                )}

            </div>
            <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Submission</AlertDialogTitle>
                        <AlertDialogDescription>
                            Do you want to submit this record? This record will be pending for approval!
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={confirmSubmit}>Submit</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    )
}