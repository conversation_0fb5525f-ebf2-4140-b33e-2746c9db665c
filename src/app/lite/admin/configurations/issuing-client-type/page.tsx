//@ts-nocheck
"use client"

import {useEffect, useState} from "react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table"
import axios from "@/utils/axiosInstance"
import axiosInstance from "@/utils/axiosInstance"
import {formatDate} from "@/utils/helpers";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle
} from "@/components/ui/alert-dialog";
import {TrashIcon} from "lucide-react";
import {
    <PERSON>alog,
    DialogContent,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ooter,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>
} from "@/components/ui/dialog";
import {Tabs} from "flowbite-react";
import {useAppSelector} from "@/store/hooks";
import {ActionsButtonGroup} from "@/components/ActionButtons";

interface IssuingClientType {
    id: number
    type: string
    code: string
    status: string
    created_at: string
    created_by: string
}

export default function IssuingClientType() {
    const [formData, setFormData] = useState({
        type: "",
        code: "", created_by: localStorage.getItem("user")
    })
    const [savedIssuingClientTypes, setSavedIssuingClientTypes] = useState<IssuingClientType[]>([])
    const [error, setError] = useState<string>("")  // Error state for handling duplicates
    const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false)
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [schemeToDelete, setSchemeToDelete] = useState<string | null>(null)
// Combine all permissions
    const user = useAppSelector((state) => state.user.user);
    const [roles, setRoles] = useState<Role[]>([])
    useEffect(() => {
        setRoles(user.roles)
    }, [])
    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions);
        return Array.from(new Set(allPermissions)); // Remove duplicates
    };

    const hasPermission = (roles: Role[], permission: string): boolean => {
        // If roles array is empty, allow all permissions
        if (!roles || roles.length === 0) {
            return true;
        }
        const allPermissions = getAllPermissions(roles);
        return allPermissions.includes(permission);
    };

    const userHasRequestPermission = hasPermission(roles, "Issuing Client Type_Request"); // true
    const userHasDeletePermission = hasPermission(roles, "Issuing Client Type_Delete"); // true
    const userHasApprovePermission = hasPermission(roles, "Issuing Client Type_Approve"); // true
    const userHasViewPermission = hasPermission(roles, "Issuing Client Type_View"); // false


    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsConfirmDialogOpen(true)
    }
    // Fetch issuing client types from the API when the component loads
    useEffect(() => {
        fetchIssuingClientTypes()
    }, [])
    const fetchIssuingClientTypes = async () => {
        try {
            const {data} = await axios.get("/issuing-client-types")
            // @ts-ignore
            setSavedIssuingClientTypes(data)
        } catch (error) {
            console.error("Error fetching issuing client types", error)
        }
    }
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const {name, value} = e.target
        setFormData((prev) => ({...prev, [name]: value}))
        if (name === "type") {
            const isDuplicate = savedIssuingClientTypes.some(
                (scheme) => scheme.type.trim().toLowerCase() === value.trim().toLowerCase()
            )
            setError(isDuplicate ? "This Name already exists. Please enter a unique name." : "")
        }
    }

    const confirmSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsConfirmDialogOpen(false)
        try {
            const {data} = await axios.post("/issuing-client-types", formData)
            // @ts-ignore
            await fetchIssuingClientTypes()
            setFormData({type: "", code: "", created_by: localStorage.getItem("user")}) // Reset form after submit
        } catch (error) {
            console.error("Error creating issuing client type", error)
        }
    }
    const handleDeleteClick = (id: string) => {
        setSchemeToDelete(id)
        setDeleteModalOpen(true)
    }

    const handleDeleteConfirm = async () => {
        if (schemeToDelete) {
            try {
                await axiosInstance.delete(`issuing-client-types/${schemeToDelete}`)
                setDeleteModalOpen(false)
                setSchemeToDelete(null)
                await fetchIssuingClientTypes()
            } catch (error) {
                console.error("Error deleting scheme:", error)
            }
        }
    }
    return (
        <div className="w-full px-4 md:px-6 lg:px-8">
            <div className="flex flex-col gap-4 md:flex-row md:gap-6">
                {userHasRequestPermission && (
                    <div className="w-full md:w-1/3">
                        <Card>
                            <CardHeader>
                                <CardTitle>Issuing Client Type Request</CardTitle>
                                <CardDescription>Add new issuing client type</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="type">Type</Label>
                                        <Input
                                            id="type"
                                            name="type"
                                            placeholder="Enter Issuing Client Type"
                                            value={formData.type}
                                            onChange={handleInputChange}
                                            required
                                        />
                                    </div>
                                    {error && <p className="text-red-600">{error}</p>}
                                    <div className="space-y-2">
                                        <Label htmlFor="code">Code</Label>
                                        <Input
                                            id="code"
                                            name="code"
                                            placeholder="Enter Code"
                                            value={formData.code}
                                            onChange={handleInputChange}
                                            required
                                        />
                                    </div>
                                    <div className="flex justify-between">
                                        <Button type="button" variant="outline">
                                            Cancel
                                        </Button>
                                        <Button type="submit" disabled={!!error}>Request</Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>
                )}
                {userHasViewPermission && (
                    <div className="w-full md:w-2/3">
                        <Card>
                            <CardHeader>
                                <CardTitle>Saved Issuing Client Types</CardTitle>
                                <CardDescription>View and manage your saved issuing client types</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Tabs aria-label="Tabs with underline" variant="underline">
                                    <Tabs.Item active title="Approved">
                                        <Table>
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead>ID</TableHead>
                                                    <TableHead>Type</TableHead>
                                                    <TableHead>Code</TableHead>
                                                    <TableHead>Created By</TableHead>
                                                    <TableHead>Created At</TableHead>
                                                    <TableHead>Status</TableHead>
                                                    <TableHead>Action</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {savedIssuingClientTypes.filter((type) => type.status.toLowerCase() === "active").map((type, index) => (
                                                    <TableRow key={index}>
                                                        <TableCell>{index + 1}</TableCell>
                                                        <TableCell>{type.type}   {type.version != null ? `(${type.version})` : ""}</TableCell>
                                                        <TableCell>{type.code.toUpperCase()}</TableCell>
                                                        {/*@ts-ignore*/}
                                                        <TableCell>{type.created_by.name}</TableCell>
                                                        <TableCell>{formatDate(type.created_at)}</TableCell>
                                                        <TableCell>{type.status.toUpperCase()}</TableCell>
                                                        <TableCell>
                                                            {userHasDeletePermission && (
                                                                <Button
                                                                    variant="destructive"
                                                                    size="sm"
                                                                    onClick={() => handleDeleteClick(type._id)}
                                                                >
                                                                    <TrashIcon className="h-4 w-4"/>
                                                                </Button>
                                                            )}
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </Tabs.Item>
                                    <Tabs.Item title="Pending">
                                        <Table>
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead>ID</TableHead>
                                                    <TableHead>Type</TableHead>
                                                    <TableHead>Code</TableHead>
                                                    <TableHead>Created By</TableHead>
                                                    <TableHead>Created At</TableHead>
                                                    <TableHead>Status</TableHead>
                                                    <TableHead>Action</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {savedIssuingClientTypes.filter((type) => type.status.toLowerCase() === "pending").map((type, index) => (
                                                    <TableRow key={index}>
                                                        <TableCell>{index + 1}</TableCell>
                                                        <TableCell>{type.type}   {type.version != null ? `(${type.version})` : ""}</TableCell>
                                                        <TableCell>{type.code.toUpperCase()}</TableCell>
                                                        {/*@ts-ignore*/}
                                                        <TableCell>{type.created_by.name}</TableCell>
                                                        <TableCell>{formatDate(type.created_at)}</TableCell>
                                                        <TableCell>{type.status.toUpperCase()}</TableCell>
                                                        <TableCell>
                                                            <ActionsButtonGroup
                                                                entity={type}
                                                                entityType="issuing-client-types" // For BIN Type
                                                                entityName={type.type}
                                                                userHasApprovePermission={userHasApprovePermission}
                                                                userHasDeletePermission={userHasViewPermission}
                                                                handleDeleteClick={handleDeleteClick}
                                                                fetchEntities={fetchIssuingClientTypes}
                                                            />
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </Tabs.Item>
                                    <Tabs.Item title="Modify">
                                        <Table>
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead>ID</TableHead>
                                                    <TableHead>Type</TableHead>
                                                    <TableHead>Code</TableHead>
                                                    <TableHead>Created By</TableHead>
                                                    <TableHead>Created At</TableHead>
                                                    <TableHead>Status</TableHead>
                                                    <TableHead>Action</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {savedIssuingClientTypes.filter((type) => type.status.toLowerCase() === "modify").map((type, index) => (
                                                    <TableRow key={index}>
                                                        <TableCell>{index + 1}</TableCell>
                                                        <TableCell>{type.type}   {type.version != null ? `(${type.version})` : ""}</TableCell>
                                                        <TableCell>{type.code.toUpperCase()}</TableCell>
                                                        {/*@ts-ignore*/}
                                                        <TableCell>{type.created_by.name}</TableCell>
                                                        <TableCell>{formatDate(type.created_at)}</TableCell>
                                                        <TableCell>{type.status.toUpperCase()}
                                                            <br/> Notes: {type.reason}</TableCell>
                                                        <TableCell>
                                                            <ActionsButtonGroup
                                                                entity={type}
                                                                entityType="issuing-client-types" // For BIN Type
                                                                entityName={type.type}
                                                                userHasApprovePermission={userHasApprovePermission}
                                                                userHasDeletePermission={userHasViewPermission}
                                                                handleDeleteClick={handleDeleteClick}
                                                                fetchEntities={fetchIssuingClientTypes}
                                                            />
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </Tabs.Item>
                                    <Tabs.Item title="Declined">
                                        <Table>
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead>ID</TableHead>
                                                    <TableHead>Type</TableHead>
                                                    <TableHead>Code</TableHead>
                                                    <TableHead>Created By</TableHead>
                                                    <TableHead>Created At</TableHead>
                                                    <TableHead>Status</TableHead>
                                                    <TableHead>Action</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {savedIssuingClientTypes.filter((type) => type.status.toLowerCase() === "decline").map((type, index) => (
                                                    <TableRow key={index}>
                                                        <TableCell>{index + 1}</TableCell>
                                                        <TableCell>{type.type}  {type.version != null ? `(${type.version})` : ""}</TableCell>
                                                        <TableCell>{type.code.toUpperCase()}</TableCell>
                                                        {/*@ts-ignore*/}
                                                        <TableCell>{type.created_by.name}</TableCell>
                                                        <TableCell>{formatDate(type.created_at)}</TableCell>
                                                        <TableCell>{type.status.toUpperCase()}</TableCell>
                                                        <TableCell>
                                                            {type.reason}
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </Tabs.Item>
                                </Tabs>

                            </CardContent>
                        </Card>
                    </div>
                )}
            </div>
            <Dialog open={deleteModalOpen} onOpenChange={setDeleteModalOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Action: Delete</DialogTitle>
                        <DialogDescription>
                            Deletion of this record will be sent to administrator for approval. Are you sure to proceed?
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setDeleteModalOpen(false)}>
                            Cancel
                        </Button>
                        <Button variant="destructive" onClick={handleDeleteConfirm}>
                            Delete
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
            <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Submission</AlertDialogTitle>
                        <AlertDialogDescription>
                            Do you want to submit this record? This record will be pending for approval!
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={confirmSubmit}>Submit</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    )
}
