"use client"

import type React from "react"

import { useState, use<PERSON>allback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Upload,
  FileSpreadsheet,
  BarChart3,
  TrendingUp,
  Download,
  Database,
  Settings,
  ArrowLeft,
  Plus,
} from "lucide-react"
import * as XLSX from "xlsx"
import { DatabaseProgress } from "@/components/database-progress"
import { DatabaseViewer } from "@/components/database-viewer"
import { DuplicateDetectionConfig } from "@/components/duplicate-detection-config"
import { DatabaseComparisonResults } from "@/components/database-comparison-results"
import { BackgroundUploadManager } from "@/components/background-upload-manager"
import { BackgroundUploadIndicator } from "@/components/background-upload-indicator"
import { saveRecordsWithDuplicateDetection, type EnhancedUploadResult } from "@/lib/enhanced-database"
import type { DatabaseRecord } from "@/lib/database"
import type { DuplicateDetectionConfig as DuplicateConfig } from "@/lib/duplicate-detection"

interface ExcelData {
  headers: string[]
  rows: any[][]
  fileName: string
  sheetNames: string[]
  currentSheet: string
}

interface ColumnStats {
  name: string
  type: "number" | "text" | "date" | "mixed"
  count: number
  nullCount: number
  uniqueCount: number
  min?: number
  max?: number
  mean?: number
  median?: number
  mode?: any
}

interface SaveProgress {
  isVisible: boolean
  savedRecords: number
  totalRecords: number
  isComplete: boolean
  isError: boolean
  errorMessage?: string
  estimatedTimeRemaining?: number
}

type ViewMode = "database" | "upload"

export default function ExcelAnalyzer() {
  const [viewMode, setViewMode] = useState<ViewMode>("database")
  const [excelData, setExcelData] = useState<ExcelData | null>(null)
  const [loading, setLoading] = useState(false)
  const [columnStats, setColumnStats] = useState<ColumnStats[]>([])
  const [selectedColumns, setSelectedColumns] = useState<string[]>([])
  const [saveProgress, setSaveProgress] = useState<SaveProgress>({
    isVisible: false,
    savedRecords: 0,
    totalRecords: 0,
    isComplete: false,
    isError: false,
  })
  const [showUploadManager, setShowUploadManager] = useState(false)
  const [showDuplicateConfig, setShowDuplicateConfig] = useState(false)
  const [showComparisonResults, setShowComparisonResults] = useState(false)
  const [duplicateConfig, setDuplicateConfig] = useState<DuplicateConfig & { compareWithDatabase: boolean }>({
    primaryKey: ["country", "region"],
    strategy: "replace",
    compareWithDatabase: true,
  })
  const [lastUploadResult, setLastUploadResult] = useState<EnhancedUploadResult | null>(null)

  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setLoading(true)
    try {
      const arrayBuffer = await file.arrayBuffer()
      const workbook = XLSX.read(arrayBuffer, { type: "array" })
      const sheetNames = workbook.SheetNames
      const firstSheet = workbook.Sheets[sheetNames[0]]
      const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 })

      const headers = jsonData[0] as string[]
      const rows = jsonData.slice(1) as any[][]

      const newData: ExcelData = {
        headers,
        rows,
        fileName: file.name,
        sheetNames,
        currentSheet: sheetNames[0],
      }

      setExcelData(newData)
      calculateColumnStats(headers, rows)
      setSelectedColumns(headers.slice(0, 2))

      // Auto-detect common duplicate detection fields
      const commonFields = ["country", "region", "name", "id", "code"]
      const detectedFields = headers.filter((h) =>
        commonFields.some((cf) => h.toLowerCase().includes(cf.toLowerCase())),
      )

      if (detectedFields.length > 0) {
        setDuplicateConfig((prev) => ({
          ...prev,
          primaryKey: detectedFields.slice(0, 2),
        }))
      }
    } catch (error) {
      console.error("Error reading Excel file:", error)
    } finally {
      setLoading(false)
    }
  }, [])

  const calculateColumnStats = (headers: string[], rows: any[][]) => {
    const stats: ColumnStats[] = headers.map((header, index) => {
      const columnData = rows.map((row) => row[index]).filter((val) => val !== undefined && val !== null && val !== "")
      const nullCount = rows.length - columnData.length

      // Determine data type
      const numericData = columnData.filter((val) => !isNaN(Number(val)) && val !== "").map(Number)
      const isNumeric = numericData.length > columnData.length * 0.8

      let type: "number" | "text" | "date" | "mixed" = "text"
      if (isNumeric) type = "number"
      else if (columnData.some((val) => !isNaN(Date.parse(val)))) type = "date"
      else if (columnData.length > 0) type = "mixed"

      // Calculate statistics for numeric columns
      let min, max, mean, median
      if (type === "number" && numericData.length > 0) {
        min = Math.min(...numericData)
        max = Math.max(...numericData)
        mean = numericData.reduce((a, b) => a + b, 0) / numericData.length
        const sorted = [...numericData].sort((a, b) => a - b)
        median =
          sorted.length % 2 === 0
            ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
            : sorted[Math.floor(sorted.length / 2)]
      }

      // Calculate mode
      const frequency: { [key: string]: number } = {}
      columnData.forEach((val) => {
        const key = String(val)
        frequency[key] = (frequency[key] || 0) + 1
      })
      const mode = Object.keys(frequency).reduce((a, b) => (frequency[a] > frequency[b] ? a : b), "")

      return {
        name: header,
        type,
        count: columnData.length,
        nullCount,
        uniqueCount: new Set(columnData).size,
        min,
        max,
        mean,
        median,
        mode: type === "number" ? Number(mode) : mode,
      }
    })

    setColumnStats(stats)
  }

  const saveToDatabase = async () => {
    if (!excelData) return

    // Reset progress state
    setSaveProgress({
      isVisible: true,
      savedRecords: 0,
      totalRecords: excelData.rows.length,
      isComplete: false,
      isError: false,
    })

    try {
      // Prepare records for database
      const records: DatabaseRecord[] = excelData.rows.map((row, index) => {
        const rowData: Record<string, any> = {}
        excelData.headers.forEach((header, colIndex) => {
          rowData[header] = row[colIndex] || null
        })

        return {
          fileName: excelData.fileName,
          sheetName: excelData.currentSheet,
          headers: excelData.headers,
          rowData,
          rowIndex: index,
          totalRows: excelData.rows.length,
        }
      })

      // Save with duplicate detection and database comparison
      const result = await saveRecordsWithDuplicateDetection(
        records,
        {
          duplicateDetection: duplicateConfig,
          compareWithDatabase: duplicateConfig.compareWithDatabase,
        },
        (saved, total, status) => {
          setSaveProgress((prev) => ({
            ...prev,
            savedRecords: saved,
            estimatedTimeRemaining: saved > 0 ? ((total - saved) / saved) * 1000 : undefined,
          }))
        },
      )

      // Store result for display
      setLastUploadResult(result)

      // Mark as complete
      setSaveProgress((prev) => ({
        ...prev,
        isComplete: true,
      }))

      // Show comparison results if available
      if (result.comparisonResult) {
        setShowComparisonResults(true)
      }

      // Show success message and return to database view
      alert(
        `Upload completed! Saved: ${result.savedRecords}, Duplicates: ${result.duplicatesHandled}, Replaced: ${result.replacedRecords}`,
      )

      // Return to database view after successful upload
      setTimeout(() => {
        setViewMode("database")
        setExcelData(null) // Clear the uploaded data
      }, 2000)
    } catch (error) {
      console.error("Error saving to database:", error)
      setSaveProgress((prev) => ({
        ...prev,
        isError: true,
        errorMessage: error instanceof Error ? error.message : "Unknown error occurred",
      }))
    }
  }

  const changeSheet = async (sheetName: string) => {
    if (!excelData) return

    setLoading(true)
    try {
      // We need to re-read the file to get the specific sheet
      // For now, we'll simulate this by updating the current sheet name
      // In a real implementation, you'd store the original file data
      const updatedData = { ...excelData, currentSheet: sheetName }
      setExcelData(updatedData)

      // Note: In a full implementation, you would:
      // 1. Store the original ArrayBuffer when first uploading
      // 2. Re-parse the specific sheet here
      // 3. Update the data accordingly

      console.log(`Switched to sheet: ${sheetName}`)
    } finally {
      setLoading(false)
    }
  }

  const exportData = () => {
    if (!excelData) return

    const ws = XLSX.utils.aoa_to_sheet([excelData.headers, ...excelData.rows])
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, "Analyzed Data")
    XLSX.writeFile(wb, `analyzed_${excelData.fileName}`)
  }

  const getChartData = () => {
    if (!excelData || selectedColumns.length < 2) return []

    const xIndex = excelData.headers.indexOf(selectedColumns[0])
    const yIndex = excelData.headers.indexOf(selectedColumns[1])

    return excelData.rows
      .slice(0, 10)
      .map((row) => ({
        x: row[xIndex],
        y: Number(row[yIndex]) || 0,
      }))
      .filter((item) => item.x && !isNaN(item.y))
  }

  // Database View Component
  const DatabaseView = () => (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Database Records</h1>
        <p className="text-muted-foreground">View and manage your Excel data records with comprehensive insights</p>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                Data Management
              </CardTitle>
              <CardDescription>Manage your uploaded Excel data and add new records</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button onClick={() => setShowUploadManager(true)} variant="outline" size="sm">
                <Database className="w-4 h-4 mr-2" />
                Upload Manager
              </Button>
              <Button onClick={() => setViewMode("upload")} className="bg-primary hover:bg-primary/90">
                <Plus className="w-4 h-4 mr-2" />
                Update Data
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Full Database Viewer */}
      <DatabaseViewer isOpen={true} onClose={() => {}} />
    </div>
  )

  // Upload View Component
  const UploadView = () => (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button onClick={() => setViewMode("database")} variant="outline" size="sm">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Database
        </Button>
        <div className="text-center flex-1">
          <h1 className="text-3xl font-bold">Excel Data Analyzer</h1>
          <p className="text-muted-foreground">
            Upload and analyze your Excel files with comprehensive insights and duplicate detection
          </p>
        </div>
      </div>

      {!excelData ? (
        <Card className="w-full max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="w-5 h-5" />
              Upload Excel File
            </CardTitle>
            <CardDescription>Select an Excel file (.xlsx, .xls) to begin analysis</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                <FileSpreadsheet className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <Label htmlFor="file-upload" className="cursor-pointer">
                  <span className="text-sm font-medium">Click to upload</span>
                  <span className="text-sm text-muted-foreground"> or drag and drop</span>
                </Label>
                <Input
                  id="file-upload"
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>
              {loading && (
                <div className="text-center">
                  <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  <p className="mt-2 text-sm text-muted-foreground">Processing file...</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {/* File Info Header */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileSpreadsheet className="w-5 h-5" />
                    {excelData.fileName}
                  </CardTitle>
                  <CardDescription>
                    {excelData.rows.length} rows × {excelData.headers.length} columns
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Select value={excelData.currentSheet} onValueChange={changeSheet}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {excelData.sheetNames.map((name) => (
                        <SelectItem key={name} value={name}>
                          {name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button onClick={() => setShowDuplicateConfig(true)} variant="outline" size="sm">
                    <Settings className="w-4 h-4 mr-2" />
                    Configure
                  </Button>
                  <Button onClick={saveToDatabase} variant="outline" size="sm">
                    <Database className="w-4 h-4 mr-2" />
                    Save to DB
                  </Button>
                  <Button onClick={exportData} variant="outline" size="sm">
                    <Download className="w-4 h-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Duplicate Detection Summary */}
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium text-blue-700">
                    Duplicate Detection: {duplicateConfig.primaryKey.join(" + ")} | Strategy: {duplicateConfig.strategy}
                  </span>
                </div>
                <Button
                  onClick={() => setShowDuplicateConfig(true)}
                  variant="ghost"
                  size="sm"
                  className="text-blue-700 hover:text-blue-800"
                >
                  <Settings className="w-4 h-4 mr-1" />
                  Configure
                </Button>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="data">Data View</TabsTrigger>
              <TabsTrigger value="statistics">Statistics</TabsTrigger>
              <TabsTrigger value="charts">Charts</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Rows</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{excelData.rows.length.toLocaleString()}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Columns</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{excelData.headers.length}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Data Types</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex gap-1 flex-wrap">
                      {Array.from(new Set(columnStats.map((stat) => stat.type))).map((type) => (
                        <Badge key={type} variant="secondary" className="text-xs">
                          {type}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Column Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-64">
                    <div className="space-y-2">
                      {columnStats.map((stat, index) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded">
                          <div>
                            <span className="font-medium">{stat.name}</span>
                            <Badge variant="outline" className="ml-2 text-xs">
                              {stat.type}
                            </Badge>
                            {duplicateConfig.primaryKey.includes(stat.name) && (
                              <Badge variant="default" className="ml-1 text-xs">
                                Primary Key
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {stat.count} values, {stat.uniqueCount} unique
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="data" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Data Preview</CardTitle>
                  <CardDescription>First 100 rows of your data</CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-96 w-full">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {excelData.headers.map((header, index) => (
                            <TableHead key={index} className="whitespace-nowrap">
                              {header}
                              {duplicateConfig.primaryKey.includes(header) && (
                                <Badge variant="default" className="ml-1 text-xs">
                                  PK
                                </Badge>
                              )}
                            </TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {excelData.rows.slice(0, 100).map((row, rowIndex) => (
                          <TableRow key={rowIndex}>
                            {row.map((cell, cellIndex) => (
                              <TableCell key={cellIndex} className="whitespace-nowrap">
                                {cell?.toString() || "—"}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="statistics" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Detailed Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-96">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Column</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Count</TableHead>
                          <TableHead>Null</TableHead>
                          <TableHead>Unique</TableHead>
                          <TableHead>Min</TableHead>
                          <TableHead>Max</TableHead>
                          <TableHead>Mean</TableHead>
                          <TableHead>Median</TableHead>
                          <TableHead>Mode</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {columnStats.map((stat, index) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">
                              {stat.name}
                              {duplicateConfig.primaryKey.includes(stat.name) && (
                                <Badge variant="default" className="ml-1 text-xs">
                                  PK
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">{stat.type}</Badge>
                            </TableCell>
                            <TableCell>{stat.count}</TableCell>
                            <TableCell>{stat.nullCount}</TableCell>
                            <TableCell>{stat.uniqueCount}</TableCell>
                            <TableCell>{stat.min?.toFixed(2) || "—"}</TableCell>
                            <TableCell>{stat.max?.toFixed(2) || "—"}</TableCell>
                            <TableCell>{stat.mean?.toFixed(2) || "—"}</TableCell>
                            <TableCell>{stat.median?.toFixed(2) || "—"}</TableCell>
                            <TableCell>{stat.mode?.toString() || "—"}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="charts" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    Data Visualization
                  </CardTitle>
                  <CardDescription>Select columns to create charts and visualizations</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>X-Axis Column</Label>
                      <Select
                        value={selectedColumns[0]}
                        onValueChange={(value) => setSelectedColumns([value, selectedColumns[1]])}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select column" />
                        </SelectTrigger>
                        <SelectContent>
                          {excelData.headers.map((header) => (
                            <SelectItem key={header} value={header}>
                              {header}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Y-Axis Column</Label>
                      <Select
                        value={selectedColumns[1]}
                        onValueChange={(value) => setSelectedColumns([selectedColumns[0], value])}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select column" />
                        </SelectTrigger>
                        <SelectContent>
                          {excelData.headers
                            .filter((header) => columnStats.find((stat) => stat.name === header)?.type === "number")
                            .map((header) => (
                              <SelectItem key={header} value={header}>
                                {header}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {selectedColumns.length === 2 && (
                    <div className="space-y-4">
                      <div className="h-64 border rounded-lg flex items-center justify-center bg-muted/10">
                        <div className="text-center">
                          <TrendingUp className="w-12 h-12 mx-auto mb-2 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground">Chart visualization would appear here</p>
                          <p className="text-xs text-muted-foreground mt-1">
                            Showing {selectedColumns[0]} vs {selectedColumns[1]}
                          </p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm">Sample Data Points</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-1 text-xs">
                              {getChartData()
                                .slice(0, 5)
                                .map((point, index) => (
                                  <div key={index} className="flex justify-between">
                                    <span>{point.x}</span>
                                    <span>{point.y}</span>
                                  </div>
                                ))}
                            </div>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm">Chart Info</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-1 text-xs">
                              <div>Data Points: {getChartData().length}</div>
                              <div>X-Axis: {selectedColumns[0]}</div>
                              <div>Y-Axis: {selectedColumns[1]}</div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  )

  return (
    <div className="container mx-auto p-6">
      {viewMode === "database" ? <DatabaseView /> : <UploadView />}

      {/* Database Progress Modal */}
      <DatabaseProgress
        isVisible={saveProgress.isVisible}
        savedRecords={saveProgress.savedRecords}
        totalRecords={saveProgress.totalRecords}
        isComplete={saveProgress.isComplete}
        isError={saveProgress.isError}
        errorMessage={saveProgress.errorMessage}
        estimatedTimeRemaining={saveProgress.estimatedTimeRemaining}
        onClose={() => setSaveProgress((prev) => ({ ...prev, isVisible: false }))}
      />

      {/* Duplicate Detection Configuration */}
      <DuplicateDetectionConfig
        headers={excelData?.headers || []}
        onConfigChange={setDuplicateConfig}
        isVisible={showDuplicateConfig}
        onClose={() => setShowDuplicateConfig(false)}
      />

      {/* Database Comparison Results */}
      {lastUploadResult?.comparisonResult && (
        <DatabaseComparisonResults
          comparisonResult={lastUploadResult.comparisonResult}
          duplicateDetails={lastUploadResult.duplicateDetails}
          isVisible={showComparisonResults}
          onClose={() => setShowComparisonResults(false)}
        />
      )}

      {/* Background Upload Indicator */}
      <BackgroundUploadIndicator onOpenManager={() => setShowUploadManager(true)} />

      {/* Background Upload Manager */}
      <BackgroundUploadManager isOpen={showUploadManager} onClose={() => setShowUploadManager(false)} />
    </div>
  )
}
