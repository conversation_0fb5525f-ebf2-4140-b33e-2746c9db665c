//@ts-nocheck
"use client"

import type React from "react"
import { useEffect, useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Tabs } from "flowbite-react"
import { She<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/sheet"
import axios from "@/utils/axiosInstance"
import { ArrowUpDown, Trash2, AlertTriangle, CheckCircle, XCircle } from "lucide-react"
import { formatDate } from "@/utils/helpers"
import { useAppSelector } from "@/store/hooks"
import { ActionsButtonGroup } from "@/components/ActionButtons"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { Alert } from "@/components/alert"
import InputMask from "react-input-mask"
import DataExporter from "@/components/DataExporter"

interface BinType {
    _id: string
    type: string
    cardScheme: string
    binVariant: string
    binCategory: string
    programmeType: string
    reason: string
    created_at: string
    created_by: string
    status: string
}

interface BinRange {
    _id: number
    binType: string
    binCode: string
    binCodePrefix: string
    binCodeSuffix: string
    currency: string
    bin_start: string
    bin_end: string
    created_at: string
    created_by: any
    status: string
    category?: string
}

interface ValidationError {
    field: string
    message: string
    type: "error" | "warning" | "info"
}

interface RangeConflict {
    conflictType: "overlap" | "duplicate" | "adjacent"
    existingRange: BinRange
    message: string
}

interface Currency {
    _id: string
    currency_code: string
    is_active: boolean
    status: string
}

interface Role {
    permissions: string[]
}

export default function BinRange() {
    const [formData, setFormData] = useState({
        binType: "",
        binCode: "",
        binCodePrefix: "",
        binCodeSuffix: "",
        currency: "",
        bin_end: "",
        bin_start: "",
        created_by: localStorage.getItem("user"),
    })

    const [savedBinRanges, setSavedBinRanges] = useState<BinRange[]>([])
    const [searchTerm, setSearchTerm] = useState("")
    const [sortConfig, setSortConfig] = useState<{ key: keyof BinRange; direction: "asc" | "desc" } | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [alert, setAlert] = useState<{ message: string; type: "success" | "error" } | null>(null)
    const [isOverlayVisible, setIsOverlayVisible] = useState(false)
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [isSheetOpen, setIsSheetOpen] = useState(false)
    const [savedbinTypes, setSavedbinTypes] = useState<BinType[]>([])
    const [currencies, setCurrencies] = useState<Currency[]>([])
    const [validationErrors, setValidationErrors] = useState<ValidationError[]>([])
    const [rangeConflicts, setRangeConflicts] = useState<RangeConflict[]>([])
    const [availableCurrencies, setAvailableCurrencies] = useState<Currency[]>([])
    const [isFormValid, setIsFormValid] = useState(false)

    const user = useAppSelector((state) => state.user.user)
    const [roles, setRoles] = useState<Role[]>([])
    const [deleteId, setDeleteId] = useState<string | null>(null)

    // Memoized calculations for performance
    const existingRangesForBinType = useMemo(() => {
        if (!formData.binType) return []
        return savedBinRanges.filter((range) => {
            const rangeTypeId = typeof range.binType === "object" ? range.binType._id : range.binType
            return rangeTypeId === formData.binType && range.status === "active"
        })
    }, [savedBinRanges, formData.binType])

    const usedCurrenciesForBinType = useMemo(() => {
        return new Set(existingRangesForBinType.map((range) => range.currency))
    }, [existingRangesForBinType])

    useEffect(() => {
        setRoles(user.roles)
        fetchBinRanges().then(() => {
            logBinRangeStructure()
        })
    }, [user.roles])

    useEffect(() => {
        fetchInitialData()
    }, [])

    useEffect(() => {
        validateForm()
    }, [formData, savedBinRanges])

    useEffect(() => {
        updateAvailableCurrencies()
    }, [formData.binType, currencies, usedCurrenciesForBinType])

    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions))
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }

    const userHasRequestPermission = hasPermission(roles, "Bin Range_Request")
    const userHasDeletePermission = hasPermission(roles, "Bin Range_Delete")
    const userHasApprovePermission = hasPermission(roles, "Bin Range_Approve")
    const userHasViewPermission = hasPermission(roles, "Bin Range_View")

    const updateAvailableCurrencies = () => {
        if (!formData.binType) {
            setAvailableCurrencies(currencies.filter((ccy) => ccy.is_active && ccy.status === "active"))
            return
        }

        // Show all currencies but mark used ones
        const available = currencies.filter((ccy) => ccy.is_active && ccy.status === "active")
        setAvailableCurrencies(available)
    }

    const validateForm = () => {
        const errors: ValidationError[] = []
        const conflicts: RangeConflict[] = []

        // Basic field validation
        if (formData.binType && !formData.currency) {
            errors.push({
                field: "currency",
                message: "Currency is required when BIN Type is selected",
                type: "error",
            })
        }

        if (formData.bin_start && formData.bin_end) {
            // Remove spaces and convert to numbers for comparison
            const startNum = Number.parseInt(formData.bin_start.replace(/\s/g, ""))
            const endNum = Number.parseInt(formData.bin_end.replace(/\s/g, ""))

            if (startNum >= endNum) {
                errors.push({
                    field: "bin_end",
                    message: "End range must be greater than start range",
                    type: "error",
                })
            }

            // Check for range conflicts
            if (formData.binType && formData.currency) {
                const conflictingRanges = existingRangesForBinType.filter((range) => {
                    if (range.currency !== formData.currency) return false

                    const existingStart = Number.parseInt(range.bin_start.replace(/\s/g, ""))
                    const existingEnd = Number.parseInt(range.bin_end.replace(/\s/g, ""))

                    // Check for overlap
                    return startNum <= existingEnd && endNum >= existingStart
                })

                conflictingRanges.forEach((range) => {
                    const existingStart = Number.parseInt(range.bin_start.replace(/\s/g, ""))
                    const existingEnd = Number.parseInt(range.bin_end.replace(/\s/g, ""))

                    if (startNum === existingStart && endNum === existingEnd) {
                        conflicts.push({
                            conflictType: "duplicate",
                            existingRange: range,
                            message: `Exact duplicate range exists: ${range.bin_start} - ${range.bin_end}`,
                        })
                    } else {
                        conflicts.push({
                            conflictType: "overlap",
                            existingRange: range,
                            message: `Range overlaps with existing: ${range.bin_start} - ${range.bin_end}`,
                        })
                    }
                })
            }
        }

        // Check BIN Code uniqueness
        if (formData.binCode) {
            const existingBinCode = savedBinRanges.find(
                (range) => range.binCode === formData.binCode && range.status === "active",
            )
            if (existingBinCode) {
                errors.push({
                    field: "binCode",
                    message: "BIN Code already exists",
                    type: "error",
                })
            }
        }

        // Validate range format
        if (formData.bin_start && !/^\d{4}\s\d{4}$/.test(formData.bin_start)) {
            errors.push({
                field: "bin_start",
                message: "Invalid format. Use: XXXX XXXX",
                type: "error",
            })
        }

        if (formData.bin_end && !/^\d{4}\s\d{4}$/.test(formData.bin_end)) {
            errors.push({
                field: "bin_end",
                message: "Invalid format. Use: XXXX XXXX",
                type: "error",
            })
        }

        setValidationErrors(errors)
        setRangeConflicts(conflicts)
        setIsFormValid(errors.length === 0 && conflicts.length === 0)
    }

    const getFieldError = (fieldName: string) => {
        return validationErrors.find((error) => error.field === fieldName)
    }

    const getBinTypeName = (binTypeId: string) => {
        const binType = savedbinTypes.find((type) => type._id === binTypeId)
        return binType ? binType.type : "N/A"
    }

    const getCurrencyCode = (currencyId: string) => {
        const currency = currencies.find((ccy) => ccy._id === currencyId)
        return currency ? currency.currency_code : "N/A"
    }

    const isCurrencyUsed = (currencyId: string) => {
        return usedCurrenciesForBinType.has(currencyId)
    }

    const getAvailableRangesSuggestion = () => {
        if (!formData.binType || !formData.currency) return null

        const usedRanges = existingRangesForBinType
            .filter((range) => range.currency === formData.currency)
            .map((range) => ({
                start: Number.parseInt(range.bin_start.replace(/\s/g, "")),
                end: Number.parseInt(range.bin_end.replace(/\s/g, "")),
            }))
            .sort((a, b) => a.start - b.start)

        if (usedRanges.length === 0) {
            return "Full range available: 0000 0000 - 9999 9999"
        }

        const gaps = []
        let lastEnd = 0

        usedRanges.forEach((range) => {
            if (range.start > lastEnd + 1) {
                const gapStart = String(lastEnd + 1).padStart(8, "0")
                const gapEnd = String(range.start - 1).padStart(8, "0")
                gaps.push(`${gapStart.slice(0, 4)} ${gapStart.slice(4)} - ${gapEnd.slice(0, 4)} ${gapEnd.slice(4)}`)
            }
            lastEnd = range.end
        })

        if (lastEnd < 99999999) {
            const gapStart = String(lastEnd + 1).padStart(8, "0")
            gaps.push(`${gapStart.slice(0, 4)} ${gapStart.slice(4)} - 9999 9999`)
        }

        return gaps.length > 0 ? `Available ranges: ${gaps.join(", ")}` : "No available ranges"
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!isFormValid) {
            setAlert({ message: "Please fix validation errors before submitting", type: "error" })
            return
        }

        setIsLoading(true)
        try {
            await axios.post("/bin-range", formData)
            await fetchBinRanges()
            setFormData({
                binType: "",
                binCode: "",
                binCodePrefix: "",
                binCodeSuffix: "",
                currency: "",
                bin_end: "",
                bin_start: "",
                created_by: localStorage.getItem("user"),
            })
            setValidationErrors([])
            setRangeConflicts([])
            setAlert({ message: "Bin Range created successfully!", type: "success" })
            setIsSheetOpen(false)
        } catch (error) {
            console.error("Error creating Bin Range", error)
            setAlert({ message: "Error creating Bin Range. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
        }
    }

    const fetchInitialData = async () => {
        setIsLoading(true)
        try {
            const [binTypes, cur] = await Promise.all([axios.get("/bin-types"), axios.get("product-currencies")])
            setSavedbinTypes(binTypes.data.data)
            setCurrencies(cur.data)
        } catch (error) {
            console.error("Error fetching BIN Types", error)
            setAlert({ message: "Error fetching data. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
        }
    }

    const logBinRangeStructure = () => {
        if (savedBinRanges.length > 0) {
            console.log("Sample bin range structure:", savedBinRanges[0])
        }
    }

    const fetchBinRanges = async () => {
        setIsLoading(true)
        try {
            const response = await axios.get("/bin-range")
            setSavedBinRanges(response.data)
        } catch (error) {
            console.error("Error fetching BIN Range", error)
            setAlert({ message: "Error fetching data. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
        }
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement> | string, selectName?: string) => {
        if (typeof e === "string" && selectName) {
            setFormData((prev) => ({ ...prev, [selectName]: e }))

            if (selectName === "binType") {
                const existingData = savedbinTypes.find((range) => range._id === e)
                if (existingData) {
                    setFormData((prev) => ({
                        ...prev,
                        binType: e,
                        binCodePrefix: existingData.binCategory.bin_prefix || "",
                        binCodeSuffix: existingData.binVariant.bin_suffix || "",
                        binCode: `${existingData.binCategory.bin_prefix}${existingData.binVariant.bin_suffix}` || "",
                        currency: existingData.binCategory?.currency?._id || "",
                        bin_start: "",
                        bin_end: "",
                    }))
                }
            }
        } else if (typeof e !== "string") {
            const { name, value } = e.target
            setFormData((prev) => ({ ...prev, [name]: value }))
        }

        if ((typeof e !== "string" && e.target.name === "binCodeSuffix") || selectName === "binCodeSuffix") {
            const value = typeof e === "string" ? e : e.target.value
            const newBinCode = `${formData.binCodePrefix}${value}`
            setFormData((prev) => ({ ...prev, binCode: newBinCode }))
        }

        if ((typeof e !== "string" && e.target.name === "binCodePrefix") || selectName === "binCodePrefix") {
            const value = typeof e === "string" ? e : e.target.value
            const newBinCode = `${value}${formData.binCodeSuffix}`
            if (formData.binCodeSuffix) {
                setFormData((prev) => ({ ...prev, binCode: newBinCode }))
            }
        }
    }

    const handleSort = (key: keyof BinRange) => {
        let direction: "asc" | "desc" = "asc"
        if (sortConfig && sortConfig.key === key && sortConfig.direction === "asc") {
            direction = "desc"
        }
        setSortConfig({ key, direction })
    }

    const filteredAndSortedRanges = savedBinRanges
        .filter((range) => {
            const searchFields = [
                range.binCode,
                getBinTypeName(range.binType),
                getCurrencyCode(range.currency),
                range.bin_start,
                range.bin_end,
            ].map((field) => field?.toLowerCase() || "")
            return searchFields.some((field) => field.includes(searchTerm.toLowerCase()))
        })
        .sort((a, b) => {
            if (sortConfig === null) {
                return 0
            }
            const { key, direction } = sortConfig
            if (key === "binType") {
                const aName = getBinTypeName(a[key])
                const bName = getBinTypeName(b[key])
                return direction === "asc" ? aName.localeCompare(bName) : bName.localeCompare(aName)
            }
            if (key === "currency") {
                const aCode = getCurrencyCode(a[key])
                const bCode = getCurrencyCode(b[key])
                return direction === "asc" ? aCode.localeCompare(bCode) : bCode.localeCompare(aCode)
            }
            if (a[key] < b[key]) {
                return direction === "asc" ? -1 : 1
            }
            if (a[key] > b[key]) {
                return direction === "asc" ? 1 : -1
            }
            return 0
        })

    const handleDelete = async () => {
        if (deleteId === null) return
        setIsLoading(true)
        setIsOverlayVisible(true)
        try {
            await axios.delete(`/bin-range/${deleteId}`)
            setSavedBinRanges((prev) => prev.filter((range) => range._id !== deleteId))
            setDeleteId(null)
            await fetchBinRanges()
            setAlert({ message: "BIN Range deleted successfully!", type: "success" })
        } catch (error) {
            console.error("Error deleting BIN Range", error)
            setAlert({ message: "Error deleting BIN Range. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
            setIsOverlayVisible(false)
            setDeleteModalOpen(false)
        }
    }

    const handleDeleteClick = (id: string) => {
        setDeleteId(id)
        setDeleteModalOpen(true)
    }

    const renderValidationMessage = (fieldName: string) => {
        const error = getFieldError(fieldName)
        if (!error) return null

        const Icon = error.type === "error" ? XCircle : error.type === "warning" ? AlertTriangle : CheckCircle
        const colorClass =
            error.type === "error" ? "text-red-500" : error.type === "warning" ? "text-yellow-500" : "text-green-500"

        return (
            <div className={`flex items-center gap-1 text-sm mt-1 ${colorClass}`}>
        <Icon className="h-4 w-4" />
        <span>{error.message}</span>
      </div>
        )
    }

    const renderTable = (status: string) => {
        const data = savedBinRanges
            .filter((row) => row.status.toLowerCase() === status.toLowerCase())
            .map((row, index) => ({
                id: index + 1,
                bin_type: getBinTypeName(row.binType),
                binCode: row.binCode,
                bin_start: row.bin_start,
                bin_end: row.bin_end,
                currency: getCurrencyCode(row.currency),
                created_by: row.created_by?.name || "N/A",
                created_at: formatDate(row.created_at),
                status: row.status.toUpperCase(),
            }))

        return (
            <>
        <DataExporter data={data} filename="bin_range" title="BIN Range Report" />
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">ID</TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort("binType")}>
                BIN Type <ArrowUpDown className="ml-2 h-4 w-4 inline" />
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort("currency")}>
                Currency <ArrowUpDown className="ml-2 h-4 w-4 inline" />
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort("binCode")}>
                BIN Code <ArrowUpDown className="ml-2 h-4 w-4 inline" />
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort("bin_start")}>
                Range Start <ArrowUpDown className="ml-2 h-4 w-4 inline" />
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort("bin_end")}>
                Range End <ArrowUpDown className="ml-2 h-4 w-4 inline" />
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort("created_by")}>
                Created By <ArrowUpDown className="ml-2 h-4 w-4 inline" />
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort("created_at")}>
                Created At <ArrowUpDown className="ml-2 h-4 w-4 inline" />
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAndSortedRanges
                .filter((range) => range.status.toLowerCase() === status)
                .map((range, index) => (
                    <TableRow key={range._id}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>{range.binType.type}</TableCell>
                  <TableCell>{range.currency.currency_code}</TableCell>
                  <TableCell>{range.binCode}</TableCell>
                  <TableCell>{range.bin_start}</TableCell>
                  <TableCell>{range.bin_end}</TableCell>
                  <TableCell>{range.created_by?.name || "N/A"}</TableCell>
                  <TableCell>{formatDate(range.created_at)}</TableCell>
                  <TableCell>{range.status.toUpperCase()}</TableCell>
                  <TableCell>
                    {userHasDeletePermission && status === "active" && (
                        <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="destructive" size="sm" onClick={() => setDeleteId(range._id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Action: Delete</AlertDialogTitle>
                            <AlertDialogDescription>
                              Deletion of this record will be sent to administrator for approval. Are you sure to
                              proceed?
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel onClick={() => setDeleteId(null)}>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}
                      {status !== "active" && (
                          <ActionsButtonGroup
                              entity={range}
                              entityType="bin-range"
                              entityName={range.binCode}
                              userHasApprovePermission={userHasApprovePermission}
                              userHasDeletePermission={userHasDeletePermission}
                              handleDeleteClick={handleDeleteClick}
                              fetchEntities={fetchBinRanges}
                          />
                      )}
                  </TableCell>
                </TableRow>
                ))}
          </TableBody>
        </Table>
      </>
        )
    }

    return (
        <div className="w-full px-4 md:px-6 lg:px-8">
      {isLoading && <LoadingOverlay />}
            {isOverlayVisible && <div className="fixed inset-0 bg-black bg-opacity-50 z-50" />}
            <Card className="w-full">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>BIN Range</CardTitle>
            <CardDescription>View and Request BIN Ranges with Advanced Validation</CardDescription>
          </div>
            {userHasRequestPermission && (
                <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
              <SheetTrigger asChild>
                <Button>Add New BIN Range</Button>
              </SheetTrigger>
              <SheetContent className="w-[600px] sm:w-[540px] overflow-y-auto">
                <SheetHeader>
                  <SheetTitle>Add BIN Range</SheetTitle>
                </SheetHeader>
                <form onSubmit={handleSubmit} className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="binType">Choose BIN Type</Label>
                    <Select onValueChange={(value) => handleInputChange(value, "binType")}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select BIN Type" />
                      </SelectTrigger>
                      <SelectContent>
                        {savedbinTypes
                            .filter((ccy) => ccy.status === "active")
                            .map((ccy) => (
                                <SelectItem key={ccy._id} value={ccy._id}>
                              {ccy.type}
                            </SelectItem>
                            ))}
                      </SelectContent>
                    </Select>
                      {renderValidationMessage("binType")}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="currency">Product CCY</Label>
                    <Select
                        disabled={!formData.binType}
                        value={formData.currency}
                        onValueChange={(value) => handleInputChange(value, "currency")}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select Product CCY" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableCurrencies.map((ccy) => (
                            <SelectItem
                                key={ccy._id}
                                value={ccy._id}
                                className={isCurrencyUsed(ccy._id) ? "bg-yellow-50 text-yellow-800" : ""}
                            >
                            <div className="flex items-center justify-between w-full">
                              <span>{ccy.currency_code}</span>
                                {isCurrencyUsed(ccy._id) && (
                                    <span className="text-xs text-yellow-600 ml-2">(In Use)</span>
                                )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                      {renderValidationMessage("currency")}
                  </div>

                    {/* Range Availability Info */}
                    {formData.binType && formData.currency && (
                        <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                      <div className="flex items-start gap-2">
                        <AlertTriangle className="h-4 w-4 text-blue-500 mt-0.5" />
                        <div className="text-sm text-blue-700">
                          <p className="font-medium">Range Availability:</p>
                          <p className="mt-1">{getAvailableRangesSuggestion()}</p>
                        </div>
                      </div>
                    </div>
                    )}

                    <div className="space-y-2">
                    <Label htmlFor="binCodePrefix">BIN Prefix</Label>
                    <InputMask
                        mask="9999 99"
                        readOnly
                        maskChar={null}
                        value={formData.binCodePrefix}
                        onChange={handleInputChange}
                    >
                      {(inputProps: any) => (
                          <Input
                              {...inputProps}
                              id="binCodePrefix"
                              name="binCodePrefix"
                              placeholder="Enter BIN Prefix"
                              required
                          />
                      )}
                    </InputMask>
                        {renderValidationMessage("binCodePrefix")}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="binCodeSuffix">BIN Suffix</Label>
                    <InputMask
                        readOnly
                        mask="99"
                        maskChar={null}
                        value={formData.binCodeSuffix}
                        onChange={handleInputChange}
                    >
                      {(inputProps: any) => (
                          <Input
                              {...inputProps}
                              id="binCodeSuffix"
                              name="binCodeSuffix"
                              placeholder="Enter BIN Suffix"
                              required
                          />
                      )}
                    </InputMask>
                      {renderValidationMessage("binCodeSuffix")}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="binCode">BIN Code</Label>
                    <InputMask
                        readOnly
                        mask="9999 9999"
                        maskChar={null}
                        value={formData.binCode}
                        onChange={handleInputChange}
                    >
                      {(inputProps: any) => (
                          <Input {...inputProps} id="binCode" name="binCode" placeholder="BIN Code" required />
                      )}
                    </InputMask>
                      {renderValidationMessage("binCode")}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bin_start">BIN Range Start</Label>
                    <InputMask mask="9999 9999" maskChar={null} value={formData.bin_start} onChange={handleInputChange}>
                      {(inputProps: any) => (
                          <Input {...inputProps} id="bin_start" name="bin_start" placeholder="BIN Range Start" required />
                      )}
                    </InputMask>
                      {renderValidationMessage("bin_start")}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bin_end">BIN Range End</Label>
                    <InputMask mask="9999 9999" maskChar={null} value={formData.bin_end} onChange={handleInputChange}>
                      {(inputProps: any) => (
                          <Input {...inputProps} id="bin_end" name="bin_end" placeholder="BIN Range End" required />
                      )}
                    </InputMask>
                      {renderValidationMessage("bin_end")}
                  </div>

                    {/* Range Conflicts Warning */}
                    {rangeConflicts.length > 0 && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                      <div className="flex items-start gap-2">
                        <XCircle className="h-4 w-4 text-red-500 mt-0.5" />
                        <div className="text-sm text-red-700">
                          <p className="font-medium">Range Conflicts Detected:</p>
                            {rangeConflicts.map((conflict, index) => (
                                <p key={index} className="mt-1">
                              • {conflict.message}
                            </p>
                            ))}
                        </div>
                      </div>
                    </div>
                    )}

                    {/* Form Status Indicator */}
                    <div
                        className={`p-3 border rounded-md ${isFormValid ? "bg-green-50 border-green-200" : "bg-gray-50 border-gray-200"}`}
                    >
                    <div className="flex items-center gap-2">
                      {isFormValid ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                          <XCircle className="h-4 w-4 text-gray-400" />
                      )}
                        <span className={`text-sm font-medium ${isFormValid ? "text-green-700" : "text-gray-600"}`}>
                        {isFormValid ? "Form is valid and ready to submit" : "Please fix validation errors"}
                      </span>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2 pt-4">
                    <SheetClose asChild>
                      <Button type="button" variant="outline">
                        Cancel
                      </Button>
                    </SheetClose>
                    <Button type="submit" disabled={isLoading || !isFormValid}>
                      {isLoading ? "Saving..." : "Save"}
                    </Button>
                  </div>
                </form>
              </SheetContent>
            </Sheet>
            )}
        </CardHeader>
        <CardContent>
          {alert && <Alert message={alert.message} type={alert.type} onClose={() => setAlert(null)} />}
            {userHasViewPermission && (
                <>
              <div className="mb-4">
                <Input
                    placeholder="Search BIN Range..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Tabs aria-label="Tabs with underline" variant="underline">
                <Tabs.Item active title="Approved">
                  {renderTable("active")}
                </Tabs.Item>
                <Tabs.Item title="Pending">{renderTable("pending")}</Tabs.Item>
                <Tabs.Item title="Modify">{renderTable("modify")}</Tabs.Item>
                <Tabs.Item title="Declined">{renderTable("declined")}</Tabs.Item>
              </Tabs>
            </>
            )}
        </CardContent>
      </Card>
      <AlertDialog open={deleteModalOpen} onOpenChange={setDeleteModalOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this BIN Range? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
    )
}
