//@ts-nocheck
"use client"

import type React from "react"
import { useEffect, useState, useMemo, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Tabs } from "flowbite-react"
import { Sheet, SheetClose, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import axios from "@/utils/axiosInstance"
import { ArrowUpDown, Trash2, Plus, Search } from "lucide-react"
import { formatDate } from "@/utils/helpers"
import { useAppSelector } from "@/store/hooks"
import { ActionsButtonGroup } from "@/components/ActionButtons"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import { Alert } from "@/components/alert"
import DataExporter from "@/components/DataExporter"
import {alertHelper} from "@/utils/alertHelper";
import InputMask from "react-input-mask";

// Interfaces
interface BinCategory {
    _id: number
    category: string
    bin_prefix: string
    currency: string
    status: string
    created_at: string
    created_by: {
        name: string
    }
}

interface Currency {
    _id: string
    currency_code: string
    is_active: boolean
    status: string
}

interface Role {
    permissions: string[]
}

interface FormData {
    category: string
    bin_prefix: string
    currency: string
    created_by: string | null
}

interface SortConfig {
    key: keyof BinCategory
    direction: "asc" | "desc"
}

interface AlertState {
    message: string
    type: "success" | "error"
}




const STATUS_TABS = [
    { key: "active", label: "Approved" },
    { key: "pending", label: "Pending" },
    { key: "modify", label: "Modify" },
    { key: "declined", label: "Declined" },
] as const

export default function BinCategory() {
    // State
    const [savedBinCategories, setSavedBinCategories] = useState<BinCategory[]>([])
    const [currencies, setCurrencies] = useState<Currency[]>([])
    const [searchTerm, setSearchTerm] = useState("")
    const [sortConfig, setSortConfig] = useState<SortConfig | null>(null)
    const [error, setError] = useState("")
    const [deleteId, setDeleteId] = useState<number | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [isInitialLoading, setIsInitialLoading] = useState(true)
    const [alert, setAlert] = useState<AlertState | null>(null)
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [isSheetOpen, setIsSheetOpen] = useState(false)

    // Redux state
    const user = useAppSelector((state) => state.user.user)
    const roles = useMemo(() => user?.roles || [], [user?.roles])

    const INITIAL_FORM_DATA: FormData = {
        category: "",
        bin_prefix: "",
        currency: "",
        created_by: user._id
    }
        const [formData, setFormData] = useState<FormData>(INITIAL_FORM_DATA)

    // Permission helpers
    const getAllPermissions = useCallback((roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions))
    }, [])

    const hasPermission = useCallback(
        (roles: Role[], permission: string): boolean => {
            if (!roles || roles.length === 0) return true
            const allPermissions = getAllPermissions(roles)
            return allPermissions.includes(permission)
        },
        [getAllPermissions],
    )

    // Permissions
    const permissions = useMemo(
        () => ({
            request: hasPermission(roles, "BIN Category_Request"),
            delete: hasPermission(roles, "BIN Category_Delete"),
            approve: hasPermission(roles, "BIN Category_Approve"),
            view: hasPermission(roles, "BIN Category_View"),
        }),
        [roles, hasPermission],
    )

    // Data fetching
    const fetchBinCategories = useCallback(async () => {
        try {
            const response = await axios.get("/bin-category")
            setSavedBinCategories(response.data)
        } catch (error) {
            console.error("Error fetching BIN Categories", error)
            setAlert({ message: "Error fetching BIN categories. Please try again.", type: "error" })
        }
    }, [])

    const fetchCurrencies = useCallback(async () => {
        try {
            const response = await axios.get("/product-currencies")
            setCurrencies(response.data)
        } catch (error) {
            console.error("Error fetching currencies", error)
            setAlert({ message: "Error fetching currencies. Please try again.", type: "error" })
        }
    }, [])

    // Initial data loading
    useEffect(() => {
        const fetchInitialData = async () => {
            setIsInitialLoading(true)
            try {
                await Promise.all([fetchBinCategories(), fetchCurrencies()])
            } finally {
                setIsInitialLoading(false)
            }
        }

        fetchInitialData()
    }, [fetchBinCategories, fetchCurrencies])

    // Form handlers
    const handleInputChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const { name, value } = e.target
            setFormData((prev) => ({ ...prev, [name]: value }))

            if (name === "category") {
                const isDuplicate = savedBinCategories.some(
                    (category) => category.category.toLowerCase() === value.toLowerCase(),
                )
                setError(isDuplicate ? "This BIN Category already exists. Please enter a unique name." : "")
            }
        },
        [savedBinCategories],
    )

    const handleSelectChange = useCallback((value: string, field: keyof FormData) => {
        setFormData((prev) => ({ ...prev, [field]: value }))
    }, [])

    const handleSubmit = useCallback(
        async (e: React.FormEvent) => {
            e.preventDefault()
            if (error) return

            setIsLoading(true)
            try {
                await axios.post("/bin-category", formData)
                await fetchBinCategories()
                setFormData(INITIAL_FORM_DATA)
                setAlert({ message: "BIN Category created successfully!", type: "success" })
                setIsSheetOpen(false)
            } catch (error) {
                console.error("Error creating BIN Category", error)
                setAlert({ message: "Error creating BIN Category. Please try again.", type: "error" })
            } finally {
                setIsLoading(false)
            }
        },
        [formData, error, fetchBinCategories],
    )

    // Sorting and filtering
    const handleSort = useCallback((key: keyof BinCategory) => {
        setSortConfig((prevConfig) => {
            const direction = prevConfig?.key === key && prevConfig.direction === "asc" ? "desc" : "asc"
            return { key, direction }
        })
    }, [])

    const filteredAndSortedCategories = useMemo(() => {
        const filtered = savedBinCategories.filter(
            (category) =>
                category.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                category.bin_prefix.toLowerCase().includes(searchTerm.toLowerCase())  ,
                // category.currency.toLowerCase().includes(searchTerm.toLowerCase()),
        )

        if (sortConfig) {
            filtered.sort((a, b) => {
                const { key, direction } = sortConfig
                const aValue = a[key]
                const bValue = b[key]

                if (aValue < bValue) return direction === "asc" ? -1 : 1
                if (aValue > bValue) return direction === "asc" ? 1 : -1
                return 0
            })
        }

        return filtered
    }, [savedBinCategories, searchTerm, sortConfig])

    // Delete handlers
    const handleDelete = useCallback(async () => {
        if (!deleteId) return

        setIsLoading(true)
        try {
            await axios.delete(`/bin-category/${deleteId}`)
            await fetchBinCategories()
            setAlert({ message: "BIN Category deleted successfully!", type: "success" })
        } catch (error) {
            console.error("Error deleting BIN Category", error)
            setAlert({ message: "Error deleting BIN Category. Please try again.", type: "error" })
        } finally {
            setIsLoading(false)
            setDeleteId(null)
            setDeleteModalOpen(false)
        }
    }, [deleteId, fetchBinCategories])

    const handleDeleteClick = useCallback((id: number) => {
        setDeleteId(id)

        setDeleteModalOpen(true)
    }, [])

    // Active currencies for dropdown
    const activeCurrencies = useMemo(
        () => currencies.filter((currency) => currency.is_active && currency.status === "active"),
        [currencies],
    )

    // Table renderer
    const renderCategoryTable = useCallback(
        (status: string) => {
            const statusCategories = filteredAndSortedCategories.filter(
                (category) => category.status.toLowerCase() === status.toLowerCase(),
            )

            const exportData = statusCategories.map((row) => ({
                id: row._id,
                category: row.category,
                bin_prefix: row.bin_prefix,
                currency: row.currency?.currency_code,
                created_by: row.created_by?.name || "N/A",
                created_at: formatDate(row.created_at),
                status: row.status.toUpperCase(),
            }))

            return (
                <div className="space-y-4">
                    <DataExporter data={exportData} filename={`bin_categories_${status}`} title="Bin Categories" />
                    <div className="rounded-md border">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-[80px]">ID</TableHead>
                                    <TableHead className="cursor-pointer hover:bg-muted/50" onClick={() => handleSort("category")}>
                                        <div className="flex items-center">
                                            Category <ArrowUpDown className="ml-2 h-4 w-4" />
                                        </div>
                                    </TableHead>
                                    <TableHead className="cursor-pointer hover:bg-muted/50" onClick={() => handleSort("bin_prefix")}>
                                        <div className="flex items-center">
                                            BIN Prefix <ArrowUpDown className="ml-2 h-4 w-4" />
                                        </div>
                                    </TableHead>
                                    <TableHead className="cursor-pointer hover:bg-muted/50" onClick={() => handleSort("currency")}>
                                        <div className="flex items-center">
                                            Currency <ArrowUpDown className="ml-2 h-4 w-4" />
                                        </div>
                                    </TableHead>
                                    <TableHead className="cursor-pointer hover:bg-muted/50" onClick={() => handleSort("created_by")}>
                                        <div className="flex items-center">
                                            Created By <ArrowUpDown className="ml-2 h-4 w-4" />
                                        </div>
                                    </TableHead>
                                    <TableHead className="cursor-pointer hover:bg-muted/50" onClick={() => handleSort("created_at")}>
                                        <div className="flex items-center">
                                            Created At <ArrowUpDown className="ml-2 h-4 w-4" />
                                        </div>
                                    </TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead className="w-[100px]">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {statusCategories.length === 0 ? (
                                    <TableRow>
                                        <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                                            No {status} categories found
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    statusCategories.map((category, index) => (
                                        <TableRow key={category._id} className="hover:bg-muted/50">
                                            <TableCell className="font-medium">{index + 1}</TableCell>
                                            <TableCell>{category.category}</TableCell>
                                            <TableCell>{category.bin_prefix || "N/A"}</TableCell>
                                            <TableCell>{category.currency?.currency_code || "N/A"}</TableCell>
                                            <TableCell>{category.created_by?.name || "N/A"}</TableCell>
                                            <TableCell>{formatDate(category.created_at)}</TableCell>
                                            <TableCell>
                        <span
                            className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                category.status === "active"
                                    ? "bg-green-100 text-green-800"
                                    : category.status === "pending"
                                        ? "bg-yellow-100 text-yellow-800"
                                        : category.status === "declined"
                                            ? "bg-red-100 text-red-800"
                                            : "bg-blue-100 text-blue-800"
                            }`}
                        >
                          {category.status.toUpperCase()}
                        </span>
                                            </TableCell>
                                            <TableCell>
                                                {permissions.delete && status === "active" && (
                                                    <AlertDialog>
                                                        <AlertDialogTrigger asChild>
                                                            <Button
                                                                variant="destructive"
                                                                size="sm"
                                                                onClick={() => setDeleteId(category._id)}
                                                                aria-label={`Delete ${category.category}`}
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </AlertDialogTrigger>
                                                        <AlertDialogContent>
                                                            <AlertDialogHeader>
                                                                <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
                                                                <AlertDialogDescription>
                                                                    Deletion of "{category.category}" will be sent to administrator for approval. Are you
                                                                    sure you want to proceed?
                                                                </AlertDialogDescription>
                                                            </AlertDialogHeader>
                                                            <AlertDialogFooter>
                                                                <AlertDialogCancel onClick={() => setDeleteId(null)}>Cancel</AlertDialogCancel>
                                                                <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                                                            </AlertDialogFooter>
                                                        </AlertDialogContent>
                                                    </AlertDialog>
                                                )}
                                                {status !== "active" && (
                                                    <ActionsButtonGroup
                                                        entity={category}
                                                        entityType="bin-category"
                                                        entityName={category.category}
                                                        userHasApprovePermission={permissions.approve}
                                                        userHasDeletePermission={permissions.delete}
                                                        handleDeleteClick={() => handleDeleteClick(category._id)}
                                                        fetchEntities={fetchBinCategories}
                                                        editFields={[
                                                            { key: 'category', label: 'Category', type: 'text', required: true },
                                                            { key: 'bin_prefix', label: 'Prefix', type: 'text', required: true }
                                                        ]}
                                                    />
                                                )}
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )}
                            </TableBody>
                        </Table>
                    </div>
                </div>
            )
        },
        [filteredAndSortedCategories, handleSort, permissions, handleDelete, handleDeleteClick, fetchBinCategories],
    )

    // Loading state
    if (isInitialLoading) {
        return (
            <div className="w-full px-4 md:px-6 lg:px-8">
                <LoadingOverlay />
                <Card className="w-full">
                    <CardHeader>
                        <div className="animate-pulse">
                            <div className="h-6 bg-muted rounded w-1/4 mb-2"></div>
                            <div className="h-4 bg-muted rounded w-1/2"></div>
                        </div>
                    </CardHeader>
                </Card>
            </div>
        )
    }

    return (
        <div className="w-full px-4 md:px-6 lg:px-8 space-y-6">
            {isLoading && <LoadingOverlay />}

            <Card className="w-full">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
                    <div className="space-y-1">
                        <CardTitle className="text-2xl font-bold">BIN Categories</CardTitle>
                        <CardDescription>View and manage your BIN Categories with currency assignments</CardDescription>
                    </div>
                    {permissions.request && (
                        <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
                            <SheetTrigger asChild>
                                <Button className="gap-2">
                                    <Plus className="h-4 w-4" />
                                    Add New BIN Category
                                </Button>
                            </SheetTrigger>
                            <SheetContent className="sm:max-w-md">
                                <SheetHeader>
                                    <SheetTitle>Add BIN Category</SheetTitle>
                                </SheetHeader>
                                <form onSubmit={handleSubmit} className="space-y-6 mt-6">
                                    <div className="space-y-2">
                                        <Label htmlFor="category">BIN Category *</Label>
                                        <Input
                                            id="category"
                                            name="category"
                                            value={formData.category}
                                            onChange={handleInputChange}
                                            placeholder="Enter BIN Category"
                                            required
                                            aria-describedby={error ? "category-error" : undefined}
                                        />
                                        {error && (
                                            <p id="category-error" className="text-sm text-destructive">
                                                {error}
                                            </p>
                                        )}
                                    </div>



                                    <div className="space-y-2">
                                        <Label htmlFor="bin_prefix">BIN Prefix</Label>
                                        <InputMask
                                            mask="9999 99"
                                            maskChar={null}
                                            value={formData.binCodePrefix}
                                            onChange={handleInputChange}
                                        >
                                            {(inputProps: any) => (
                                                <Input
                                                    {...inputProps}
                                                    id="bin_prefix"
                                                    name="bin_prefix"
                                                    placeholder="Enter BIN Prefix"
                                                    required
                                                />
                                            )}
                                        </InputMask>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="currency">Product Currency *</Label>
                                        <Select
                                            value={formData.currency}
                                            onValueChange={(value) => handleSelectChange(value, "currency")}
                                            required
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select Product Currency" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {activeCurrencies.map((currency) => (
                                                    <SelectItem key={currency._id} value={currency._id}>
                                                        {currency.currency_code}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="flex justify-end space-x-3 pt-4">
                                        <SheetClose asChild>
                                            <Button type="button" variant="outline">
                                                Cancel
                                            </Button>
                                        </SheetClose>
                                        <Button type="submit" disabled={!!error || isLoading || !formData.currency}>
                                            {isLoading ? "Saving..." : "Save"}
                                        </Button>
                                    </div>
                                </form>
                            </SheetContent>
                        </Sheet>
                    )}
                </CardHeader>

                <CardContent className="space-y-6">
                    {alert && <Alert message={alert.message} type={alert.type} onClose={() => setAlert(null)} />}

                    {permissions.view && (
                        <>
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                                <Input
                                    placeholder="Search BIN Categories, BIN Prefix, or Currency..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>

                            <Tabs aria-label="Category status tabs" variant="underline">
                                {STATUS_TABS.map((tab, index) => (
                                    <Tabs.Item key={tab.key} active={index === 0} title={tab.label}>
                                        {renderCategoryTable(tab.key)}
                                    </Tabs.Item>
                                ))}
                            </Tabs>
                        </>
                    )}
                </CardContent>
            </Card>

            <AlertDialog open={deleteModalOpen} onOpenChange={setDeleteModalOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete this BIN Category? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    )
}
