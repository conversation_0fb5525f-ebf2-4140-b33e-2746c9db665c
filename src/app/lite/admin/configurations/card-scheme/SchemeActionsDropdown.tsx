import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, Too<PERSON><PERSON>Content, TooltipTrigger } from "@/components/ui/tooltip";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { CheckCircleIcon, XCircleIcon } from "@heroicons/react/24/outline";
import { Edit2Icon, TrashIcon, PencilIcon } from 'lucide-react';
import { Button } from "@/components/ui/button";
import axiosInstance from "@/utils/axiosInstance";
import Swal from "sweetalert2";

export const SchemeActionsButtonGroup = ({
                                             scheme,
                                             userHasApprovePermission,
                                             userHasDeletePermission,
                                             userHasEditPermission, // Add this new permission prop
                                             handleDeleteClick,
                                             fetchCardScheme
                                         }) => {
    const [isDialogOpen, setIsDialogOpen] = useState({
        approve: false,
        decline: false,
        modify: false,
        delete: false,
        edit: false, // Add edit dialog state
    });

    const [declineReason, setDeclineReason] = useState("");
    const [modifyInstructions, setModifyInstructions] = useState("");

    // Add edit form state
    const [editFormData, setEditFormData] = useState({
        scheme_name: "",
        description: "",
        // Add other fields as needed
    });

    const handleDialogToggle = (type, state) => {
        setIsDialogOpen((prev) => ({ ...prev, [type]: state }));

        // Initialize edit form when opening edit dialog
        if (type === "edit" && state) {
            setEditFormData({
                scheme_name: scheme?.scheme_name || "",
                description: scheme?.description || "",
                // Initialize other fields as needed
            });
        }
    };

    const handleApprove = async () => {
        try {
            const response = await axiosInstance.post("cardScheme/approve-scheme", {
                schemeId: scheme?._id,
            });
            if (response.status === 200) {
                Swal.fire({
                    title: "Success",
                    text: `Scheme approved successfully!`,
                    icon: "success"
                });
                fetchCardScheme();
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: `Failed to approve`
                });
            }
        } catch (error) {
            console.error("Error approving scheme:", error);
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: `An error occurred while approving the scheme. Please try again.`
            });
        } finally {
            handleDialogToggle("approve", false);
        }
    };

    const handleDecline = async () => {
        try {
            if (!declineReason.trim()) {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: `Please provide a reason for declining.`
                });
                return;
            }
            const response = await axiosInstance.post("cardScheme/decline-scheme", {
                schemeId: scheme?._id,
                reason: declineReason,
            });
            if (response.status === 200) {
                Swal.fire({
                    title: "Success",
                    text: `Scheme declined successfully!`,
                    icon: "success"
                });
                fetchCardScheme();
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: `Decline failed with status: ${response.status}`
                });
            }
        } catch (error) {
            console.error("Error declining scheme:", error);
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "An error occurred while declining the scheme. Please try again."
            });
        } finally {
            setDeclineReason("");
            handleDialogToggle("decline", false);
        }
    };

    const handleModify = async () => {
        try {
            if (!modifyInstructions.trim()) {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: `Please provide modification instructions.`
                });
                return;
            }
            const response = await axiosInstance.post("cardScheme/modify-scheme", {
                schemeId: scheme?._id,
                instructions: modifyInstructions,
            });
            if (response.status === 200) {
                Swal.fire({
                    title: "Success",
                    text: `Scheme modification request submitted successfully!`,
                    icon: "success"
                });
                fetchCardScheme();
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: `Modification failed with status: ${response.status}`
                });
            }
        } catch (error) {
            console.error("Error modifying scheme:", error);
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "An error occurred while modifying the scheme. Please try again."
            });
        } finally {
            setModifyInstructions("");
            handleDialogToggle("modify", false);
        }
    };

    // Add edit handler
    const handleEdit = async () => {
        try {
            if (!editFormData.scheme_name.trim()) {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: `Please provide a scheme name.`
                });
                return;
            }

            const response = await axiosInstance.put(`cardScheme/${scheme?._id}`, {
                scheme_name: editFormData.scheme_name,
                // Add other fields as needed
            });

            if (response.status === 200) {
                Swal.fire({
                    title: "Success",
                    text: `Scheme updated successfully!`,
                    icon: "success"
                });
                fetchCardScheme();
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: `Edit failed with status: ${response.status}`
                });
            }
        } catch (error) {
            console.error("Error editing scheme:", error);
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "An error occurred while editing the scheme. Please try again."
            });
        } finally {
            handleDialogToggle("edit", false);
        }
    };

    const handleEditInputChange = (field, value) => {
        setEditFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    return (
        <div className="flex space-x-4">
            {/*{userHasApprovePermission && (*/}
            {/*    <>*/}
            {/*        /!* Approve Button *!/*/}
            {/*        <Tooltip>*/}
            {/*            <TooltipTrigger>*/}
            {/*                <Button*/}
            {/*                    className="p-2 rounded-md bg-green-500 text-white hover:bg-green-600"*/}
            {/*                    onClick={() => handleDialogToggle("approve", true)}*/}
            {/*                >*/}
            {/*                    <CheckCircleIcon className="h-6 w-6" />*/}
            {/*                </Button>*/}
            {/*            </TooltipTrigger>*/}
            {/*            <TooltipContent>Approve</TooltipContent>*/}
            {/*        </Tooltip>*/}
            {/*        <Dialog*/}
            {/*            open={isDialogOpen.approve}*/}
            {/*            onOpenChange={(state) => handleDialogToggle("approve", state)}*/}
            {/*        >*/}
            {/*            <DialogContent>*/}
            {/*                <DialogHeader>*/}
            {/*                    <DialogTitle>Confirm Approval</DialogTitle>*/}
            {/*                    <DialogDescription>*/}
            {/*                        Are you sure you want to approve the card scheme "{scheme?.scheme_name}"?*/}
            {/*                    </DialogDescription>*/}
            {/*                </DialogHeader>*/}
            {/*                <DialogFooter>*/}
            {/*                    <Button className="btn btn-outline"*/}
            {/*                            onClick={() => handleDialogToggle("approve", false)}>*/}
            {/*                        Cancel*/}
            {/*                    </Button>*/}
            {/*                    <Button className="btn btn-primary" onClick={handleApprove}>*/}
            {/*                        Confirm Approval*/}
            {/*                    </Button>*/}
            {/*                </DialogFooter>*/}
            {/*            </DialogContent>*/}
            {/*        </Dialog>*/}

            {/*        /!* Decline Button *!/*/}
            {/*        <Tooltip>*/}
            {/*            <TooltipTrigger>*/}
            {/*                <Button*/}
            {/*                    className="p-2 rounded-md bg-yellow-500 text-white hover:bg-yellow-600"*/}
            {/*                    onClick={() => handleDialogToggle("decline", true)}*/}
            {/*                >*/}
            {/*                    <XCircleIcon className="h-6 w-6" />*/}
            {/*                </Button>*/}
            {/*            </TooltipTrigger>*/}
            {/*            <TooltipContent>Decline</TooltipContent>*/}
            {/*        </Tooltip>*/}
            {/*        <Dialog*/}
            {/*            open={isDialogOpen.decline}*/}
            {/*            onOpenChange={(state) => handleDialogToggle("decline", state)}*/}
            {/*        >*/}
            {/*            <DialogContent>*/}
            {/*                <DialogHeader>*/}
            {/*                    <DialogTitle>Decline Card Scheme</DialogTitle>*/}
            {/*                    <DialogDescription>*/}
            {/*                        Please provide a reason for declining "{scheme?.scheme_name}".*/}
            {/*                    </DialogDescription>*/}
            {/*                </DialogHeader>*/}
            {/*                <div className="py-4">*/}
            {/*                    <Label htmlFor="decline-reason">Reason for Decline</Label>*/}
            {/*                    <Textarea*/}
            {/*                        id="decline-reason"*/}
            {/*                        value={declineReason}*/}
            {/*                        onChange={(e) => setDeclineReason(e.target.value)}*/}
            {/*                        placeholder="Enter the reason for declining this card scheme..."*/}
            {/*                        className="mt-2"*/}
            {/*                        required*/}
            {/*                    />*/}
            {/*                </div>*/}
            {/*                <DialogFooter>*/}
            {/*                    <Button*/}
            {/*                        className="btn btn-outline"*/}
            {/*                        onClick={() => {*/}
            {/*                            handleDialogToggle("decline", false);*/}
            {/*                            setDeclineReason("");*/}
            {/*                        }}*/}
            {/*                    >*/}
            {/*                        Cancel*/}
            {/*                    </Button>*/}
            {/*                    <Button*/}
            {/*                        className="btn btn-danger"*/}
            {/*                        onClick={handleDecline}*/}
            {/*                        disabled={!declineReason.trim()}*/}
            {/*                    >*/}
            {/*                        Confirm Decline*/}
            {/*                    </Button>*/}
            {/*                </DialogFooter>*/}
            {/*            </DialogContent>*/}
            {/*        </Dialog>*/}

            {/*        /!* Modify Button *!/*/}
            {/*        <Tooltip>*/}
            {/*            <TooltipTrigger>*/}
            {/*                <Button*/}
            {/*                    className="p-2 rounded-md bg-blue-500 text-white hover:bg-blue-600"*/}
            {/*                    onClick={() => handleDialogToggle("modify", true)}*/}
            {/*                >*/}
            {/*                    <Edit2Icon className="h-6 w-6" />*/}
            {/*                </Button>*/}
            {/*            </TooltipTrigger>*/}
            {/*            <TooltipContent>Modify</TooltipContent>*/}
            {/*        </Tooltip>*/}
            {/*        <Dialog*/}
            {/*            open={isDialogOpen.modify}*/}
            {/*            onOpenChange={(state) => handleDialogToggle("modify", state)}*/}
            {/*        >*/}
            {/*            <DialogContent>*/}
            {/*                <DialogHeader>*/}
            {/*                    <DialogTitle>Modify Card Scheme</DialogTitle>*/}
            {/*                    <DialogDescription>*/}
            {/*                        Please provide modification instructions for "{scheme?.scheme_name}".*/}
            {/*                    </DialogDescription>*/}
            {/*                </DialogHeader>*/}
            {/*                <div className="py-4">*/}
            {/*                    <Label htmlFor="modify-instructions">*/}
            {/*                        Modification Instructions*/}
            {/*                    </Label>*/}
            {/*                    <Textarea*/}
            {/*                        id="modify-instructions"*/}
            {/*                        value={modifyInstructions}*/}
            {/*                        onChange={(e) => setModifyInstructions(e.target.value)}*/}
            {/*                        placeholder="Enter detailed instructions for modifications needed..."*/}
            {/*                        className="mt-2"*/}
            {/*                        required*/}
            {/*                    />*/}
            {/*                </div>*/}
            {/*                <DialogFooter>*/}
            {/*                    <Button*/}
            {/*                        className="btn btn-outline"*/}
            {/*                        onClick={() => {*/}
            {/*                            handleDialogToggle("modify", false);*/}
            {/*                            setModifyInstructions("");*/}
            {/*                        }}*/}
            {/*                    >*/}
            {/*                        Cancel*/}
            {/*                    </Button>*/}
            {/*                    <Button*/}
            {/*                        className="btn btn-primary"*/}
            {/*                        onClick={handleModify}*/}
            {/*                        disabled={!modifyInstructions.trim()}*/}
            {/*                    >*/}
            {/*                        Confirm Modification*/}
            {/*                    </Button>*/}
            {/*                </DialogFooter>*/}
            {/*            </DialogContent>*/}
            {/*        </Dialog>*/}
            {/*    </>*/}
            {/*)}*/}

            {/* Edit Button - Add this new section */}
            {userHasEditPermission && (
                <>
                    <Tooltip>
                        <TooltipTrigger>
                            <Button
                                className="p-2 rounded-md bg-purple-500 text-white hover:bg-purple-600"
                                onClick={() => handleDialogToggle("edit", true)}
                            >
                                <PencilIcon className="h-6 w-6" />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Edit</TooltipContent>
                    </Tooltip>
                    <Dialog
                        open={isDialogOpen.edit}
                        onOpenChange={(state) => handleDialogToggle("edit", state)}
                    >
                        <DialogContent className="max-w-md">
                            <DialogHeader>
                                <DialogTitle>Edit Card Scheme</DialogTitle>
                                <DialogDescription>
                                    Update the details for "{scheme?.scheme_name}".
                                </DialogDescription>
                            </DialogHeader>
                            <div className="py-4 space-y-4">
                                <div>
                                    <Label htmlFor="edit-scheme-name">Scheme Name</Label>
                                    <Input
                                        id="edit-scheme-name"
                                        value={editFormData.scheme_name}
                                        onChange={(e) => handleEditInputChange("scheme_name", e.target.value)}
                                        placeholder="Enter scheme name..."
                                        className="mt-2"
                                        required
                                    />
                                </div>

                                {/* Add more fields as needed */}
                            </div>
                            <DialogFooter>
                                <Button
                                    className="btn btn-outline"
                                    onClick={() => handleDialogToggle("edit", false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    className="btn btn-primary"
                                    onClick={handleEdit}
                                    disabled={!editFormData.scheme_name.trim()}
                                >
                                    Save Changes
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                </>
            )}

            {userHasDeletePermission && (
                <>
                    {/* Delete Button */}
                    <Tooltip>
                        <TooltipTrigger>
                            <Button
                                className="p-2 rounded-md bg-red-500 text-white hover:bg-red-600"
                                onClick={() => handleDialogToggle("delete", true)}
                            >
                                <TrashIcon className="h-6 w-6" />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Delete</TooltipContent>
                    </Tooltip>
                    <Dialog
                        open={isDialogOpen.delete}
                        onOpenChange={(state) => handleDialogToggle("delete", state)}
                    >
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Delete Card Scheme</DialogTitle>
                                <DialogDescription>
                                    Deletion of this record will be sent to the administrator for approval. Are you sure
                                    to proceed?
                                </DialogDescription>
                            </DialogHeader>
                            <DialogFooter>
                                <Button
                                    onClick={() => handleDialogToggle("delete", false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    className="btn btn-danger"
                                    onClick={() => {
                                        handleDeleteClick(scheme._id);
                                        handleDialogToggle("delete", false);
                                    }}
                                >
                                    Delete
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                </>
            )}
        </div>
    );
};
