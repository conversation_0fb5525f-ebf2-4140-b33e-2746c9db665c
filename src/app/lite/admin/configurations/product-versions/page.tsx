//@ts-nocheck
"use client"

import { AlertDialogTrigger } from "@/components/ui/alert-dialog"
import type React from "react"
import { useEffect, useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
    ArrowUpDown,
    ChevronDown,
    ChevronUp,
    Loader2,
    TrashIcon,
    Plus,
    Building2,
    CreditCard,
    FileText,
    Search,
    AlertCircle,
    CheckCircle2,
    Clock,
    XCircle,
    Download,
} from "lucide-react"
import axios from "@/utils/axiosInstance"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useAppSelector } from "@/store/hooks"
import { ActionsButtonGroup } from "@/components/ActionButtons"
import { LoadingOverlay } from "@/components/LoadingOverlay"
import DataExporter from "@/components/DataExporter"
import { alertHelper } from "@/utils/alertHelper"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

interface Country {
    _id: number
    country_name: string
    country_code: string
    currency_code: string
    created_at: string
    is_active: boolean
}

interface ProductVersion {
    _id: number
    version_name: string
    version_number: string
    version_variant: string
    status: string
    created_at: string
    created_by: string
    version_code?: string
    company?: any
}

interface BinRange {
    _id: number
    binType: string
    binCode: string
    binCodePrefix: string
    binCodeSuffix: string
    currency: string
    bin_start: string
    bin_end: string
    created_at: string
    created_by: any
    status: string
    category?: string
}

interface Company {
    _id: string
    company_name: string
    status: string
}

interface Role {
    permissions: string[]
}

type SortKey = keyof ProductVersion
type SortOrder = "asc" | "desc"

const formatDate = (dateString: string): string => {
    const options: Intl.DateTimeFormatOptions = { year: "numeric", month: "short", day: "numeric" }
    return new Date(dateString).toLocaleString("en-GB", options)
}

interface CardProgrammeType {
    _id: number
    programme_type: string
    description: string
    status: string
    created_at: string
    created_by: string
    bin_type: any
}

const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
        case "active":
            return <CheckCircle2 className="h-4 w-4 text-green-500" />
        case "pending":
            return <Clock className="h-4 w-4 text-yellow-500" />
        case "modify":
            return <AlertCircle className="h-4 w-4 text-blue-500" />
        case "decline":
            return <XCircle className="h-4 w-4 text-red-500" />
        default:
            return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
}

const getStatusBadge = (status: string) => {
    const variants = {
        active: "default",
        pending: "secondary",
        modify: "outline",
        decline: "destructive",
    }
    return (
        <Badge variant={variants[status.toLowerCase()] || "secondary"} className="flex items-center gap-1">
      {getStatusIcon(status)}
            {status.toUpperCase()}
    </Badge>
    )
}

export default function ProductVersion() {
    const [savedProductVersions, setSavedProductVersions] = useState<ProductVersion[]>([])
    const [savedProgrammeTypes, setSavedProgrammeTypes] = useState<CardProgrammeType[]>([])
    const [savedCountries, setSavedCountries] = useState<Country[]>([])
    const [companies, setCompanies] = useState<Company[]>([])
    const [sortKey, setSortKey] = useState<SortKey>("id")
    const [sortOrder, setSortOrder] = useState<SortOrder>("asc")
    const [cardSchemes, setCardSchemes] = useState<any[]>([])
    const [deleteId, setDeleteId] = useState<number | null>(null)
    const [cardTypes, setCardTypes] = useState<any[]>([])
    const [searchTerm, setSearchTerm] = useState("")
    const [activeTab, setActiveTab] = useState("approved")
    const [formData, setFormData] = useState({
        bin_range: "",
        bin_type: "",
        bin_currency: "",
        version_name: "",
        card_type: "",
        version_code: "",
        company: "",
        created_by: localStorage.getItem("user"),
    })
    const [savedBinRanges, setSavedBinRanges] = useState<BinRange[]>([])
    const [selectedBin, setSelectedBin] = useState<string>("")
    const [selectedCurrency, setSelectedCurrency] = useState<string>("")
    const [error, setError] = useState("")
    const [isLoading, setIsLoading] = useState(false)
    const [isSubmitting, setIsSubmitting] = useState(false) // Separate state for form submission
    const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false)
    const [loadingStates, setLoadingStates] = useState<{ [key: string]: boolean }>({})
    const [selectedVersions, setSelectedVersions] = useState<number[]>([])
    const [filters, setFilters] = useState({
        company: "",
        cardType: "",
        dateRange: { from: null, to: null },
    })

    const setLoadingState = (key: string, loading: boolean) => {
        setLoadingStates((prev) => ({ ...prev, [key]: loading }))
    }

    // Combine all permissions
    const user = useAppSelector((state) => state.user.user)
    const [roles, setRoles] = useState<Role[]>([])

    useEffect(() => {
        setRoles(user.roles)
    }, [user.roles])

    const getAllPermissions = (roles: Role[]): string[] => {
        const allPermissions = roles.flatMap((role) => role.permissions)
        return Array.from(new Set(allPermissions))
    }

    const hasPermission = (roles: Role[], permission: string): boolean => {
        if (!roles || roles.length === 0) {
            return true
        }
        const allPermissions = getAllPermissions(roles)
        return allPermissions.includes(permission)
    }

    const userHasRequestPermission = hasPermission(roles, "Product Versions_Request")
    const userHasDeletePermission = hasPermission(roles, "Product Versions_Delete")
    const userHasApprovePermission = hasPermission(roles, "Product Versions_Approve")
    const userHasViewPermission = hasPermission(roles, "Product Versions_View")

    // Check if company dropdown should be shown
    const shouldShowCompanyDropdown = () => {
        if (!selectedBin) return false
        const binTypeLower = selectedBin.toLowerCase()
        return true;
    }

    useEffect(() => {
        fetchInitialData()
    }, [])

    const fetchInitialData = async () => {
        setIsLoading(true)
        try {
            const [binRangeResponse, companiesResponse] = await Promise.all([
                axios.get("/bin-range"),
                axios.get("/companies"),
            ])
            setSavedBinRanges(binRangeResponse.data)
            setCompanies(companiesResponse.data.data)
        } catch (error) {
            console.error("Error fetching initial data", error)
        } finally {
            setIsLoading(false)
        }
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        if (error) {
            return
        }
        setIsConfirmDialogOpen(true)
    }

    useEffect(() => {
        fetchData()
    }, [])

    const fetchData = async () => {
        setIsLoading(true)
        try {
            const [versions] = await Promise.all([axios.get("/product-versions")])
            setSavedProductVersions(versions.data)
        } catch (error) {
            console.error("Error fetching data", error)
        } finally {
            setIsLoading(false)
        }
    }

    const generateVersionName = (
        binType: string,
        currency: string,
        cardType: string,
        company?: string
    ) => {
        let versionName = `${binType} ${currency} ${cardType.toLowerCase() === "physical" ? "PHY" : "VTL"}`;

        // Only include company name if it's a cobrand or whitelabel type and company is selected
        if (company && shouldShowCompanyDropdown()) {
            const selectedCompany = companies.find((comp) => comp._id === company);
            if (selectedCompany) {
                versionName = `${selectedCompany.company_name.toUpperCase()} ${binType} ${currency} ${cardType.toLowerCase() === "physical" ? "PHY" : "VTL"}`;
            }
        }

        // Replace multiple spaces with a single space
        return versionName.replace(/\s+/g, ' ').trim();
    };


    // Enhanced duplicate checking function
    const checkForDuplicate = (versionName: string, companyId: string) => {
        if (!versionName) return false
        // For cobrand/whitelabel, company is required for duplicate check
        if (shouldShowCompanyDropdown() && !companyId) return false

        const isDuplicate = savedProductVersions.some((version) => {
            const isSameName = version.version_name.toLowerCase() === versionName.toLowerCase()
            // For cobrand/whitelabel, check company match
            if (shouldShowCompanyDropdown()) {
                const isSameCompany = version.company?._id === companyId || version.company === companyId
                return isSameName && isSameCompany
            }
            // For non-cobrand/whitelabel, just check name
            return isSameName
        })
        return isDuplicate
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement> | string, fieldName?: string) => {
        let name, value
        if (typeof e === "string") {
            name = fieldName
            value = e
        } else {
            name = e.target.name
            value = e.target.value
        }

        if (name === "bin_range") {
            const bin_range = savedBinRanges.find((r) => r._id === value)
            setSelectedBin(bin_range?.binType.type)
            setSelectedCurrency(bin_range?.currency.currency_code)
            if (bin_range) {
                const newFormData = {
                    ...formData,
                    bin_range: value,
                    bin_type: bin_range?.binType._id,
                    bin_currency: bin_range?.currency._id,
                }
                // Clear company if switching to non-cobrand/whitelabel type
                const binTypeLower = bin_range.binType.type.toLowerCase()
                const isCompanyRequired = binTypeLower.includes("cobrand") || binTypeLower.includes("whitelabel")
                if (!isCompanyRequired) {
                    newFormData.company = ""
                }
                setFormData(newFormData)
                // Regenerate version name if card type is selected
                if (formData.card_type) {
                    const version_name = generateVersionName(
                        bin_range.binType.type,
                        bin_range.currency.currency_code,
                        formData.card_type,
                        isCompanyRequired ? newFormData.company : "",
                    )
                    setFormData((prev) => ({ ...prev, version_name }))
                }
            }
        } else if (name === "card_type") {
            const version_name = generateVersionName(selectedBin, selectedCurrency, value, formData.company)
            setFormData((prev) => ({
                ...prev,
                card_type: value,
                version_name: version_name,
            }))
            // Check for duplicates
            // if (shouldShowCompanyDropdown() && formData.company) {
            //     const isDuplicate = checkForDuplicate(version_name, formData.company)
            //     setError(
            //         isDuplicate
            //             ? "This Product Version already exists for the selected company. Please choose a different combination."
            //             : "",
            //     )
            // } else if (!shouldShowCompanyDropdown()) {
            //     const isDuplicate = checkForDuplicate(version_name, "")
            //     setError(isDuplicate ? "This Product Version already exists. Please choose a different combination." : "")
            // }
        } else if (name === "company") {
            const companyValue = value === "none" ? "" : value
            const version_name = generateVersionName(selectedBin, selectedCurrency, formData.card_type, companyValue)
            setFormData((prev) => ({
                ...prev,
                company: companyValue,
                version_name: version_name,
            }))
            if (shouldShowCompanyDropdown() && companyValue && version_name) {
                const isDuplicate = checkForDuplicate(version_name, companyValue)
                setError(
                    isDuplicate
                        ? "This Product Version already exists for the selected company. Please choose a different combination."
                        : "",
                )
            } else {
                setError("")
            }
        } else {
            setFormData((prev) => ({ ...prev, [name]: value }))
        }
    }

    const handleDelete = async (id) => {
        setLoadingState(`delete_${id}`, true)
        try {
            await axios.delete(`/product-versions/${id}`)
            await fetchData()
            alertHelper.showToast("Product Version deleted successfully!", "success")
        } catch (error) {
            console.error("Error deleting product version", error)
            alertHelper.showToast("Error deleting product version", "error")
        } finally {
            setLoadingState(`delete_${id}`, false)
        }
    }

    const confirmSubmit = async () => {
        if (error) {
            return
        }

        setIsSubmitting(true)
        try {
            const { data } = await axios.post("/product-versions", formData)
            await fetchData()

            // Reset form
            setFormData({
                bin_range: "",
                bin_type: "",
                bin_currency: "",
                version_name: "",
                card_type: "",
                version_code: "",
                company: "",
                created_by: localStorage.getItem("user"),
            })

            // Reset other states
            setSelectedBin("")
            setSelectedCurrency("")
            setError("")

            // Close dialog
            setIsConfirmDialogOpen(false)

            alertHelper.showToast("Product Version created successfully!", "success")
        } catch (error) {
            console.error("Error creating product version", error)
            setError("An error occurred while creating the product version. Please try again.")
            alertHelper.showToast("Error creating product version", "error")
        } finally {
            setIsSubmitting(false)
        }
    }

    const handleSort = (key: SortKey) => {
        setSortOrder(sortOrder === "asc" && sortKey === key ? "desc" : "asc")
        setSortKey(key)
    }

    const sortedProductVersions = [...savedProductVersions].sort((a, b) => {
        const aValue = a[sortKey]
        const bValue = b[sortKey]
        if (sortKey === "created_at") {
            return sortOrder === "asc"
                ? new Date(aValue).getTime() - new Date(bValue).getTime()
                : new Date(bValue).getTime() - new Date(aValue).getTime()
        }
        if (typeof aValue === "string" && typeof bValue === "string") {
            return sortOrder === "asc" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue)
        }
        if (typeof aValue === "number" && typeof bValue === "number") {
            return sortOrder === "asc" ? aValue - bValue : bValue - aValue
        }
        return 0
    })

    const getSortIcon = (key: SortKey) => {
        if (sortKey === key) {
            return sortOrder === "asc" ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
        }
        return <ArrowUpDown className="h-4 w-4" />
    }

    // Filter data based on search term
    const filteredProductVersions = sortedProductVersions.filter(
        (version) =>
            version.version_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            version.company?.company_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            version.version_code?.toLowerCase().includes(searchTerm.toLowerCase()),
    )

    // Get counts for each status
    const getStatusCounts = () => {
        const counts = {
            approved: savedProductVersions.filter((v) => v.status.toLowerCase() === "active").length,
            pending: savedProductVersions.filter((v) => v.status.toLowerCase() === "pending").length,
            modify: savedProductVersions.filter((v) => v.status.toLowerCase() === "modify").length,
            declined: savedProductVersions.filter((v) => v.status.toLowerCase() === "decline").length,
        }
        return counts
    }

    const statusCounts = getStatusCounts()

    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case "k":
                        e.preventDefault()
                        // Focus search input
                        document.getElementById("search-input")?.focus()
                        break
                    case "n":
                        e.preventDefault()
                        // Focus first form input
                        document.getElementById("bin_range")?.focus()
                        break
                }
            }
        }
        document.addEventListener("keydown", handleKeyDown)
        return () => document.removeEventListener("keydown", handleKeyDown)
    }, [])

    // Only show loading overlay for initial data loading, not form submission
    if (isLoading && savedProductVersions.length === 0) {
        return <LoadingOverlay />
    }

    const renderTable = (status: string) => {
        const data = filteredProductVersions
            .filter((row) => row.status.toLowerCase() === status.toLowerCase())
            .map((row) => ({
                id: row._id,
                version_name: row.version_name,
                version_code: row.version_code || "N/A",
                company: row.company?.company_name || "N/A",
                created_by: row.created_by?.name || "N/A",
                created_at: formatDate(row.created_at),
                status: row.status.toUpperCase(),
            }))

        const filteredVersions = filteredProductVersions.filter((type) => type.status.toLowerCase() === status)

        const exportData = (type: string) => {
            // Implement your export logic here
            console.log(`Exporting data as ${type}`)
        }

        return (
            <div className="space-y-4">
        {/* Search and Export Controls */}
                <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
                id="search-input"
                placeholder="Search versions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => exportData("csv")}>Export as CSV</DropdownMenuItem>
                <DropdownMenuItem onClick={() => exportData("excel")}>Export as Excel</DropdownMenuItem>
                <DropdownMenuItem onClick={() => exportData("pdf")}>Export as PDF</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <DataExporter data={data} filename="product_version" title="Product Version Report" />
          </div>
        </div>

                {/* Table */}
                <div className="border rounded-lg overflow-hidden">
          <Table>
            <TableHeader className="bg-gray-50">
              <TableRow>
                <TableHead className="px-6 py-4 w-12">
                  <input
                      type="checkbox"
                      onChange={(e) => {
                          if (e.target.checked) {
                              setSelectedVersions(filteredVersions.map((v) => v._id))
                          } else {
                              setSelectedVersions([])
                          }
                      }}
                      className="rounded border-gray-300"
                  />
                </TableHead>
                  {[
                      { key: "version_name", label: "Version Name", icon: <FileText className="h-4 w-4" /> },
                      { key: "version_code", label: "Product Code", icon: <CreditCard className="h-4 w-4" /> },
                      { key: "company", label: "Company", icon: <Building2 className="h-4 w-4" /> },
                      { key: "created_by", label: "Created By" },
                      { key: "created_at", label: "Created Date" },
                      { key: "status", label: "Status" },
                      { key: "action", label: "Actions" },
                  ].map((column) => (
                      <TableHead key={column.key} className="px-6 py-4 font-semibold">
                    {column.key !== "action" ? (
                        <Button
                            variant="ghost"
                            onClick={() => handleSort(column.key as SortKey)}
                            className="hover:bg-transparent text-left font-semibold p-0 h-auto text-gray-700"
                        >
                        <span className="flex items-center gap-2">
                          {column.icon}
                            {column.label}
                            {getSortIcon(column.key as SortKey)}
                        </span>
                      </Button>
                    ) : (
                        <span className="flex items-center gap-2">{column.label}</span>
                    )}
                  </TableHead>
                  ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProductVersions
                  .filter((type) => type.status.toLowerCase() === status)
                  .map((version, index) => (
                      <TableRow key={version._id} className={index % 2 === 0 ? "bg-white" : "bg-gray-50/50"}>
                    <TableCell className="px-6 py-4">
                      <input
                          type="checkbox"
                          checked={selectedVersions.includes(version._id)}
                          onChange={(e) => {
                              if (e.target.checked) {
                                  setSelectedVersions([...selectedVersions, version._id])
                              } else {
                                  setSelectedVersions(selectedVersions.filter((id) => id !== version._id))
                              }
                          }}
                          className="rounded border-gray-300"
                      />
                    </TableCell>
                    <TableCell className="px-6 py-4 font-medium">{version.version_name}</TableCell>
                    <TableCell className="px-6 py-4">
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">{version.version_code || "N/A"}</code>
                    </TableCell>
                    <TableCell className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-gray-400" />
                          {version.company?.company_name || "N/A"}
                      </div>
                    </TableCell>
                    <TableCell className="px-6 py-4">{version.created_by?.name || "N/A"}</TableCell>
                    <TableCell className="px-6 py-4 text-gray-600">{formatDate(version.created_at)}</TableCell>
                    <TableCell className="px-6 py-4">{getStatusBadge(version.status)}</TableCell>
                    <TableCell className="px-6 py-4">
                      <div className="flex gap-2">
                        {userHasDeletePermission && status === "active" && (
                            <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setDeleteId(version._id)}
                                  className="text-red-600 border-red-200 hover:bg-red-50"
                                  disabled={loadingStates[`delete_${version._id}`]}
                              >
                                {loadingStates[`delete_${version._id}`] ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                    <TrashIcon className="h-4 w-4" />
                                )}
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle className="flex items-center gap-2">
                                  <AlertCircle className="h-5 w-5 text-red-500" />
                                  Confirm Deletion
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  Deletion of this record will be sent to administrator for approval. Are you sure you
                                  want to proceed with deleting "{version.version_name}"?
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel onClick={() => setDeleteId(null)}>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                    onClick={() => handleDelete(version._id)}
                                    className="bg-red-600 hover:bg-red-700"
                                    disabled={loadingStates[`delete_${version._id}`]}
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                          {status !== "active" && (
                              <ActionsButtonGroup
                                  entity={version}
                                  entityType="product-versions"
                                  entityName={version.version_name}
                                  userHasApprovePermission={userHasApprovePermission}
                                  userHasDeletePermission={userHasViewPermission}
                                  handleDeleteClick={() => handleDelete(version._id)}
                                  fetchEntities={fetchData}
                              />
                          )}
                      </div>
                    </TableCell>
                  </TableRow>
                  ))}
            </TableBody>
          </Table>

                    {filteredProductVersions.filter((type) => type.status.toLowerCase() === status).length === 0 && (
                        <div className="text-center py-12 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">No product versions found</p>
              <p className="text-sm">Try adjusting your search criteria</p>
            </div>
                    )}
        </div>
      </div>
        )
    }

    return (
        <div className="min-h-screen bg-gray-50/50">
      <div className="px-4 py-8 w-full">
        {/* Header */}
          <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-blue-100 rounded-lg">
              <CreditCard className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Product Versions</h1>
              <p className="text-gray-600">Manage and track your product version configurations</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Form Section */}
            {userHasRequestPermission && (
                <div className="xl:col-span-1">
              <Card className="shadow-sm border-0 bg-white">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Plus className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle className="text-xl text-gray-900">New Product Version</CardTitle>
                      <CardDescription className="text-gray-600">
                        Create a new product version configuration
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="bin_range" className="text-sm font-semibold text-gray-700">
                        Type & Currency
                      </Label>
                      <Select
                          onValueChange={(value) => handleInputChange(value, "bin_range")}
                          value={formData.bin_range}
                      >
                        <SelectTrigger id="bin_range" className="h-11">
                          <SelectValue placeholder="Select type and currency" />
                        </SelectTrigger>
                        <SelectContent>
                          {savedBinRanges
                              .filter((s) => s.status === "active")
                              .map((scheme) => (
                                  <SelectItem key={scheme._id} value={scheme._id}>
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline" className="text-xs">
                                    {scheme.binType.type}
                                  </Badge>
                                    {scheme.currency.currency_code}
                                </div>
                              </SelectItem>
                              ))}
                        </SelectContent>
                      </Select>
                    </div>

                      {/* Conditionally render company dropdown */}
                      {shouldShowCompanyDropdown() && (
                          <div className="space-y-2">
                        <Label htmlFor="company" className="text-sm font-semibold text-gray-700">
                          Company <span className="text-red-500">*</span>
                        </Label>
                        <Select
                            required
                            onValueChange={(value) => handleInputChange(value, "company")}
                            value={formData.company || ""}
                        >
                          <SelectTrigger id="company" className="h-11">
                            <SelectValue placeholder="Select company" />
                          </SelectTrigger>
                          <SelectContent>
                            {companies.map((company) => (
                                <SelectItem key={company._id} value={company._id}>
                                <div className="flex items-center gap-2">
                                  <Building2 className="h-4 w-4 text-gray-400" />
                                    {company.company_name}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <p className="text-xs text-gray-500">
                          Company selection is required for cobrand and whitelabel types
                        </p>
                      </div>
                      )}

                      <div className="space-y-2">
                      <Label htmlFor="cardType" className="text-sm font-semibold text-gray-700">
                        Card Type
                      </Label>
                      <Select
                          onValueChange={(value) => handleInputChange(value, "card_type")}
                          disabled={!formData.bin_type}
                          value={formData.card_type}
                      >
                        <SelectTrigger id="cardType" className="h-11">
                          <SelectValue placeholder="Select card type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Physical">
                            <div className="flex items-center gap-2">
                              <CreditCard className="h-4 w-4" />
                              Physical Card
                            </div>
                          </SelectItem>
                          <SelectItem value="Virtual">
                            <div className="flex items-center gap-2">
                              <FileText className="h-4 w-4" />
                              Virtual Card
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="version_name" className="text-sm font-semibold text-gray-700">
                        Generated Version Name
                      </Label>
                      <Input
                          id="version_name"
                          name="version_name"
                          placeholder="Auto-generated based on selections"
                          value={formData.version_name}
                          readOnly
                          className="h-11 bg-gray-50 font-mono text-sm"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="version_code" className="text-sm font-semibold text-gray-700">
                        Product Code
                      </Label>
                      <Input
                          id="version_code"
                          name="version_code"
                          placeholder="Enter product code"
                          value={formData.version_code}
                          onChange={(e) => handleInputChange(e)}
                          className="h-11"
                      />
                    </div>

                      {error && (
                          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-red-500" />
                          <p className="text-red-700 text-sm font-medium">{error}</p>
                        </div>
                      </div>
                      )}

                      <Separator />

                    <div className="flex gap-3 pt-2">
                      <Button
                          type="button"
                          variant="outline"
                          className="flex-1 h-11 bg-transparent"
                          onClick={() => {
                              setFormData({
                                  bin_range: "",
                                  bin_type: "",
                                  bin_currency: "",
                                  version_name: "",
                                  card_type: "",
                                  version_code: "",
                                  company: "",
                                  created_by: localStorage.getItem("user"),
                              })
                              setSelectedBin("")
                              setSelectedCurrency("")
                              setError("")
                          }}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" disabled={!!error || isSubmitting} className="flex-1 h-11">
                        {isSubmitting ? (
                            <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Creating...
                          </>
                        ) : (
                            <>
                            <Plus className="mr-2 h-4 w-4" />
                            Create Version
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </div>
            )}

            {/* Table Section */}
            {userHasViewPermission && (
                <div className={userHasRequestPermission ? "xl:col-span-2" : "xl:col-span-3"}>
              <Card className="shadow-sm border-0 bg-white">
                <CardHeader className="border-b bg-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-xl text-gray-900">Product Versions Overview</CardTitle>
                      <CardDescription className="text-gray-600">
                        View and manage all product version configurations
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-sm">
                        {savedProductVersions.length} Total
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <div className="border-b bg-gray-50/50 px-6">
                      <TabsList className="grid w-full grid-cols-4 bg-transparent h-auto p-0">
                        <TabsTrigger
                            value="approved"
                            className="data-[state=active]:bg-white data-[state=active]:shadow-sm border-b-2 border-transparent data-[state=active]:border-green-500 rounded-none py-4"
                        >
                          <div className="flex items-center gap-2">
                            <CheckCircle2 className="h-4 w-4 text-green-500" />
                            <span>Approved</span>
                            <Badge variant="secondary" className="ml-1 text-xs">
                              {statusCounts.approved}
                            </Badge>
                          </div>
                        </TabsTrigger>
                        <TabsTrigger
                            value="pending"
                            className="data-[state=active]:bg-white data-[state=active]:shadow-sm border-b-2 border-transparent data-[state=active]:border-yellow-500 rounded-none py-4"
                        >
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-yellow-500" />
                            <span>Pending</span>
                            <Badge variant="secondary" className="ml-1 text-xs">
                              {statusCounts.pending}
                            </Badge>
                          </div>
                        </TabsTrigger>
                        <TabsTrigger
                            value="modify"
                            className="data-[state=active]:bg-white data-[state=active]:shadow-sm border-b-2 border-transparent data-[state=active]:border-blue-500 rounded-none py-4"
                        >
                          <div className="flex items-center gap-2">
                            <AlertCircle className="h-4 w-4 text-blue-500" />
                            <span>Modify</span>
                            <Badge variant="secondary" className="ml-1 text-xs">
                              {statusCounts.modify}
                            </Badge>
                          </div>
                        </TabsTrigger>
                        <TabsTrigger
                            value="declined"
                            className="data-[state=active]:bg-white data-[state=active]:shadow-sm border-b-2 border-transparent data-[state=active]:border-red-500 rounded-none py-4"
                        >
                          <div className="flex items-center gap-2">
                            <XCircle className="h-4 w-4 text-red-500" />
                            <span>Declined</span>
                            <Badge variant="secondary" className="ml-1 text-xs">
                              {statusCounts.declined}
                            </Badge>
                          </div>
                        </TabsTrigger>
                      </TabsList>
                    </div>
                    <div className="p-6">
                      <TabsContent value="approved" className="mt-0">
                        {renderTable("active")}
                      </TabsContent>
                      <TabsContent value="pending" className="mt-0">
                        {renderTable("pending")}
                      </TabsContent>
                      <TabsContent value="modify" className="mt-0">
                        {renderTable("modify")}
                      </TabsContent>
                      <TabsContent value="declined" className="mt-0">
                        {renderTable("decline")}
                      </TabsContent>
                    </div>
                  </Tabs>
                </CardContent>
              </Card>
            </div>
            )}
        </div>

          {/* Confirmation Dialog */}
          <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-blue-500" />
                Confirm Submission
              </AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to submit this product version? It will be sent for administrator approval.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
              <AlertDialogAction disabled={!!error || isSubmitting} onClick={confirmSubmit}>
                {isSubmitting ? (
                    <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                    <>
                    <CheckCircle2 className="mr-2 h-4 w-4" />
                    Submit for Approval
                  </>
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
    )
}
