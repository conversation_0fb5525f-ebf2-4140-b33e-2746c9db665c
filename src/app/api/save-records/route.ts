//@ts-nocheck
import { type NextRequest, NextResponse } from "next/server"
import { saveRecordsToMongoDB, saveFileMetadataToMongoDB } from "@/lib/mongodb"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { records, metadata } = body

    if (!records || !Array.isArray(records)) {
      return NextResponse.json({ error: "Invalid records data" }, { status: 400 })
    }

    // Save file metadata first
    let metadataId = null
    if (metadata) {
      metadataId = await saveFileMetadataToMongoDB(metadata)
    }

    // Transform records to the new format
    const transformedRecords = records.map((record: any) => ({
      fileName: record.fileName,
      sheetName: record.sheetName,
      headers: record.headers,
      rowData: record.rowData,
      rowIndex: record.rowIndex,
      totalRows: record.totalRows,
    }))

    // Save records with progress tracking
    const savedIds = await saveRecordsToMongoDB(transformedRecords, () => {})

    return NextResponse.json({
      success: true,
      savedCount: savedIds.length,
      metadataId,
      message: `Successfully saved ${savedIds.length} records to PocztaPost collection`,
    })
  } catch (error) {
    console.error("Error in save-records API:", error)
    return NextResponse.json({ error: `Failed to save records: ${error}` }, { status: 500 })
  }
}
