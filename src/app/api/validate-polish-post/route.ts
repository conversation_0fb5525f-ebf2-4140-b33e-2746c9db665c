import { type NextRequest, NextResponse } from "next/server"
import type { DatabaseRecord } from "@/lib/database"

interface ValidationError {
  rowIndex: number
  field: string
  value: any
  error: string
  severity: "error" | "warning"
}

interface ValidationResult {
  hasErrors: boolean
  hasWarnings: boolean
  totalRecords: number
  validRecords: DatabaseRecord[]
  invalidRecords: DatabaseRecord[]
  errors: ValidationError[]
  summary: {
    emptyCountries: number
    duplicateCountries: number
    invalidPrices: number
    invalidDeliveryTimes: number
  }
}

export async function POST(request: NextRequest) {
  try {
    const { records }: { records: DatabaseRecord[] } = await request.json()

    const errors: ValidationError[] = []
    const validRecords: DatabaseRecord[] = []
    const invalidRecords: DatabaseRecord[] = []

    let emptyCountries = 0
    let duplicateCountries = 0
    let invalidPrices = 0
    let invalidDeliveryTimes = 0

    // Track countries to detect duplicates within the file
    const countryMap = new Map<string, number[]>()

    // First pass: collect all countries and their row indices
    records.forEach((record, index) => {
      const country = record.rowData.Country
      if (country && country.toString().trim()) {
        const normalizedCountry = country.toString().toLowerCase().trim()
        if (!countryMap.has(normalizedCountry)) {
          countryMap.set(normalizedCountry, [])
        }
        countryMap.get(normalizedCountry)!.push(index)
      }
    })

    // Second pass: validate each record
    records.forEach((record, index) => {
      const rowErrors: ValidationError[] = []
      let hasErrors = false

      // Validate Country field
      const country = record.rowData.Country
      if (!country || !country.toString().trim()) {
        emptyCountries++
        rowErrors.push({
          rowIndex: index,
          field: "Country",
          value: country,
          error: "Country field is empty or missing",
          severity: "error",
        })
        hasErrors = true
      } else {
        // Check for duplicates within the file
        const normalizedCountry = country.toString().toLowerCase().trim()
        const duplicateIndices = countryMap.get(normalizedCountry) || []
        if (duplicateIndices.length > 1) {
          duplicateCountries++
          rowErrors.push({
            rowIndex: index,
            field: "Country",
            value: country,
            error: `Duplicate country found at rows: ${duplicateIndices.map((i) => i + 2).join(", ")}`,
            severity: "error",
          })
          hasErrors = true
        }
      }

      // Validate price fields
      const priceFields = ["Poczta_Post 50g", "Poczta_Post 100g", "Poczta_Post 350g", "Poczta_Post 500g"]
      priceFields.forEach((field) => {
        const value = record.rowData[field]
        if (value && value !== "") {
          const numericValue = Number.parseFloat(value.toString().replace(/[^\d.-]/g, ""))
          if (isNaN(numericValue) || numericValue < 0) {
            invalidPrices++
            rowErrors.push({
              rowIndex: index,
              field,
              value,
              error: "Invalid price format or negative value",
              severity: "error",
            })
            hasErrors = true
          }
        }
      })

      // Validate delivery time
      const deliveryTime = record.rowData.PPDeliveryTime
      if (deliveryTime && deliveryTime !== "") {
        const deliveryTimeStr = deliveryTime.toString().trim()
        // Basic validation for delivery time format
        if (deliveryTimeStr.length > 50) {
          invalidDeliveryTimes++
          rowErrors.push({
            rowIndex: index,
            field: "PPDeliveryTime",
            value: deliveryTime,
            error: "Delivery time text is too long (max 50 characters)",
            severity: "warning",
          })
        }
      }

      // Add errors to main errors array
      errors.push(...rowErrors)

      // Categorize record
      if (hasErrors) {
        invalidRecords.push(record)
      } else {
        validRecords.push(record)
      }
    })

    const result: ValidationResult = {
      hasErrors: errors.some((e) => e.severity === "error"),
      hasWarnings: errors.some((e) => e.severity === "warning"),
      totalRecords: records.length,
      validRecords,
      invalidRecords,
      errors,
      summary: {
        emptyCountries,
        duplicateCountries: Math.floor(duplicateCountries / 2), // Divide by 2 since each duplicate is counted twice
        invalidPrices,
        invalidDeliveryTimes,
      },
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error validating Polish Post records:", error)
    return NextResponse.json({ error: "Failed to validate records" }, { status: 500 })
  }
}
