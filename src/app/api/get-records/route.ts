import { type NextRequest, NextResponse } from "next/server"
import { getRecordsFromMongoDB, getRecordCountFromMongoDB, getUniqueFileNames } from "@/lib/mongodb"
import { connectToDatabase } from "@/lib/mongodb"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const fileName = searchParams.get("fileName") || undefined
    const limit = Number.parseInt(searchParams.get("limit") || "100")
    const skip = Number.parseInt(searchParams.get("skip") || "0")
    const getFileNames = searchParams.get("getFileNames") === "true"
    const search = searchParams.get("search") || undefined
    const searchField = searchParams.get("searchField") || undefined

    if (getFileNames) {
      const fileNames = await getUniqueFileNames()
      return NextResponse.json({
        success: true,
        fileNames,
      })
    }

    // Build search filter
    let searchFilter = {}
    if (search && search.trim()) {
      const searchTerm = search.trim()

      if (searchField && searchField !== "all") {
        // Search in specific field
        searchFilter = {
          [searchField]: {
            $regex: searchTerm,
            $options: "i", // case insensitive
          },
        }
      } else {
        // Search across all non-metadata fields
        // First get a sample record to determine available fields
        const database = await connectToDatabase()
        const collection = database.collection("PocztaPost")
        const sampleRecord = await collection.findOne(fileName ? { _fileName: fileName } : {})

        if (sampleRecord) {
          const searchableFields = Object.keys(sampleRecord).filter((key) => !key.startsWith("_") && key !== "_id")

          // Create OR query for all searchable fields
          searchFilter = {
            $or: searchableFields.map((field) => ({
              [field]: {
                $regex: searchTerm,
                $options: "i",
              },
            })),
          }
        }
      }
    }

    // Combine fileName filter with search filter
    let finalFilter = {}
    if (fileName) {
      finalFilter = { _fileName: fileName }
    }

    if (Object.keys(searchFilter).length > 0) {
      if (Object.keys(finalFilter).length > 0) {
        finalFilter = { $and: [finalFilter, searchFilter] }
      } else {
        finalFilter = searchFilter
      }
    }

    const records = await getRecordsFromMongoDB(undefined, limit, skip, finalFilter)
    const totalCount = await getRecordCountFromMongoDB(undefined, finalFilter)

    return NextResponse.json({
      success: true,
      records,
      totalCount,
      currentPage: Math.floor(skip / limit) + 1,
      totalPages: Math.ceil(totalCount / limit),
    })
  } catch (error) {
    console.error("Error in get-records API:", error)
    return NextResponse.json({ error: `Failed to fetch records: ${error}` }, { status: 500 })
  }
}
