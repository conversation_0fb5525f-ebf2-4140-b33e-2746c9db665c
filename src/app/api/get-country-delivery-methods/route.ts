//@ts-nocheck

import { type NextRequest, NextResponse } from "next/server"
import { connectToDatabase } from "@/lib/mongodb"
import type { PocztaPostRecord, DHLDeliveryRecord } from "@/lib/mongodb"

export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url)
        const country = searchParams.get("country")

        if (!country) {
            return NextResponse.json(
                {
                    success: false,
                    error: "Country parameter is required",
                },
                { status: 400 },
            )
        }

        const database = await connectToDatabase()
        const polishPostCollection = database.collection("PocztaPost")
        const dhlCollection = database.collection("DHLDeliveryMethods")

        // Case-insensitive search for the country in both collections
        const [polishPostRecords, dhlRecords] = await Promise.all([
            polishPostCollection
                .find({
                    Country: { $regex: new RegExp(`^${country}$`, "i") },
                })
                .toArray() as Promise<PocztaPostRecord[]>,

            dhlCollection
                .find({
                    Country: { $regex: new RegExp(`^${country}$`, "i") },
                })
                .toArray() as Promise<DHLDeliveryRecord[]>,
        ])

        return NextResponse.json({
            success: true,
            country,
            polishPost: {
                records: polishPostRecords,
                count: polishPostRecords.length,
            },
            dhl: {
                records: dhlRecords,
                count: dhlRecords.length,
            },
            totalCount: polishPostRecords.length + dhlRecords.length,
        })
    } catch (error) {
        console.error("Error in get-country-delivery-methods API:", error)
        return NextResponse.json(
            {
                success: false,
                error: `Failed to fetch records: ${error}`,
            },
            { status: 500 },
        )
    }
}
