import { NextResponse } from "next/server"
import { connectToDatabase } from "@/lib/mongodb"

export async function POST(request: Request) {
  try {
    const { countries } = await request.json()

    if (!Array.isArray(countries) || countries.length === 0) {
      return NextResponse.json({ duplicates: [] })
    }

    const db = await connectToDatabase()
    const collection = db.collection("DHLDeliveryMethods")

    // Convert all countries to lowercase for case-insensitive comparison
    const lowercaseCountries = countries.map((country) => country.toLowerCase())

    // Find existing countries in the database
    const existingCountries = await collection
      .find({
        "rowData.Country": { $regex: new RegExp(`^(${lowercaseCountries.join("|")})$`, "i") },
      })
      .project({ "rowData.Country": 1 })
      .toArray()

    // Extract country names and convert to lowercase
    const existingCountryNames = existingCountries.map((doc) => doc.rowData.Country.toString().toLowerCase().trim())

    // Find duplicates (countries that exist in both arrays)
    const duplicates = lowercaseCountries.filter((country) => existingCountryNames.includes(country))

    return NextResponse.json({ duplicates })
  } catch (error) {
    console.error("Error checking for duplicate countries:", error)
    return NextResponse.json({ error: "Failed to check for duplicate countries" }, { status: 500 })
  }
}
