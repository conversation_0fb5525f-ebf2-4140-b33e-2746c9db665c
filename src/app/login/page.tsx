//@ts-nocheck

"use client"

import type React from "react"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useEffect, useState } from "react"
import { usePathname, useRouter } from "next/navigation"
import axiosInstance from "@/utils/axiosInstance"
import { AlertCircle, Loader2, Eye, EyeOff, ArrowLeft, Shield, Mail, Lock, CheckCircle } from "lucide-react"
import Link from "next/link"

export default function Login() {
    const [email, setEmail] = useState("")
    const [password, setPassword] = useState("")
    const [verificationCode, setVerificationCode] = useState("")
    const [needsVerification, setNeedsVerification] = useState(false)
    const [tempToken, setTempToken] = useState("")
    const [showPassword, setShowPassword] = useState(false)
    const router = useRouter()
    const pathname = usePathname()
    const [redirectUrl, setRedirectUrl] = useState<string | null>(null)
    const [errorMessage, setErrorMessage] = useState<string | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [successMessage, setSuccessMessage] = useState<string | null>(null)

    // Initialize redirectUrl from localStorage when component mounts
    useEffect(() => {
        if (typeof window !== "undefined") {
            setRedirectUrl(localStorage.getItem("redirectUrl"))
        }
    }, [])

    // Clean up redirect URL after successful login
    useEffect(() => {
        if (typeof window !== "undefined" && successMessage) {
            localStorage.removeItem("redirectUrl")
        }
    }, [successMessage])

    // Step 1: Initial login attempt
    const handleLogin = async (e: React.FormEvent) => {
        e.preventDefault()
        setErrorMessage(null)
        setSuccessMessage(null)
        setIsLoading(true)

        try {
            const response = await axiosInstance.post(
                "auth/login",
                { email, password },
                {
                    withCredentials: true,
                },
            )

            // Check if the user has 2FA enabled
            const twoFactorStatus = response.data.twoFactorEnabled

            if (twoFactorStatus) {
                // User has 2FA enabled, proceed to verification step
                setNeedsVerification(true)
                setTempToken(response.data.tempToken) // Store temporary token for 2FA verification
                setSuccessMessage("Please enter the verification code from your authenticator app")
            } else {
                // User doesn't have 2FA enabled, proceed with login
                handleSuccessfulLogin(response.data)
            }
        } catch (error: any) {
            if (error.response && error.response.data.message) {
                setErrorMessage(error.response.data.message)
            } else {
                setErrorMessage("Login failed. Please try again.")
            }
        } finally {
            setIsLoading(false)
        }
    }

    // Step 2: Verify 2FA code
    const handleVerify = async (e: React.FormEvent) => {
        e.preventDefault()
        setErrorMessage(null)
        setIsLoading(true)

        try {
            // Use the existing verifyTwoFactor API endpoint that your settings page uses
            const response = await axiosInstance.post(
                "auth/verify-login-2fa",
                {
                    email,
                    verificationCode,
                    tempToken, // Include the temporary token received from the login step
                },
                {
                    withCredentials: true,
                },
            )

            if (response.data.success) {
                handleSuccessfulLogin(response.data)
            } else {
                setErrorMessage(response.data.message || "Verification failed")
                setIsLoading(false)
            }
        } catch (error: any) {
            if (error.response && error.response.data.message) {
                setErrorMessage(error.response.data.message)
            } else {
                setErrorMessage("Verification failed. Please check your code and try again.")
            }
            setIsLoading(false)
        }
    }

    // Handle successful login after verification
    const handleSuccessfulLogin = (data: any) => {
        const token = data.token
        localStorage.setItem("authToken", token)
        setSuccessMessage("Login successful! Redirecting...")

        // Redirect to the saved page or dashboard
        if (redirectUrl) {
            setTimeout(() => {
                router.push(decodeURIComponent(redirectUrl))
            }, 500)
        } else if (data.user.dashboard === "programmeManager") {
            setTimeout(() => {
                router.push("/manager/")
            }, 500)
        } else if (data.user.dashboard === "cardholder") {
            setTimeout(() => {
                router.push("/cardholder/")
            }, 500)
        } else if (data.user.dashboard === "infinity") {
            setTimeout(() => {
                router.push("/lite/admin/dashboard/")
            }, 500)
        } else if (data.user.dashboard === "corporate") {
            setTimeout(() => {
                router.push("/corporate/")
            }, 500)
        } else {
            if (confirm("You are not allowed to login!!")) {
                router.refresh()
            }
        }
    }

    const handleBackToLogin = () => {
        setNeedsVerification(false)
        setVerificationCode("")
        setErrorMessage(null)
        setSuccessMessage(null)
    }

    return (
        <div
            className="min-h-screen flex items-center justify-center bg-cover bg-center relative"
            style={{ backgroundImage: "url(/bg-about1.png)" }}
        >
      {/* Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60" />

            {/* Animated Background Elements */}
            <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

            {/* Main Container */}
            <div className="relative z-10 w-full max-w-6xl mx-auto px-4 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 items-center">
          {/* Left Side - Branding */}
            <div className="hidden lg:block text-white space-y-8">
            <div className="space-y-6">
              <div className="inline-flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20">
                <Shield className="w-5 h-5 text-primary-400" />
                <span className="text-sm font-medium">Secure Authentication</span>
              </div>

              <h1 className="text-5xl font-bold leading-tight">
                Welcome to Your
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary-400 to-purple-400">
                  Secure Portal
                </span>
              </h1>

              <p className="text-xl text-gray-300 leading-relaxed max-w-lg">
                Access your dashboard with enterprise-grade security and seamless user experience.
              </p>
            </div>

            <div className="grid grid-cols-2 gap-6 pt-8">
              <div className="space-y-2">
                <div className="w-12 h-12 bg-primary-500/20 rounded-xl flex items-center justify-center">
                  <Shield className="w-6 h-6 text-primary-400" />
                </div>
                <h3 className="font-semibold">Bank-Level Security</h3>
                <p className="text-sm text-gray-400">256-bit encryption with 2FA protection</p>
              </div>

              <div className="space-y-2">
                <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-purple-400" />
                </div>
                <h3 className="font-semibold">Instant Access</h3>
                <p className="text-sm text-gray-400">Lightning-fast authentication process</p>
              </div>
            </div>
          </div>

            {/* Right Side - Login Form */}
            <div className="w-full max-w-md mx-auto lg:mx-0 lg:ml-auto">
            <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
              {/* Header with Smooth Transitions */}
                <div className="px-8 pt-8 pb-6 text-center border-b border-gray-100">
                <div className="mb-6">
                  <div className="relative w-40 h-10 mx-auto">
                    <Image src="/logo.jpeg" alt="Company Logo" fill className="object-contain" priority />
                  </div>
                </div>

                <div className="space-y-2 overflow-hidden">
                  <h2 className="text-2xl font-bold text-gray-900 transition-all duration-300">
                    {needsVerification ? "Verify Your Identity" : "Sign In"}
                  </h2>
                  <div className="h-10 flex items-center justify-center">
                    <p
                        className={`text-gray-600 text-sm transition-all duration-300 transform ${
                            needsVerification ? "translate-y-0 opacity-100" : "translate-y-0 opacity-100"
                        }`}
                    >
                      {needsVerification
                          ? "Enter the 6-digit code from your authenticator"
                          : "Enter your credentials to access your account"}
                    </p>
                  </div>
                </div>
              </div>

                {/* Form Content with Slide Transitions */}
                <div className="px-8 py-6 overflow-hidden">
                {/* Alert Messages */}
                    <div className={`transition-all duration-300 ${errorMessage || successMessage ? "mb-6" : "mb-0 h-0"}`}>
                  {errorMessage && (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-2xl flex items-start space-x-3 animate-in slide-in-from-top-2 duration-300">
                      <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                      <p className="text-red-800 text-sm font-medium">{errorMessage}</p>
                    </div>
                  )}

                        {successMessage && (
                            <div className="p-4 bg-green-50 border border-green-200 rounded-2xl flex items-start space-x-3 animate-in slide-in-from-top-2 duration-300">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <p className="text-green-800 text-sm font-medium">{successMessage}</p>
                    </div>
                        )}
                </div>

                    {/* Forms Container with Slide Animation */}
                    <div className="relative">
                  <div
                      className={`transition-all duration-500 ease-in-out transform ${
                          needsVerification ? "-translate-x-full opacity-0" : "translate-x-0 opacity-100"
                      }`}
                      style={{
                          position: needsVerification ? "absolute" : "relative",
                          width: "100%",
                          top: 0,
                          left: 0,
                      }}
                  >
                    {/* Login Form */}
                      <form onSubmit={handleLogin} className="space-y-5">
                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-sm font-semibold text-gray-700">
                          Email Address
                        </Label>
                        <div className="relative group">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <Mail className="h-5 w-5 text-gray-400 group-focus-within:text-primary-500 transition-colors" />
                          </div>
                          <Input
                              id="email"
                              type="email"
                              placeholder="Enter your email address"
                              required
                              value={email}
                              onChange={(e) => setEmail(e.target.value)}
                              className="pl-12 h-12 border-2 border-gray-200 rounded-xl focus:border-primary-500 focus:ring-0 transition-all duration-200 bg-gray-50/50 focus:bg-white"
                              disabled={isLoading}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="password" className="text-sm font-semibold text-gray-700">
                          Password
                        </Label>
                        <div className="relative group">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <Lock className="h-5 w-5 text-gray-400 group-focus-within:text-primary-500 transition-colors" />
                          </div>
                          <Input
                              id="password"
                              type={showPassword ? "text" : "password"}
                              placeholder="Enter your password"
                              required
                              value={password}
                              onChange={(e) => setPassword(e.target.value)}
                              className="pl-12 pr-12 h-12 border-2 border-gray-200 rounded-xl focus:border-primary-500 focus:ring-0 transition-all duration-200 bg-gray-50/50 focus:bg-white"
                              disabled={isLoading}
                          />
                          <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                              disabled={isLoading}
                          >
                            {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                          </button>
                        </div>
                      </div>

                      <Button
                          type="submit"
                          className="w-full h-12 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                          disabled={isLoading}
                      >
                        {isLoading ? (
                            <>
                            <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                            Signing you in...
                          </>
                        ) : (
                            "Sign In to Your Account"
                        )}
                      </Button>

                      <div className="text-center pt-2">
                        <Link
                            href="/forget-password"
                            className="text-sm text-primary-600 hover:text-primary-700 font-medium hover:underline transition-colors"
                        >
                          Forgot your password?
                        </Link>
                      </div>
                    </form>
                  </div>

                        {/* 2FA Verification Form */}
                        <div
                            className={`transition-all duration-500 ease-in-out transform ${
                                needsVerification ? "translate-x-0 opacity-100" : "translate-x-full opacity-0"
                            }`}
                            style={{
                                position: needsVerification ? "relative" : "absolute",
                                width: "100%",
                                top: 0,
                                left: 0,
                            }}
                        >
                    <form onSubmit={handleVerify} className="space-y-5">
                      <div className="space-y-3">
                        <Label htmlFor="verificationCode" className="text-sm font-semibold text-gray-700">
                          Authentication Code
                        </Label>
                        <Input
                            id="verificationCode"
                            type="text"
                            inputMode="numeric"
                            pattern="[0-9]*"
                            placeholder="• • • • • •"
                            required
                            value={verificationCode}
                            onChange={(e) => setVerificationCode(e.target.value.replace(/[^0-9]/g, ""))}
                            maxLength={6}
                            className="text-center text-2xl tracking-[0.5em] h-14 border-2 border-gray-200 rounded-xl focus:border-primary-500 focus:ring-0 transition-all duration-200 bg-gray-50/50 focus:bg-white font-mono"
                            disabled={isLoading}
                        />
                        <p className="text-xs text-gray-500 text-center">
                          Open your authenticator app and enter the 6-digit code
                        </p>
                      </div>

                      <Button
                          type="submit"
                          className="w-full h-12 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                          disabled={isLoading || verificationCode.length !== 6}
                      >
                        {isLoading ? (
                            <>
                            <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                            Verifying...
                          </>
                        ) : (
                            "Verify & Continue"
                        )}
                      </Button>

                      <Button
                          type="button"
                          variant="outline"
                          className="w-full h-12 border-2 border-gray-200 text-gray-700 hover:bg-gray-50 rounded-xl transition-all duration-200"
                          onClick={handleBackToLogin}
                          disabled={isLoading}
                      >
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Sign In
                      </Button>
                    </form>
                  </div>
                </div>
              </div>
            </div>

                {/* Mobile Branding */}
                <div className="lg:hidden mt-8 text-center">
              <p className="text-white/80 text-sm">Protected by enterprise-grade security</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    )
}
