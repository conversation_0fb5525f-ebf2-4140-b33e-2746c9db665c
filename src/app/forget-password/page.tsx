//@ts-nocheck
"use client"

import type React from "react"
import {useEffect, useState} from "react"

import Image from "next/image"
import {Button} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {useRouter} from "next/navigation"
import axiosInstance from "@/utils/axiosInstance"
import {Eye, EyeOff, Loader2} from "lucide-react"

export default function ResetPassword() {
    const [email, setEmail] = useState("")
    const [otp, setOtp] = useState("")
    const [password, setPassword] = useState("")
    const [confirmPassword, setConfirmPassword] = useState("")
    const [showPassword, setShowPassword] = useState(false)
    const [showConfirmPassword, setShowConfirmPassword] = useState(false)
    const [otpRequested, setOtpRequested] = useState(false)
    const [requestingOtp, setRequestingOtp] = useState(false)
    const router = useRouter()
    const [errorMessage, setErrorMessage] = useState<string | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [successMessage, setSuccessMessage] = useState<string | null>(null)

    const validatePassword = (password: string) => {
        const errors = []

        if (password.length < 8) {
            errors.push("Password must be at least 8 characters long")
        }

        if (!/[A-Z]/.test(password)) {
            errors.push("Password must contain at least one uppercase letter")
        }

        if (!/[a-z]/.test(password)) {
            errors.push("Password must contain at least one lowercase letter")
        }

        if (!/[0-9]/.test(password)) {
            errors.push("Password must contain at least one number")
        }

        if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
            errors.push("Password must contain at least one special character")
        }

        return errors
    }

    // Initialize with empty email, will be populated in useEffect
    useEffect(() => {
        // Only run in browser environment
        if (typeof window !== "undefined") {
            // Check if there's an email in the URL hash
            const hashParams = new URLSearchParams(window.location.hash.substring(1))
            const emailFromHash = hashParams.get("email")

            if (emailFromHash) {
                setEmail(emailFromHash)
                // Clean up the URL without refreshing the page
                window.history.replaceState({}, document.title, window.location.pathname)
            } else {
                // Check localStorage as fallback
                const storedEmail = localStorage.getItem("resetPasswordEmail")
                if (storedEmail) {
                    setEmail(storedEmail)
                }
            }
        }
    }, [])

    const handleRequestOtp = async (e: React.FormEvent) => {
        e.preventDefault()
        setErrorMessage(null)
        setSuccessMessage(null)

        if (!email) {
            setErrorMessage("Please enter your email address.")
            return
        }

        setRequestingOtp(true)

        try {
            const response = await axiosInstance.post("password/forgot-password", { email })

            // Store email in localStorage for persistence
            if (typeof window !== "undefined") {
                localStorage.setItem("resetPasswordEmail", email)
            }

            setSuccessMessage("An OTP has been sent to your email. Please check your inbox.")
            setOtpRequested(true)
        } catch (error: any) {
            if (error.response && error.response.data.message) {
                setErrorMessage(error.response.data.message)
            } else {
                setErrorMessage("Failed to send OTP. Please try again.")
            }
        } finally {
            setRequestingOtp(false)
        }
    }

    const handleResetPassword = async (e: React.FormEvent) => {
        e.preventDefault()
        setErrorMessage(null)
        setSuccessMessage(null)

        // Validate OTP
        if (otp.length < 4) {
            setErrorMessage("Please enter a valid OTP.")
            return
        }

        // Validate passwords match
        if (password !== confirmPassword) {
            setErrorMessage("Passwords do not match.")
            return
        }

        // Validate password strength
        const passwordErrors = validatePassword(password)
        if (passwordErrors.length > 0) {
            setErrorMessage(passwordErrors[0])
            return
        }

        setIsLoading(true)

        try {
            const response = await axiosInstance.post("password/reset-password", {
                email,
                otp,
                password,
            })

            setSuccessMessage("Password has been reset successfully!")

            // Clear stored email
            if (typeof window !== "undefined") {
                localStorage.removeItem("resetPasswordEmail")
            }

            // Redirect to login after a delay
            setTimeout(() => {
                router.push("/login")
            }, 3000)
        } catch (error: any) {
            if (error.response && error.response.data.message) {
                setErrorMessage(error.response.data.message)
            } else {
                setErrorMessage("Failed to reset password. Please check your OTP and try again.")
            }
        } finally {
            setIsLoading(false)
        }
    }

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword)
    }

    const toggleConfirmPasswordVisibility = () => {
        setShowConfirmPassword(!showConfirmPassword)
    }

    return (
        <div
            className="relative flex items-center justify-center min-h-screen bg-cover bg-center"
            style={{ backgroundImage: "url(/bg-about1.png)" }}
        >
            <div className="bg-white p-8 rounded-lg shadow-lg w-[350px] max-w-full">
                <div className="text-center mb-6">
                    <Image src="/logo.jpeg" alt="Logo" width={300} height={50} className="h-full w-full object-cover pb-5" />
                </div>
                <h1 className="text-3xl font-bold text-center mb-6">Reset Password</h1>

                {errorMessage && (
                    <div
                        className="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
                        role="alert"
                    >
                        <span className="font-medium">Error:</span> {errorMessage}
                    </div>
                )}
                {successMessage && (
                    <div
                        className="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400"
                        role="alert"
                    >
                        <span className="font-medium">Success:</span> {successMessage}
                    </div>
                )}

                {!otpRequested ? (
                    // Step 1: Request OTP form
                    <form onSubmit={handleRequestOtp}>
                        <div className="grid gap-4">
                            <div className="grid gap-2">
                                <Label htmlFor="email">Email</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    placeholder="<EMAIL>"
                                    required
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                />
                            </div>
                            <Button type="submit" className="w-full" disabled={requestingOtp}>
                                {requestingOtp ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Sending OTP...
                                    </>
                                ) : (
                                    "Request OTP"
                                )}
                            </Button>
                            <a className="text-center underline" href="/login">
                                Back to Login
                            </a>
                        </div>
                    </form>
                ) : (
                    // Step 2: Reset password form
                    <form onSubmit={handleResetPassword}>
                        <div className="grid gap-4">
                            <div className="grid gap-2">
                                <Label htmlFor="email">Email</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    placeholder="<EMAIL>"
                                    required
                                    value={email}
                                    readOnly
                                    className="bg-gray-50"
                                />
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="otp">OTP Code</Label>
                                <Input
                                    id="otp"
                                    type="text"
                                    placeholder="Enter OTP sent to your email"
                                    required
                                    value={otp}
                                    onChange={(e) => setOtp(e.target.value)}
                                />
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="password">New Password</Label>
                                <div className="relative">
                                    <Input
                                        id="password"
                                        type={showPassword ? "text" : "password"}
                                        required
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                    />
                                    <button
                                        type="button"
                                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500"
                                        onClick={togglePasswordVisibility}
                                    >
                                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                    </button>
                                </div>
                                {password && (
                                    <div className="text-xs mt-1">
                                        <p className={`${/[A-Z]/.test(password) ? "text-green-500" : "text-red-500"}`}>
                                            ✓ At least one uppercase letter
                                        </p>
                                        <p className={`${/[a-z]/.test(password) ? "text-green-500" : "text-red-500"}`}>
                                            ✓ At least one lowercase letter
                                        </p>
                                        <p className={`${/[0-9]/.test(password) ? "text-green-500" : "text-red-500"}`}>
                                            ✓ At least one number
                                        </p>
                                        <p
                                            className={`${/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password) ? "text-green-500" : "text-red-500"}`}
                                        >
                                            ✓ At least one special character
                                        </p>
                                        <p className={`${password.length >= 8 ? "text-green-500" : "text-red-500"}`}>
                                            ✓ At least 8 characters long
                                        </p>
                                    </div>
                                )}
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="confirmPassword">Confirm Password</Label>
                                <div className="relative">
                                    <Input
                                        id="confirmPassword"
                                        type={showConfirmPassword ? "text" : "password"}
                                        required
                                        value={confirmPassword}
                                        onChange={(e) => setConfirmPassword(e.target.value)}
                                    />
                                    <button
                                        type="button"
                                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500"
                                        onClick={toggleConfirmPasswordVisibility}
                                    >
                                        {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                    </button>
                                </div>
                            </div>
                            <Button type="submit" className="w-full" disabled={isLoading}>
                                {isLoading ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Resetting password...
                                    </>
                                ) : (
                                    "Set New Password"
                                )}
                            </Button>
                            <div className="flex justify-between">
                                <a className="text-center underline" href="/login">
                                    Back to Login
                                </a>
                                <button type="button" className="text-center underline" onClick={() => setOtpRequested(false)}>
                                    Request New OTP
                                </button>
                            </div>
                        </div>
                    </form>
                )}
            </div>
        </div>
    )
}
