import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import axiosInstance from "@/utils/axiosInstance"
import { Loader2 } from "lucide-react"

interface RestrictLostDialogProps {
    cardId: string
    expDate:string
    onApiSuccess: () => void
}

export function RestrictLostDialog({ cardId,expDate, onApiSuccess }: RestrictLostDialogProps) {
    const [loading, setLoading] = useState(false)

    const handleRestrictLost = async () => {
        try {
            setLoading(true)
            const response = await axiosInstance.post(`cards/${cardId}/restrictLost`,{expDate})

            if (typeof onApiSuccess === "function") {
                onApiSuccess()
            }
        } catch (error) {
            console.error("Failed to restrict lost card", error)
            alert("Failed to restrict lost card. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant="destructive">Report Lost</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Report Card as Lost</DialogTitle>
                    <DialogDescription>
                        Are you sure you want to report this card as lost? This action will immediately block all transactions on
                        this card to prevent unauthorized use.
                    </DialogDescription>
                </DialogHeader>

                <DialogFooter>
                    <Button type="submit" onClick={handleRestrictLost} disabled={loading} variant="destructive">
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />
                                <span>Reporting...</span>
                            </>
                        ) : (
                            "Report Lost"
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

