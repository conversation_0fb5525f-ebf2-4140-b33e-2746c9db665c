//@ts-nocheck
"use client"

import { useEffect, useState } from "react"
import { use<PERSON>out<PERSON>, useSearchParams } from "next/navigation"
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from "@/components/ui/sheet"
import axiosInstance from "@/utils/axiosInstance"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { CardManagement } from "./card/card-design"
import { Badge } from "@/components/ui/badge"
import { LoadingOverlay } from "@/components/LoadingOverlay"

interface CardData {
    cardHash: string
    cardMask: string
    cardKey: string
    cardUuid: string
    expDate: string
    issueDate: string
    status: string
    statusCode: string
    terminating: boolean
    productCode: string
    productDesc: string
    holder: string
    accounts: Array<{
        accNo: string
        primary: boolean
        currencyCode: string
        currencyName: string
    }>
    embossName1: string
    source: string
    kind: string
    main: boolean
    limits: Array<{
        name: string
        value: number
        noLimit: boolean
    }>
    visual: string
    tokenized: boolean
    delivery: {
        deliveryType: string
    }
    contactless: boolean
    cardTechnologyMode: string
    creationDate: string
    autoRenewal: boolean
}

interface CardDetailsSheetProps {
    isOpen: boolean
    onOpenChange: (open: boolean) => void
    cardId: string
    companyId: string
}

export function CardDetailsSheet({ isOpen, onOpenChange, cardId, companyId }: CardDetailsSheetProps) {
    const router = useRouter()
    const searchParams = useSearchParams()
    const [loading, setLoading] = useState(true)
    const [card, setCard] = useState<CardData | null>(null)
    const [error, setError] = useState<string | null>(null)

    // Check for card ID in URL when component mounts or URL changes
    // useEffect(() => {
    //     const urlCardId = searchParams.get("card")
    //     if (urlCardId && !isOpen) {
    //         // If there's a card ID in the URL but sheet is not open, open it
    //         onOpenChange(true)
    //     }
    // }, [searchParams, isOpen, onOpenChange])

    // Update URL when sheet is opened/closed
    // useEffect(() => {
    //     if (isOpen && cardId) {
    //         // Update URL with card ID
    //         const url = new URL(window.location.href)
    //         url.searchParams.set("card", cardId)
    //         window.history.pushState({}, "", url.toString())
    //     } else {
    //         // Remove card ID from URL when sheet is closed
    //         if (searchParams.has("card")) {
    //             const url = new URL(window.location.href)
    //             url.searchParams.delete("card")
    //             window.history.pushState({}, "", url.toString())
    //         }
    //     }
    // }, [isOpen, cardId, searchParams])

    useEffect(() => {
        if (isOpen && cardId) {
            // Fetch card details here
            fetchCardDetails(cardId)
        }
    }, [isOpen, cardId])

    const handleSheetOpenChange = (open: boolean) => {
        onOpenChange(open)
    }

    const fetchCardDetails = async (cardId) => {
        try {
            const response = await axiosInstance.get<CardData>(`client/card/${cardId}`)
            setCard(response.data.card)
        } catch (error) {
            console.error("Error fetching card details:", error)
            setError("Failed to load card details. Please try again later.")
        } finally {
            setLoading(false)
        }
    }

    const splitCardMask = (cardMask) => {
        if (cardMask) {
            // Remove any spaces if they exist
            const cleanedMask = cardMask.replace(/\s+/g, "")

            // Split the string into chunks of 4 characters
            const chunks = []
            for (let i = 0; i < cleanedMask.length; i += 4) {
                chunks.push(cleanedMask.slice(i, i + 4))
            }

            return chunks
        }
    }

    function getStatusColor(status: string) {
        switch (status.toUpperCase()) {
            case "ACTIVE":
                return "bg-green-500"
            case "INACTIVE":
                return "bg-yellow-500"
            case "BLOCKED":
                return "bg-red-500"
            case "ORDERED":
                return "bg-blue-500"
            default:
                return "bg-gray-500"
        }
    }

    function formatLimitName(name: string) {
        return name
            .split("_")
            .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
            .join(" ")
    }

    const cardMaskChunks = splitCardMask(card?.cardMask)
    function LimitProgress({ current, max, noLimit }: { current: number; max: number; noLimit: boolean }) {
        if (noLimit) {
            return <div className="font-medium">No Limit</div>
        }

        const percentage = (current / max) * 100
        return (
            <div className="space-y-2">
                <Progress value={percentage} className="h-2" />
                <div className="flex justify-between text-sm text-gray-500">
                    <span>{current.toLocaleString()} EUR</span>
                    <span>{max.toLocaleString()} EUR</span>
                </div>
            </div>
        )
    }

    return (
        <Sheet open={isOpen} onOpenChange={handleSheetOpenChange}>
            <SheetContent side="right" className="w-[55%]  bg-white  sm:max-w-none max-h-screen overflow-y-auto">
                <SheetHeader>
                    <SheetTitle className="text-xl font-semibold text-primary">
                        **** {card?.cardMask.slice(-4,card?.cardMask.length )} - {card?.embossName1}
                    </SheetTitle>
                    <SheetDescription>
                        View and manage card information. &nbsp;
                        {card?.status && <Badge className={getStatusColor(card?.status)}>{card?.status}</Badge>}
                    </SheetDescription>
                </SheetHeader>
                {card ? (
                    <CardManagement companyId={companyId} card={card} onApiSuccess={fetchCardDetails} />
                ) : (
                    <LoadingOverlay />
                )}
            </SheetContent>
        </Sheet>
    )

    function LoadingSkeleton() {
        return (
            <div className="min-h-screen   p-4 md:p-8">
                <div className="mx-auto max-w-6xl space-y-8">
                    <Skeleton className="h-8 bg-white w-3/4" />
                    <div className="space-y-6">
                        <Skeleton className="h-10 bg-white w-full" />
                        <div className="grid gap-6 md:grid-cols-2">
                            <Skeleton className="h-64 bg-white w-full" />
                            <Skeleton className="h-64 bg-white w-full" />
                        </div>
                        <Skeleton className="h-64 bg-white w-full" />
                        <Skeleton className="h-64 bg-white w-full" />
                    </div>
                </div>
            </div>
        )
    }
}

