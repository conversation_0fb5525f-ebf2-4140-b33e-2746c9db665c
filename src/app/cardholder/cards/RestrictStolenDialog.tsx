import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import axiosInstance from "@/utils/axiosInstance"
import { Loader2 } from "lucide-react"

interface RestrictStolenDialogProps {
    cardId: string
    onApiSuccess: () => void
}

export function RestrictStolenDialog({ cardId, onApiSuccess }: RestrictStolenDialogProps) {
    const [loading, setLoading] = useState(false)

    const handleRestrictStolen = async () => {
        try {
            setLoading(true)
            const response = await axiosInstance.post(`cards/${cardId}/restrictStolen`)

            if (typeof onApiSuccess === "function") {
                onApiSuccess()
            }
        } catch (error) {
            console.error("Failed to restrict stolen card", error)
            alert("Failed to restrict stolen card. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant="destructive">Report Stolen</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Report Card as Stolen</DialogTitle>
                    <DialogDescription>
                        Are you sure you want to report this card as stolen? This action will immediately block all transactions on
                        this card to prevent unauthorized use.
                    </DialogDescription>
                </DialogHeader>

                <DialogFooter>
                    <Button type="submit" onClick={handleRestrictStolen} disabled={loading} variant="destructive">
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Reporting...
                            </>
                        ) : (
                            "Report Stolen"
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

