import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    <PERSON>alogHeader,
    <PERSON>alogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import axiosInstance from "@/utils/axiosInstance"
import {ArrowRight, ArrowRightIcon, ArrowRightToLine, ChevronRight, Loader2, RefreshCcw} from "lucide-react"
import {AiOutlineAlignRight} from "react-icons/ai";

interface ReplaceCardDialogProps {
    cardId: string
    expDate: string
    onApiSuccess: () => void
}

export function ReplaceCardDialog({ cardId,expDate, onApiSuccess }: ReplaceCardDialogProps) {
    const [loading, setLoading] = useState(false)

    const handleReplaceCard = async () => {
        try {
            setLoading(true)
            const data = {
                expDate
            }
            const response = await axiosInstance.post(`cards/${cardId}/replaceCard`,data)

            if (typeof onApiSuccess === "function") {
                onApiSuccess()
            }
        } catch (error) {
            console.error("Failed to replace card", error)
            alert("Failed to replace card. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <RefreshCcw className="h-5 w-5 text-gray-600"/>
                        </div>
                        <span className="font-normal">Replace card</span>
                    </div>
                    <ChevronRight/>
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Replace Card</DialogTitle>
                    <DialogDescription>
                        Are you sure you want to replace this card? This action will deactivate the current card and issue a new
                        one.
                    </DialogDescription>
                </DialogHeader>

                <DialogFooter>
                    <Button type="submit" onClick={handleReplaceCard} disabled={loading}>
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Replacing...
                            </>
                        ) : (
                            "Replace Card"
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

