import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import axiosInstance from "@/utils/axiosInstance"
import {ChevronRight, Loader2, Pencil} from "lucide-react"

interface UpdateEmbossNameDialogProps {
    cardId: string
    onApiSuccess: () => void
    initialEmbossName1?: string
    initialEmbossName2?: string
}

export function UpdateEmbossNameDialog({
                                           cardId,
                                           onApiSuccess,
                                           initialEmbossName1 = "",
                                           initialEmbossName2 = "",
                                       }: UpdateEmbossNameDialogProps) {
    const [loading, setLoading] = useState(false)
    const [embossName1, setEmbossName1] = useState(initialEmbossName1)
    const [embossName2, setEmbossName2] = useState(initialEmbossName2)

    const handleUpdateEmbossName = async () => {
        try {
            setLoading(true)
            const response = await axiosInstance.post(`cards/${cardId}/changeName`, {
                embossName1,
                embossName2,
            })

            if (typeof onApiSuccess === "function") {
                onApiSuccess()
            }
        } catch (error) {
            console.error("Failed to update emboss name", error)
            alert("Failed to update emboss name. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <Pencil className="h-5 w-5 text-gray-600"/>
                        </div>
                        <span className="font-normal">Update Card Name</span>
                    </div>
                    <ChevronRight/>
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Update Emboss Name</DialogTitle>
                    <DialogDescription>
                        Enter the new emboss names for the card. These names will be printed on the card.
                    </DialogDescription>
                </DialogHeader>

                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="embossName1" className="text-right">
                            Emboss Name 1 (Max: 26)
                        </Label>
                        <Input
                            id="embossName1"
                            maxLength={26}
                            value={embossName1}
                            onChange={(e) => setEmbossName1(e.target.value)}
                            className="col-span-3"
                        />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="embossName2" className="text-right">
                            Emboss Name 2 (Max: 19)
                        </Label>
                        <Input
                            id="embossName2"
                            maxLength={19}
                            value={embossName2}
                            onChange={(e) => setEmbossName2(e.target.value)}
                            className="col-span-3"
                        />
                    </div>
                </div>

                <DialogFooter>
                    <Button type="submit" onClick={handleUpdateEmbossName} disabled={loading}>
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Updating...
                            </>
                        ) : (
                            "Update Emboss Name"
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

