//@ts-nocheck
"use client"

import {useEffect, useState} from "react"
import {Button} from "@/components/ui/button"
import {ChevronRight, CreditCard, List} from "lucide-react"
import {CardDetailsModal} from "./card-details-modal"
import {CardControlsModal} from "./card-controls-modal"
import {Card, CardContent} from "@/components/ui/card"
import Image from "next/image"
import Link from "next/link"
import {LockCardDialog} from "@/app/cardholder/cards/lock-card"
import {UnLockCardDialog} from "@/app/cardholder/cards/unlock-card"
import {ReplaceCardDialog} from "@/app/cardholder/cards/ReplaceCardDialog"
import {RestrictCardDialog} from "@/app/cardholder/cards/card/RestrictCardDialog";
import {ResignCardDialog} from "@/app/cardholder/cards/ResignCardDialog";
import {ResetPinTriesDialog} from "@/app/cardholder/cards/ResetPinTriesDialog";
import {UpdateEmbossNameDialog} from "@/app/cardholder/cards/UpdateEmbossNameDialog";
import {ForcePinLockDialog} from "@/app/cardholder/cards/ForcePinLockDialog";
import {Change3DSecureDialog} from "@/app/cardholder/cards/card/Change3DSecureDialog";
import {SetCardLimitsDialog} from "@/app/cardholder/cards/card/SetCardLimitsDialog";
import {PinManagementDialog} from "@/app/cardholder/cards/card/pin-management-dialog";
import {ActivateCardDialog} from "@/app/cardholder/cards/ActivateCardDialog";
import {Tabs} from "flowbite-react";
import {Table, TableBody, TableCell, TableHeader, TableRow} from "@/components/ui/table";
import axios from "@/utils/axiosInstance";
import {CardImage} from "@types/types";
import {LoadingOverlay} from "@/components/LoadingOverlay";
import {OTPDialog} from "@/app/cardholder/cards/card/otp-modal";


interface CardData {
    cardHash: string
    cardMask: string
    cardKey: string
    cardUuid: string
    expDate: string
    issueDate: string
    status: string
    statusCode: string
    terminating: boolean
    productCode: string
    productDesc: string
    holder: string
    accounts: Array<{
        accNo: string
        primary: boolean
        currencyCode: string
        currencyName: string
    }>
    embossName1: string
    source: string
    kind: string
    main: boolean
    limits: Array<{
        name: string
        value: number
        noLimit: boolean
    }>
    visual: string
    tokenized: boolean
    delivery: {
        deliveryType: string
    }
    contactless: boolean
    cardTechnologyMode: string
    creationDate: string
    autoRenewal: boolean
}

interface CardDetailsModalProps {
    card: CardData
    companyId: string
    onApiSuccess: () => {}
}
const asset = process.env.NEXT_PUBLIC_API_URL
export function CardManagement({card, onApiSuccess, companyId}: CardDetailsModalProps) {
    const [showCardDetails, setShowCardDetails] = useState(false)
    const [cardImages, setCardImages] = useState<CardImage[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [showCardControls, setShowCardControls] = useState(false)
    const [currentImage, setCurrentImage] = useState(null)
    useEffect(() => {
        fetchData()
    }, [])

    const fetchData = async () => {
        try {
            setIsLoading(true)
            setError(null)
            const response = await axios.get<CardImage[]>("/images")
            setCardImages(response.data)


            const images = response.data.filter(r=>r.product_version.version_code === card.productCode  );

            setCurrentImage(images.find(r=>r.company._id === companyId))
        } catch (error) {
            console.error("Error fetching data", error)
            setError("Failed to fetch card images. Please try again later.")
        } finally {
            setIsLoading(false)
        }
    }
    const splitCardMask = (cardMask) => {
        if (cardMask) {
            // Remove any spaces if they exist
            const cleanedMask = cardMask.replace(/\s+/g, "")

            // Split the string into chunks of 4 characters
            const chunks = []
            for (let i = 0; i < cleanedMask.length; i += 4) {
                chunks.push(cleanedMask.slice(i, i + 4))
            }

            return chunks
        }
    }



    function getStatusColor(status: string) {
        switch (status.toUpperCase()) {
            case "ACTIVE":
                return "bg-green-500"
            case "INACTIVE":
                return "bg-yellow-500"
            case "BLOCKED":
                return "bg-red-500"
            case "ORDERED":
                return "bg-blue-500"
            default:
                return "bg-gray-500"
        }
    }


    if(isLoading){
        return  <LoadingOverlay/>
    }

    const cardMaskChunks = splitCardMask(card?.cardMask)
    return (
        <>

            <Tabs className="mt-4">
                <Tabs.item title="Card Details">
                    <div className="max-w-3xl  mx-auto p-6">
                        {/* Card Display and Action Buttons */}
                        <div className="flex mb-8">
                            <div className="grid gap-6 md:grid-cols-1">
                                {/* Card Preview  physical */}
                                {card.cardTechnologyMode.toUpperCase() === "DUAL"   && (
                                    <div className="card-container">
                                        <div className="card h-[510px] ">
                                            {/* Front side */}
                                            <div className="card-face card-front">
                                                <Image
                                                    unoptimized
                                                    src={currentImage?  `${asset}${currentImage.front_side}` : "/cards/physical-front.png"}
                                                    alt={`Front`}
                                                    width={330}
                                                    height={510}
                                                    className="object-cover"
                                                    loading="lazy"
                                                />
                                                <Card
                                                    className="absolute inset-0 bg-cover bg-center w-[330px] h-[510px] from-primary-900/80 to-primary-700/80 text-white backdrop-blur-sm" style={{
                                                    backgroundImage: `url('${currentImage ? asset + currentImage.front_side : "/cards/physical-front.png"}')`
                                                }}>
                                                    <CardContent className="relative z-10 p-6">
                                                        {cardMaskChunks.length > 0 && (
                                                            <>
                                                                <div className="pt-[440px] ml-[200px] text-primary">* *
                                                                    *
                                                                    * {cardMaskChunks[3]} {card.status.toUpperCase()}</div>
                                                            </>
                                                        )}
                                                    </CardContent>
                                                </Card>
                                            </div>

                                            {/* Back side */}
                                            <div className="card-face card-back">
                                                <Image
                                                    src={currentImage?  `${asset}${currentImage.back_side}` : "/cards/back-vp.png"}
                                                    alt={`back`}
                                                    width={330}
                                                    height={510}
                                                    className="object-cover"
                                                />
                                                <Card
                                                    className="absolute inset-0 bg-cover bg-center w-[330px] h-[510px] from-primary-900/80 to-primary-700/80 text-white backdrop-blur-sm  " style={{
                                                    backgroundImage: `url('${currentImage ? asset + currentImage.back_side : "/cards/back-vp.png"}')`
                                                }}>
                                                    <CardContent className="relative z-10 p-6 text-primary">
                                                        <div className="mb-4 h-4"></div>
                                                        <div
                                                            className="mb-4 absolute font-mono tracking-[.5em]  mt-[40px] ml-[200px]">
                                                            {cardMaskChunks.length > 0 && (
                                                                <div>
                                                                    <div className="mb-3">{cardMaskChunks[0]}</div>

                                                                    <div className="mb-3">{cardMaskChunks[1]}</div>

                                                                    <div className="mb-4">{cardMaskChunks[2]}</div>

                                                                    <div className="mb-3">{cardMaskChunks[3]}</div>
                                                                </div>
                                                            )}
                                                        </div>
                                                        <div className="flex justify-between">
                                                            <div>
                                                                <div
                                                                    className="mt-[190px] ml-[220px]">{card.expDate}</div>
                                                            </div>
                                                            <div>
                                                                <div
                                                                    className=" mt-[250px] ml-[-100px] absolute font-bold">{card.embossName1}</div>
                                                            </div>
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                            </div>
                                        </div>
                                    </div>
                                )}
                                {/* Card Preview  VIRTUAL */}
                                {card.cardTechnologyMode.toUpperCase() === "VIRTUAL" &&     (

                                        <div className="card-container">
                                            <div className="card h-[210px]">
                                                {/* Front side */}
                                                <div className="card-face card-front">
                                                    <Image
                                                        src={currentImage?  `${asset}${currentImage.front_side}` : "/cards/pp-fd.svg"}
                                                        alt={`Front`}
                                                        unoptimized
                                                        loading="lazy"
                                                        width={330}
                                                        height={210}
                                                        className="object-cover"
                                                    />
                                                    <Card
                                                        className="absolute inset-0 bg-cover bg-center w-[330px] h-[210px] from-primary-900/80 to-primary-700/80 text-white backdrop-blur-sm"
                                                        style={{
                                                            backgroundImage: `url('${currentImage ? asset + currentImage.front_side : "/cards/pp-fd.svg"}')`
                                                        }}>
                                                        <CardContent className="relative z-10 p-6">
                                                            {cardMaskChunks.length > 0 && (
                                                                <>
                                                                    <div className="pt-[150px] text-primary">* * *
                                                                        * {cardMaskChunks[3]}</div>
                                                                </>
                                                            )}
                                                        </CardContent>
                                                    </Card>
                                                </div>

                                                {/* Back side */}
                                                <div className="card-face card-back">
                                                    <Image
                                                        src={currentImage?  `${asset}${currentImage.back_side}` : "/cards/pp-bd.svg"}
                                                        alt={`back`}
                                                        width={330}
                                                        height={210}
                                                        unoptimized
                                                        loading="lazy"
                                                        className="object-cover"
                                                    />
                                                    <Card
                                                        className="absolute inset-0 bg-cover bg-center w-[330px] h-[210px] from-primary-900/80 to-primary-700/80 text-white backdrop-blur-sm"
                                                        style={{
                                                            backgroundImage: `url('${currentImage ? asset + currentImage.back_side : "/cards/pp-fd.svg"}')`
                                                        }}>
                                                        <CardContent className="relative z-10 p-6 text-primary">
                                                            <div className="mb-4 h-4"></div>
                                                            <div
                                                                className="mb-4 font-mono tracking-[.5em]">{card.cardMask}</div>
                                                            <div className="flex justify-between">
                                                                <div>
                                                                    <div className="text-sm opacity-80">Expiry</div>
                                                                    <div>{card.expDate}</div>
                                                                </div>
                                                                <div>
                                                                    <div className="text-sm opacity-80">Holder</div>
                                                                    <div>{card.embossName1}</div>
                                                                </div>
                                                            </div>
                                                        </CardContent>
                                                    </Card>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                            </div>

                            {/* Action Buttons */}
                            <div className="flex justify-start items-center ml-8 space-x-4">
                                {/*<VerifyPinDialog cardId={card.cardKey}/>*/}

                                <Button
                                    variant="ghost"
                                    className="flex flex-col items-center gap-2 h-auto py-3 hover:bg-green-50"
                                    onClick={() => setShowCardDetails(true)}
                                >
                                    <div
                                        className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                                        <CreditCard className="h-6 w-6 text-green-600"/>
                                    </div>
                                    <span className="text-sm">Card details</span>
                                </Button>

                                {card.status.toUpperCase() === "BLOCKED" ? (
                                    <UnLockCardDialog
                                        onApiSuccess={() => {
                                            onApiSuccess(card.cardKey)
                                        }}
                                        expDate={card.expDate}
                                        cardId={card.cardKey}
                                    />
                                ) : (
                                    card.status.toUpperCase() === "ACTIVE" && (
                                        <LockCardDialog
                                            onApiSuccess={() => {
                                                onApiSuccess(card.cardKey)
                                            }}
                                            expDate={card.expDate}
                                            cardId={card.cardKey}
                                        />
                                    )
                                )}
                                <Link href={card.cardKey + "/transaction"}>
                                <Button variant="ghost"
                                        className="flex flex-col items-center gap-2 h-auto py-3 hover:bg-green-50">
                                    <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                                        <List className="h-6 w-6 text-green-600"/>
                                    </div>
                                    <span className="text-sm">Transactions</span>
                                </Button>
                                </Link>
                            </div>
                        </div>

                        {/* Manage Card Section */}
                        <div className="space-y-4">
                            <h2 className="text-sm font-medium text-gray-500">Manage card</h2>

                            <div className="space-y-1 -mx-2">



                                {card.status.toUpperCase() === "ACTIVE" ? (
                                    <>
                                        {/*<ChangePinDialog*/}
                                        {/*    cardId={card.cardKey}*/}
                                        {/*    expDate={card.expDate}*/}
                                        {/*    onApiSuccess={() => {*/}
                                        {/*        onApiSuccess(card.cardKey).then(r => {*/}
                                        {/*            alert("Pin Changed Successfully");*/}
                                        {/*        })*/}
                                        {/*    }}*/}
                                        {/*/>*/}

                                        <PinManagementDialog cardId={card.cardKey} expDate={card.expDate}
                                                             onApiSuccess={() => {
                                                                 onApiSuccess(card.cardKey).then(r => {
                                                                     alert("Pin Changed Successfully");
                                                                 })
                                                             }}/>

                                        <SetCardLimitsDialog cardId={card.cardKey}
                                                             onApiSuccess={() => {
                                                                 onApiSuccess(card.cardKey).then(r => {
                                                                     alert("Limit Updated Successfully");
                                                                 })
                                                             }}/>






                                        {/*<ForcePinLockDialog*/}
                                        {/*    cardId={card.cardKey}*/}
                                        {/*    onApiSuccess={() => {*/}
                                        {/*        onApiSuccess(card.cardKey).then(r => {*/}
                                        {/*            alert("Pin Tries Reset Successfully");*/}
                                        {/*        })*/}
                                        {/*    }}*/}
                                        {/*/>*/}
                                        <ResignCardDialog
                                            cardId={card.cardKey}
                                            expDate={card.expDate}
                                            onApiSuccess={() => {
                                                onApiSuccess(card.cardKey).then(r => {
                                                    alert("Card resigned successfully");
                                                })
                                            }}
                                        />

                                        {card.cardTechnologyMode.toLowerCase() !== "virtual"&&(
                                            <UpdateEmbossNameDialog
                                                cardId={card.cardKey}
                                                expDate={card.expDate}
                                                onApiSuccess={() => {
                                                    onApiSuccess(card.cardKey).then(r => {
                                                        alert("Card name updated successfully");
                                                    })
                                                }}
                                            />
                                        )}



                                        {card.cardTechnologyMode.toUpperCase() !== "VIRTUAL" &&(
                                            <RestrictCardDialog
                                                onApiSuccess={() => {
                                                    onApiSuccess(card.cardKey)
                                                }}
                                                expDate={card.expDate}
                                                cardId={card.cardKey}
                                            />
                                        ) }
                                        <ReplaceCardDialog
                                            onApiSuccess={() => {
                                                onApiSuccess(card.cardKey)
                                            }}
                                            expDate={card.expDate}
                                            cardId={card.cardKey}
                                        />
                                    </>
                                ) : (


                                    card.cardTechnologyMode.toUpperCase() === "DUAL" && (
                                        <ActivateCardDialog
                                            cardId={card.cardKey}
                                            expDate={card.expDate}
                                            onApiSuccess={() => {
                                                onApiSuccess(card.cardKey).then(r => {
                                                    alert("Card Activated Successfully");
                                                })
                                            }}
                                        />
                                    )


                                )}

                            </div>
                        </div>
                    </div>
                </Tabs.item>

                <Tabs.item title="3DS Settings">
                    <Change3DSecureDialog
                        cardId={card.cardKey}
                        expDate={card.expDate}
                        onApiSuccess={() => {
                            onApiSuccess(card.cardKey).then(r => {
                                alert("3D Answer changed Successfully");
                            })
                        }}
                    />
                <Table>
                    <TableBody>
                        <TableRow>
                            <TableCell className="font-medium">3Ds Phone Number</TableCell>
                            <TableCell>
                                (*************
                               </TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell className="font-medium">3Ds Password</TableCell>
                            <TableCell>****</TableCell>
                        </TableRow>

                        <TableRow>
                            <TableCell className="font-medium"></TableCell>
                            <TableCell>
                                <Button>Change Phone Number</Button> &nbsp;
                                <Button>Change Password</Button>
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
                </Tabs.item>
                <Tabs.item title="3DS OTP">
                    <OTPDialog
                        cardId={card.cardKey}
                        expDate={card.expDate}
                        onApiSuccess={() => {
                            onApiSuccess(card.cardKey).then(r => {
                                alert("3D Answer changed Successfully");
                            })
                        }}
                    />
                </Tabs.item>
            </Tabs>



            <CardDetailsModal open={showCardDetails} onOpenChange={setShowCardDetails} card={card}/>
            <CardControlsModal open={showCardControls} onOpenChange={setShowCardControls} card={card}/>
        </>
    )
}

