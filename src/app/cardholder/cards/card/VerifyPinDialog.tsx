//@ts-nocheck
"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {LockKeyhole, ChevronRight, Grid3x3, Key} from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"

interface VerifyPinDialogProps {
    cardId: string
}

export function VerifyPinDialog({ cardId }: VerifyPinDialogProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [loading, setLoading] = useState(false)
    const [pin, setPin] = useState("")
    const [apiResponse, setApiResponse] = useState<string | null>(null)

    const handleVerifyPin = async () => {
        try {
            setLoading(true)
            const response = await axiosInstance.post(`pin/${cardId}/auth-pin`, { pin })
            setApiResponse(response.data.apiResponse.holderCode )
            setPin("")
        } catch (error) {
            console.error("Failed to verify PIN", error)
            setApiResponse("Failed to verify PIN. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    const resetDialog = () => {
        setPin("")
        setApiResponse(null)
        setIsOpen(false)
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="ghost" className="w-full justify-between px-4 py-2 h-auto hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <LockKeyhole className="h-5 w-5 text-gray-600" />
                        </div>
                        <span className="font-normal">Verify Pin</span>
                    </div>
                    <ChevronRight />
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Verify PIN</DialogTitle>
                    <DialogDescription>
                        {apiResponse ? "PIN verification result" : "Enter your PIN to verify it."}
                    </DialogDescription>
                </DialogHeader>
                {!apiResponse ? (
                    <>
                        <div className="grid gap-4 py-4">
                            <Input
                                id="pin"
                                type="password"
                                placeholder="Enter PIN"
                                value={pin}
                                onChange={(e) => setPin(e.target.value)}
                                maxLength={4}
                                pattern="\d{4}"
                            />
                        </div>
                        <DialogFooter>
                            <Button variant="outline" onClick={resetDialog}>
                                Cancel
                            </Button>
                            <Button onClick={handleVerifyPin} disabled={loading || pin.length !== 4}>
                                {loading ? "Verifying..." : "Verify PIN"}
                            </Button>
                        </DialogFooter>
                    </>
                ) : (
                    <>
                        <div className="py-4">
                            <p className="text-center">{apiResponse}</p>
                        </div>
                        <DialogFooter>
                            <Button onClick={resetDialog}>Close</Button>
                        </DialogFooter>
                    </>
                )}
            </DialogContent>
        </Dialog>
    )
}

