import React, { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import axiosInstance from "@/utils/axiosInstance"
import { Loader2 } from "lucide-react"

interface CloseCardDialogProps {
    cardId: string
    expDate: string;
    onApiSuccess?: () => void
}

export function CloseCardDialog({ cardId,expDate, onApiSuccess }: CloseCardDialogProps) {
    const [loading, setLoading] = useState(false)
    const [isOpen, setIsOpen] = useState(false)

    const handleCloseCard = async () => {
        try {
            setLoading(true)

            const data = {
                expDate
            }
            const response = await axiosInstance.post(`cards/${cardId}/close`,data)
            console.log("Card closed successfully:", response.data)
            if (typeof onApiSuccess === "function") {
                onApiSuccess()
            }
            alert("Card closed successfully.")
            setIsOpen(false)
        } catch (error) {
            console.error("Failed to close card:", error)
            alert("Failed to close card. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="destructive">Close Card</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Close Card</DialogTitle>
                    <DialogDescription>Do you really want to close this card? This action cannot be undone.</DialogDescription>
                </DialogHeader>

                <DialogFooter>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Cancel
                    </Button>
                    <Button variant="destructive" onClick={handleCloseCard} disabled={loading}>
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Closing...
                            </>
                        ) : (
                            "Close Card"
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

