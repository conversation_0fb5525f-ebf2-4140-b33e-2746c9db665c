//@ts-nocheck
"use client"
import { useEffect, useState } from "react"
import { useAppSelector } from "@/store/hooks"
import IndividualOnboardingPage from "@/app/lite/admin/individual/v1/[id]/page";

export default function CardholderProfile() {
    const user = useAppSelector((state) => state.user.user)
    return <IndividualOnboardingPage params={{
        id: user.recordId
    }}/>
}
// "use client"
//
// import { useEffect, useState } from "react"
// import { useAppSelector } from "@/store/hooks"
// import axiosInstance from "@/utils/axiosInstance"
// import { formatDate } from "@/utils/helpers"
// import { Loader2, User, CreditCard, MapPin, AlertCircle, CheckCircle } from "lucide-react"
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
// import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table"
// import { Badge } from "@/components/ui/badge"
// import { Button } from "@/components/ui/button"
// import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
// import { Progress } from "@/components/ui/progress"
// import { motion } from "framer-motion"
// import {LoadingOverlay} from "@/components/LoadingOverlay";
//
// export default function IndividualOnboardingPage() {
//     const [onboarding, setOnboarding] = useState<any>(null)
//     const [account, setAccount] = useState<any>(null)
//     const [loading, setLoading] = useState(true)
//     const [activeTab, setActiveTab] = useState("summary")
//     const user = useAppSelector((state) => state.user.user)
//
//     useEffect(() => {
//         fetchOnboardingDetails()
//     }, [])
//
//     const fetchOnboardingDetails = async () => {
//         try {
//             if (user.recordId !== null) {
//                 const response = await axiosInstance.get(`onboarding/personal/${user.recordId}`)
//                 setOnboarding(response.data.data)
//                 setAccount(response.data.account[0])
//             }
//         } catch (error) {
//             console.error("Error fetching onboarding details:", error)
//         } finally {
//             setLoading(false)
//         }
//     }
//
//     if (loading) {
//         return (
//             <LoadingOverlay/>
//         )
//     }
//
//     if (!onboarding) {
//         return (
//             <Card className="w-full max-w-md mx-auto mt-8">
//                 <CardContent className="py-10">
//                     <AlertCircle className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
//                     <p className="text-center text-lg font-medium text-muted-foreground">No Onboarding Details found.</p>
//                 </CardContent>
//             </Card>
//         )
//     }
//
//     const tabContent = {
//         summary: <SummaryTab onboarding={onboarding} account={account} />,
//         personal: <PersonalDetailsTab onboarding={onboarding} />,
//         banking: <BankingDetailsTab account={account} onboarding={onboarding} />,
//     }
//
//     return (
//         <div className="flex h-screen bg-gray-100">
//             <aside className="w-64 bg-white shadow-md">
//                 <div className="p-6">
//                     <Avatar className="h-16 w-16 mx-auto mb-4">
//                         <AvatarImage
//                             src={onboarding.personalInfo.avatarUrl}
//                             alt={`${onboarding.personalInfo.firstName} ${onboarding.personalInfo.lastName}`}
//                         />
//                         <AvatarFallback>
//                             {onboarding.personalInfo.firstName[0]}
//                             {onboarding.personalInfo.lastName[0]}
//                         </AvatarFallback>
//                     </Avatar>
//                     <h2 className="text-xl font-bold text-center mb-1">
//                         {onboarding.personalInfo.firstName} {onboarding.personalInfo.lastName}
//                     </h2>
//                     <p className="text-sm text-muted-foreground text-center mb-6">{onboarding.personalInfo.email}</p>
//                     <nav>
//                         <Button
//                             variant={activeTab === "summary" ? "default" : "ghost"}
//                             className="w-full justify-start mb-2"
//                             onClick={() => setActiveTab("summary")}
//                         >
//                             <CheckCircle className="mr-2 h-4 w-4" /> Summary
//                         </Button>
//                         <Button
//                             variant={activeTab === "personal" ? "default" : "ghost"}
//                             className="w-full justify-start mb-2"
//                             onClick={() => setActiveTab("personal")}
//                         >
//                             <User className="mr-2 h-4 w-4" /> Personal Details
//                         </Button>
//                         <Button
//                             variant={activeTab === "banking" ? "default" : "ghost"}
//                             className="w-full justify-start"
//                             onClick={() => setActiveTab("banking")}
//                         >
//                             <CreditCard className="mr-2 h-4 w-4" /> Banking Details
//                         </Button>
//                     </nav>
//                 </div>
//             </aside>
//             <main className="flex-1 p-8 overflow-y-auto">
//                 <motion.div
//                     key={activeTab}
//                     initial={{ opacity: 0, y: 20 }}
//                     animate={{ opacity: 1, y: 0 }}
//                     transition={{ duration: 0.3 }}
//                 >
//                     <h1 className="text-3xl font-bold mb-6">Banking Customer Application</h1>
//                     {tabContent[activeTab]}
//                 </motion.div>
//             </main>
//         </div>
//     )
// }
//
// function SummaryTab({ onboarding, account }) {
//     const completionPercentage = 80 // This should be calculated based on the actual completion status
//
//     return (
//         <div className="grid gap-6 md:grid-cols-2">
//             <Card>
//                 <CardHeader>
//                     <CardTitle>Onboarding Progress</CardTitle>
//                     <CardDescription>Your application is almost complete</CardDescription>
//                 </CardHeader>
//                 <CardContent>
//                     <Progress value={completionPercentage} className="mb-2" />
//                     <p className="text-sm text-muted-foreground">{completionPercentage}% Complete</p>
//                 </CardContent>
//             </Card>
//             <Card>
//                 <CardHeader>
//                     <CardTitle>Account Status</CardTitle>
//                     <CardDescription>Current status of your account</CardDescription>
//                 </CardHeader>
//                 <CardContent>
//                     <Badge
//                         variant="outline"
//                         className={account?.status === "Active" ? "bg-green-500 text-white" : "bg-yellow-500 text-white"}
//                     >
//                         {account?.status || "Not Available"}
//                     </Badge>
//                 </CardContent>
//             </Card>
//             <Card>
//                 <CardHeader>
//                     <CardTitle>Personal Information</CardTitle>
//                 </CardHeader>
//                 <CardContent>
//                     <div className="flex items-center mb-2">
//                         <User className="mr-2 h-4 w-4 text-muted-foreground" />
//                         <p>
//                             {onboarding.personalInfo.firstName} {onboarding.personalInfo.lastName}
//                         </p>
//                     </div>
//                     <div className="flex items-center mb-2">
//                         <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
//                         <p>
//                             {onboarding.address.city}, {onboarding.address.country}
//                         </p>
//                     </div>
//                 </CardContent>
//             </Card>
//             <Card>
//                 <CardHeader>
//                     <CardTitle>Banking Information</CardTitle>
//                 </CardHeader>
//                 <CardContent>
//                     <div className="flex items-center mb-2">
//                         <CreditCard className="mr-2 h-4 w-4 text-muted-foreground" />
//                         <p>IBAN: {account?.accNo || "Not Available"}</p>
//                     </div>
//                     <div className="flex items-center">
//                         <AlertCircle className="mr-2 h-4 w-4 text-muted-foreground" />
//                         <p>Currency: {account?.currencyName || "Not Specified"}</p>
//                     </div>
//                 </CardContent>
//             </Card>
//         </div>
//     )
// }
//
// function PersonalDetailsTab({ onboarding }) {
//     return (
//         <Card>
//             <CardHeader>
//                 <CardTitle>Personal Details</CardTitle>
//                 <CardDescription>Your personal information on file</CardDescription>
//             </CardHeader>
//             <CardContent>
//                 <Table>
//                     <TableBody>
//                         <TableRow>
//                             <TableCell className="font-medium">Full Name</TableCell>
//                             <TableCell>
//                                 {onboarding.personalInfo.firstName} {onboarding.personalInfo.middleName}{" "}
//                                 {onboarding.personalInfo.lastName}
//                             </TableCell>
//                         </TableRow>
//                         <TableRow>
//                             <TableCell className="font-medium">Date of Birth</TableCell>
//                             <TableCell>{formatDate(onboarding.personalInfo.dateOfBirth)}</TableCell>
//                         </TableRow>
//                         <TableRow>
//                             <TableCell className="font-medium">Email</TableCell>
//                             <TableCell>{onboarding.personalInfo.email}</TableCell>
//                         </TableRow>
//                         <TableRow>
//                             <TableCell className="font-medium">Phone Number</TableCell>
//                             <TableCell>{onboarding.personalInfo.phone}</TableCell>
//                         </TableRow>
//                         <TableRow>
//                             <TableCell className="font-medium">Address</TableCell>
//                             <TableCell>
//                                 {onboarding.address.streetAddress}
//                                 {onboarding.address.building && `, Building ${onboarding.address.building}`}
//                                 {onboarding.address.apartment && `, Apt ${onboarding.address.apartment}`}
//                                 <br />
//                                 {onboarding.address.city}, {onboarding.address.stateProvince} {onboarding.address.postalCode}
//                                 <br />
//                                 {onboarding.address.country}
//                             </TableCell>
//                         </TableRow>
//                     </TableBody>
//                 </Table>
//             </CardContent>
//         </Card>
//     )
// }
//
// function BankingDetailsTab({ account, onboarding }) {
//     return (
//         <Card>
//             <CardHeader>
//                 <CardTitle>Banking Details</CardTitle>
//                 <CardDescription>Your banking information on file</CardDescription>
//             </CardHeader>
//             <CardContent>
//                 <Table>
//                     <TableBody>
//                         <TableRow>
//                             <TableCell className="font-medium">Bank Name</TableCell>
//                             <TableCell>RYVYL EU</TableCell>
//                         </TableRow>
//                         <TableRow>
//                             <TableCell className="font-medium">IBAN</TableCell>
//                             <TableCell>**********************</TableCell>
//                         </TableRow>
//                         <TableRow>
//                             <TableCell className="font-medium">Account Currency</TableCell>
//                             <TableCell>{account?.currencyName || "Not Specified"}</TableCell>
//                         </TableRow>
//                         <TableRow>
//                             <TableCell className="font-medium">SWIFT Code</TableCell>
//                             <TableCell>TRUDBG21</TableCell>
//                         </TableRow>
//                         <TableRow>
//                             <TableCell className="font-medium">Account Name</TableCell>
//                             <TableCell>
//                                 {onboarding.personalInfo.firstName} {onboarding.personalInfo.lastName}
//                             </TableCell>
//                         </TableRow>
//                         <TableRow>
//                             <TableCell className="font-medium">Account Status</TableCell>
//                             <TableCell>
//                                 <Badge
//                                     variant="outline"
//                                     className={account?.status === "Active" ? "bg-green-500 text-white" : "bg-yellow-500 text-white"}
//                                 >
//                                     {account?.status || "Not Available"}
//                                 </Badge>
//                             </TableCell>
//                         </TableRow>
//                     </TableBody>
//                 </Table>
//             </CardContent>
//         </Card>
//     )
// }
//
