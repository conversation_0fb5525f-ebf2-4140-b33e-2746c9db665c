//@ts-nocheck
"use client"

import React, {useEffect, useState} from "react"
import {useAppSelector} from "@/store/hooks"
import axiosInstance from "@/utils/axiosInstance"
import syncTokenToCookie from "@/utils/axiosInstance"
import {calculateRiskLevel, formatDate} from "@/utils/helpers"
import {Activity, AlertTriangle, ChevronDown, Copy, CreditCard, Paperclip, Plus, User, X,} from "lucide-react"
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card"
import {Table, TableBody, TableCell, TableHead, TableRow} from "@/components/ui/table"
import {Badge} from "@/components/ui/badge"
import {Button} from "@/components/ui/button"
import {Collapsible, CollapsibleContent, CollapsibleTrigger} from "@/components/ui/collapsible"
import {ScrollArea} from "@/components/ui/scroll-area"
import {LoadingOverlay} from "@/components/LoadingOverlay";
import CardholderProfile from "@/app/cardholder/profile/page";
import {country_currency} from "@/utils/data";

export default function Dashboard() {
    const [onboarding, setOnboarding] = useState<any>(null)
    const [account, setAccount] = useState<any>(null)
    const [cards, setCards] = useState<any[]>([])
    const [loading, setLoading] = useState(true)
    const user = useAppSelector((state) => state.user.user)
    const [accountBalance, setBalance] = useState(0.00)
    useEffect(() => {
        fetchOnboardingDetails()
    }, [])


    const fetchOnboardingDetails = async () => {
        try {
            if (user.recordId !== null) {
                const response = await axiosInstance.get(`onboarding/personal/${user.recordId}`)

                setOnboarding(response.data.data)
                setAccount(response.data.account[0])
                setCards(response.data.cards || [])

            }
        } catch (error) {
            console.error("Error fetching onboarding details:", error)
        } finally {
            setLoading(false)
        }
    }
    useEffect(() => {
        // Sync existing localStorage token to cookie for server-side access
        syncTokenToCookie()
    }, [])
    if (loading) {
        return (
            <LoadingOverlay/>
        )
    }

    if (!onboarding) {
        return (
            <Card className="w-full max-w-md mx-auto mt-8">
                <CardContent className="py-10">
                    <AlertTriangle className="h-16 w-16 text-yellow-500 mx-auto mb-4"/>
                    <p className="text-center text-lg font-medium text-muted-foreground">No Onboarding Details
                        found.</p>
                </CardContent>
            </Card>
        )
    }

    return (
        <div className="container mx-auto px-4 py-8">
            {/*<h1 className="text-3xl font-bold mb-6">Cardholders</h1>*/}

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-6">



                {/*<Card>*/}
                {/*    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">*/}
                {/*        <CardTitle className="text-sm font-medium">Account Balance</CardTitle>*/}
                {/*        <CreditCard className="h-4 w-4 text-muted-foreground"/>*/}
                {/*    </CardHeader>*/}
                {/*    <CardContent>*/}
                {/*        <div className="text-2xl font-bold">*/}
                {/*            {  Intl.NumberFormat('en-US', {*/}
                {/*                style: 'currency',*/}
                {/*                currency: country_currency.find(r=>r.numericCode===account.accountCurrency)?.currencyCode || 'EUR'*/}
                {/*            }).format(accountBalance)}*/}
                {/*        </div>*/}
                
                {/*        <div className="flex flex-col">*/}
                {/*            <span className="text-sm text-muted-foreground">IBAN: </span>*/}
                {/*            <span className="text-sm text-muted-foreground">{account.accountNumber}</span>*/}
                {/*        </div>*/}
                {/*    </CardContent>*/}
                {/*</Card>*/}
                {/*<Card>*/}
                {/*    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">*/}
                {/*        <CardTitle className="text-sm font-medium">Total Virtual Cards</CardTitle>*/}
                {/*        <CreditCard className="h-4 w-4 text-muted-foreground"/>*/}
                {/*    </CardHeader>*/}
                {/*    <CardContent>*/}
                {/*        <div className="text-2xl font-bold">3,546</div>*/}
                {/*        <div className="mt-2 grid grid-cols-2 gap-2">*/}
                {/*            <div className="flex flex-col">*/}
                {/*                <span className="text-xs text-muted-foreground">Active</span>*/}
                {/*                <span className="text-sm font-medium text-green-600">2,845</span>*/}
                {/*            </div>*/}
                {/*            <div className="flex flex-col">*/}
                {/*                <span className="text-xs text-muted-foreground">Inactive</span>*/}
                {/*                <span className="text-sm font-medium text-red-600">701</span>*/}
                {/*            </div>*/}
                {/*        </div>*/}
                {/*    </CardContent>*/}
                {/*</Card>*/}
                {/*<Card>*/}
                {/*    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">*/}
                {/*        <CardTitle className="text-sm font-medium">Total Physical Cards</CardTitle>*/}
                {/*        <CreditCard className="h-4 w-4 text-muted-foreground"/>*/}
                {/*    </CardHeader>*/}
                {/*    <CardContent>*/}
                {/*        <div className="text-2xl font-bold">1,892</div>*/}
                {/*        <div className="mt-2 grid grid-cols-2 gap-2">*/}
                {/*            <div className="flex flex-col">*/}
                {/*                <span className="text-xs text-muted-foreground">Active</span>*/}
                {/*                <span className="text-sm font-medium text-green-600">1,456</span>*/}
                {/*            </div>*/}
                {/*            <div className="flex flex-col">*/}
                {/*                <span className="text-xs text-muted-foreground">Inactive</span>*/}
                {/*                <span className="text-sm font-medium text-red-600">436</span>*/}
                {/*            </div>*/}
                {/*        </div>*/}
                {/*    </CardContent>*/}
                {/*</Card>*/}

            </div>

            <CardholderProfile/>
        </div>
    )
}

function DashboardCard({icon, title, value, footer}) {
    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{title}</CardTitle>
                {icon}
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold">{value}</div>
                {footer && <p className="text-xs text-muted-foreground">{footer}</p>}
            </CardContent>
        </Card>
    )
}

