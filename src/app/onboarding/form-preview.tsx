//@ts-nocheck
import type React from "react"

type Address = {
    street: string
    building_number: string
    apartment_number: string
    city: string
    state: string
    postal_code: string
    country: string
}

type FormData = {
    company_name: string
    company_industry: string
    company_number: string
    company_phone: string
    registration_date: string
    contact_name: string
    contact_role: string
    company_email: string
    country_of_incorporation: string
    company_website: string
    type_of_business: string
    card_usage: string
    cardholder_groups: string
    fund_loading: string
    business_sector: string
    regions: string
    countries: string
    business_purpose: string
    card_user_groups: string
    number_of_cards: string
    monthly_loading_value: string
    admin_name: string
    admin_role: string
    admin_email: string
    admin_phone: string
    registered_address: Address
    operational_address: Address
}

export function FormPreview({ formData }: { formData: FormData }) {
    // Helper function to format date
    const formatDate = (dateString: string) => {
        if (!dateString) return "Not provided"
        try {
            return new Date(dateString).toLocaleDateString()
        } catch (e) {
            return dateString
        }
    }

    // Helper function to check if an address is empty
    const isAddressEmpty = (address: Address) => {
        return !Object.values(address).some((value) => value.trim() !== "")
    }

    // Helper function to format address
    const formatAddress = (address: Address) => {
        if (isAddressEmpty(address)) return "Not provided"

        const parts = []
        if (address.building_number) parts.push(address.building_number)
        if (address.apartment_number) parts.push(`Apt ${address.apartment_number}`)
        if (address.street) parts.push(address.street)
        if (address.city) parts.push(address.city)
        if (address.state) parts.push(address.state)
        if (address.postal_code) parts.push(address.postal_code)
        if (address.country) parts.push(address.country)

        return parts.join(", ")
    }

    // Helper function to display a field
    const DisplayField = ({ label, value }: { label: string; value: string | number }) => (
        <div className="py-2">
            <div className="text-sm font-medium text-gray-500">{label}</div>
            <div className="mt-1 text-sm text-gray-900">{value || "Not provided"}</div>
        </div>
    )

    // Helper function to display a section
    const PreviewSection = ({ title, children }: { title: string; children: React.ReactNode }) => (
        <div className="mb-6">
            <h3 className="text-lg font-medium text-primary mb-3 pb-2 border-b">{title}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">{children}</div>
        </div>
    )

    return (
        <div className="space-y-6">
            <PreviewSection title="Company Information">
                <DisplayField label="Company Name" value={formData.company_name} />
                <DisplayField label="Company Industry" value={formData.company_industry} />
                <DisplayField label="Registration Number" value={formData.company_number} />
                <DisplayField label="Company Phone" value={formData.company_phone} />
                <DisplayField label="Registration Date" value={formatDate(formData.registration_date)} />
                <DisplayField label="Contact Name" value={formData.contact_name} />
                <DisplayField label="Contact Role" value={formData.contact_role} />
                <DisplayField label="Company Email" value={formData.company_email} />
                <DisplayField label="Country of Incorporation" value={formData.country_of_incorporation} />
                <DisplayField label="Company Website" value={formData.company_website} />
            </PreviewSection>

            <PreviewSection title="Card Programme Purpose">
                <DisplayField label="Type of Business" value={formData.type_of_business} />
                <DisplayField label="Card Usage" value={formData.card_usage} />
                <DisplayField label="Cardholder Groups" value={formData.cardholder_groups} />
                <DisplayField label="Fund Loading Per Month (€)" value={formData.fund_loading} />
            </PreviewSection>

            <PreviewSection title="Merchant Business Case Details">
                <DisplayField label="Business Sector" value={formData.business_sector} />
                <DisplayField label="Regions of Operation" value={formData.regions} />
                <DisplayField label="Countries of Operation" value={formData.countries} />
                <div className="col-span-2">
                    <DisplayField label="Business Purpose" value={formData.business_purpose} />
                </div>
                <DisplayField label="Card User Groups" value={formData.card_user_groups} />
                <DisplayField label="Number of Cards (Year 1)" value={formData.number_of_cards} />
                <DisplayField label="Monthly Loading Value (EUR)" value={formData.monthly_loading_value} />
                <DisplayField label="Administrator Name" value={formData.admin_name} />
                <DisplayField label="Administrator Role" value={formData.admin_role} />
                <DisplayField label="Administrator Email" value={formData.admin_email} />
                <DisplayField label="Administrator Phone" value={formData.admin_phone} />
            </PreviewSection>

            <PreviewSection title="Company Addresses">
                <div className="col-span-2 md:col-span-1">
                    <div className="text-sm font-medium text-gray-500">Registered Address</div>
                    <div className="mt-1 text-sm text-gray-900">{formatAddress(formData.registered_address)}</div>
                </div>

                <div className="col-span-2 md:col-span-1">
                    <div className="text-sm font-medium text-gray-500">Operational Address</div>
                    <div className="mt-1 text-sm text-gray-900">{formatAddress(formData.operational_address)}</div>
                </div>
            </PreviewSection>
        </div>
    )
}
