//@ts-nocheck
"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle, Info, Loader2 } from "lucide-react"
import axiosInstance from "@/utils/axiosInstance"
import PhoneInput, { isValidPhoneNumber } from "react-phone-number-input"
import "react-phone-number-input/style.css"
import { Checkbox } from "@/components/ui/checkbox"
import { CountrySelector } from "@/components/country-select"

type Address = {
  street: string
  building_number: string
  apartment_number: string
  city: string
  state: string
  postal_code: string
  country: string
}

type FormData = {
  company_name: string
  company_industry: string
  company_number: string
  company_phone: string
  registration_date: string
  contact_name: string
  contact_role: string
  company_email: string
  country_of_incorporation: string
  company_website: string
  type_of_business: string
  card_usage: string
  cardholder_groups: string
  fund_loading: string
  business_sector: string
  regions: string
  countries: string
  business_purpose: string
  card_user_groups: string
  number_of_cards: string
  monthly_loading_value: string
  admin_first_name: string
  admin_last_name: string
  admin_role: string
  admin_email: string
  admin_phone: string
  registered_address: Address
  operational_address: Address
}

// Define validation errors type
type ValidationErrors = {
  [key in keyof FormData]?: string
} & {
  registered_address?: {
    [key in keyof Address]?: string
  }
  operational_address?: {
    [key in keyof Address]?: string
  }
}

const initialFormData: FormData = {
  company_name: "",
  company_industry: "",
  company_number: "",
  company_phone: "",
  registration_date: "",
  contact_name: "",
  contact_role: "",
  company_email: "",
  country_of_incorporation: "",
  company_website: "",
  type_of_business: "",
  card_usage: "",
  cardholder_groups: "",
  fund_loading: "",
  business_sector: "",
  regions: "",
  countries: "",
  business_purpose: "",
  card_user_groups: "",
  number_of_cards: "",
  monthly_loading_value: "",
  admin_first_name: "",
  admin_last_name: "",
  admin_role: "",
  admin_email: "",
  admin_phone: "",
  registered_address: {
    street: "",
    building_number: "",
    apartment_number: "",
    city: "",
    state: "",
    postal_code: "",
    country: "",
  },
  operational_address: {
    street: "",
    building_number: "",
    apartment_number: "",
    city: "",
    state: "",
    postal_code: "",
    country: "",
  },
}

export default function CompanyInformationForm() {
  const [formData, setFormData] = useState<FormData>(initialFormData)
  const [alert, setAlert] = useState<{ type: "success" | "error"; message: string } | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [phoneErrors, setPhoneErrors] = useState({
    company_phone: false,
    admin_phone: false,
  })
  const [useSameAddress, setUseSameAddress] = useState(false)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set())

  useEffect(() => {
    if (useSameAddress) {
      setFormData((prev) => ({
        ...prev,
        operational_address: { ...prev.registered_address },
      }))
    }
  }, [useSameAddress, formData.registered_address])

  // Validate a single field
  const validateField = (name: string, value: any): string => {
    // Email validation
    if (name.includes("email")) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!value) return "Email is required"
      if (!emailRegex.test(value)) return "Please enter a valid email address"
    }

    // URL validation
    if (name === "company_website") {
      if (value) {
        try {
          new URL(value)
        } catch (e) {
          return "Please enter a valid URL (e.g., https://example.com)"
        }
      }
    }

    // Required fields validation
    const requiredFields = [
      "company_name",
      "company_industry",
      "company_number",
      "registration_date",
      "contact_name",
      "contact_role",
      "company_email",
      "country_of_incorporation",
      "type_of_business",
      "business_sector",
      "business_purpose",
      "admin_first_name",
      "admin_last_name",
      "admin_role",
      "admin_email",
    ]

    if (requiredFields.includes(name) && !value) {
      return `${name.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())} is required`
    }

    // Number validation
    const numberFields = ["number_of_cards", "monthly_loading_value", "fund_loading"]
    if (numberFields.includes(name)) {
      if (value && (isNaN(Number(value)) || Number(value) < 0)) {
        return "Please enter a valid positive number"
      }
    }

    // Date validation
    if (name === "registration_date") {
      if (value) {
        const date = new Date(value)
        const today = new Date()
        if (date > today) {
          return "Registration date cannot be in the future"
        }
      }
    }

    // Address validation
    if (name.includes("registered_address.") || name.includes("operational_address.")) {
      const addressField = name.split(".")[1]
      const requiredAddressFields = ["street", "city", "postal_code", "country"]
      if (requiredAddressFields.includes(addressField) && !value) {
        return `${addressField.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())} is required`
      }
    }

    return ""
  }

  // Validate all form fields
  const validateForm = (): ValidationErrors => {
    const newErrors: ValidationErrors = {}

    // Validate top-level fields
    Object.entries(formData).forEach(([key, value]) => {
      if (key !== "registered_address" && key !== "operational_address") {
        const error = validateField(key, value)
        if (error) {
          newErrors[key as keyof FormData] = error
        }
      }
    })

    // Validate registered address
    newErrors.registered_address = {}
    Object.entries(formData.registered_address).forEach(([key, value]) => {
      const error = validateField(`registered_address.${key}`, value)
      if (error) {
        if (!newErrors.registered_address) newErrors.registered_address = {}
        newErrors.registered_address[key as keyof Address] = error
      }
    })

    // Validate operational address (only if not using same address)
    if (!useSameAddress) {
      newErrors.operational_address = {}
      Object.entries(formData.operational_address).forEach(([key, value]) => {
        const error = validateField(`operational_address.${key}`, value)
        if (error) {
          if (!newErrors.operational_address) newErrors.operational_address = {}
          newErrors.operational_address[key as keyof Address] = error
        }
      })
    }

    // Phone validation
    if (!formData.company_phone || !isValidPhoneNumber(formData.company_phone)) {
      newErrors.company_phone = "Please enter a valid phone number"
    }

    if (!formData.admin_phone || !isValidPhoneNumber(formData.admin_phone)) {
      newErrors.admin_phone = "Please enter a valid phone number"
    }

    return newErrors
  }

  // Check if there are any errors
  const hasErrors = (errors: ValidationErrors): boolean => {
    if (
        Object.keys(errors)
            .filter((key) => key !== "registered_address" && key !== "operational_address")
            .some((key) => errors[key as keyof ValidationErrors])
    ) {
      return true
    }

    if (errors.registered_address && Object.keys(errors.registered_address).length > 0) {
      return true
    }

    if (!useSameAddress && errors.operational_address && Object.keys(errors.operational_address).length > 0) {
      return true
    }

    return false
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Mark all fields as touched
    const allFields = new Set<string>()
    Object.keys(formData).forEach((key) => {
      if (key !== "registered_address" && key !== "operational_address") {
        allFields.add(key)
      }
    })
    Object.keys(formData.registered_address).forEach((key) => {
      allFields.add(`registered_address.${key}`)
    })
    if (!useSameAddress) {
      Object.keys(formData.operational_address).forEach((key) => {
        allFields.add(`operational_address.${key}`)
      })
    }
    setTouchedFields(allFields)

    // Validate all fields
    const validationErrors = validateForm()
    setErrors(validationErrors)

    if (hasErrors(validationErrors)) {
      setAlert({ type: "error", message: "Please correct the errors in the form before continuing." })
      // Scroll to the first error
      const firstErrorElement = document.querySelector(".error-message")
      if (firstErrorElement) {
        firstErrorElement.scrollIntoView({ behavior: "smooth", block: "center" })
      }
      return
    }

    if (!isPreviewMode) {
      // If not in preview mode, switch to preview instead of submitting
      setIsPreviewMode(true)
      window.scrollTo(0, 0) // Scroll to top to see the preview
      return
    }

    setIsSubmitting(true)
    try {
      // Try using axios instance first
      let success = false
      try {
        const response = await axiosInstance.post("companies/save", formData)
        if (response.status === 201) {
          success = true
        }
      } catch (axiosError) {
        // If axios fails, try using fetch as fallback
        console.warn("Axios request failed, trying fetch as fallback", axiosError)
        const response = await fetch("/api/companies/save", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(formData),
        })

        if (response.ok) {
          success = true
        }
      }

      if (success) {
        setAlert({ type: "success", message: "Company information saved successfully!" })
        setIsSubmitted(true)
      } else {
        throw new Error("Failed to submit form")
      }
    } catch (error) {
      setAlert({ type: "error", message: "Error submitting form. Please try again." })
      console.error("Error submitting form:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prevData) => ({ ...prevData, [name]: value }))

    // Mark field as touched
    setTouchedFields((prev) => new Set(prev).add(name))

    // Validate field on change
    const error = validateField(name, value)
    setErrors((prev) => ({
      ...prev,
      [name]: error,
    }))
  }

  const handleCountryChange = (name: string, value: string) => {
    setFormData((prevData) => ({ ...prevData, [name]: value }))

    // Mark field as touched
    setTouchedFields((prev) => new Set(prev).add(name))

    // Validate field on change
    const error = validateField(name, value)
    setErrors((prev) => ({
      ...prev,
      [name]: error,
    }))
  }

  const handleAddressChange = (
      e: React.ChangeEvent<HTMLInputElement>,
      addressType: "registered_address" | "operational_address",
  ) => {
    const { name, value } = e.target

    // Mark field as touched
    setTouchedFields((prev) => new Set(prev).add(`${addressType}.${name}`))

    // Validate field
    const error = validateField(`${addressType}.${name}`, value)
    setErrors((prev) => ({
      ...prev,
      [addressType]: {
        ...prev[addressType],
        [name]: error,
      },
    }))

    if (addressType === "registered_address" && useSameAddress) {
      // Update both addresses when changing registered address and checkbox is checked
      setFormData((prev) => ({
        ...prev,
        registered_address: {
          ...prev.registered_address,
          [name]: value,
        },
        operational_address: {
          ...prev.operational_address,
          [name]: value,
        },
      }))
    } else {
      // Normal update for a single address
      setFormData((prev) => ({
        ...prev,
        [addressType]: {
          ...prev[addressType],
          [name]: value,
        },
      }))
    }
  }

  const handleAddressCountryChange = (value: string, addressType: "registered_address" | "operational_address") => {
    // Mark field as touched
    setTouchedFields((prev) => new Set(prev).add(`${addressType}.country`))

    // Validate field
    const error = validateField(`${addressType}.country`, value)
    setErrors((prev) => ({
      ...prev,
      [addressType]: {
        ...prev[addressType],
        country: error,
      },
    }))

    if (addressType === "registered_address" && useSameAddress) {
      // Update both addresses when changing registered address and checkbox is checked
      setFormData((prev) => ({
        ...prev,
        registered_address: {
          ...prev.registered_address,
          country: value,
        },
        operational_address: {
          ...prev.operational_address,
          country: value,
        },
      }))
    } else {
      // Normal update for a single address
      setFormData((prev) => ({
        ...prev,
        [addressType]: {
          ...prev[addressType],
          country: value,
        },
      }))
    }
  }

  const handlePhoneChange = (value: string | undefined, field: "company_phone" | "admin_phone") => {
    setFormData((prevData) => ({ ...prevData, [field]: value || "" }))

    // Mark field as touched
    setTouchedFields((prev) => new Set(prev).add(field))

    // Validate phone
    const isValid = value ? isValidPhoneNumber(value) : false
    setPhoneErrors((prev) => ({ ...prev, [field]: !isValid }))
    setErrors((prev) => ({
      ...prev,
      [field]: !isValid ? "Please enter a valid phone number" : "",
    }))
  }

  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target

    // Mark field as touched
    setTouchedFields((prev) => new Set(prev).add(name))

    // Validate field on blur
    const error = validateField(name, value)
    setErrors((prev) => ({
      ...prev,
      [name]: error,
    }))
  }

  const handleAddressBlur = (
      e: React.FocusEvent<HTMLInputElement>,
      addressType: "registered_address" | "operational_address",
  ) => {
    const { name, value } = e.target

    // Mark field as touched
    setTouchedFields((prev) => new Set(prev).add(`${addressType}.${name}`))

    // Validate field
    const error = validateField(`${addressType}.${name}`, value)
    setErrors((prev) => ({
      ...prev,
      [addressType]: {
        ...prev[addressType],
        [name]: error,
      },
    }))
  }

  const handleCountryBlur = (name: string) => {
    // Mark field as touched
    setTouchedFields((prev) => new Set(prev).add(name))

    // Validate field
    const error = validateField(name, formData[name as keyof FormData])
    setErrors((prev) => ({
      ...prev,
      [name]: error,
    }))
  }

  const handleAddressCountryBlur = (addressType: "registered_address" | "operational_address") => {
    // Mark field as touched
    setTouchedFields((prev) => new Set(prev).add(`${addressType}.country`))

    // Validate field
    const error = validateField(`${addressType}.country`, formData[addressType].country)
    setErrors((prev) => ({
      ...prev,
      [addressType]: {
        ...prev[addressType],
        country: error,
      },
    }))
  }

  if (isSubmitted) {
    return (
        <div className="flex items-center justify-center min-h-screen bg-gray-100">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-2xl font-bold text-center text-green-600">
                <CheckCircle className="w-16 h-16 mx-auto mb-4" />
                Submission Successful!
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center text-gray-600">
                Thank you for submitting your company information. We will review your application and get back to you
                soon.
              </p>
            </CardContent>
            <CardFooter>
              <Button onClick={() => window.location.reload()} className="w-full">
                Submit Another Application
              </Button>
            </CardFooter>
          </Card>
        </div>
    )
  }

  if (isPreviewMode) {
    return (
        <div className="space-y-8 max-w-4xl mx-auto p-4">
          {alert && (
              <Alert variant={alert.type === "success" ? "default" : "destructive"}>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>{alert.type === "success" ? "Success" : "Error"}</AlertTitle>
                <AlertDescription>{alert.message}</AlertDescription>
              </Alert>
          )}

          <Card className="shadow-lg border-t-4 border-t-primary">
            <CardHeader className="bg-primary/10 border-b">
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-primary/20 rounded-full">
                  <CheckCircle className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold text-primary">Review Your Information</CardTitle>
                  <CardDescription>Please review your information before final submission.</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-8 pt-6">
              <FormPreviewComponent formData={formData} />
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row gap-4 border-t pt-6">
              <Button
                  type="button"
                  variant="outline"
                  className="w-full sm:w-1/2 border-gray-300 bg-transparent"
                  onClick={() => setIsPreviewMode(false)}
              >
                <AlertCircle className="mr-2 h-4 w-4" />
                Back to Edit
              </Button>
              <Button type="button" className="w-full sm:w-1/2 " onClick={handleSubmit} disabled={isSubmitting}>
                {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Submitting...
                    </>
                ) : (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Confirm & Submit
                    </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>
    )
  }

  return (
      <form onSubmit={handleSubmit} className="space-y-8 max-w-4xl mx-auto p-4">
        {alert && (
            <Alert variant={alert.type === "success" ? "default" : "destructive"}>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>{alert.type === "success" ? "Success" : "Error"}</AlertTitle>
              <AlertDescription>{alert.message}</AlertDescription>
            </Alert>
        )}

        <Card className="shadow-lg border-t-4 border-t-primary">
          <CardHeader className="bg-gray-50 border-b">
            <CardTitle className="text-2xl font-bold text-primary">Company Information Form</CardTitle>
            <CardDescription>Please fill out all the required information about your company.</CardDescription>
            <div className="mt-2 flex items-start space-x-2 text-sm text-amber-600 bg-amber-50 p-2 rounded">
              <Info className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <p>Fields marked with an asterisk (*) are required.</p>
            </div>
          </CardHeader>

          <CardContent className="space-y-8">
            <Section title="Company Information">
              <InputGrid>
                <ValidatedInput
                    id="company_name"
                    label="Company Name"
                    value={formData.company_name}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="Enter company name"
                    error={errors.company_name}
                    touched={touchedFields.has("company_name")}
                    required
                />
                <ValidatedInput
                    id="company_industry"
                    label="Company Industry"
                    value={formData.company_industry}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="e.g. Technology, Finance"
                    error={errors.company_industry}
                    touched={touchedFields.has("company_industry")}
                    required
                />
                <ValidatedInput
                    id="company_number"
                    label="Company Registration Number"
                    value={formData.company_number}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="Enter registration number"
                    error={errors.company_number}
                    touched={touchedFields.has("company_number")}
                    required
                />
                <div className="space-y-2">
                  <Label htmlFor="company_phone" className="flex">
                    Company Telephone <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <PhoneInput
                      international
                      countryCallingCodeEditable={true}
                      defaultCountry="US"
                      value={formData.company_phone}
                      onChange={(value) => handlePhoneChange(value, "company_phone")}
                      required
                      className={`w-full p-2 border rounded-md ${
                          touchedFields.has("company_phone") && errors.company_phone ? "border-red-500" : "border-gray-300"
                      } ${formData.company_phone && !errors.company_phone ? "border-green-500" : ""}`}
                  />
                  {touchedFields.has("company_phone") && errors.company_phone && (
                      <p className="text-red-500 text-sm mt-1 error-message">{errors.company_phone}</p>
                  )}
                </div>
                <ValidatedInput
                    id="registration_date"
                    label="Date of Registration"
                    value={formData.registration_date}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    type="date"
                    error={errors.registration_date}
                    touched={touchedFields.has("registration_date")}
                    required
                />
                <ValidatedInput
                    id="contact_name"
                    label="Contact Name"
                    value={formData.contact_name}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="Full name"
                    error={errors.contact_name}
                    touched={touchedFields.has("contact_name")}
                    required
                />
                <ValidatedInput
                    id="contact_role"
                    label="Role of Contact"
                    value={formData.contact_role}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="e.g. CEO, CFO"
                    error={errors.contact_role}
                    touched={touchedFields.has("contact_role")}
                    required
                />
                <ValidatedInput
                    id="company_email"
                    label="Company Email"
                    value={formData.company_email}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    type="email"
                    placeholder="<EMAIL>"
                    error={errors.company_email}
                    touched={touchedFields.has("company_email")}
                    required
                />
                <div className="space-y-2">
                  <Label htmlFor="country_of_incorporation" className="flex">
                    Country of Incorporation <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <CountrySelector
                      value={formData.country_of_incorporation}
                      onChange={(value) => handleCountryChange("country_of_incorporation", value)}
                      onBlur={() => handleCountryBlur("country_of_incorporation")}
                      error={errors.country_of_incorporation}
                      touched={touchedFields.has("country_of_incorporation")}
                      required
                      placeholder="Select country of incorporation"
                  />
                  {touchedFields.has("country_of_incorporation") && errors.country_of_incorporation && (
                      <p className="text-red-500 text-sm mt-1 error-message">{errors.country_of_incorporation}</p>
                  )}
                </div>
                <ValidatedInput
                    id="company_website"
                    label="Company Website"
                    value={formData.company_website}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    type="url"
                    placeholder="https://www.example.com"
                    error={errors.company_website}
                    touched={touchedFields.has("company_website")}
                />
              </InputGrid>
            </Section>

            <Section title="Card Programme Purpose">
              <InputGrid>
                <ValidatedInput
                    id="type_of_business"
                    label="Type of Business"
                    value={formData.type_of_business}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="e.g. Corporation, LLC"
                    error={errors.type_of_business}
                    touched={touchedFields.has("type_of_business")}
                    required
                />
                <ValidatedInput
                    id="card_usage"
                    label="Card Usage"
                    value={formData.card_usage}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="Describe card usage"
                    error={errors.card_usage}
                    touched={touchedFields.has("card_usage")}
                />
                <ValidatedInput
                    id="cardholder_groups"
                    label="Cardholder Groups"
                    value={formData.cardholder_groups}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="e.g. Employees, Contractors"
                    error={errors.cardholder_groups}
                    touched={touchedFields.has("cardholder_groups")}
                />
                <ValidatedInput
                    id="fund_loading"
                    label="Fund Loading Per Calendar Month (€)"
                    value={formData.fund_loading}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    type="number"
                    placeholder="Enter amount"
                    error={errors.fund_loading}
                    touched={touchedFields.has("fund_loading")}
                />
              </InputGrid>
            </Section>

            <Section title="Merchant Business Case Details">
              <InputGrid>
                <ValidatedInput
                    id="business_sector"
                    label="Business Sector"
                    value={formData.business_sector}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="e.g. Retail, Manufacturing"
                    error={errors.business_sector}
                    touched={touchedFields.has("business_sector")}
                    required
                />
                <ValidatedInput
                    id="regions"
                    label="Regions of Operation"
                    value={formData.regions}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="e.g. North America, Europe"
                    error={errors.regions}
                    touched={touchedFields.has("regions")}
                />
                <ValidatedInput
                    id="countries"
                    label="Countries of Operation"
                    value={formData.countries}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="List countries"
                    error={errors.countries}
                    touched={touchedFields.has("countries")}
                />
                <div className="space-y-2 col-span-2">
                  <Label htmlFor="business_purpose" className="flex">
                    Business Purpose (For using cards) <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Textarea
                      id="business_purpose"
                      name="business_purpose"
                      value={formData.business_purpose}
                      onChange={handleInputChange}
                      onBlur={handleBlur}
                      required
                      className={`min-h-[100px] ${
                          touchedFields.has("business_purpose") && errors.business_purpose ? "border-red-500" : ""
                      }`}
                      placeholder="Describe the purpose of using cards in your business"
                  />
                  {touchedFields.has("business_purpose") && errors.business_purpose && (
                      <p className="text-red-500 text-sm mt-1 error-message">{errors.business_purpose}</p>
                  )}
                </div>
                <ValidatedInput
                    id="card_user_groups"
                    label="Card User Groups (List roles)"
                    value={formData.card_user_groups}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="e.g. Sales, Management"
                    error={errors.card_user_groups}
                    touched={touchedFields.has("card_user_groups")}
                />
                <ValidatedInput
                    id="number_of_cards"
                    label="How many cards required in Year 1"
                    value={formData.number_of_cards}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    type="number"
                    placeholder="Enter number"
                    error={errors.number_of_cards}
                    touched={touchedFields.has("number_of_cards")}
                />
                <ValidatedInput
                    id="monthly_loading_value"
                    label="Monthly Loading Value (EUR)"
                    value={formData.monthly_loading_value}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    type="number"
                    placeholder="Enter amount"
                    error={errors.monthly_loading_value}
                    touched={touchedFields.has("monthly_loading_value")}
                />
                <ValidatedInput
                    id="admin_first_name"
                    label="Administrator First Name"
                    value={formData.admin_first_name}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="First name"
                    error={errors.admin_first_name}
                    touched={touchedFields.has("admin_first_name")}
                    required
                />
                <ValidatedInput
                    id="admin_last_name"
                    label="Administrator Last Name"
                    value={formData.admin_last_name}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="Last name"
                    error={errors.admin_last_name}
                    touched={touchedFields.has("admin_last_name")}
                    required
                />
                <ValidatedInput
                    id="admin_role"
                    label="Administrator Role"
                    value={formData.admin_role}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    placeholder="e.g. Finance Manager"
                    error={errors.admin_role}
                    touched={touchedFields.has("admin_role")}
                    required
                />
                <ValidatedInput
                    id="admin_email"
                    label="Administrator Email"
                    value={formData.admin_email}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    type="email"
                    placeholder="<EMAIL>"
                    error={errors.admin_email}
                    touched={touchedFields.has("admin_email")}
                    required
                />
                <div className="space-y-2">
                  <Label htmlFor="admin_phone" className="flex">
                    Administrator Mobile Telephone <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <PhoneInput
                      international
                      countryCallingCodeEditable={true}
                      defaultCountry="US"
                      value={formData.admin_phone}
                      onChange={(value) => handlePhoneChange(value, "admin_phone")}
                      required
                      className={`w-full p-2 border rounded-md ${
                          touchedFields.has("admin_phone") && errors.admin_phone ? "border-red-500" : "border-gray-300"
                      } ${formData.admin_phone && !errors.admin_phone ? "border-green-500" : ""}`}
                  />
                  {touchedFields.has("admin_phone") && errors.admin_phone && (
                      <p className="text-red-500 text-sm mt-1 error-message">{errors.admin_phone}</p>
                  )}
                </div>
              </InputGrid>
            </Section>

            <ValidatedAddressSection
                title="Company Registered Address"
                address={formData.registered_address}
                onChange={(e) => handleAddressChange(e, "registered_address")}
                onBlur={(e) => handleAddressBlur(e, "registered_address")}
                errors={errors.registered_address}
                touchedFields={touchedFields}
                addressType="registered_address"
                onCountryChange={(value) => handleAddressCountryChange(value, "registered_address")}
                onCountryBlur={() => handleAddressCountryBlur("registered_address")}
            />

            <div className="flex items-center space-x-2 my-4">
              <Checkbox
                  id="same-address"
                  checked={useSameAddress}
                  onCheckedChange={(checked) => setUseSameAddress(checked === true)}
              />
              <Label
                  htmlFor="same-address"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Use registered address as operational address
              </Label>
            </div>

            <ValidatedAddressSection
                title="Company Operational Address"
                address={formData.operational_address}
                onChange={(e) => handleAddressChange(e, "operational_address")}
                onBlur={(e) => handleAddressBlur(e, "operational_address")}
                errors={errors.operational_address}
                touchedFields={touchedFields}
                addressType="operational_address"
                disabled={useSameAddress}
                onCountryChange={(value) => handleAddressCountryChange(value, "operational_address")}
                onCountryBlur={() => handleAddressCountryBlur("operational_address")}
            />
          </CardContent>

          <CardFooter>
            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
              ) : (
                  "Review Information"
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
  )
}

function Section({ title, children }: { title: string; children: React.ReactNode }) {
  return (
      <div className="space-y-4 pb-6 border-b border-gray-200">
        <h3 className="text-xl font-semibold text-primary">{title}</h3>
        {children}
      </div>
  )
}

function InputGrid({ children }: { children: React.ReactNode }) {
  return <div className="grid grid-cols-1 md:grid-cols-2 gap-6">{children}</div>
}

interface ValidatedInputProps {
  id: string
  label: string
  value: string
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  onBlur: (e: React.FocusEvent<HTMLInputElement>) => void
  placeholder?: string
  type?: string
  error?: string
  touched: boolean
  required?: boolean
  disabled?: boolean
}

function ValidatedInput({
                          id,
                          label,
                          value,
                          onChange,
                          onBlur,
                          placeholder,
                          type = "text",
                          error,
                          touched,
                          required = false,
                          disabled = false,
                        }: ValidatedInputProps) {
  return (
      <div className="space-y-2">
        <Label htmlFor={id} className="flex">
          {label} {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
        <Input
            id={id}
            name={id}
            value={value}
            onChange={onChange}
            onBlur={onBlur}
            type={type}
            required={required}
            className={`w-full ${touched && error ? "border-red-500" : ""} ${value && !error ? "border-green-500" : ""}`}
            placeholder={placeholder}
            disabled={disabled}
        />
        {touched && error && <p className="text-red-500 text-sm mt-1 error-message">{error}</p>}
      </div>
  )
}

interface ValidatedAddressSectionProps {
  title: string
  address: Address
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  onBlur: (e: React.FocusEvent<HTMLInputElement>) => void
  errors?: { [key in keyof Address]?: string }
  touchedFields: Set<string>
  addressType: "registered_address" | "operational_address"
  disabled?: boolean
  onCountryChange: (value: string) => void
  onCountryBlur: () => void
}

function ValidatedAddressSection({
                                   title,
                                   address,
                                   onChange,
                                   onBlur,
                                   errors = {},
                                   touchedFields,
                                   addressType,
                                   disabled = false,
                                   onCountryChange,
                                   onCountryBlur,
                                 }: ValidatedAddressSectionProps) {
  return (
      <Section title={title}>
        <InputGrid>
          {[
            { id: "street", label: "Street", placeholder: "Enter street name", required: true },
            { id: "building_number", label: "Building Number", placeholder: "Enter building number" },
            { id: "apartment_number", label: "Apartment Number", placeholder: "Enter apartment number" },
            { id: "city", label: "City", placeholder: "Enter city", required: true },
            { id: "state", label: "State / Province", placeholder: "Enter state or province" },
            { id: "postal_code", label: "Postal / ZIP Code", placeholder: "Enter postal or ZIP code", required: true },
          ].map(({ id, label, placeholder, required = false }) => (
              <div key={id} className="space-y-2">
                <Label htmlFor={id} className="flex">
                  {label} {required && <span className="text-red-500 ml-1">*</span>}
                </Label>
                <Input
                    id={id}
                    name={id}
                    value={address[id as keyof Address]}
                    onChange={onChange}
                    onBlur={onBlur}
                    required={required}
                    className={`w-full ${
                        touchedFields.has(`${addressType}.${id}`) && errors[id as keyof Address] ? "border-red-500" : ""
                    } ${address[id as keyof Address] && !errors[id as keyof Address] ? "border-green-500" : ""}`}
                    placeholder={placeholder}
                    disabled={disabled}
                />
                {touchedFields.has(`${addressType}.${id}`) && errors[id as keyof Address] && (
                    <p className="text-red-500 text-sm mt-1 error-message">{errors[id as keyof Address]}</p>
                )}
              </div>
          ))}
          <div className="space-y-2">
            <Label htmlFor={`${addressType}-country`} className="flex">
              Country <span className="text-red-500 ml-1">*</span>
            </Label>
            <CountrySelector
                value={address.country}
                onChange={onCountryChange}
                onBlur={onCountryBlur}
                error={errors.country}
                touched={touchedFields.has(`${addressType}.country`)}
                required
                disabled={disabled}
                placeholder="Select country"
            />
            {touchedFields.has(`${addressType}.country`) && errors.country && (
                <p className="text-red-500 text-sm mt-1 error-message">{errors.country}</p>
            )}
          </div>
        </InputGrid>
      </Section>
  )
}

function FormPreviewComponent({ formData }: { formData: FormData }) {
  // Helper function to format date
  const formatDate = (dateString: string) => {
    if (!dateString) return "Not provided"
    try {
      return new Date(dateString).toLocaleDateString()
    } catch (e) {
      return dateString
    }
  }

  // Helper function to check if an address is empty
  const isAddressEmpty = (address: Address) => {
    return !Object.values(address).some((value) => value.trim() !== "")
  }

  // Helper function to format address
  const formatAddress = (address: Address) => {
    if (isAddressEmpty(address)) return "Not provided"

    const parts = []
    if (address.building_number) parts.push(address.building_number)
    if (address.apartment_number) parts.push(`Apt ${address.apartment_number}`)
    if (address.street) parts.push(address.street)
    if (address.city) parts.push(address.city)
    if (address.state) parts.push(address.state)
    if (address.postal_code) parts.push(address.postal_code)
    if (address.country) parts.push(address.country)

    return parts.join(", ")
  }

  // Helper function to format admin name
  const formatAdminName = () => {
    const firstName = formData.admin_first_name || ""
    const lastName = formData.admin_last_name || ""
    if (!firstName && !lastName) return "Not provided"
    return `${firstName} ${lastName}`.trim()
  }

  return (
      <div className="space-y-8">
        {/* Company Information Section */}
        <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
          <div className="bg-primary/10 px-4 py-3 border-b">
            <h3 className="font-semibold text-lg text-primary">Company Information</h3>
          </div>
          <div className="p-4">
            <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
              <PreviewItem label="Company Name" value={formData.company_name} />
              <PreviewItem label="Company Industry" value={formData.company_industry} />
              <PreviewItem label="Registration Number" value={formData.company_number} />
              <PreviewItem label="Company Phone" value={formData.company_phone} />
              <PreviewItem label="Registration Date" value={formatDate(formData.registration_date)} />
              <PreviewItem label="Contact Name" value={formData.contact_name} />
              <PreviewItem label="Contact Role" value={formData.contact_role} />
              <PreviewItem label="Company Email" value={formData.company_email} />
              <PreviewItem label="Country of Incorporation" value={formData.country_of_incorporation} />
              <PreviewItem label="Company Website" value={formData.company_website} />
            </dl>
          </div>
        </div>

        {/* Card Programme Purpose Section */}
        <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
          <div className="bg-primary/10 px-4 py-3 border-b">
            <h3 className="font-semibold text-lg text-primary">Card Programme Purpose</h3>
          </div>
          <div className="p-4">
            <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
              <PreviewItem label="Type of Business" value={formData.type_of_business} />
              <PreviewItem label="Card Usage" value={formData.card_usage} />
              <PreviewItem label="Cardholder Groups" value={formData.cardholder_groups} />
              <PreviewItem label="Fund Loading Per Month (€)" value={formData.fund_loading} />
            </dl>
          </div>
        </div>

        {/* Merchant Business Case Details Section */}
        <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
          <div className="bg-primary/10 px-4 py-3 border-b">
            <h3 className="font-semibold text-lg text-primary">Merchant Business Case Details</h3>
          </div>
          <div className="p-4">
            <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
              <PreviewItem label="Business Sector" value={formData.business_sector} />
              <PreviewItem label="Regions of Operation" value={formData.regions} />
              <PreviewItem label="Countries of Operation" value={formData.countries} />
              <div className="col-span-1 md:col-span-2">
                <PreviewItem label="Business Purpose" value={formData.business_purpose} />
              </div>
              <PreviewItem label="Card User Groups" value={formData.card_user_groups} />
              <PreviewItem label="Number of Cards (Year 1)" value={formData.number_of_cards} />
              <PreviewItem label="Monthly Loading Value (EUR)" value={formData.monthly_loading_value} />
              <PreviewItem label="Administrator Name" value={formatAdminName()} />
              <PreviewItem label="Administrator Role" value={formData.admin_role} />
              <PreviewItem label="Administrator Email" value={formData.admin_email} />
              <PreviewItem label="Administrator Phone" value={formData.admin_phone} />
            </dl>
          </div>
        </div>

        {/* Company Addresses Section */}
        <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
          <div className="bg-primary/10 px-4 py-3 border-b">
            <h3 className="font-semibold text-lg text-primary">Company Addresses</h3>
          </div>
          <div className="p-4">
            <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
              <div>
                <dt className="text-sm font-medium text-gray-500 mb-1">Registered Address</dt>
                <dd className="text-sm text-gray-900 bg-gray-50 p-3 rounded border">
                  {formatAddress(formData.registered_address)}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 mb-1">Operational Address</dt>
                <dd className="text-sm text-gray-900 bg-gray-50 p-3 rounded border">
                  {formatAddress(formData.operational_address)}
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
  )
}

// Add this helper component at the end of the file
function PreviewItem({ label, value }: { label: string; value: string | number }) {
  return (
      <div>
        <dt className="text-sm font-medium text-gray-500 mb-1">{label}</dt>
        <dd className="text-sm text-gray-900 bg-gray-50 p-3 rounded border">{value || "Not provided"}</dd>
      </div>
  )
}
