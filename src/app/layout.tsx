'use client'
import "./globals.css";

import {Provider} from 'react-redux';
import store from '../store';


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
      <html lang="en" suppressHydrationWarning>
      <title>Ryvyl Cards</title>
      <body
          className={`antialiased`}
      >
      <Provider store={store}>
          {children}
      </Provider>
      </body>
      </html>
  );
}
