@tailwind base;
@tailwind components;
@tailwind utilities;

body {
    font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }
}

@layer base {
    :root {

        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
        --background: 0 0% 98%;
        --foreground: 0 0% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 3.9%;
        --primary: 179, 100%, 33%;
        --primary-foreground: 0 0% 98%;
        --secondary: 0 0% 96.1%;
        --secondary-foreground: 0 0% 9%;
        --muted: 0 0% 96.1%;
        --muted-foreground: 0 0% 45.1%;
        --accent: 0 0% 96.1%;
        --accent-foreground: 0 0% 9%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 215, 44%, 89%;
        --input: 0 0% 89.8%;
        --ring: 202 77% 45%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
        --hover: 178, 100%, 28%;
        --hover-foreground: 0 0% 100%;
        --divider: 215, 44%, 89%;

    }

    .dark {
        --sidebar-background: 240 5.9% 10%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 240 3.7% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }

}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }

    thead th {
        color: hsl(var(--primary)) !important;
        font-weight: bold !important;
    }

    label {
        @apply text-muted-foreground ;
    }


}

.card-container {
    perspective: 1000px; /* Create a 3D effect */
}

.card {
    width: 330px;

    transform-style: preserve-3d;
    transition: transform 0.6s;
    position: relative;
}

.card:hover {
    transform: rotateY(180deg); /* Flip the card on hover */
}

.card-face {
    backface-visibility: hidden; /* Hide the back face when flipped */
    position: absolute;
    width: 100%;
    height: 100%;
}

.card-front {
    z-index: 2;
}

.card-back {
    transform: rotateY(180deg); /* Flip the back side */
    z-index: 1;
}

.checkbox-wrapper-16 *,
.checkbox-wrapper-16 *:after,
.checkbox-wrapper-16 *:before {
    box-sizing: border-box;
}

.checkbox-wrapper-16 .checkbox-input {
    clip: rect(0 0 0 0);
    -webkit-clip-path: inset(100%);
    clip-path: inset(100%);
    height: 1px;
    overflow: hidden;
    position: absolute;
    white-space: nowrap;
    width: 1px;
}

.checkbox-wrapper-16 .checkbox-input:checked + .checkbox-tile {
    border-color: hsl(178.57deg 98.82% 33.33%);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    color: hsl(178.57deg 98.82% 33.33%);
}

.checkbox-wrapper-16 .checkbox-input:checked + .checkbox-tile:before {
    transform: scale(1);
    opacity: 1;
    background-color: hsl(178.57deg 98.82% 33.33%);
    border-color: hsl(178.57deg 98.82% 33.33%);
}

.checkbox-wrapper-16 .checkbox-input:checked + .checkbox-tile .checkbox-icon,
.checkbox-wrapper-16 .checkbox-input:checked + .checkbox-tile .checkbox-label {
    color: hsl(178.57deg 98.82% 33.33%);
}

.checkbox-wrapper-16 .checkbox-input:focus + .checkbox-tile {
    border-color: hsl(178.57deg 98.82% 33.33%);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1), 0 0 0 4px rgba(15, 162, 159, 0.46);
}

.checkbox-wrapper-16 .checkbox-input:focus + .checkbox-tile:before {
    transform: scale(1);
    opacity: 1;
}

.checkbox-wrapper-16 .checkbox-tile {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    min-height: 7rem;
    border-radius: 0.5rem;
    border: 2px solid #b5bfd9;
    background-color: #fff;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    transition: 0.15s ease;
    cursor: pointer;
    position: relative;
}

.checkbox-wrapper-16 .checkbox-tile:before {
    content: "";
    position: absolute;
    display: block;
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #b5bfd9;
    background-color: #fff;
    border-radius: 50%;
    top: 0.25rem;
    left: 0.25rem;
    opacity: 0;
    transform: scale(0);
    transition: 0.25s ease;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='192' height='192' fill='%23FFFFFF' viewBox='0 0 256 256'%3E%3Crect width='256' height='256' fill='none'%3E%3C/rect%3E%3Cpolyline points='216 72.005 104 184 48 128.005' fill='none' stroke='%23FFFFFF' stroke-linecap='round' stroke-linejoin='round' stroke-width='32'%3E%3C/polyline%3E%3C/svg%3E");
    background-size: 12px;
    background-repeat: no-repeat;
    background-position: 50% 50%;
}

.checkbox-wrapper-16 .checkbox-tile:hover {
    border-color: hsl(178.57deg 98.82% 33.33%);
}

.checkbox-wrapper-16 .checkbox-tile:hover:before {
    transform: scale(1);
    opacity: 1;
}

.checkbox-wrapper-16 .checkbox-icon {
    transition: 0.375s ease;
    color: #494949;
}

.checkbox-wrapper-16 .checkbox-icon svg {
    width: 3rem;
    height: 3rem;
}

.checkbox-wrapper-16 .checkbox-label {
    color: #707070;
    transition: 0.375s ease;
    text-align: center;
}



@layer base {
  * {
    @apply border-border outline-ring/50;
    }
  body {
    @apply bg-background text-foreground;
    }
}

.iti__tel-input{
    background: red !important;
}
.sidebar-white-bg {
    --sidebar-background: 255 255 255;
    --sidebar-foreground: 15 23 42;
    --sidebar-primary: 15 23 42;
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 248 250 252;
    --sidebar-accent-foreground: 15 23 42;
    --sidebar-border: 226 232 240;
    --sidebar-ring: 59 130 246;
}
/* Virtual Card 3D Styles */
.card-3d-virtual {
    position: relative;
    width: 384px;
    height: 240px;
    transform-style: preserve-3d;
    transition: transform 0.6s ease-in-out;
}

.card-3d-virtual.flipped {
    transform: rotateY(180deg);
}

/* Physical Card 3D Styles */
.card-3d-physical {
    position: relative;
    width: 330px;
    height: 510px;
    transform-style: preserve-3d;
    transition: transform 0.6s ease-in-out;
}

.card-3d-physical.flipped {
    transform: rotateY(180deg);
}

/* Common Card Face Styles */
.card-face {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 16px;
    overflow: hidden;
}

.card-back {
    transform: rotateY(180deg);
}

/* Smooth animations */
.card-3d-virtual *,
.card-3d-physical * {
    transition: all 0.3s ease;
}

/* Hover effects for Virtual Cards */
.card-3d-virtual:hover {
    transform: translateY(-4px) scale(1.02);
    filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.15));
}

/*.card-3d-virtual.flipped:hover {*/
/*    transform: rotateY(180deg) translateY(-4px) scale(1.02);*/
/*}*/

/*!* Hover effects for Physical Cards *!*/
/*.card-3d-physical:hover {*/
/*    transform: translateY(-4px) scale(1.02);*/
/*    filter: drop-shadow(0 25px 50px rgba(0, 0, 0, 0.15));*/
/*}*/

.card-3d-physical.flipped:hover {
    transform: rotateY(180deg) translateY(-4px) scale(1.02);
}

/* Glass morphism effect */
.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-3d-virtual {
        width: 320px;
        height: 200px;
    }

    .card-3d-physical {
        width: 280px;
        height: 430px;
    }
}

@media (max-width: 480px) {
    .card-3d-virtual {
        width: 280px;
        height: 175px;
    }

    .card-3d-physical {
        width: 240px;
        height: 370px;
    }
}
