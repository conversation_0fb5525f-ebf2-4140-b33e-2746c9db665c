import express from "express"
import Activity from "../models/Activity.js"

const router = express.Router()

// Get activity statistics
router.get("/stats", async (req, res) => {
    try {
        const { startDate, endDate } = req.query

        const start = startDate ? new Date(startDate) : new Date(Date.now() - 24 * 60 * 60 * 1000)
        const end = endDate ? new Date(endDate) : new Date()

        const stats = await Activity.getActivityStats(start, end)

        res.json({
            success: true,
            data: stats,
            period: {
                start: start,
                end: end,
            },
        })
    } catch (error) {
        console.error("Error fetching activity stats:", error)
        res.status(500).json({
            success: false,
            error: "Failed to fetch activity statistics",
        })
    }
})

// Get hourly activity data
router.get("/hourly", async (req, res) => {
    try {
        const { startDate, endDate } = req.query

        const start = startDate ? new Date(startDate) : new Date(Date.now() - 24 * 60 * 60 * 1000)
        const end = endDate ? new Date(endDate) : new Date()

        const hourlyData = await Activity.getHourlyActivity(start, end)

        res.json({
            success: true,
            data: hourlyData,
        })
    } catch (error) {
        console.error("Error fetching hourly activity:", error)
        res.status(500).json({
            success: false,
            error: "Failed to fetch hourly activity data",
        })
    }
})

// Get daily activity data
router.get("/daily", async (req, res) => {
    try {
        const { days = 7 } = req.query
        const dailyData = await Activity.getDailyActivity(Number.parseInt(days))

        res.json({
            success: true,
            data: dailyData,
        })
    } catch (error) {
        console.error("Error fetching daily activity:", error)
        res.status(500).json({
            success: false,
            error: "Failed to fetch daily activity data",
        })
    }
})

// Get popular pages
router.get("/popular-pages", async (req, res) => {
    try {
        const { limit = 10, startDate, endDate } = req.query

        const start = startDate ? new Date(startDate) : new Date(Date.now() - 24 * 60 * 60 * 1000)
        const end = endDate ? new Date(endDate) : new Date()

        const popularPages = await Activity.getPopularPages(Number.parseInt(limit), start, end)

        res.json({
            success: true,
            data: popularPages,
        })
    } catch (error) {
        console.error("Error fetching popular pages:", error)
        res.status(500).json({
            success: false,
            error: "Failed to fetch popular pages",
        })
    }
})

// Get recent activities
router.get("/recent", async (req, res) => {
    try {
        const { limit = 50, offset = 0 } = req.query

        const activities = await Activity.find()
            .sort({ timestamp: -1 })
            .limit(Number.parseInt(limit))
            .skip(Number.parseInt(offset))
            .select("-__v") // Exclude version field

        const total = await Activity.countDocuments()

        res.json({
            success: true,
            data: {
                activities: activities,
                total: total,
                limit: Number.parseInt(limit),
                offset: Number.parseInt(offset),
            },
        })
    } catch (error) {
        console.error("Error fetching recent activities:", error)
        res.status(500).json({
            success: false,
            error: "Failed to fetch recent activities",
        })
    }
})

// Create new activity (for manual logging)
router.post("/activity", async (req, res) => {
    try {
        const activityData = req.body

        // Validate required fields
        if (!activityData.url || !activityData.pathname || !activityData.method) {
            return res.status(400).json({
                success: false,
                error: "Missing required fields: url, pathname, method",
            })
        }

        const activity = new Activity(activityData)
        await activity.save()

        res.status(201).json({
            success: true,
            data: activity,
        })
    } catch (error) {
        console.error("Error creating activity:", error)
        res.status(500).json({
            success: false,
            error: "Failed to create activity record",
        })
    }
})

// Get method distribution
router.get("/methods", async (req, res) => {
    try {
        const { startDate, endDate } = req.query

        const start = startDate ? new Date(startDate) : new Date(Date.now() - 24 * 60 * 60 * 1000)
        const end = endDate ? new Date(endDate) : new Date()

        const pipeline = [
            {
                $match: {
                    timestamp: { $gte: start, $lte: end },
                },
            },
            {
                $group: {
                    _id: "$method",
                    count: { $sum: 1 },
                },
            },
            {
                $project: {
                    method: "$_id",
                    count: 1,
                    _id: 0,
                },
            },
            {
                $sort: { count: -1 },
            },
        ]

        const methodData = await Activity.aggregate(pipeline)

        res.json({
            success: true,
            data: methodData,
        })
    } catch (error) {
        console.error("Error fetching method distribution:", error)
        res.status(500).json({
            success: false,
            error: "Failed to fetch method distribution",
        })
    }
})

// Get device distribution
router.get("/devices", async (req, res) => {
    try {
        const { startDate, endDate } = req.query

        const start = startDate ? new Date(startDate) : new Date(Date.now() - 24 * 60 * 60 * 1000)
        const end = endDate ? new Date(endDate) : new Date()

        const pipeline = [
            {
                $match: {
                    timestamp: { $gte: start, $lte: end },
                },
            },
            {
                $group: {
                    _id: "$deviceType",
                    count: { $sum: 1 },
                },
            },
            {
                $project: {
                    name: "$_id",
                    value: "$count",
                    _id: 0,
                },
            },
            {
                $sort: { value: -1 },
            },
        ]

        const deviceData = await Activity.aggregate(pipeline)

        res.json({
            success: true,
            data: deviceData,
        })
    } catch (error) {
        console.error("Error fetching device distribution:", error)
        res.status(500).json({
            success: false,
            error: "Failed to fetch device distribution",
        })
    }
})

// Delete old activities (cleanup endpoint)
router.delete("/cleanup", async (req, res) => {
    try {
        const { days = 30 } = req.query
        const cutoffDate = new Date(Date.now() - Number.parseInt(days) * 24 * 60 * 60 * 1000)

        const result = await Activity.deleteMany({
            timestamp: { $lt: cutoffDate },
        })

        res.json({
            success: true,
            message: `Deleted ${result.deletedCount} activities older than ${days} days`,
        })
    } catch (error) {
        console.error("Error cleaning up activities:", error)
        res.status(500).json({
            success: false,
            error: "Failed to cleanup old activities",
        })
    }
})

export default router
