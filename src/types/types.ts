// src/types/types.ts

export type Company = {
    _id: string;
    company_name: string;
    company_number: string;
    company_email: string;
    registration_date: string;
};
export interface CardImage {
    _id: string
    scheme_name: string
    company: string
    front_side: string
    back_side: string
    card_type: string
    card_category: string
    created_at: string
    created_by: string
    product_version?: { version_name: string }
}

export type CardProgram = {
    _id: string;
    company: Company;  // References Company type
    cardScheme: string;  // Assuming _id reference type
    binRange: string;    // Assuming _id reference type
    cardProgrammeType: string; // Assuming _id reference type
    customerType: string; // Assuming _id reference type
    issuingClientType: string; // Assuming _id reference type
    productCcy: string;  // Assuming _id reference type
    productVersionName: string; // Assuming _id reference type
    customerRoute: string;
    operationalFunction: string;
    applicationStatus: string;
    isCorporateRegulated: string;
    regulatorRegion: string;
    mccSector: string;
    createdAt: Date;
    updatedAt: Date;
};

export interface Role {
    _id: string
    name: string
    permissions: string[]
    createdAt: string
    updatedAt: string
    __v: number
}