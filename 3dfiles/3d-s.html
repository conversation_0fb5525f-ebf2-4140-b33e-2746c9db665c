<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authorisation request for card ending **** 1234</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
<div id="app" class="max-w-md mx-auto mt-10">
    <!-- Screen 1: Security Question -->
    <div id="otp-screen" class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="bg-teal-600 text-white p-4 flex justify-between items-center">
            <div class="flex items-center">
                <span class="font-bold">Authorisation request for card ending **** 1234</span>
            </div>
            <button class="text-white">Cancel</button>
        </div>
        <div class=" text-white p-4 flex justify-between items-center">
            <div class="flex items-center">
                <img src="logo/icon-dark.svg" alt="YourBank Logo" class="h-10 w-10 mr-2">
            </div>
            <img src="logo/visa-10.svg" alt="YourBank Logo" class="h-5 mr-2">
        </div>

        <div class="p-6">
            <h2 class="text-2xl font-bold mb-4">Payment security</h2>

            <div class="bg-blue-100 text-blue-800 p-4 rounded-full mb-4 flex items-center">
                <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                          clip-rule="evenodd"/>
                </svg>
                <span>A new OTP has been sent</span>
            </div>

            <p class="mb-4">We have sent you a text message with a OTP to your registered mobile number ending in
                7878.</p>

            <p class="mb-4 font-semibold">Brightsparks requests authorisation for EUR 23.16 from 24 March 2025 @
                16:55</p>

            <form class="space-y-4 " id="otp-form">
                <div>
                    <label for="static-password">3DS Static Password</label>
                <input
                        type="text" id="static-password"
                        placeholder="Enter your saved 3DS Password"
                        class="w-full p-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[#01a9a5]"
                >
                </div>
                <div>
                    <label for="s-otp">3DS OTP</label>
                    <input
                            type="text"
                            placeholder="Enter your 3DS OTP available within Cardholder Dashboard" id="s-otp"
                            class="w-full p-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[#01a9a5]"
                >
                    </div>



                <button
                        class="w-full bg-[#01a9a5] text-white py-3 rounded-full hover:bg-[#01a9a5] transition duration-300"
                >
                    Submit
                </button>
                <button
                        class="w-full border border-teal-600 text-teal-600 py-3 rounded-full hover:bg-teal-50 transition duration-300"
                >
                    Resend code
                </button>

            </form>

            <div class="mt-6 space-y-2 text-gray-600">
                <a href="" class="underline">▶ Learn more about authentication</a><br>
                <a href="" class="underline">▶ Link to URL cardholder login</a>
            </div>
        </div>
    </div>


    <div id="password-screen" class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="bg-teal-600 text-white p-4 flex justify-between items-center">
            <div class="flex items-center">
                <span class="font-bold">Authorisation request for card ending **** 1234</span>
            </div>
            <button class="text-white">Cancel</button>
        </div>
        <div class=" text-white p-4 flex justify-between items-center">
            <div class="flex items-center">
                <img src="logo/icon-dark.svg" alt="YourBank Logo" class="h-10 w-10 mr-2">
            </div>
            <img src="logo/visa-10.svg" alt="YourBank Logo" class="h-5 mr-2">
        </div>

        <div class="p-6">
            <h2 class="text-2xl font-bold mb-4">Payment security</h2>

            <p class="mb-4">You are paying BriteSpark the amount of  EUR 99.00  on 27/02/24. Please answer our security question to complete your payment.</p>

            <div class="mb-4">
                <label class="block mb-2 font-semibold">3D Secure Password</label>

            </div>

            <form id="password-form" class="space-y-4">
                <input
                        type="text"
                        id="password"
                        placeholder="Enter your 3D secure password"
                        class="w-full p-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-teal-600"
                >
                <button
                        type="submit"
                        class="w-full bg-teal-600 text-white py-3 rounded-full hover:bg-teal-700 transition duration-300"
                >
                    Submit
                </button>
            </form>

            <div class="mt-6 space-y-2 text-gray-600">
                <a href="" class="underline">▶ Learn more about authentication</a><br>
                <a href="" class="underline">▶ Link to URL cardholder login</a>
            </div>
        </div>
    </div>


    <div id="security-question-screen" class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="bg-teal-600 text-white p-4 flex justify-between items-center">
            <div class="flex items-center">
                <span class="font-bold">Authorisation request for card ending **** 1234</span>
            </div>
            <button class="text-white">Cancel</button>
        </div>
        <div class=" text-white p-4 flex justify-between items-center">
            <div class="flex items-center">
                <img src="logo/icon-dark.svg" alt="YourBank Logo" class="h-10 w-10 mr-2">
            </div>
            <img src="logo/visa-10.svg" alt="YourBank Logo" class="h-5 mr-2">
        </div>

        <div class="p-6">
            <h2 class="text-2xl font-bold mb-4">Payment security</h2>

            <p class="mb-4">You are paying BriteSpark the amount of  EUR 99.00  on 27/02/24. Please answer our security question to complete your payment.</p>

            <div class="mb-4">
                <label class="block mb-2 font-semibold">Question</label>
                <p>In what town were you born?</p>
            </div>

            <form id="security-question-form" class="space-y-4">
                <input
                        type="text"
                        id="security-answer"
                        placeholder="Enter your answer below"
                        class="w-full p-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-teal-600"
                >
                <button
                        type="submit"
                        class="w-full bg-teal-600 text-white py-3 rounded-full hover:bg-teal-700 transition duration-300"
                >
                    Submit
                </button>
            </form>

            <div class="mt-6 space-y-2 text-gray-600">
                <a href="" class="underline">▶ Learn more about authentication</a><br>
                <a href="" class="underline">▶ Link to URL cardholder login</a>
            </div>
        </div>
    </div>

    <!-- Screen 2: Authentication Not Complete -->
    <div id="auth-not-complete-screen" class="bg-white shadow-lg rounded-lg overflow-hidden hidden">
        <div class="bg-teal-600 text-white p-4 flex justify-between items-center">
            <div class="flex items-center">

                <span class="font-bold">Authorisation request for card ending **** 1234</span>
            </div>
            <button class="text-white">Cancel</button>
        </div>
        <div class=" text-white p-4 flex justify-between items-center">
            <div class="flex items-center">
                <img src="logo/icon-dark.svg" alt="YourBank Logo" class="h-10 w-10 mr-2">
            </div>
            <img src="logo/visa-10.svg" alt="YourBank Logo" class="h-5 mr-2">
        </div>

        <div class="p-6">
            <h2 class="text-2xl font-bold mb-4">Payment security</h2>

            <div class="bg-red-100 text-red-800 p-4 rounded-full mb-4 flex items-center">
                <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
                <span>Authentication not complete</span>
            </div>

            <p class="mb-4">We have sent you a text message with a OTP to your registered mobile number ending in
                7878.</p>

            <p class="mb-4 font-semibold">Brightsparks requests authorisation for EUR 23.16 from 24 March 2025 @
                16:55</p>
            <form id="code-form" class="space-y-4">
                <div>
                    <label for="statics-password">3DS Static Password</label>
                    <input
                            type="text" id="statics-password"
                            placeholder="Enter your saved 3DS Password"
                            class="w-full p-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[#01a9a5]"
                    >
                </div>
                <div>
                    <label for="s-otp">3DS OTP</label>
                    <input
                            type="text"
                            placeholder="Enter your 3DS OTP available within Cardholder Dashboard" id="s-otp"
                            class="w-full p-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[#01a9a5]"
                    >
                </div>


                <button
                        type="submit"
                        class="w-full bg-teal-600 text-white py-3 rounded-full hover:bg-teal-700 transition duration-300"
                >
                    Submit
                </button>
                <button
                        type="button"
                        id="resend-code-btn"
                        class="w-full border border-teal-600 text-teal-600 py-3 rounded-full hover:bg-teal-50 transition duration-300"
                >
                    Resend code
                </button>
                <button
                        type="button"
                        id="change-method-btn"
                        class="w-full border border-teal-600 text-teal-600 py-3 rounded-full hover:bg-teal-50 transition duration-300"
                >
                    Switch security method
                </button>
            </form>

            <div class="mt-6 space-y-2 text-gray-600">
                <a href="" class="underline">▶ Learn more about authentication</a><br>
                <a href="" class="underline">▶ Link to URL cardholder login</a>
            </div>
        </div>
    </div>

    <!-- Screen 3: Select Security Method -->
    <div id="security-method-screen" class="bg-white shadow-lg rounded-lg overflow-hidden hidden">
        <div class="bg-teal-600 text-white p-4 flex justify-between items-center">
            <div class="flex items-center">

                <span class="font-bold">Authorisation request for card ending **** 1234</span>
            </div>
            <button class="text-white">Cancel</button>
        </div>
        <div class=" text-white p-4 flex justify-between items-center">
            <div class="flex items-center">
                <img src="logo/icon-dark.svg" alt="YourBank Logo" class="h-10 w-10 mr-2">
            </div>
            <img src="logo/visa-10.svg" alt="YourBank Logo" class="h-5 mr-2">
        </div>

        <div class="p-6">
            <h2 class="text-2xl font-bold mb-4">Select your security method</h2>

            <form id="security-method-form" class="space-y-4">
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input type="radio" name="security-method" class="mr-2">
                        <span>Receive 3DS OTP via WApp</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="security-method" class="mr-2" checked>
                        <span>Answer a security question</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="security-method" class="mr-2">
                        <span>Receive 3DS OTP via email</span>
                    </label>
                </div>

                <button
                        type="submit"
                        class="w-full bg-teal-600 text-white py-3 rounded-full hover:bg-teal-700 transition duration-300"
                >
                    Select this method
                </button>
                <button
                        type="button"
                        id="back-btn"
                        class="w-full border border-teal-600 text-teal-600 py-3 rounded-full hover:bg-teal-50 transition duration-300"
                >
                    Back
                </button>
            </form>

            <div class="mt-6 space-y-2 text-gray-600">
                <a href="" class="underline">▶ Learn more about authentication</a><br>
                <a href="" class="underline">▶ Link to URL cardholder login</a>
            </div>
        </div>
    </div>
</div>

<script>
    // Screen management
    const screens = {
        otpScreen: document.getElementById('otp-screen'),
        passwordScreen: document.getElementById('password-screen'),
        authNotComplete: document.getElementById('auth-not-complete-screen'),
        securityMethod: document.getElementById('security-method-screen'),
        securityQuestion: document.getElementById('security-question-screen'),
    };

    function showScreen(screenToShow) {
        Object.values(screens).forEach(screen => screen.classList.add('hidden'));
        screenToShow.classList.remove('hidden');
    }

    // Event Listeners
    document.getElementById('otp-form').addEventListener('submit', function(e) {
        e.preventDefault();
        showScreen(screens.authNotComplete);
    });

    // document.getElementById('password-form').addEventListener('submit', function(e) {
    //     e.preventDefault();
    //     showScreen(screens.authNotComplete);
    // });

    document.getElementById('change-method-btn').addEventListener('click', function() {
        showScreen(screens.securityMethod);
    });

    document.getElementById('back-btn').addEventListener('click', function() {
        showScreen(screens.authNotComplete);
    });

    document.getElementById('security-method-form').addEventListener('submit', function(e) {
        e.preventDefault();
        showScreen(screens.securityQuestion);
    });

    document.getElementById('resend-code-btn').addEventListener('click', function() {
        alert('Code resent to your registered mobile number');
    });

    // Initialize with first screen
    showScreen(screens.otpScreen);
</script>
</body>
</html>