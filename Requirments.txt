Setup simple laravel project with .env file
Database used = mysql

Connecting DB
1. Create a database in phpmyadmin
2. Create a database user(if required)
3. Connect database details in .env file
4. Run the project


Connect google drive functionality
1. Create a new project with google developer console
2. Enable Google drive api
3. Create a content screen
4. Create API credential
4. edit .env and add there
     --GOOGLE_APP_ID
     --GOOGLE_SECRET
     --GOOGLE_CALLBACK

