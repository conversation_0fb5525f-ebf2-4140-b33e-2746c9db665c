const fs = require("fs");

const raw = fs.readFileSync("backup.json", "utf-8");
const fixed = raw
    .replace(/ObjectId\('([^']+)'\)/g, '{"$oid":"$1"}')   // Fix ObjectId
    .replace(/(\w+):/g, '"$1":')                          // Quote keys
    .replace(/'/g, '"');                                  // Replace single quotes

// Now `fixed` is valid JSON if wrapped in []
const parts = fixed.split("}\n{").join("},\n{"); // join multi objects
const wrapped = `[${parts}]`;

fs.writeFileSync("backup_fixed.json", wrapped);
