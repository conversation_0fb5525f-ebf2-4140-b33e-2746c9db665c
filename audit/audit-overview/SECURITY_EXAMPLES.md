# 🚨 Security Issues - Real Code Examples & Fixes

This document shows **actual problematic code** found in the Ryvyl Cards codebase and provides **working solutions**.

---

## 1. 🔴 Hardcoded Sensitive Information

### ❌ **Problem Examples Found**

**📍 Database Connection (`src/lib/mongodb.ts:3`)**
```typescript
const MONGODB_URI = "mongodb+srv://hostingstratified20:<EMAIL>/ryvyl"
const DB_NAME = "ryvyl"
```

**📍 Company ID (`src/app/corporate/cards/page.tsx:21`)**
```typescript
const DEFAULT_COMPANY_ID = "6785126247b8a6a67fbf7cad"
```

**📍 CORS Origin (`server.js:38`)**
```javascript
server.use(cors({
    origin: 'http://localhost:3000'  // Hardcoded!
}));
```

### ✅ **Fixed Version**
```typescript
// src/config/environment.ts
export const config = {
    mongodb: {
        uri: process.env.MONGODB_URI || "",
        dbName: process.env.DB_NAME || "ryvyl"
    },
    cors: {
        origins: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000']
    },
    jwt: {
        secret: process.env.JWT_SECRET || "",
        expiresIn: process.env.JWT_EXPIRES_IN || "1h"
    }
}

// Validation
if (!config.mongodb.uri) {
    throw new Error("MONGODB_URI environment variable is required")
}
```

---

## 2. 🔴 Bad Authentication Logic

### ❌ **Problem: Client-Side JWT Decoding (`src/middleware.ts:6-28`)**
```typescript
const decodeJWT = (token: string) => {
    try {
        const parts = token.split(".")
        if (parts.length !== 3) return null
        
        const header = JSON.parse(atob(parts[0]))      // ⚠️ Client-side only!
        const payload = JSON.parse(atob(parts[1]))     // ⚠️ No verification!
        
        return {
            header,
            payload,
            isExpired: payload.exp ? Date.now() >= payload.exp * 1000 : false
        }
    } catch (error) {
        return null
    }
}
```

### ✅ **Fixed Version**
```typescript
// src/lib/auth-server.ts
import { jwtVerify, SignJWT } from 'jose'

const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET)

export async function verifyJWT(token: string) {
    try {
        const { payload } = await jwtVerify(token, JWT_SECRET)
        return payload
    } catch (error) {
        console.error("JWT verification failed:", error)
        return null
    }
}

export async function createJWT(payload: any) {
    return await new SignJWT(payload)
        .setProtectedHeader({ alg: 'HS256' })
        .setIssuedAt()
        .setExpirationTime('1h')
        .sign(JWT_SECRET)
}

// src/middleware.ts - Fixed version
export async function middleware(request: NextRequest) {
    const token = request.cookies.get('auth-token')?.value
    
    if (!token) {
        return NextResponse.redirect(new URL('/login', request.url))
    }
    
    const payload = await verifyJWT(token)
    if (!payload) {
        return NextResponse.redirect(new URL('/login', request.url))
    }
    
    // Add user info to headers for API routes
    const requestHeaders = new Headers(request.headers)
    requestHeaders.set('x-user-id', payload.sub as string)
    
    return NextResponse.next({
        request: { headers: requestHeaders }
    })
}
```

---

## 3. 🔴 Inconsistent Token Storage

### ❌ **Problem: Mixed Storage (`src/utils/axiosInstance.js:12` + `src/components/auto-logout-provider.tsx:37`)**
```javascript
// In axiosInstance.js
const token = localStorage.getItem("authToken")  // ⚠️ XSS vulnerable!

// In auto-logout-provider.tsx
localStorage.removeItem("authToken")             // ⚠️ Inconsistent!
sessionStorage.clear()                           // ⚠️ Multiple storages!

// In some components
const cookieToken = request.cookies.get("token")?.value  // ⚠️ Different cookie names!
```

### ✅ **Fixed Version**
```typescript
// src/lib/auth-client.ts
class AuthManager {
    private static readonly TOKEN_COOKIE = 'auth-token'
    
    static setToken(token: string) {
        // Use httpOnly cookie (set from server)
        document.cookie = `${this.TOKEN_COOKIE}=${token}; HttpOnly; Secure; SameSite=Strict; Max-Age=3600; Path=/`
    }
    
    static getToken(): string | null {
        // For client-side, token should come from server context
        return null // Client shouldn't directly access httpOnly cookies
    }
    
    static clearToken() {
        document.cookie = `${this.TOKEN_COOKIE}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
        // Clear any legacy storage
        localStorage.removeItem("authToken")
        sessionStorage.clear()
    }
}

// Usage in components
const handleLogout = () => {
    AuthManager.clearToken()
    router.push('/login')
}
```

---

## 4. 🔴 No File Type Validation

### ❌ **Problem: Weak Validation (`src/app/api/save-records-enhanced/route.ts:196`)**
```typescript
export async function POST(request: NextRequest) {
    try {
        const body = await request.json()
        const { records } = body  // ⚠️ No file validation!
        
        if (!records || !Array.isArray(records)) {  // ⚠️ Basic check only!
            return NextResponse.json({ error: "Invalid records data" }, { status: 400 })
        }
        // Direct processing without security checks
    }
}
```

### ✅ **Fixed Version**
```typescript
// src/lib/file-validator.ts
import { z } from 'zod'

const ALLOWED_MIME_TYPES = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'text/csv',                                                          // .csv
    'application/vnd.ms-excel'                                          // .xls
]

const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB
const MAX_RECORDS = 100000

export const FileUploadSchema = z.object({
    records: z.array(z.object({
        fileName: z.string()
            .min(1)
            .max(255)
            .regex(/^[a-zA-Z0-9._-]+\.(xlsx|csv|xls)$/, "Invalid filename format"),
        sheetName: z.string().min(1).max(100),
        headers: z.array(z.string().max(50)).max(100),
        rowData: z.record(z.string().max(1000)),
        rowIndex: z.number().min(0).max(MAX_RECORDS),
        totalRows: z.number().min(1).max(MAX_RECORDS)
    })).max(MAX_RECORDS),
    metadata: z.object({
        originalName: z.string(),
        mimeType: z.enum(ALLOWED_MIME_TYPES as [string, ...string[]]),
        size: z.number().max(MAX_FILE_SIZE)
    })
})

export function validateFileUpload(file: File): { valid: boolean; error?: string } {
    // Check file type
    if (!ALLOWED_MIME_TYPES.includes(file.type)) {
        return { valid: false, error: `Invalid file type: ${file.type}` }
    }
    
    // Check file size
    if (file.size > MAX_FILE_SIZE) {
        return { valid: false, error: `File too large: ${file.size} bytes` }
    }
    
    // Check filename
    const nameRegex = /^[a-zA-Z0-9._-]+\.(xlsx|csv|xls)$/
    if (!nameRegex.test(file.name)) {
        return { valid: false, error: `Invalid filename: ${file.name}` }
    }
    
    return { valid: true }
}

// Fixed API route
export async function POST(request: NextRequest) {
    try {
        const contentLength = request.headers.get('content-length')
        if (contentLength && parseInt(contentLength) > MAX_FILE_SIZE) {
            return NextResponse.json({ error: "File too large" }, { status: 413 })
        }
        
        const body = await request.json()
        const validatedData = FileUploadSchema.parse(body)
        
        // Additional security: scan for malicious content
        for (const record of validatedData.records) {
            const recordStr = JSON.stringify(record.rowData)
            if (containsMaliciousContent(recordStr)) {
                return NextResponse.json({ error: "Malicious content detected" }, { status: 400 })
            }
        }
        
        // Process validated data...
    } catch (error) {
        if (error instanceof z.ZodError) {
            return NextResponse.json({ 
                error: "Invalid input", 
                details: error.errors 
            }, { status: 400 })
        }
        return NextResponse.json({ error: "Internal server error" }, { status: 500 })
    }
}

function containsMaliciousContent(content: string): boolean {
    const maliciousPatterns = [
        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        /javascript:/gi,
        /vbscript:/gi,
        /onload\s*=/gi,
        /onerror\s*=/gi
    ]
    
    return maliciousPatterns.some(pattern => pattern.test(content))
}
```

---

## 5. 🔴 Missing Input Validation & Sanitization

### ❌ **Problem: No Validation (`src/app/api/get-records/route.ts:5-14`)**
```typescript
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url)
        const fileName = searchParams.get("fileName") || undefined      // ⚠️ No validation!
        const limit = Number.parseInt(searchParams.get("limit") || "100") // ⚠️ No bounds check!
        const skip = Number.parseInt(searchParams.get("skip") || "0")
        const search = searchParams.get("search") || undefined          // ⚠️ No sanitization!
        const searchField = searchParams.get("searchField") || undefined // ⚠️ SQL injection risk!
    }
}
```

### ✅ **Fixed Version**
```typescript
// src/lib/input-validator.ts
import { z } from 'zod'
import DOMPurify from 'isomorphic-dompurify'

export const GetRecordsSchema = z.object({
    fileName: z.string().optional().refine(
        (val) => !val || /^[a-zA-Z0-9._-]+$/.test(val),
        "Invalid filename format"
    ),
    limit: z.number().min(1).max(1000).default(100),
    skip: z.number().min(0).max(1000000).default(0),
    search: z.string().max(100).optional(),
    searchField: z.enum(['name', 'email', 'status', 'country']).optional()
})

export function sanitizeInput(input: string): string {
    // Remove HTML tags and dangerous characters
    const cleaned = DOMPurify.sanitize(input, { ALLOWED_TAGS: [] })
    // Remove SQL injection patterns
    return cleaned.replace(/['"`;\\]/g, '')
}

// Fixed API route
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url)
        
        // Parse and validate all parameters
        const rawParams = {
            fileName: searchParams.get("fileName") || undefined,
            limit: parseInt(searchParams.get("limit") || "100"),
            skip: parseInt(searchParams.get("skip") || "0"),
            search: searchParams.get("search") || undefined,
            searchField: searchParams.get("searchField") || undefined
        }
        
        const validatedParams = GetRecordsSchema.parse(rawParams)
        
        // Sanitize search input
        if (validatedParams.search) {
            validatedParams.search = sanitizeInput(validatedParams.search)
        }
        
        // Build safe search filter
        let searchFilter = {}
        if (validatedParams.search && validatedParams.searchField) {
            // Use parameterized queries to prevent injection
            searchFilter = {
                [validatedParams.searchField]: {
                    $regex: validatedParams.search,
                    $options: 'i'
                }
            }
        }
        
        const records = await getRecordsFromMongoDB(
            validatedParams.fileName,
            validatedParams.limit,
            validatedParams.skip,
            searchFilter
        )
        
        return NextResponse.json({ success: true, data: records })
        
    } catch (error) {
        if (error instanceof z.ZodError) {
            return NextResponse.json({ 
                error: "Invalid parameters", 
                details: error.errors 
            }, { status: 400 })
        }
        
        console.error("API Error:", error)
        return NextResponse.json({ 
            error: "Internal server error" 
        }, { status: 500 })
    }
}
```

---

## 6. 🔴 Missing Security Headers & Rate Limiting

### ❌ **Problem: No Security Middleware (`server.js`)**
```javascript
const server = express()
server.use(express.json());        // ⚠️ No security headers!
server.use(cookieParser());        // ⚠️ No rate limiting!
server.use(cors());               // ⚠️ No protection!
```

### ✅ **Fixed Version**
```typescript
// src/middleware/security.ts
import helmet from 'helmet'
import rateLimit from 'express-rate-limit'
import slowDown from 'express-slow-down'

// Rate limiting configuration
export const createRateLimiter = (windowMs: number, max: number) => 
    rateLimit({
        windowMs,
        max,
        message: { error: 'Too many requests, please try again later' },
        standardHeaders: true,
        legacyHeaders: false,
        handler: (req, res) => {
            res.status(429).json({ 
                error: 'Rate limit exceeded',
                retryAfter: Math.round(windowMs / 1000)
            })
        }
    })

// Speed limiting for suspicious behavior
export const speedLimiter = slowDown({
    windowMs: 15 * 60 * 1000, // 15 minutes
    delayAfter: 50,           // Allow 50 requests per windowMs without delay
    delayMs: 500              // Add 500ms delay per request after delayAfter
})

// Security headers
export const securityHeaders = helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            frameSrc: ["'none'"],
            objectSrc: ["'none'"],
            upgradeInsecureRequests: []
        }
    },
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
    },
    noSniff: true,
    frameguard: { action: 'deny' },
    xssFilter: true
})

// Fixed server.js
import express from 'express'
import { securityHeaders, createRateLimiter, speedLimiter } from './src/middleware/security'

const server = express()

// Apply security middleware
server.use(securityHeaders)
server.use(speedLimiter)

// Different rate limits for different endpoints
server.use('/api/auth/', createRateLimiter(15 * 60 * 1000, 5))    // 5 auth attempts per 15 min
server.use('/api/upload/', createRateLimiter(60 * 1000, 10))      // 10 uploads per minute
server.use('/api/', createRateLimiter(15 * 60 * 1000, 100))       // 100 API calls per 15 min

// Body parsing with limits
server.use(express.json({ limit: '10mb' }))
server.use(express.urlencoded({ extended: true, limit: '10mb' }))

// CORS with proper configuration
server.use(cors({
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
    credentials: true,
    optionsSuccessStatus: 200,
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization']
}))
```
