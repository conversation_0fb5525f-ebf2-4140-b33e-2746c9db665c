# 🚨 Missing Critical Features - Examples & Implementation

This document shows **missing critical features** that need to be implemented for security and functionality.

---

## 11. 🔴 Missing JWT Refresh Token Implementation

### ❌ **Problem: No Token Refresh Endpoint**
```typescript
// Currently missing: POST /api/auth/refresh
// Users get logged out when tokens expire, no seamless refresh
```

### ✅ **Implementation**
```typescript
// src/app/api/auth/refresh/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { jwtVerify, SignJWT } from 'jose'
import { cookies } from 'next/headers'

const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET!)
const REFRESH_SECRET = new TextEncoder().encode(process.env.REFRESH_JWT_SECRET!)

export async function POST(request: NextRequest) {
    try {
        const cookieStore = cookies()
        const refreshToken = cookieStore.get('refresh-token')?.value
        
        if (!refreshToken) {
            return NextResponse.json({ error: 'No refresh token provided' }, { status: 401 })
        }
        
        // Verify refresh token
        const { payload } = await jwtVerify(refreshToken, REFRESH_SECRET)
        
        if (!payload.sub || !payload.tokenVersion) {
            return NextResponse.json({ error: 'Invalid refresh token' }, { status: 401 })
        }
        
        // Check if user still exists and token version matches
        const user = await getUserById(payload.sub as string)
        if (!user || user.tokenVersion !== payload.tokenVersion) {
            return NextResponse.json({ error: 'Invalid refresh token' }, { status: 401 })
        }
        
        // Generate new access token
        const newAccessToken = await new SignJWT({
            sub: user.id,
            email: user.email,
            dashboard: user.dashboard,
            roles: user.roles
        })
            .setProtectedHeader({ alg: 'HS256' })
            .setIssuedAt()
            .setExpirationTime('15m') // Short-lived access token
            .sign(JWT_SECRET)
        
        // Generate new refresh token (optional rotation)
        const newRefreshToken = await new SignJWT({
            sub: user.id,
            tokenVersion: user.tokenVersion
        })
            .setProtectedHeader({ alg: 'HS256' })
            .setIssuedAt()
            .setExpirationTime('7d') // Long-lived refresh token
            .sign(REFRESH_SECRET)
        
        // Set secure cookies
        const response = NextResponse.json({ 
            success: true,
            message: 'Token refreshed successfully'
        })
        
        response.cookies.set('access-token', newAccessToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            maxAge: 15 * 60, // 15 minutes
            path: '/'
        })
        
        response.cookies.set('refresh-token', newRefreshToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            maxAge: 7 * 24 * 60 * 60, // 7 days
            path: '/'
        })
        
        return response
        
    } catch (error) {
        console.error('Token refresh failed:', error)
        return NextResponse.json({ error: 'Token refresh failed' }, { status: 401 })
    }
}

// Helper function to get user by ID
async function getUserById(userId: string) {
    // Implementation depends on your database
    const db = await connectToDatabase()
    return await db.collection('users').findOne({ _id: userId })
}
```

### **Client-Side Auto-Refresh**
```typescript
// src/lib/auth-interceptor.ts
import axios from 'axios'

let isRefreshing = false
let failedQueue: any[] = []

const processQueue = (error: any, token: string | null = null) => {
    failedQueue.forEach(({ resolve, reject }) => {
        if (error) {
            reject(error)
        } else {
            resolve(token)
        }
    })
    
    failedQueue = []
}

// Add response interceptor for automatic token refresh
axios.interceptors.response.use(
    (response) => response,
    async (error) => {
        const originalRequest = error.config
        
        if (error.response?.status === 401 && !originalRequest._retry) {
            if (isRefreshing) {
                // If already refreshing, queue the request
                return new Promise((resolve, reject) => {
                    failedQueue.push({ resolve, reject })
                }).then(() => {
                    return axios(originalRequest)
                }).catch((err) => {
                    return Promise.reject(err)
                })
            }
            
            originalRequest._retry = true
            isRefreshing = true
            
            try {
                await axios.post('/api/auth/refresh')
                processQueue(null)
                return axios(originalRequest)
            } catch (refreshError) {
                processQueue(refreshError)
                // Redirect to login
                window.location.href = '/login'
                return Promise.reject(refreshError)
            } finally {
                isRefreshing = false
            }
        }
        
        return Promise.reject(error)
    }
)
```

---

## 12. 🔴 Missing Session Invalidation on Logout

### ❌ **Problem: No Proper Logout Implementation**
```typescript
// Current logout just clears client-side storage
const handleLogout = () => {
    localStorage.removeItem("authToken")  // ⚠️ Token still valid on server!
    router.push('/login')
}
```

### ✅ **Implementation**
```typescript
// src/app/api/auth/logout/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { jwtVerify } from 'jose'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
    try {
        const cookieStore = cookies()
        const accessToken = cookieStore.get('access-token')?.value
        const refreshToken = cookieStore.get('refresh-token')?.value
        
        if (accessToken) {
            try {
                // Verify and get user info from token
                const { payload } = await jwtVerify(accessToken, JWT_SECRET)
                
                if (payload.sub) {
                    // Invalidate all user sessions by incrementing token version
                    await invalidateUserSessions(payload.sub as string)
                    
                    // Log logout activity
                    await logActivity({
                        userId: payload.sub as string,
                        action: 'LOGOUT',
                        timestamp: new Date(),
                        ip: request.ip || 'unknown'
                    })
                }
            } catch (error) {
                // Token might be expired, but still proceed with logout
                console.warn('Token verification failed during logout:', error)
            }
        }
        
        // Clear cookies
        const response = NextResponse.json({ 
            success: true, 
            message: 'Logged out successfully' 
        })
        
        response.cookies.set('access-token', '', {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            maxAge: 0,
            path: '/'
        })
        
        response.cookies.set('refresh-token', '', {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            maxAge: 0,
            path: '/'
        })
        
        return response
        
    } catch (error) {
        console.error('Logout error:', error)
        return NextResponse.json({ error: 'Logout failed' }, { status: 500 })
    }
}

async function invalidateUserSessions(userId: string) {
    const db = await connectToDatabase()
    
    // Increment token version to invalidate all existing tokens
    await db.collection('users').updateOne(
        { _id: userId },
        { 
            $inc: { tokenVersion: 1 },
            $set: { lastLogout: new Date() }
        }
    )
}
```

---

## 13. 🔴 Missing Authorization Checks for Card Access

### ❌ **Problem: No Card Access Validation**
```typescript
// In src/app/api/cards/[id]/route.ts (doesn't exist!)
// Users can potentially access any card by ID
```

### ✅ **Implementation**
```typescript
// src/app/api/cards/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/lib/auth-server'

export async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        // Verify authentication
        const token = request.cookies.get('access-token')?.value
        if (!token) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
        }
        
        const payload = await verifyToken(token)
        if (!payload) {
            return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
        }
        
        const userId = payload.sub as string
        const cardId = params.id
        
        // Validate card ID format
        if (!isValidObjectId(cardId)) {
            return NextResponse.json({ error: 'Invalid card ID' }, { status: 400 })
        }
        
        // Get card with authorization check
        const card = await getCardWithAuth(cardId, userId)
        if (!card) {
            return NextResponse.json({ error: 'Card not found or access denied' }, { status: 404 })
        }
        
        // Sanitize card data before sending to client
        const sanitizedCard = sanitizeCardData(card)
        
        return NextResponse.json({ success: true, data: sanitizedCard })
        
    } catch (error) {
        console.error('Card access error:', error)
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    }
}

async function getCardWithAuth(cardId: string, userId: string) {
    const db = await connectToDatabase()
    
    // Get user to check permissions
    const user = await db.collection('users').findOne({ _id: userId })
    if (!user) return null
    
    let query: any = { _id: cardId }
    
    // Apply authorization based on user role
    switch (user.dashboard) {
        case 'cardholder':
            // Cardholders can only access their own cards
            query.userId = userId
            break
            
        case 'corporate':
            // Corporate users can access cards from their company
            query.companyId = user.companyId
            break
            
        case 'manager':
            // Managers can access cards they manage
            const managedCompanies = user.managedCompanies || []
            query.companyId = { $in: managedCompanies }
            break
            
        default:
            return null // No access for other roles
    }
    
    return await db.collection('cards').findOne(query)
}

function sanitizeCardData(card: any) {
    // Remove sensitive fields before sending to client
    const { cardNumber, cvv, pin, ...sanitized } = card
    
    return {
        ...sanitized,
        cardMask: card.cardNumber ? maskCardNumber(card.cardNumber) : null,
        // Only include last 4 digits
        lastFour: card.cardNumber ? card.cardNumber.slice(-4) : null
    }
}

function maskCardNumber(cardNumber: string): string {
    if (cardNumber.length < 4) return '****'
    return '**** **** **** ' + cardNumber.slice(-4)
}
```

---

## 14. 🔴 Missing API Documentation

### ❌ **Problem: No API Documentation**
```typescript
// No OpenAPI/Swagger documentation
// No endpoint documentation
// No integration guides
```

### ✅ **Implementation**
```typescript
// src/app/api/docs/route.ts - Auto-generated API docs
import { NextResponse } from 'next/server'

const API_DOCUMENTATION = {
    openapi: '3.0.0',
    info: {
        title: 'Ryvyl Cards API',
        version: '1.0.0',
        description: 'API for Ryvyl Cards management platform'
    },
    servers: [
        {
            url: process.env.API_BASE_URL || 'http://localhost:3000/api',
            description: 'Development server'
        }
    ],
    paths: {
        '/auth/login': {
            post: {
                summary: 'User login',
                tags: ['Authentication'],
                requestBody: {
                    required: true,
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    email: { type: 'string', format: 'email' },
                                    password: { type: 'string', minLength: 8 }
                                },
                                required: ['email', 'password']
                            }
                        }
                    }
                },
                responses: {
                    '200': {
                        description: 'Login successful',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'object',
                                    properties: {
                                        success: { type: 'boolean' },
                                        user: { $ref: '#/components/schemas/User' }
                                    }
                                }
                            }
                        }
                    },
                    '401': {
                        description: 'Invalid credentials'
                    }
                }
            }
        },
        '/cards': {
            get: {
                summary: 'Get user cards',
                tags: ['Cards'],
                security: [{ bearerAuth: [] }],
                parameters: [
                    {
                        name: 'limit',
                        in: 'query',
                        schema: { type: 'integer', minimum: 1, maximum: 100 },
                        description: 'Number of cards to return'
                    },
                    {
                        name: 'type',
                        in: 'query',
                        schema: { type: 'string', enum: ['physical', 'virtual'] },
                        description: 'Card type filter'
                    }
                ],
                responses: {
                    '200': {
                        description: 'Cards retrieved successfully',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'object',
                                    properties: {
                                        success: { type: 'boolean' },
                                        data: {
                                            type: 'array',
                                            items: { $ref: '#/components/schemas/Card' }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    components: {
        schemas: {
            User: {
                type: 'object',
                properties: {
                    id: { type: 'string' },
                    email: { type: 'string', format: 'email' },
                    name: { type: 'string' },
                    dashboard: { 
                        type: 'string', 
                        enum: ['individual', 'corporate', 'cardholder', 'manager', 'b2b'] 
                    }
                }
            },
            Card: {
                type: 'object',
                properties: {
                    id: { type: 'string' },
                    cardMask: { type: 'string', example: '**** **** **** 1234' },
                    type: { type: 'string', enum: ['physical', 'virtual'] },
                    status: { type: 'string', enum: ['active', 'inactive', 'blocked', 'expired'] },
                    expiryDate: { type: 'string', format: 'date' }
                }
            }
        },
        securitySchemes: {
            bearerAuth: {
                type: 'http',
                scheme: 'bearer',
                bearerFormat: 'JWT'
            }
        }
    }
}

export async function GET() {
    return NextResponse.json(API_DOCUMENTATION)
}
```

### **Deployment Documentation**
```markdown
# docs/DEPLOYMENT.md

## Ryvyl Cards - Deployment Guide

### Prerequisites
- Node.js 18+ 
- MongoDB Atlas account
- Environment variables configured

### Environment Variables
```bash
# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/database
DB_NAME=ryvyl

# Authentication
JWT_SECRET=your-super-secure-jwt-secret-here
REFRESH_JWT_SECRET=your-refresh-token-secret-here

# CORS
ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# Security
NODE_ENV=production
```

### Local Development
```bash
# 1. Clone repository
git clone <repository-url>
cd ryvyl-cards

# 2. Install dependencies
npm install

# 3. Set up environment
cp .env.example .env.local
# Edit .env.local with your values

# 4. Run development server
npm run dev
```

### Production Deployment

#### Vercel Deployment
```bash
# 1. Install Vercel CLI
npm i -g vercel

# 2. Deploy
vercel --prod

# 3. Set environment variables in Vercel dashboard
```

#### Docker Deployment
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

### Security Checklist
- [ ] All environment variables set
- [ ] HTTPS enabled
- [ ] Security headers configured
- [ ] Rate limiting enabled
- [ ] Database access restricted
- [ ] Monitoring configured
```

---

## 15. 🔴 Missing Error Monitoring Setup

### ❌ **Problem: No Error Tracking**
```typescript
// No centralized error monitoring
// No performance tracking
// No user session replay
```

### ✅ **Implementation**
```typescript
// src/lib/monitoring.ts
import * as Sentry from '@sentry/nextjs'

// Initialize Sentry
Sentry.init({
    dsn: process.env.SENTRY_DSN,
    environment: process.env.NODE_ENV,
    tracesSampleRate: 1.0,
    beforeSend(event) {
        // Filter out sensitive data
        if (event.request?.data) {
            delete event.request.data.password
            delete event.request.data.token
        }
        return event
    }
})

// Custom error logger
export const logError = (error: Error, context?: Record<string, any>) => {
    Sentry.withScope((scope) => {
        if (context) {
            Object.keys(context).forEach(key => {
                scope.setTag(key, context[key])
            })
        }
        Sentry.captureException(error)
    })
}

// Performance monitoring
export const trackPerformance = (name: string, fn: () => Promise<any>) => {
    return Sentry.startSpan({ name }, async () => {
        return await fn()
    })
}

// Usage in API routes
export async function GET(request: NextRequest) {
    try {
        return await trackPerformance('get-cards', async () => {
            // API logic here
        })
    } catch (error) {
        logError(error as Error, {
            endpoint: '/api/cards',
            userId: 'user-id',
            method: 'GET'
        })
        throw error
    }
}
```

This comprehensive set of examples shows the actual problematic code found in your codebase and provides working, secure implementations to replace them. Each example includes the exact file location where the problem exists and a complete solution that addresses the security, performance, or functionality issue.
