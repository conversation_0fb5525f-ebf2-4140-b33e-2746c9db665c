# 🔧 Code Quality Issues - Real Examples & Fixes

This document shows **actual code quality problems** and **performance issues** found in the codebase with working solutions.

---

## 7. 🔴 Magic Numbers & Strings

### ❌ **Problem: Hardcoded Values (`src/app/corporate/cards/page.tsx:22-23`)**
```typescript
const DEFAULT_COMPANY_ID = "6785126247b8a6a67fbf7cad"  // ⚠️ Magic string!
const PHYSICAL_CARD_LIMIT = 5                           // ⚠️ Magic number!
const VIRTUAL_CARD_LIMIT = 6                            // ⚠️ Magic number!

// More magic numbers scattered throughout
const BATCH_SIZE = 1000                                 // ⚠️ In database.ts
const limit = Number.parseInt(searchParams.get("limit") || "100")  // ⚠️ Magic default
```

### ✅ **Fixed Version**
```typescript
// src/config/constants.ts
export const CARD_LIMITS = {
  PHYSICAL: 5,
  VIRTUAL: 6,
  DAILY_SPEND: 10000,
  MONTHLY_SPEND: 50000
} as const

export const DATABASE_CONFIG = {
  BATCH_SIZE: 1000,
  MAX_CONNECTIONS: 10,
  TIMEOUT_MS: 30000,
  RETRY_ATTEMPTS: 3
} as const

export const API_DEFAULTS = {
  PAGE_SIZE: 100,
  MAX_PAGE_SIZE: 1000,
  SEARCH_LIMIT: 50
} as const

export const VALIDATION_RULES = {
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  MAX_FILENAME_LENGTH: 255,
  MAX_RECORDS_PER_UPLOAD: 100000
} as const

// Usage in components
import { CARD_LIMITS, API_DEFAULTS } from '@/config/constants'

const CardManagement = () => {
  const [physicalCards, setPhysicalCards] = useState<Card[]>([])
  
  const canCreatePhysicalCard = physicalCards.length < CARD_LIMITS.PHYSICAL
  const defaultPageSize = API_DEFAULTS.PAGE_SIZE
  
  // ...
}
```

---

## 8. 🔴 Complex Components Without Memoization

### ❌ **Problem: Performance Issues (`src/components/company-profile.tsx:446-463`)**
```typescript
const CompanyProfile = ({ params }: { params: { id: string } }) => {
    const [companyData, setCompanyData] = useState<Company | null>(null)
    const [loading, setLoading] = useState(true)
    
    // ⚠️ No memoization - runs on every render!
    const fetchCompanyDetails = useCallback(async () => {
        try {
            setLoading(true)
            const response = await axiosInstance.get(`/company/${params.id}`)
            setCompanyData(response.data.company)
            // ... more expensive operations
        } catch (error: any) {
            console.error("Error:", error)
        } finally {
            setLoading(false)
        }
    }, [params.id])  // ⚠️ Missing dependencies!
    
    // ⚠️ Expensive calculation on every render!
    const processedData = companyData ? {
        ...companyData,
        formattedBalance: formatCurrency(companyData.balance),
        riskScore: calculateRiskScore(companyData),
        complianceStatus: checkCompliance(companyData)
    } : null
    
    // ⚠️ No memoization for expensive components!
    return (
        <div>
            <ExpensiveChart data={processedData} />
            <ComplexTable records={companyData?.records} />
        </div>
    )
}
```

### ✅ **Fixed Version**
```typescript
import React, { useState, useCallback, useMemo, memo } from 'react'

// Memoized expensive components
const ExpensiveChart = memo(({ data }: { data: any }) => {
    console.log('ExpensiveChart rendered')
    // Expensive chart rendering logic
    return <div>Chart for {data?.name}</div>
})

const ComplexTable = memo(({ records }: { records: any[] }) => {
    console.log('ComplexTable rendered')
    // Complex table logic
    return <div>Table with {records?.length} records</div>
})

// Memoized calculation functions
const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount)
}

const calculateRiskScore = (company: Company): number => {
    // Expensive risk calculation
    return Math.random() * 100
}

const checkCompliance = (company: Company): string => {
    // Complex compliance check
    return company.verified ? 'Compliant' : 'Pending'
}

const CompanyProfile = ({ params }: { params: { id: string } }) => {
    const [companyData, setCompanyData] = useState<Company | null>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    
    // Properly memoized fetch function
    const fetchCompanyDetails = useCallback(async () => {
        if (!params.id) return
        
        try {
            setLoading(true)
            setError(null)
            const response = await axiosInstance.get(`/company/${params.id}`)
            setCompanyData(response.data.company)
        } catch (error: any) {
            console.error("Error in fetchCompanyDetails:", error)
            setError("Failed to load company data")
        } finally {
            setLoading(false)
        }
    }, [params.id])
    
    // Memoized expensive calculations
    const processedData = useMemo(() => {
        if (!companyData) return null
        
        return {
            ...companyData,
            formattedBalance: formatCurrency(companyData.balance),
            riskScore: calculateRiskScore(companyData),
            complianceStatus: checkCompliance(companyData)
        }
    }, [companyData])
    
    // Memoized filtered records
    const filteredRecords = useMemo(() => {
        return companyData?.records?.filter(record => record.active) || []
    }, [companyData?.records])
    
    useEffect(() => {
        fetchCompanyDetails()
    }, [fetchCompanyDetails])
    
    if (loading) return <LoadingSkeleton />
    if (error) return <ErrorDisplay error={error} onRetry={fetchCompanyDetails} />
    if (!processedData) return <EmptyState />
    
    return (
        <div>
            <ExpensiveChart data={processedData} />
            <ComplexTable records={filteredRecords} />
        </div>
    )
}

export default memo(CompanyProfile)
```

---

## 9. 🔴 Scattered Authentication Checks

### ❌ **Problem: Duplicated Auth Logic**
```typescript
// In src/app/corporate/layout.tsx:97-123
useEffect(() => {
    const checkAuth = async () => {
        try {
            const response = await axiosInstance.get("users/me", { withCredentials: true })
            if (u && u.dashboard === "corporate" && u.recordId !== null) {
                // Auth logic here
            } else {
                router.push("/login")
            }
        } catch (error) {
            router.push("/login")
        }
    }
    checkAuth()
}, [router, dispatch])

// In src/app/lite/admin/layout.tsx:229-250 (DUPLICATE!)
useEffect(() => {
    const checkAuth = async () => {
        try {
            const response = await axiosInstance.get("users/me", { withCredentials: true })
            if (response.data && response.data.dashboard !== "cardholder") {
                // Different auth logic here
            } else {
                router.push("/login")
            }
        } catch (error) {
            router.push("/login")
        }
    }
    checkAuth()
}, [router, dispatch])

// In src/components/with-auth.tsx:12-26 (ANOTHER DUPLICATE!)
const checkAuth = async () => {
    try {
        const response = await axiosInstance.get('/users/me', { withCredentials: true });
        if (!response.data) {
            router.push('/login');
        }
    } catch (error) {
        router.push('/login');
    }
};
```

### ✅ **Fixed Version**
```typescript
// src/hooks/useAuth.ts - Centralized authentication
import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useDispatch } from 'react-redux'
import { setUser, clearUser } from '@/store/userSlice'
import axiosInstance from '@/utils/axiosInstance'

export interface User {
    id: string
    email: string
    name: string
    dashboard: 'individual' | 'corporate' | 'cardholder' | 'manager' | 'b2b'
    recordId?: string
    roles: string[]
    permissions: string[]
}

export interface AuthState {
    user: User | null
    isAuthenticated: boolean
    isLoading: boolean
    error: string | null
}

export const useAuth = (requiredDashboard?: string) => {
    const [authState, setAuthState] = useState<AuthState>({
        user: null,
        isAuthenticated: false,
        isLoading: true,
        error: null
    })
    
    const router = useRouter()
    const dispatch = useDispatch()
    
    const checkAuth = useCallback(async () => {
        try {
            setAuthState(prev => ({ ...prev, isLoading: true, error: null }))
            
            const response = await axiosInstance.get('/users/me', { withCredentials: true })
            const userData = response.data
            
            if (!userData || !userData.id || !userData.email) {
                throw new Error('Invalid user data')
            }
            
            // Check dashboard access if required
            if (requiredDashboard && userData.dashboard !== requiredDashboard) {
                throw new Error(`Access denied. Required: ${requiredDashboard}, Got: ${userData.dashboard}`)
            }
            
            // Additional validation based on dashboard type
            if (userData.dashboard === 'corporate' && !userData.recordId) {
                throw new Error('Corporate users must have a record ID')
            }
            
            const user: User = {
                id: userData._id,
                email: userData.email,
                name: userData.name,
                dashboard: userData.dashboard,
                recordId: userData.recordId,
                roles: userData.roles || [],
                permissions: userData.permissions || []
            }
            
            setAuthState({
                user,
                isAuthenticated: true,
                isLoading: false,
                error: null
            })
            
            dispatch(setUser(user))
            
        } catch (error: any) {
            console.error('Authentication failed:', error)
            
            setAuthState({
                user: null,
                isAuthenticated: false,
                isLoading: false,
                error: error.message || 'Authentication failed'
            })
            
            dispatch(clearUser())
            
            // Clear any stored auth data
            localStorage.removeItem('authToken')
            sessionStorage.clear()
            
            // Redirect to login
            router.push('/login')
        }
    }, [requiredDashboard, router, dispatch])
    
    const logout = useCallback(async () => {
        try {
            await axiosInstance.post('/auth/logout')
        } catch (error) {
            console.error('Logout error:', error)
        } finally {
            setAuthState({
                user: null,
                isAuthenticated: false,
                isLoading: false,
                error: null
            })
            
            dispatch(clearUser())
            localStorage.removeItem('authToken')
            sessionStorage.clear()
            router.push('/login')
        }
    }, [router, dispatch])
    
    useEffect(() => {
        checkAuth()
    }, [checkAuth])
    
    return {
        ...authState,
        checkAuth,
        logout,
        hasRole: (role: string) => authState.user?.roles.includes(role) || false,
        hasPermission: (permission: string) => authState.user?.permissions.includes(permission) || false
    }
}

// Usage in layouts - Corporate
const CorporateLayout = ({ children }: { children: React.ReactNode }) => {
    const { user, isAuthenticated, isLoading, error } = useAuth('corporate')
    
    if (isLoading) return <LoadingSpinner />
    if (error) return <ErrorPage error={error} />
    if (!isAuthenticated) return null // Will redirect to login
    
    return (
        <div className="corporate-layout">
            <Header user={user} />
            <Sidebar permissions={user?.permissions} />
            <main>{children}</main>
        </div>
    )
}

// Usage in components - Protected component
const ProtectedComponent = () => {
    const { user, hasPermission } = useAuth()
    
    if (!hasPermission('cards.manage')) {
        return <AccessDenied />
    }
    
    return <CardManagement user={user} />
}
```

---

## 10. 🔴 Unhandled Promise Rejections

### ❌ **Problem: Missing Error Handling**
```typescript
// In src/components/activity-sidebar.tsx:177-203
const fetchLogs = async () => {
    setLoading(true)
    setError(null)
    try {
        const response = await axios.get<ActivityResponse>("/api/activity", {
            params: { limit: 20, type: activityType }
        })
        
        if (response.data.success) {
            setLogs(response.data.data)
        } else {
            setError("Failed to fetch activities")  // ⚠️ Generic error!
        }
    } catch (error: any) {
        console.error("Failed to fetch activities:", error)  // ⚠️ Only console log!
        setError("Failed to fetch activities")               // ⚠️ No specific handling!
    } finally {
        setLoading(false)
    }
}

// In src/components/company-profile.tsx:577-585
const getAccountBalance = async (account = "**********************") => {
    try {
        const response = await axiosInstance.get(`/legacy/fetch-balance/${account}`)
        setBalance(response.data.availableBalance)
    } catch (error) {
        console.error("Error fetching account balance:", error)  // ⚠️ Silent failure!
        // ⚠️ No user feedback, no retry mechanism!
    }
}
```

### ✅ **Fixed Version**
```typescript
// src/lib/error-handler.ts
export class AppError extends Error {
    constructor(
        message: string,
        public code: string,
        public statusCode: number = 500,
        public isOperational: boolean = true
    ) {
        super(message)
        this.name = 'AppError'
    }
}

export const handleApiError = (error: any): AppError => {
    if (error.response) {
        // Server responded with error status
        const { status, data } = error.response
        return new AppError(
            data.message || 'Server error occurred',
            data.code || 'SERVER_ERROR',
            status
        )
    } else if (error.request) {
        // Network error
        return new AppError(
            'Network error - please check your connection',
            'NETWORK_ERROR',
            0
        )
    } else {
        // Other error
        return new AppError(
            error.message || 'An unexpected error occurred',
            'UNKNOWN_ERROR'
        )
    }
}

// src/hooks/useAsyncOperation.ts
import { useState, useCallback } from 'react'
import { AppError, handleApiError } from '@/lib/error-handler'

interface AsyncState<T> {
    data: T | null
    loading: boolean
    error: AppError | null
}

export const useAsyncOperation = <T>() => {
    const [state, setState] = useState<AsyncState<T>>({
        data: null,
        loading: false,
        error: null
    })
    
    const execute = useCallback(async (
        operation: () => Promise<T>,
        onSuccess?: (data: T) => void,
        onError?: (error: AppError) => void
    ) => {
        setState({ data: null, loading: true, error: null })
        
        try {
            const result = await operation()
            setState({ data: result, loading: false, error: null })
            onSuccess?.(result)
            return result
        } catch (error) {
            const appError = handleApiError(error)
            setState({ data: null, loading: false, error: appError })
            onError?.(appError)
            throw appError
        }
    }, [])
    
    const reset = useCallback(() => {
        setState({ data: null, loading: false, error: null })
    }, [])
    
    return { ...state, execute, reset }
}

// Fixed components
const ActivitySidebar = () => {
    const [logs, setLogs] = useState<ActivityLog[]>([])
    const { loading, error, execute } = useAsyncOperation<ActivityResponse>()
    
    const fetchLogs = useCallback(async () => {
        await execute(
            async () => {
                const response = await axios.get<ActivityResponse>("/api/activity", {
                    params: { limit: 20, type: activityType },
                    timeout: 10000 // 10 second timeout
                })
                
                if (!response.data.success) {
                    throw new AppError(
                        response.data.error || 'Failed to fetch activities',
                        'FETCH_ACTIVITIES_FAILED'
                    )
                }
                
                return response.data
            },
            (data) => {
                setLogs(data.data)
            },
            (error) => {
                // Log to monitoring service
                console.error('Activity fetch failed:', {
                    error: error.message,
                    code: error.code,
                    timestamp: new Date().toISOString()
                })
                
                // Show user-friendly error
                toast.error(`Failed to load activities: ${error.message}`)
            }
        )
    }, [activityType, execute])
    
    // Auto-retry with exponential backoff
    useEffect(() => {
        let retryCount = 0
        const maxRetries = 3
        
        const fetchWithRetry = async () => {
            try {
                await fetchLogs()
                retryCount = 0 // Reset on success
            } catch (error) {
                if (retryCount < maxRetries) {
                    retryCount++
                    const delay = Math.pow(2, retryCount) * 1000 // Exponential backoff
                    setTimeout(fetchWithRetry, delay)
                }
            }
        }
        
        fetchWithRetry()
    }, [fetchLogs])
    
    if (loading) return <LoadingSpinner />
    if (error) return <ErrorDisplay error={error} onRetry={fetchLogs} />
    
    return <ActivityList logs={logs} />
}
```
