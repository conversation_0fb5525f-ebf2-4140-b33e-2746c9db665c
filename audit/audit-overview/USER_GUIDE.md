# 📖 Ryvyl Cards - User Guide & Documentation

This document provides **missing basic documentation** on how to use the app and access different dashboard types.

---

## 🚀 Getting Started

### Application Overview
Ryvyl Cards is a multi-tenant financial platform that provides different dashboard experiences based on user roles:

- **Individual**: Personal card management
- **Corporate**: Business card management with team features
- **Cardholder**: Limited access for card users
- **Manager**: Administrative oversight capabilities
- **B2B**: Business-to-business integration features

### First Time Setup

#### 1. **Environment Configuration**
```bash
# Create environment file
cp .env.example .env.local

# Required environment variables
MONGODB_URI=your-mongodb-connection-string
JWT_SECRET=your-jwt-secret
ALLOWED_ORIGINS=http://localhost:3000
NODE_ENV=development
```

#### 2. **Install Dependencies**
```bash
npm install
```

#### 3. **Run Development Server**
```bash
npm run dev
```

#### 4. **Access the Application**
Open [http://localhost:3000](http://localhost:3000) in your browser.

---

## 🔐 Authentication & Access

### Login Process
1. Navigate to `/login`
2. Enter your email and password
3. System automatically redirects to appropriate dashboard based on user role

### Dashboard Access URLs

| User Type | Dashboard URL | Description |
|-----------|---------------|-------------|
| Individual | `/individual` | Personal card management |
| Corporate | `/corporate` | Business card management |
| Cardholder | `/cardholder` | Limited card access |
| Manager | `/manager` | Administrative dashboard |
| B2B | `/b2b` | Business integration |
| Admin | `/lite/admin` | System administration |

---

## 🏢 Manager Dashboard Guide

### Accessing Manager Dashboard

#### **Method 1: Direct URL**
```
http://localhost:3000/manager
```

#### **Method 2: Role-Based Redirect**
1. Login with manager credentials
2. System automatically redirects to manager dashboard

### Manager Dashboard Features

#### **1. Company Management**
- **Location**: `/manager/companies`
- **Features**:
  - View all companies
  - Add new companies
  - Edit company details
  - Manage company status

#### **2. User Management**
- **Location**: `/manager/users`
- **Features**:
  - View all users across companies
  - Create new user accounts
  - Assign roles and permissions
  - Manage user status

#### **3. Card Oversight**
- **Location**: `/manager/cards`
- **Features**:
  - View all cards across companies
  - Monitor card usage
  - Block/unblock cards
  - Generate reports

#### **4. Activity Monitoring**
- **Location**: `/manager/activity`
- **Features**:
  - Real-time activity logs
  - User action tracking
  - Security event monitoring
  - Export activity reports

#### **5. System Settings**
- **Location**: `/manager/settings`
- **Features**:
  - Configure system parameters
  - Manage card limits
  - Set validation rules
  - Update security settings

### Manager Permissions

```typescript
// Manager role permissions
const MANAGER_PERMISSIONS = [
    'companies.view',
    'companies.create',
    'companies.edit',
    'users.view',
    'users.create',
    'users.edit',
    'cards.view',
    'cards.manage',
    'activity.view',
    'settings.manage',
    'reports.generate'
]
```

---

## 🏢 Corporate Dashboard Guide

### Accessing Corporate Dashboard

#### **Prerequisites**
- User must have `dashboard: "corporate"` role
- User must have a valid `recordId` (company association)

#### **Access URL**
```
http://localhost:3000/corporate
```

### Corporate Dashboard Features

#### **1. Account Management**
- **Location**: `/corporate/accounts`
- **Features**:
  - View account balances
  - Transaction history
  - Account statements
  - Payment processing

#### **2. Card Management**
- **Location**: `/corporate/cards`
- **Features**:
  - Create physical cards (limit: 5)
  - Create virtual cards (limit: 6)
  - Manage card status
  - Set card limits
  - View card transactions

#### **3. Team Management**
- **Location**: `/corporate/team`
- **Features**:
  - Add team members
  - Assign card access
  - Manage permissions
  - View team activity

#### **4. Settings & Configuration**
- **Location**: `/corporate/settings`
- **Features**:
  - Company profile management
  - Notification preferences
  - Security settings
  - Integration configuration

---

## 👤 Individual Dashboard Guide

### Accessing Individual Dashboard

#### **Access URL**
```
http://localhost:3000/individual
```

### Individual Dashboard Features

#### **1. Personal Cards**
- View personal cards
- Request new cards
- Manage card settings
- View transaction history

#### **2. Account Overview**
- Account balance
- Recent transactions
- Payment history
- Account statements

#### **3. Profile Management**
- Personal information
- Contact details
- Security settings
- Notification preferences

---

## 💳 Cardholder Dashboard Guide

### Accessing Cardholder Dashboard

#### **Access URL**
```
http://localhost:3000/cardholder
```

### Cardholder Features (Limited Access)

#### **1. Card Information**
- View assigned cards
- Check card status
- View transaction history
- Download statements

#### **2. Card Controls**
- Activate/deactivate cards
- Set spending limits
- Block/unblock cards
- Report lost/stolen cards

---

## 🔧 File Upload & Data Processing

### Supported File Types
- **Excel Files**: `.xlsx`, `.xls`
- **CSV Files**: `.csv`
- **Maximum Size**: 50MB
- **Maximum Records**: 100,000 per file

### Upload Process

#### **1. Access Upload Interface**
```
http://localhost:3000/corporate/data-upload
```

#### **2. File Validation**
- File type validation
- Size limit checking
- Content scanning for malicious data
- Header validation

#### **3. Processing Options**
```typescript
interface UploadOptions {
    duplicateHandling: 'skip' | 'replace' | 'merge'
    validationLevel: 'strict' | 'moderate' | 'lenient'
    batchSize: number // 1-1000
}
```

#### **4. Progress Tracking**
- Real-time upload progress
- Validation results
- Error reporting
- Success confirmation

---

## 🛠️ Troubleshooting

### Common Issues

#### **1. Cannot Access Manager Dashboard**
**Problem**: Getting redirected to login or access denied

**Solutions**:
```typescript
// Check user role in database
db.users.findOne({ email: "<EMAIL>" })

// Ensure user has correct dashboard setting
{
    "dashboard": "manager",
    "roles": ["manager"],
    "permissions": ["companies.view", "users.manage"]
}
```

#### **2. Corporate Dashboard Access Denied**
**Problem**: User cannot access corporate features

**Solutions**:
```typescript
// Check user has company association
{
    "dashboard": "corporate",
    "recordId": "valid-company-id",
    "companyId": "company-object-id"
}
```

#### **3. File Upload Failures**
**Problem**: Files not uploading or processing

**Solutions**:
- Check file size (max 50MB)
- Verify file format (.xlsx, .csv)
- Ensure proper headers in file
- Check network connection

#### **4. Authentication Issues**
**Problem**: Getting logged out frequently

**Solutions**:
- Check JWT token expiration
- Verify environment variables
- Clear browser cache and cookies
- Check server logs for errors

### Debug Commands

#### **Check User Permissions**
```bash
# MongoDB query to check user
db.users.findOne({ email: "<EMAIL>" })
```

#### **Verify Environment**
```bash
# Check required environment variables
echo $MONGODB_URI
echo $JWT_SECRET
```

#### **Check Application Logs**
```bash
# View application logs
npm run dev
# Check browser console for errors
```

---

## 📊 API Usage Examples

### Authentication
```typescript
// Login
const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
    })
})
```

### Get Cards
```typescript
// Fetch user cards
const response = await fetch('/api/cards', {
    headers: {
        'Authorization': `Bearer ${token}`
    }
})
```

### Upload Data
```typescript
// Upload file data
const response = await fetch('/api/save-records-enhanced', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
        records: fileData,
        options: {
            duplicateHandling: 'skip',
            validationLevel: 'strict',
            batchSize: 1000
        }
    })
})
```

---

## 🔒 Security Best Practices

### For Users
1. **Use strong passwords** (minimum 8 characters)
2. **Log out when finished** using the application
3. **Don't share login credentials** with others
4. **Report suspicious activity** immediately

### For Administrators
1. **Regularly update user permissions**
2. **Monitor activity logs** for unusual behavior
3. **Keep software updated** with latest security patches
4. **Backup data regularly**

### For Developers
1. **Never commit secrets** to version control
2. **Use environment variables** for configuration
3. **Implement proper input validation**
4. **Follow secure coding practices**

---

## 📞 Support & Contact

### Getting Help
1. **Check this documentation** first
2. **Review error messages** in browser console
3. **Check application logs** for detailed errors
4. **Contact system administrator** if issues persist

### Reporting Issues
When reporting issues, include:
- User role and dashboard type
- Steps to reproduce the problem
- Error messages (if any)
- Browser and operating system information
- Screenshots (if applicable)

---

## 🔄 Regular Maintenance

### Daily Tasks
- Monitor application logs
- Check system performance
- Review security alerts

### Weekly Tasks
- Update user permissions
- Review activity reports
- Backup database

### Monthly Tasks
- Update dependencies
- Security audit
- Performance optimization
- User access review
