# Authentication Module Documentation

## Module Identity

- **Module Name**: Authentication System
- **Type**: Core Security Module
- **Repository**: ryvyl-cards-main
- **Framework**: Next.js 14 with JWT
- **Version**: 1.0.0
- **Location**: `src/middleware.ts`, `src/utils/auth-utils.ts`, `src/components/with-auth.tsx`

## Purpose and Functionality

### Business Purpose
The Authentication module provides secure user authentication and authorization for the Ryvyl Cards platform, supporting multiple user types (individual, corporate, cardholder, manager, B2B) with role-based access control.

### Core Features
- **JWT-based Authentication**: Token-based authentication system
- **Multi-tenant Authorization**: Role-based access control for different user types
- **Session Management**: User session tracking and management
- **Activity Logging**: Comprehensive authentication event logging
- **Auto-logout**: Inactivity-based session termination

### Target Users
- **Individual Customers**: Personal account access
- **Corporate Clients**: Business account management
- **Cardholders**: Limited card-specific access
- **Managers**: Administrative access
- **B2B Partners**: API-based integration access

## Technical Architecture

### Pattern
**Middleware-based Authentication** with JWT tokens and role-based routing

### Key Components

#### 1. Authentication Middleware (`src/middleware.ts`)
```typescript
export async function middleware(request: NextRequest) {
    const decodeJWT = (token: string) => {
        // Client-side JWT decoding (⚠️ SECURITY ISSUE)
        const parts = token.split(".")
        const payload = JSON.parse(atob(parts[1]))
        return { payload, isExpired: Date.now() >= payload.exp * 1000 }
    }
}
```

#### 2. Auth Utilities (`src/utils/auth-utils.ts`)
```typescript
export async function verifyToken(token: string): Promise<UserJwtPayload | null> {
    const secret = new TextEncoder().encode(process.env.JWT_SECRET)
    const { payload } = await jwtVerify(token, secret)
    return payload as UserJwtPayload
}
```

#### 3. HOC Authentication (`src/components/with-auth.tsx`)
```typescript
const withAuth = (WrappedComponent: React.ComponentType) => {
    // Authentication wrapper for protected components
}
```

### Data Flow

```mermaid
sequenceDiagram
    participant User
    participant Client
    participant Middleware
    participant API
    participant Database
    
    User->>Client: Login Request
    Client->>API: POST /api/auth/login
    API->>Database: Validate Credentials
    Database-->>API: User Data + Roles
    API-->>Client: JWT Token
    Client->>Client: Store in localStorage (⚠️)
    Client->>Middleware: Protected Route Access
    Middleware->>Middleware: Decode JWT (Client-side ⚠️)
    Middleware-->>Client: Route Access Decision
```

### Performance Characteristics
- **Token Validation**: Client-side (fast but insecure)
- **Session Storage**: localStorage (persistent but vulnerable)
- **Route Protection**: Middleware-based (efficient)

## Dependencies and Integrations

### Libraries
```json
{
  "jose": "^6.0.11",              // JWT verification (unused in middleware)
  "jsonwebtoken": "^9.0.2",       // JWT creation
  "bcryptjs": "^2.4.3"            // Password hashing
}
```

### Internal Dependencies
- **Redux Store**: User state management
- **Axios Instance**: API communication with token injection
- **Activity Logger**: Authentication event tracking

### External Integrations
- **MongoDB**: User credential storage
- **Session Storage**: Client-side token persistence

## Development Info

### Build Process
- **TypeScript Compilation**: Included in Next.js build
- **Middleware Compilation**: Edge runtime compatible
- **Environment Variables**: JWT_SECRET required

### Testing
- **Unit Tests**: ❌ Missing
- **Integration Tests**: ❌ Missing
- **Security Tests**: ❌ Missing

### Linting
- **ESLint**: Basic configuration
- **Security Rules**: ❌ Missing security-specific rules

### Setup Requirements
```bash
# Environment variables required
JWT_SECRET=your-secret-key
DATABASE_URL=mongodb-connection-string
```

## Deployment & Operations

### CI/CD
- **Build**: Next.js production build
- **Deployment**: Vercel/similar platform
- **Environment**: Production environment variables

### Configuration
```typescript
// Required environment variables
process.env.JWT_SECRET        // JWT signing secret
process.env.DATABASE_URL      // Database connection
process.env.NODE_ENV         // Environment mode
```

### Scaling Considerations
- **Stateless Design**: JWT tokens enable horizontal scaling
- **Session Storage**: Client-side storage reduces server load
- **Middleware Performance**: Edge runtime for fast execution

## Monitoring & Health

### Logging
- **Activity Tracking**: All authentication events logged
- **Error Logging**: Console-based error logging
- **Audit Trail**: User login/logout events tracked

### Metrics
- **Login Success Rate**: Tracked via activity logs
- **Session Duration**: Tracked via activity logs
- **Failed Attempts**: Logged for security monitoring

### Alerts
- **Failed Login Attempts**: ❌ Not implemented
- **Suspicious Activity**: ❌ Not implemented
- **Token Expiration**: ❌ Not implemented

## Database Usage

### Collections Used
- **users**: User credentials and profile data
- **activities**: Authentication event logging

### Schema
```typescript
interface User {
  _id: ObjectId
  email: string
  passwordHash: string
  roles: string[]
  dashboard: 'individual' | 'corporate' | 'cardholder' | 'manager' | 'b2b'
  recordId?: string
  permissions?: string[]
}
```

## Security Considerations

### 🔴 CRITICAL VULNERABILITIES

#### 1. Client-Side JWT Validation
- **Location**: `src/middleware.ts:6-28`
- **Issue**: JWT tokens decoded and validated client-side
- **Risk**: Authentication bypass, token tampering
- **CVSS**: 9.1 (Critical)
- **Fix**: Implement server-side JWT verification

#### 2. Insecure Token Storage
- **Location**: `src/utils/axiosInstance.js:12`
- **Issue**: JWT stored in localStorage
- **Risk**: XSS token theft
- **CVSS**: 8.5 (High)
- **Fix**: Use httpOnly cookies

#### 3. Missing CSRF Protection
- **Issue**: No CSRF tokens implemented
- **Risk**: Cross-site request forgery
- **CVSS**: 7.5 (High)
- **Fix**: Implement CSRF protection

### 🟡 HIGH RISK ISSUES

#### 4. Weak Session Management
- **Issue**: No session invalidation on logout
- **Risk**: Session hijacking
- **Fix**: Implement proper session management

#### 5. Missing Rate Limiting
- **Issue**: No protection against brute force attacks
- **Risk**: Credential stuffing attacks
- **Fix**: Implement rate limiting

### Security Fixes Required

```typescript
// 1. Server-side JWT verification
export async function middleware(request: NextRequest) {
    const token = getTokenFromRequest(request)
    try {
        const payload = await verifyToken(token)
        // Proceed with verified token
    } catch (error) {
        return NextResponse.redirect('/login')
    }
}

// 2. Secure token storage
const setSecureToken = (token: string) => {
    document.cookie = `token=${token}; HttpOnly; Secure; SameSite=Strict; Max-Age=3600`
}

// 3. CSRF protection
const csrfToken = generateCSRFToken()
headers['X-CSRF-Token'] = csrfToken
```

## Operational Procedures

### Start/Stop
- **Start**: Automatic with Next.js application
- **Stop**: Graceful shutdown with application

### Troubleshooting

#### Common Issues
1. **Token Expiration**: Check JWT expiration time
2. **Invalid Credentials**: Verify user exists in database
3. **Role Access Denied**: Check user roles and permissions

#### Debug Commands
```bash
# Check JWT token validity
node -e "console.log(JSON.parse(atob('JWT_PAYLOAD_HERE')))"

# Verify database connection
mongosh $DATABASE_URL --eval "db.users.findOne()"
```

## APIs & Integration Points

### Authentication Endpoints
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/users/me` - Get current user
- `POST /api/auth/refresh` - ❌ Not implemented

### Integration Contracts
```typescript
interface LoginRequest {
  email: string
  password: string
}

interface LoginResponse {
  token: string
  user: UserProfile
  dashboard: string
}
```

### Consumers
- **All Protected Routes**: Require authentication
- **API Endpoints**: Token-based access
- **Dashboard Components**: Role-based rendering

## Development Context for AI Agents

### Patterns Used
- **HOC Pattern**: `withAuth` for component protection
- **Middleware Pattern**: Request interception
- **JWT Pattern**: Stateless authentication

### Extension Points
- **Custom Auth Providers**: Add OAuth, SAML
- **Role System**: Extend role-based permissions
- **Session Management**: Add Redis-based sessions

### Impact of Changes
- **Middleware Changes**: Affects all protected routes
- **Token Format Changes**: Requires client updates
- **Role Changes**: Impacts authorization logic

## Ownership & Contact

### Responsible Team
- **Development Team**: Full-stack developers
- **Security Team**: Security review and compliance
- **DevOps Team**: Deployment and monitoring

### Subject Matter Experts
- **Authentication Logic**: Senior Backend Developer
- **Security Implementation**: Security Engineer
- **Frontend Integration**: Frontend Lead

### Documentation Links
- **API Documentation**: ❌ Not available
- **Security Guidelines**: ❌ Not available
- **Deployment Guide**: ❌ Not available

## Security Score: 2.5/10 (Critical Risk)

The authentication module has critical security vulnerabilities that require immediate attention. Client-side JWT validation and insecure token storage pose significant risks to the entire application.
