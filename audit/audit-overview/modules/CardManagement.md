# Card Management Module Documentation

## Module Identity

- **Module Name**: Card Management System
- **Type**: Core Business Module
- **Repository**: ryvyl-cards-main
- **Framework**: Next.js 14 with React
- **Version**: 1.0.0
- **Location**: `src/app/*/cards/`, `src/components/card-*`

## Purpose and Functionality

### Business Purpose
The Card Management module handles the complete lifecycle of physical and virtual payment cards, including creation, status management, limits enforcement, and card image handling for the Ryvyl Cards platform.

### Core Features
- **Card Creation**: Physical and virtual card issuance
- **Card Status Management**: Active, inactive, blocked, expired states
- **Card Limits**: Configurable spending and transaction limits
- **Card Images**: Custom card design and branding
- **Card Analytics**: Usage statistics and reporting
- **Multi-tenant Support**: Different card types per user role

### Target Users
- **Corporate Clients**: Business card management
- **Cardholders**: Personal card access and management
- **Managers**: Administrative card oversight
- **Individual Customers**: Personal card services

## Technical Architecture

### Pattern
**Component-based Architecture** with role-specific card management interfaces

### Key Components

#### 1. Corporate Card Management (`src/app/corporate/cards/page.tsx`)
```typescript
const DEFAULT_COMPANY_ID = "6785126247b8a6a67fbf7cad"  // ⚠️ Hardcoded
const PHYSICAL_CARD_LIMIT = 5
const VIRTUAL_CARD_LIMIT = 6

interface CardImage {
    _id: string
    front_side: string
    back_side: string
    product_version: { version_code: string }
    company: { _id: string }
}
```

#### 2. Cardholder Interface (`src/app/cardholder/cards/`)
- **Card Display**: Personal card information
- **Transaction History**: Card usage tracking
- **Card Controls**: Activation, blocking, limits

#### 3. Card Data Structure
```typescript
interface Card {
    embossName1: string
    cardMask: string        // Masked card number
    expDate: string
    status: 'active' | 'inactive' | 'blocked' | 'expired'
    productDesc: string     // PHY/VIRTUAL indicator
    createdAt: Date
}
```

### Data Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant Database
    participant CardProcessor
    
    User->>Frontend: Request New Card
    Frontend->>API: POST /api/cards/create
    API->>Database: Validate User Limits
    Database-->>API: Limit Check Result
    API->>CardProcessor: Create Card Request
    CardProcessor-->>API: Card Details
    API->>Database: Store Card Data
    Database-->>API: Confirmation
    API-->>Frontend: Card Created
    Frontend-->>User: Display New Card
```

### Performance Characteristics
- **Card Loading**: Paginated with 20 cards per page
- **Image Loading**: Lazy loading for card images
- **Status Updates**: Real-time status synchronization
- **Bulk Operations**: Batch processing for multiple cards

## Dependencies and Integrations

### Libraries
```json
{
  "react": "^18.3.1",           // UI components
  "next": "14.2.13",            // Framework
  "lucide-react": "^0.476.0",   // Icons
  "date-fns": "3.6.0"           // Date formatting
}
```

### Internal Dependencies
- **Authentication Module**: User verification
- **Company Profile**: Company-specific card settings
- **Activity Logger**: Card operation tracking
- **Data Exporter**: Card data export functionality

### External Integrations
- **Card Processing API**: Third-party card issuance
- **Payment Gateway**: Transaction processing
- **Image Storage**: Card design assets
- **Compliance Services**: KYC/AML verification

## Development Info

### Build Process
- **Component Compilation**: Next.js React compilation
- **Image Optimization**: Next.js image optimization
- **Type Checking**: TypeScript validation

### Testing
- **Unit Tests**: ❌ Missing
- **Integration Tests**: ❌ Missing
- **E2E Tests**: ❌ Missing for card workflows

### Linting
- **ESLint**: Basic React linting
- **Type Safety**: Partial TypeScript coverage

### Setup Requirements
```bash
# Environment variables
CARD_PROCESSOR_API_URL=https://api.cardprocessor.com
CARD_PROCESSOR_API_KEY=your-api-key
IMAGE_STORAGE_URL=https://storage.example.com
```

## Deployment & Operations

### CI/CD
- **Build**: Next.js production build
- **Testing**: ❌ No automated testing
- **Deployment**: Vercel/similar platform

### Configuration
```typescript
// Card limits configuration
const CARD_LIMITS = {
  PHYSICAL: 5,
  VIRTUAL: 6,
  DAILY_SPEND: 10000,
  MONTHLY_SPEND: 50000
}
```

### Scaling Considerations
- **Card Volume**: Supports thousands of cards per company
- **Image Storage**: CDN for card image delivery
- **Database Indexing**: Optimized queries for card lookup

## Monitoring & Health

### Logging
- **Card Operations**: All card actions logged
- **Error Tracking**: Card creation/update failures
- **Performance Metrics**: Card loading times

### Metrics
- **Card Creation Rate**: New cards per day
- **Card Activation Rate**: Percentage of activated cards
- **Error Rate**: Failed card operations
- **Response Times**: API response performance

### Alerts
- **Card Creation Failures**: ❌ Not implemented
- **Limit Breaches**: ❌ Not implemented
- **Suspicious Activity**: ❌ Not implemented

## Database Usage

### Collections Used
- **cards**: Card information and metadata
- **card_images**: Card design assets
- **card_transactions**: Transaction history
- **card_limits**: User-specific limits

### Schema
```typescript
interface CardDocument {
  _id: ObjectId
  companyId: ObjectId
  userId: ObjectId
  cardNumber: string        // Encrypted
  cardMask: string         // Display format
  expiryDate: Date
  status: CardStatus
  type: 'physical' | 'virtual'
  limits: {
    daily: number
    monthly: number
    transaction: number
  }
  createdAt: Date
  updatedAt: Date
}
```

### Indexes
```javascript
// Required indexes for performance
db.cards.createIndex({ "companyId": 1, "status": 1 })
db.cards.createIndex({ "userId": 1, "type": 1 })
db.cards.createIndex({ "cardMask": 1 })
```

## Security Considerations

### 🔴 CRITICAL VULNERABILITIES

#### 1. Hardcoded Company ID
- **Location**: `src/app/corporate/cards/page.tsx:21`
- **Issue**: Default company ID hardcoded in source
- **Risk**: Unauthorized access to company data
- **CVSS**: 8.5 (High)
- **Fix**: Remove hardcoded values, use dynamic company resolution

#### 2. Insufficient Access Control
- **Issue**: No proper authorization checks for card access
- **Risk**: Users accessing other users' cards
- **CVSS**: 8.0 (High)
- **Fix**: Implement proper authorization middleware

#### 3. Card Data Exposure
- **Issue**: Sensitive card data in client-side state
- **Risk**: Card information exposure
- **CVSS**: 7.5 (High)
- **Fix**: Minimize client-side card data exposure

### 🟡 MEDIUM RISK ISSUES

#### 4. Missing Input Validation
- **Issue**: Insufficient validation for card operations
- **Risk**: Invalid data processing
- **Fix**: Implement comprehensive input validation

#### 5. No Rate Limiting
- **Issue**: No protection against card creation abuse
- **Risk**: Resource exhaustion
- **Fix**: Implement rate limiting for card operations

### Security Fixes Required

```typescript
// 1. Dynamic company resolution
const getCompanyId = async (userId: string) => {
  const user = await User.findById(userId)
  return user.companyId
}

// 2. Authorization middleware
const authorizeCardAccess = async (cardId: string, userId: string) => {
  const card = await Card.findById(cardId)
  if (card.userId !== userId) {
    throw new Error('Unauthorized access')
  }
}

// 3. Data sanitization
const sanitizeCardData = (card: Card) => ({
  id: card._id,
  mask: card.cardMask,
  status: card.status,
  type: card.type
  // Exclude sensitive fields
})
```

## Operational Procedures

### Start/Stop
- **Start**: Automatic with application startup
- **Stop**: Graceful shutdown with application

### Troubleshooting

#### Common Issues
1. **Card Creation Failures**: Check API connectivity and limits
2. **Image Loading Issues**: Verify CDN configuration
3. **Status Sync Problems**: Check database connectivity

#### Debug Commands
```bash
# Check card creation logs
grep "card creation" /var/log/application.log

# Verify card limits
db.cards.aggregate([
  { $group: { _id: "$companyId", count: { $sum: 1 } } }
])
```

### Maintenance
- **Card Expiry Processing**: Monthly batch job
- **Image Cleanup**: Remove unused card images
- **Limit Monitoring**: Check for limit breaches

## APIs & Integration Points

### Card Management Endpoints
- `GET /api/cards` - List user cards
- `POST /api/cards/create` - Create new card
- `PUT /api/cards/:id/status` - Update card status
- `GET /api/cards/:id/transactions` - Get card transactions
- `POST /api/cards/:id/limits` - Update card limits

### Integration Contracts
```typescript
interface CreateCardRequest {
  type: 'physical' | 'virtual'
  embossName: string
  limits: CardLimits
  design?: string
}

interface CardResponse {
  id: string
  mask: string
  status: CardStatus
  expiryDate: string
  type: string
}
```

### Consumers
- **Mobile App**: Card management interface
- **Web Dashboard**: Administrative interface
- **API Partners**: Third-party integrations

## Development Context for AI Agents

### Patterns Used
- **Component Composition**: Reusable card components
- **State Management**: Local state for card operations
- **Error Boundaries**: Component-level error handling

### Extension Points
- **Card Types**: Add new card product types
- **Limits Engine**: Configurable limit rules
- **Design System**: Custom card designs
- **Workflow Engine**: Card approval workflows

### Impact of Changes
- **Schema Changes**: Requires database migration
- **API Changes**: Affects mobile and web clients
- **Limit Changes**: Impacts card creation logic

## Ownership & Contact

### Responsible Team
- **Product Team**: Card feature requirements
- **Development Team**: Implementation and maintenance
- **Operations Team**: Card processing and support

### Subject Matter Experts
- **Card Processing**: Senior Backend Developer
- **UI/UX**: Frontend Lead
- **Security**: Security Engineer

### Documentation Links
- **Card API Documentation**: ❌ Not available
- **Design Guidelines**: ❌ Not available
- **Operations Runbook**: ❌ Not available

## Security Score: 3.8/10 (High Risk)

The card management module has significant security issues including hardcoded values, insufficient access controls, and missing validation that require immediate attention to protect sensitive card data.
