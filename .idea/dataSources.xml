<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="@localhost" uuid="42c52431-e4bc-4ead-8f8e-a98b4cff3f4d">
      <driver-ref>mariadb</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>org.mariadb.jdbc.Driver</jdbc-driver>
      <jdbc-url>*****************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="ryvylcards" uuid="953597bc-28ed-4022-a968-0b2f2c211387">
      <driver-ref>mongo</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.dbschema.MongoJdbcDriver</jdbc-driver>
      <jdbc-url>mongodb+srv://cluster0.alfcv.mongodb.net/ryvyl</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>